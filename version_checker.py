import os
import sys
import json
import platform
import re
import requests
import base64
import zlib
import uuid
import time
import random
import hashlib
import subprocess
import socket
import qrcode
from PIL import Image
from datetime import datetime, timedelta
from cryptography.fernet import Fernet, InvalidToken
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import binascii # For Base64 error handling
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple

# 安全打印函数
def safe_print(*args, **kwargs):
    """安全的打印函数，确保即使在编码问题时也能正常工作"""
    try:
        print(*args, **kwargs)
    except UnicodeEncodeError:
        # 尝试使用ASCII编码（'backslashreplace'将非ASCII字符替换为\uXXXX转义序列）
        try:
            encoded_args = [str(arg).encode('ascii', 'backslashreplace').decode('ascii') for arg in args]
            print(*encoded_args, **kwargs)
        except Exception:
            # 如果还是失败，只打印一条简单的消息
            print("无法显示包含非ASCII字符的内容")
    except Exception as e:
        print(f"打印出错: {type(e).__name__}")

# 禁用QtWebEngine的沙箱模式，提高兼容性
os.environ['QTWEBENGINE_DISABLE_SANDBOX'] = '1'
# 启用硬件加速，提高性能（如果遇到渲染问题可以重新禁用）
os.environ['QTWEBENGINE_CHROMIUM_FLAGS'] = '--log-level=3 --disable-logging'

# 平台特定配置
CURRENT_OS = platform.system().lower()
if CURRENT_OS == 'windows':
    os.environ['OPENSSL_CONF'] = ''
    os.environ['QT_LOGGING_RULES'] = "qt.webenginecontext.warning=false;qt.webengine.warning=false"
elif CURRENT_OS == 'darwin':  # macOS
    os.environ['QT_MAC_WANTS_LAYER'] = '1'
    # macOS额外优化
    os.environ['QT_MAC_DISABLE_NATIVE_MENUBAR'] = '1'  # 禁用原生菜单栏以避免某些渲染问题
    os.environ['QT_QPA_PLATFORM'] = 'cocoa'  # 确保使用Cocoa平台插件
    os.environ['QSG_RENDER_LOOP'] = 'basic'  # 使用基本渲染循环，提高稳定性
    safe_print("已应用macOS平台高级配置")
elif CURRENT_OS == 'linux':
    os.environ['QT_X11_NO_MITSHM'] = '1'  # 尝试解决共享内存问题
    # Linux额外优化
    os.environ['QT_QPA_PLATFORM'] = 'xcb'  # 优先使用XCB后端
    os.environ['XDG_SESSION_TYPE'] = 'x11'  # 明确指定X11会话类型
    os.environ['QT_QPA_PLATFORMTHEME'] = 'generic'  # 使用通用平台主题
    os.environ['QT_SCALE_FACTOR'] = '1'  # 设定缩放因子为1，避免自动缩放问题
    safe_print("已应用Linux平台高级配置")

from PySide6.QtWidgets import (
    QMessageBox, QDialog, QVBoxLayout, QLabel, QPushButton,
    QHBoxLayout, QTextBrowser, QFrame, QWidget, QApplication, QSizePolicy, QLineEdit,
    QCheckBox, QSizePolicy, QTextBrowser
)
from PySide6.QtCore import QObject, Qt, QPropertyAnimation, QSize, QPoint, QUrl, QTimer, Signal, QRegularExpression, QCoreApplication
from PySide6.QtGui import (
    QColor, QPainter, QPen, QFont, QDesktopServices, QFontMetrics,
    QPixmap, QImage, QPainterPath, QBrush, 
    QRegularExpressionValidator, QCursor, QFocusEvent, QKeyEvent
)
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtWebEngineCore import QWebEnginePage, QWebEngineProfile, QWebEngineSettings
from utils import get_app_data_dir # 导入 get_app_data_dir

# 设置标准输出和标准错误的编码为UTF-8
import io
import codecs
# 确保sys.stdout和sys.stderr使用UTF-8编码
if sys.stdout.encoding != 'UTF-8':
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
if sys.stderr.encoding != 'UTF-8':
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# 设置WebEngine资源路径，确保在不同平台上能找到WebEngine进程
def setup_webengine_paths():
    """设置WebEngine资源路径，确保能找到WebEngine进程"""
    try:
        import PySide6
        pyside6_dir = os.path.dirname(PySide6.__file__)
        safe_print(f"PySide6 安装目录: {pyside6_dir}")

        # 尝试几种可能的插件路径结构
        possible_plugin_paths = [
            os.path.join(pyside6_dir, "Qt", "plugins"),                # 结构1: PySide6/Qt/plugins
            os.path.join(pyside6_dir, "plugins"),                       # 结构2: PySide6/plugins (常见于某些wheels)
            os.path.join(pyside6_dir, "..", "..", "Qt", "plugins"),    # 结构3: 如果PySide6在site-packages/PySide6/PySide6这种结构
            pyside6_dir,                                                # 直接在PySide6目录
        ]
        
        # 对于conda环境，插件可能在不同的位置
        if 'CONDA_PREFIX' in os.environ:
            conda_qt_plugins = os.path.join(os.environ['CONDA_PREFIX'], 'Library', 'plugins')  # Windows Conda
            if os.path.isdir(conda_qt_plugins):
                possible_plugin_paths.append(conda_qt_plugins)
            conda_qt_plugins_unix = os.path.join(os.environ['CONDA_PREFIX'], 'lib', 'qt', 'plugins')  # Unix Conda
            if os.path.isdir(conda_qt_plugins_unix):
                possible_plugin_paths.append(conda_qt_plugins_unix)
            safe_print(f"Conda 环境检测到，已添加Conda特定插件路径: {os.environ['CONDA_PREFIX']}")

        # Linux特定路径
        if CURRENT_OS == 'linux':
            # 常见的Linux发行版Qt路径
            linux_paths = [
                "/usr/lib/qt/plugins",
                "/usr/lib/x86_64-linux-gnu/qt5/plugins",  # Debian/Ubuntu
                "/usr/lib/qt5/plugins",                   # Fedora/RHEL
                "/usr/lib64/qt5/plugins",                 # OpenSUSE
                "/usr/local/lib/qt5/plugins"              # 源码安装
            ]
            for path in linux_paths:
                if os.path.isdir(path):
                    possible_plugin_paths.append(path)
                    safe_print(f"添加Linux系统Qt插件路径: {path}")

        # 添加所有可能的路径
        plugin_path_found = False
        for path_to_try in possible_plugin_paths:
            normalized_path = os.path.normpath(path_to_try)
            if os.path.isdir(normalized_path):
                from PySide6.QtCore import QCoreApplication
                QCoreApplication.addLibraryPath(normalized_path)
                safe_print(f"添加Qt插件路径: {normalized_path}")
                plugin_path_found = True
                
                # 检查WebEngine核心进程是否存在
                if CURRENT_OS == 'windows':
                    process_exe = os.path.join(normalized_path, "..", "bin", "QtWebEngineProcess.exe")
                    alt_process_exe = os.path.join(pyside6_dir, "QtWebEngineProcess.exe")
                    if os.path.exists(process_exe) or os.path.exists(alt_process_exe):
                        safe_print(f"找到QtWebEngineProcess.exe (Windows): {process_exe if os.path.exists(process_exe) else alt_process_exe}")
                elif CURRENT_OS == 'darwin':
                    framework_path = os.path.join(normalized_path, "..", "Frameworks", "QtWebEngineCore.framework")
                    if os.path.exists(framework_path):
                        safe_print(f"找到QtWebEngineCore.framework (macOS): {framework_path}")
                elif CURRENT_OS == 'linux':
                    # Linux上WebEngine进程可能在多个位置
                    linux_process_paths = [
                        os.path.join(normalized_path, "..", "libexec", "QtWebEngineProcess"),  # 标准路径
                        os.path.join(pyside6_dir, "libexec", "QtWebEngineProcess"),            # 直接在PySide6目录
                        "/usr/lib/qt5/libexec/QtWebEngineProcess",                            # 系统安装Qt
                        "/usr/lib/x86_64-linux-gnu/libexec/QtWebEngineProcess",               # Debian/Ubuntu
                        "/usr/lib64/qt5/libexec/QtWebEngineProcess"                           # Fedora/RHEL
                    ]
                    for proc_path in linux_process_paths:
                        if os.path.exists(proc_path):
                            safe_print(f"找到QtWebEngineProcess (Linux): {proc_path}")
                            # Linux上需要设置额外的环境变量指向进程路径
                            process_dir = os.path.dirname(proc_path)
                            os.environ["QTWEBENGINEPROCESS_PATH"] = proc_path
                            os.environ["QT_QPA_PLATFORM_PLUGIN_PATH"] = normalized_path
                            break
        
        # 对于打包后的应用，可能需要添加可执行文件所在目录的plugins
        if getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
            exe_plugin_path = os.path.join(sys._MEIPASS, 'PySide6', 'Qt', 'plugins')
            if os.path.isdir(exe_plugin_path):
                from PySide6.QtCore import QCoreApplication
                QCoreApplication.addLibraryPath(exe_plugin_path)
                safe_print(f"为打包应用添加Qt插件路径: {exe_plugin_path}")
            else:
                safe_print(f"打包应用Qt插件路径不存在: {exe_plugin_path}")
                
        if not plugin_path_found:
            safe_print("警告: 未能找到任何Qt插件路径，WebEngine可能无法正常工作")
            
    except Exception as e:
        safe_print(f"设置WebEngine路径时出错: {e}")

# WebEngine相关路径的设置和系统依赖检查为延迟加载
# 这些耗时的操作不应该在导入time阻塞程序启动
_webengine_paths_setup = False
_system_deps_checked = False

def lazy_setup_webengine_paths():
    """延迟加载：设置WebEngine资源路径，确保能找到WebEngine进程"""
    global _webengine_paths_setup
    if _webengine_paths_setup:
        return  # 如果已经设置过，则直接返回
        
    _webengine_paths_setup = True  # 标记为已设置
    
    try:
        import PySide6
        pyside6_dir = os.path.dirname(PySide6.__file__)
        # 精简输出，只在debug模式下才显示详细路径
        # safe_print(f"PySide6 安装目录: {pyside6_dir}")

        # 尝试几种可能的插件路径结构
        possible_plugin_paths = [
            os.path.join(pyside6_dir, "Qt", "plugins"),                # 结构1: PySide6/Qt/plugins
            os.path.join(pyside6_dir, "plugins"),                       # 结构2: PySide6/plugins (常见于某些wheels)
            os.path.join(pyside6_dir, "..", "..", "Qt", "plugins"),    # 结构3: 如果PySide6在site-packages/PySide6/PySide6这种结构
            pyside6_dir,                                                # 直接在PySide6目录
        ]
        
        # 对于conda环境，插件可能在不同的位置
        if 'CONDA_PREFIX' in os.environ:
            conda_qt_plugins = os.path.join(os.environ['CONDA_PREFIX'], 'Library', 'plugins')  # Windows Conda
            if os.path.isdir(conda_qt_plugins):
                possible_plugin_paths.append(conda_qt_plugins)
            conda_qt_plugins_unix = os.path.join(os.environ['CONDA_PREFIX'], 'lib', 'qt', 'plugins')  # Unix Conda
            if os.path.isdir(conda_qt_plugins_unix):
                possible_plugin_paths.append(conda_qt_plugins_unix)
            # 精简不必要的日志
            # safe_print(f"Conda 环境检测到，已添加Conda特定插件路径: {os.environ['CONDA_PREFIX']}")

        # Linux特定路径
        if CURRENT_OS == 'linux':
            # 常见的Linux发行版Qt路径
            linux_paths = [
                "/usr/lib/qt/plugins",
                "/usr/lib/x86_64-linux-gnu/qt5/plugins",  # Debian/Ubuntu
                "/usr/lib/qt5/plugins",                   # Fedora/RHEL
                "/usr/lib64/qt5/plugins",                 # OpenSUSE
                "/usr/local/lib/qt5/plugins"              # 源码安装
            ]
            for path in linux_paths:
                if os.path.isdir(path):
                    possible_plugin_paths.append(path)
                    # 减少详细日志
                    # safe_print(f"添加Linux系统Qt插件路径: {path}")

        # 添加所有可能的路径
        plugin_path_found = False
        webengine_process_found = False  # 跟踪是否找到了WebEngine进程
        
        for path_to_try in possible_plugin_paths:
            normalized_path = os.path.normpath(path_to_try)
            if os.path.isdir(normalized_path):
                from PySide6.QtCore import QCoreApplication
                QCoreApplication.addLibraryPath(normalized_path)
                # 减少路径添加的详细日志，除非发生问题
                # safe_print(f"添加Qt插件路径: {normalized_path}")
                plugin_path_found = True
                
                # 检查WebEngine核心进程是否存在，但减少日志输出
                if CURRENT_OS == 'windows' and not webengine_process_found:
                    process_exe = os.path.join(normalized_path, "..", "bin", "QtWebEngineProcess.exe")
                    alt_process_exe = os.path.join(pyside6_dir, "QtWebEngineProcess.exe")
                    if os.path.exists(process_exe) or os.path.exists(alt_process_exe):
                        webengine_process_found = True
                        # 只保留重要日志
                        # safe_print(f"找到QtWebEngineProcess.exe (Windows)")
                elif CURRENT_OS == 'darwin' and not webengine_process_found:
                    framework_path = os.path.join(normalized_path, "..", "Frameworks", "QtWebEngineCore.framework")
                    if os.path.exists(framework_path):
                        webengine_process_found = True
                        # safe_print(f"找到QtWebEngineCore.framework (macOS)")
                elif CURRENT_OS == 'linux' and not webengine_process_found:
                    # Linux上WebEngine进程可能在多个位置
                    linux_process_paths = [
                        os.path.join(normalized_path, "..", "libexec", "QtWebEngineProcess"),  # 标准路径
                        os.path.join(pyside6_dir, "libexec", "QtWebEngineProcess"),            # 直接在PySide6目录
                        "/usr/lib/qt5/libexec/QtWebEngineProcess",                            # 系统安装Qt
                        "/usr/lib/x86_64-linux-gnu/libexec/QtWebEngineProcess",               # Debian/Ubuntu
                        "/usr/lib64/qt5/libexec/QtWebEngineProcess"                           # Fedora/RHEL
                    ]
                    for proc_path in linux_process_paths:
                        if os.path.exists(proc_path):
                            webengine_process_found = True
                            # safe_print(f"找到QtWebEngineProcess (Linux)")
                            # Linux上需要设置额外的环境变量指向进程路径
                            process_dir = os.path.dirname(proc_path)
                            os.environ["QTWEBENGINEPROCESS_PATH"] = proc_path
                            os.environ["QT_QPA_PLATFORM_PLUGIN_PATH"] = normalized_path
                            break
        
        # 对于打包后的应用，可能需要添加可执行文件所在目录的plugins
        if not plugin_path_found and getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
            exe_plugin_path = os.path.join(sys._MEIPASS, 'PySide6', 'Qt', 'plugins')
            if os.path.isdir(exe_plugin_path):
                from PySide6.QtCore import QCoreApplication
                QCoreApplication.addLibraryPath(exe_plugin_path)
                plugin_path_found = True
                # safe_print(f"为打包应用添加Qt插件路径: {exe_plugin_path}")
        
        # 仅在出现问题时记录警告        
        if not plugin_path_found:
            safe_print("警告: 未能找到任何Qt插件路径，WebEngine可能无法正常工作")
        elif not webengine_process_found:
            safe_print("警告: 未能找到WebEngine进程，WebEngine可能无法正常工作")
            
    except Exception as e:
        safe_print(f"设置WebEngine路径时出错: {e}")

# 延迟加载的系统依赖检查函数
def check_system_dependencies():
    """检查系统是否满足WebEngine运行的基本要求"""
    global _system_deps_checked
    if _system_deps_checked:
        return []  # 如果已经检查过，则直接返回空列表
        
    _system_deps_checked = True  # 标记为已检查
    
    missing_deps = []
    
    # Windows平台特定检查
    if CURRENT_OS == 'windows':
        # 检查VC++运行库
        try:
            # 尝试加载一些VC++ DLL
            import ctypes
            ctypes.WinDLL('vcruntime140.dll')
            ctypes.WinDLL('msvcp140.dll')
            # 减少非必要日志
            # safe_print("Visual C++ Redistributable检查通过")
        except Exception:
            missing_deps.append("Microsoft Visual C++ Redistributable (可能需要安装最新版本)")
            safe_print("警告: 未找到Visual C++ Redistributable 2015-2022")
        
        # 检查DirectX - 不再使用dxdiag，这个命令太慢，改用更高效的方法
        try:
            # 检查D3D11.dll存在作为DirectX存在的标记
            import ctypes
            ctypes.WinDLL('d3d11.dll')
            # DirectX 存在
        except Exception:
            missing_deps.append("DirectX (可能需要更新)")
            safe_print("警告: 可能缺少DirectX组件，建议更新DirectX")
    
    # Linux平台特定检查 - 简化检查过程，减少外部命令调用
    elif CURRENT_OS == 'linux':
        # 仅做最基本的检查，并缩短超时时间
        try:
            # 1. 检查显示服务器
            has_display = bool(os.environ.get('DISPLAY') or os.environ.get('WAYLAND_DISPLAY'))
            if not has_display:
                missing_deps.append("X11或Wayland显示服务器 (可能需要安装xorg或wayland)")
                safe_print("警告: 未检测到X11或Wayland显示服务器")
                
            # 2. 简化OpenGL检查 - 只检查文件是否存在
            opengl_paths = [
                '/usr/lib/libGL.so',
                '/usr/lib/x86_64-linux-gnu/libGL.so',
                '/usr/lib64/libGL.so'
            ]
            has_opengl = any(os.path.exists(path) for path in opengl_paths)
            if not has_opengl:
                missing_deps.append("OpenGL库 (可能需要安装mesa或相关驱动)")
                safe_print("警告: 可能缺少OpenGL支持")
                
            # 3. 简化字体配置检查
            fontconfig_paths = [
                '/etc/fonts/fonts.conf',
                '/usr/share/fontconfig/conf.avail'
            ]
            has_fontconfig = any(os.path.exists(path) for path in fontconfig_paths)
            if not has_fontconfig:
                missing_deps.append("fontconfig (可能需要安装fontconfig)")
                safe_print("警告: 未找到fontconfig配置")
                
        except Exception as e:
            safe_print(f"Linux依赖检查时出错: {e}")
    
    # macOS平台特定检查 - 大幅简化，macOS通常有所需的系统库
    elif CURRENT_OS == 'darwin':
        # macOS通常有所需的系统库，只做最基本的框架检查
        try:
            frameworks_path = '/System/Library/Frameworks'
            required_frameworks = ['OpenGL.framework', 'Cocoa.framework']
            for framework in required_frameworks:
                if not os.path.exists(os.path.join(frameworks_path, framework)):
                    missing_deps.append(f"{framework} (可能需要重新安装或更新macOS)")
                    safe_print(f"警告: 未找到框架 {framework}")
        except Exception as e:
            safe_print(f"macOS依赖检查时出错: {e}")
    
    # 检查Python和PySide6版本 - 只保留基本信息
    python_version = sys.version.split()[0]
    try:
        import PySide6
        pyside_version = PySide6.__version__
        # 只在debug模式或有问题时输出版本信息
        # safe_print(f"Python版本: {python_version}, PySide6版本: {pyside_version}")
    except (ImportError, AttributeError):
        safe_print("警告: 无法确定PySide6版本")
    
    # 只有发现缺失依赖时才详细输出
    if missing_deps:
        safe_print("检测到以下可能缺失的系统依赖:")
        for dep in missing_deps:
            safe_print(f"- {dep}")
            
    return missing_deps

# 获取系统主板UUID的跨平台函数
def get_machine_uuid():
    """获取机器的唯一标识符（主板UUID或类似标识）"""
    try:
        system = platform.system().lower()
        
        if system == 'windows':
            # Windows 平台获取机器唯一标识
            # 尝试多种方法获取硬件信息用于生成一致的标识符
            hardware_info = []
            
            # 获取卷序列号
            try:
                cmd = 'powershell -command "Get-WmiObject -Class Win32_OperatingSystem | Select-Object -ExpandProperty SerialNumber"'
                output = subprocess.check_output(cmd, shell=True).decode('utf-8').strip()
                if output:
                    hardware_info.append(f"os:{output}")
            except Exception as e:
                safe_print(f"获取系统序列号时出错: {e}")

            # 获取主板序列号
            try:
                cmd = 'powershell -command "Get-WmiObject -Class Win32_BaseBoard | Select-Object -ExpandProperty SerialNumber"'
                output = subprocess.check_output(cmd, shell=True).decode('utf-8').strip()
                if output and output != "None" and len(output) > 1:
                    hardware_info.append(f"mb:{output}")
            except Exception:
                pass
                
            # 获取BIOS序列号
            try:
                cmd = 'powershell -command "Get-WmiObject -Class Win32_BIOS | Select-Object -ExpandProperty SerialNumber"'
                output = subprocess.check_output(cmd, shell=True).decode('utf-8').strip()
                if output and output != "None" and len(output) > 1:
                    hardware_info.append(f"bios:{output}")
            except Exception:
                pass
                
            # 获取CPU ID
            try:
                cmd = 'powershell -command "Get-WmiObject -Class Win32_Processor | Select-Object -ExpandProperty ProcessorId"'
                output = subprocess.check_output(cmd, shell=True).decode('utf-8').strip()
                if output:
                    hardware_info.append(f"cpu:{output}")
            except Exception:
                pass
            
            # 获取计算机名和用户名
            try:
                computer_name = os.environ.get('COMPUTERNAME', '')
                user_name = os.environ.get('USERNAME', '')
                if computer_name:
                    hardware_info.append(f"computer:{computer_name}")
                if user_name:
                    hardware_info.append(f"user:{user_name}")
            except Exception:
                pass
                
            # 如果获取到了硬件信息，则使用它们的组合生成一个UUID
            if hardware_info:
                # 使用收集到的硬件信息创建一个唯一标识符
                combined_info = "|".join(hardware_info)
                # 使用SHA-256哈希创建一致的UUID
                hash_obj = hashlib.sha256(combined_info.encode())
                # 使用哈希值的一部分作为UUID的各个部分
                hash_hex = hash_obj.hexdigest()
                uuid_parts = [
                    hash_hex[0:8],
                    hash_hex[8:12],
                    hash_hex[12:16],
                    hash_hex[16:20],
                    hash_hex[20:32]
                ]
                machine_uuid = "-".join(uuid_parts)
                # safe_print(f"已生成基于硬件信息的机器UUID: {machine_uuid[:8]}...")
                return machine_uuid
            else:
                # 如果无法获取硬件信息，回退到使用计算机名和用户名
                computer_name = os.environ.get('COMPUTERNAME', 'unknown')
                user_name = os.environ.get('USERNAME', 'unknown')
                fallback_id = f"{computer_name}_{user_name}"
                return str(uuid.uuid5(uuid.NAMESPACE_DNS, fallback_id))

        elif system == 'darwin':  # macOS
            try:
                # 尝试获取硬件UUID (ioreg -d2 -c IOPlatformExpertDevice | awk -F\" '/IOPlatformUUID/{print $(NF-1)}')
                output = subprocess.check_output("ioreg -d2 -c IOPlatformExpertDevice | awk -F\\\" '/IOPlatformUUID/{print $(NF-1)}'", 
                                                shell=True).decode('utf-8').strip()
                if output:
                    return output
            except Exception as e:
                safe_print(f"获取macOS UUID时出错: {e}")
                
            # 备用方法: 获取MAC地址并组合
            try:
                output = subprocess.check_output("ifconfig en0 | awk '/ether/{print $2}'", shell=True).decode('utf-8').strip()
                if output:
                    return output.replace(':', '')
            except Exception:
                pass
                
        elif system == 'linux':
            try:
                # 尝试读取 /sys/class/dmi/id/product_uuid (需要root权限)
                with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                    return f.read().strip()
            except Exception:
                pass
                
            # 备用方法: 获取 /etc/machine-id
            try:
                with open('/etc/machine-id', 'r') as f:
                    return f.read().strip()
            except Exception:
                pass
                
            # 再备用: 获取MAC地址
            try:
                output = subprocess.check_output("cat /sys/class/net/*/address | head -n 1", shell=True).decode('utf-8').strip()
                if output:
                    return output.replace(':', '')
            except Exception:
                pass
        
        # 如果以上方法都失败，使用Python UUID模块生成一个基于主机名和当前用户的UUID
        # 这不是真正的硬件UUID，但在同一台机器上应该是一致的
        hostname = platform.node()
        username = os.environ.get('USER', os.environ.get('USERNAME', 'unknown'))
        unique_id = f"{hostname}_{username}"
        return str(uuid.uuid5(uuid.NAMESPACE_DNS, unique_id))
            
    except Exception as e:
        safe_print(f"获取机器UUID时出错: {e}")
        # 最后的备用方案：使用随机UUID，但这将导致每次生成的ID不同
        # 在生产环境中应考虑更好的备份方案
        return str(uuid.uuid4())

# 多层加密相关功能
class SecurityManager:
    """处理加密和解密功能的安全管理器"""
    
    # 额外的常量密钥，用于增强安全性
    EXTRA_KEY = b'YCursor_QQ_Verification_Security_Layer'
    
    @staticmethod
    def generate_encryption_key(machine_id, salt=None):
        """基于机器ID生成加密密钥"""
        if not salt:
            # 如果没有提供salt，使用固定salt
            salt = b'YCursor_QQ_Verification_Salt'
        else:
            # 确保salt是bytes类型
            if isinstance(salt, str):
                salt = salt.encode('utf-8')
        
        # 确保machine_id是bytes类型
        if isinstance(machine_id, str):
            machine_id = machine_id.encode('utf-8')
        
        # 使用PBKDF2生成密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,  # 32字节密钥用于Fernet
            salt=salt,
            iterations=100000,  # 较高的迭代次数增加破解难度
        )
        key = base64.urlsafe_b64encode(kdf.derive(machine_id))
        return key
    
    @staticmethod
    def generate_obfuscated_data(actual_data):
        """生成包含实际数据的混淆数据"""
        # 创建随机干扰数据
        noise_count = random.randint(5, 15)  # 添加5-15条随机干扰数据
        result = {}
        
        # 添加随机字段和值
        for _ in range(noise_count):
            fake_key = f"data_{random.randint(10000, 99999)}"
            fake_value = {
                "timestamp": int(time.time()) - random.randint(0, 1000000),
                "value": ''.join(random.choices('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=random.randint(10, 30))),
                "type": random.choice(["config", "temp", "cache", "session", "data"]),
                "status": random.choice([0, 1, 2, 3])
            }
            result[fake_key] = fake_value
        
        # 在随机位置添加真实数据
        real_key = f"data_{random.randint(10000, 99999)}"
        real_position = random.randint(0, noise_count)
        
        # 在结果字典中插入真实数据
        temp_result = {}
        count = 0
        for key, value in result.items():
            if count == real_position:
                temp_result[real_key] = actual_data
            temp_result[key] = value
            count += 1
            
        # 如果还没添加真实数据(real_position == noise_count)，则在最后添加
        if real_position == noise_count:
            temp_result[real_key] = actual_data
            
        # 添加真实数据的位置信息（也进行了混淆）
        position_key = hashlib.md5(real_key.encode()).hexdigest()[:8]
        temp_result["_meta"] = {
            "version": f"{random.randint(1, 9)}.{random.randint(0, 9)}.{random.randint(0, 9)}",
            "created": int(time.time()),
            position_key: real_key,  # 存储真实数据的实际键值
        }
        
        return temp_result
    
    @staticmethod
    def extract_actual_data(obfuscated_data):
        """从混淆数据中提取实际数据"""
        if not isinstance(obfuscated_data, dict) or "_meta" not in obfuscated_data:
            raise ValueError("无效的混淆数据格式")
            
        meta = obfuscated_data.get("_meta", {})
        
        # 查找真实数据的键
        real_key = None
        for key, value in meta.items():
            if key != "version" and key != "created":
                real_key = value
                break
                
        if not real_key or real_key not in obfuscated_data:
            raise ValueError("无法找到真实数据")
            
        return obfuscated_data[real_key]
    
    @staticmethod
    def encrypt_verification_data(machine_id, data, extra_salt=None):
        """加密验证数据
        
        Args:
            machine_id: 机器唯一标识
            data: 要加密的数据字典
            extra_salt: 可选的额外salt
            
        Returns:
            str: 加密后的Base64字符串
        """
        try:
            # 生成混淆后的数据
            obfuscated_data = SecurityManager.generate_obfuscated_data(data)
            
            # 转换为JSON
            json_data = json.dumps(obfuscated_data)
            
            # 压缩数据
            compressed_data = zlib.compress(json_data.encode('utf-8'))
            
            # 生成加密密钥
            salt = SecurityManager.EXTRA_KEY
            if extra_salt:
                salt = extra_salt
            
            key = SecurityManager.generate_encryption_key(machine_id, salt)
            
            # Fernet加密
            fernet = Fernet(key)
            encrypted_data = fernet.encrypt(compressed_data)
            
            # Base64编码
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            safe_print(f"加密验证数据时出错: {e}")
            return None
    
    @staticmethod
    def decrypt_verification_data(machine_id, encrypted_data, extra_salt=None):
        """解密验证数据
        
        Args:
            machine_id: 机器唯一标识
            encrypted_data: 加密后的Base64字符串
            extra_salt: 可选的额外salt
            
        Returns:
            dict: 解密后的数据字典，失败返回None
        """
        try:
            # Base64解码
            binary_data = base64.urlsafe_b64decode(encrypted_data)
            
            # 生成解密密钥
            salt = SecurityManager.EXTRA_KEY
            if extra_salt:
                salt = extra_salt
            
            key = SecurityManager.generate_encryption_key(machine_id, salt)
            
            # Fernet解密
            fernet = Fernet(key)
            decrypted_data = fernet.decrypt(binary_data)
            
            # 解压缩
            decompressed_data = zlib.decompress(decrypted_data)
            
            # JSON解析
            json_data = json.loads(decompressed_data.decode('utf-8'))
            
            # 提取实际数据
            return SecurityManager.extract_actual_data(json_data)
            
        except Exception as e:
            # safe_print(f"解密验证数据时出错: {e}")
            return None

# QQ群验证相关类
@dataclass
class QQGroup:
    """QQ群数据类"""
    group_name: str
    group_id: str
    member_count: Optional[int] = None

class QQGroupAPI:
    """QQ群API，用于扫码登录和获取群列表"""
    
    def __init__(self, logger=None):
        """初始化QQ群API
        
        Args:
            logger: 日志函数，默认为safe_print
        """
        self.session = requests.Session()
        self.base_url = "https://qun.qq.com"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Content-Type": "application/json;charset=UTF-8",
            "Referer": "https://qun.qq.com/member.html",
            "Origin": "https://qun.qq.com"
        }
        self.session.headers.update(self.headers)
        self.login_params = {
            "appid": "715030901",
            "daid": "73",
            "pt_3rd_aid": "0",
        }
        self.logger = logger if logger else safe_print
        self.qr_img_data = None  # 存储二维码图片数据
        self.current_qq = ""     # 存储当前登录的QQ号
        self.qrsig = None        # 存储qrsig cookie值
        self.ptqrtoken = None    # 存储ptqrtoken值

    def get_login_qrcode(self) -> bytes:
        """
        获取登录二维码图片数据
        :return: 二维码图片数据(bytes)，失败返回None
        """
        try:
            self.logger("正在初始化登录...")
            # 1. 访问群管理页面获取xlogin URL
            response = self.session.get("https://qun.qq.com/member.html")
            xlogin_url = f"https://xui.ptlogin2.qq.com/cgi-bin/xlogin?appid={self.login_params['appid']}&daid={self.login_params['daid']}&s=8&pt_3rd_aid=0"
                
            # 2. 访问xlogin页面获取必要参数
            self.logger("正在获取登录参数...")
            response = self.session.get(xlogin_url)
            
            # 3. 获取二维码
            self.logger("正在获取二维码...")
            qr_code_url = "https://ssl.ptlogin2.qq.com/ptqrshow"
            qr_response = self.session.get(qr_code_url, params={
                "appid": self.login_params["appid"],
                "e": "2",
                "l": "M",
                "s": "3",
                "d": "72",
                "v": "4",
                "t": str(time.time()),
                "daid": self.login_params["daid"],
                "pt_3rd_aid": self.login_params["pt_3rd_aid"],
            })

            if qr_response.status_code != 200:
                self.logger("获取二维码失败")
                return None
                
            # 保存qrsig和计算ptqrtoken
            self.qrsig = qr_response.cookies.get("qrsig")
            if self.qrsig:
                self.ptqrtoken = self._get_ptqrtoken(self.qrsig)
                
            # 保存二维码图片数据
            self.qr_img_data = qr_response.content
            return qr_response.content
            
        except Exception as e:
            self.logger(f"获取登录二维码失败: {str(e)}")
            return None

    def check_qrcode_status(self) -> Tuple[int, str, str]:
        """
        检查二维码状态
        :return: (状态码, 状态消息, 重定向URL)
            状态码:
            0 - 二维码未失效
            1 - 二维码认证中
            2 - 登录成功
            3 - 二维码已失效
            4 - 发生错误
        """
        if not self.ptqrtoken:
            return 4, "未初始化二维码", ""
            
        try:
            qr_state_url = "https://ssl.ptlogin2.qq.com/ptqrlogin"
            state_response = self.session.get(qr_state_url, params={
                "u1": "https://qun.qq.com/member.html",
                "ptqrtoken": self.ptqrtoken,
                "ptredirect": "0",
                "h": "1",
                "t": "1",
                "g": "1",
                "from_ui": "1",
                "ptlang": "2052",
                "action": "0-0-" + str(int(time.time())),
                "js_ver": "23123123",
                "js_type": "1",
                "login_sig": "",
                "pt_uistyle": "40",
                "aid": self.login_params["appid"],
                "daid": self.login_params["daid"],
            })
            
            response_text = state_response.text
            
            redirect_url = ""
            # 尝试提取重定向URL，用于登录成功的情况
            match = re.search(r"'(https://[^']+)'", response_text)
            if match:
                redirect_url = match.group(1)
            
            # 判断状态码
            if "二维码未失效" in response_text:
                return 0, "请扫码登录QQ...", redirect_url
            elif "二维码认证中" in response_text:
                return 1, "扫码成功，请在手机上确认...", redirect_url
            elif "登录成功" in response_text:
                return 2, "登录成功！", redirect_url
            elif "二维码已失效" in response_text:
                return 3, "二维码已失效，请重新获取", redirect_url
            else:
                return 4, f"未知状态: {response_text[:50]}...", redirect_url
                
        except Exception as e:
            self.logger(f"检查二维码状态失败: {str(e)}")
            return 4, f"检查失败: {str(e)}", ""

    def finish_login(self, redirect_url: str) -> bool:
        """
        完成登录流程
        :param redirect_url: 登录成功后的重定向URL
        :return: 是否成功
        """
        try:
            # 访问跳转URL获取必要的Cookie
            response = self.session.get(redirect_url, allow_redirects=True)
            
            # 检查关键Cookie是否存在
            required_cookies = ['skey', 'p_uin']
            missing_cookies = [cookie for cookie in required_cookies if not self.session.cookies.get(cookie)]
            
            if missing_cookies:
                self.logger(f"缺少必要的Cookie: {', '.join(missing_cookies)}")
                return False
            
            # 再次访问群管理页面
            self.session.get("https://qun.qq.com/member.html")
            
            # 尝试获取当前登录的QQ号
            uin_cookie = self.session.cookies.get('uin', '')
            p_uin_cookie = self.session.cookies.get('p_uin', '')
            
            # 提取QQ号
            if uin_cookie:
                uin_match = re.search(r'o0*(\d+)', uin_cookie)
                if uin_match:
                    self.current_qq = uin_match.group(1)
            elif p_uin_cookie:
                p_uin_match = re.search(r'o0*(\d+)', p_uin_cookie)
                if p_uin_match:
                    self.current_qq = p_uin_match.group(1)
                    
            if self.current_qq:
                pass # QQ号已获取，无需额外日志
            else:
                self.logger("登录成功，但无法获取QQ号") # 此日志不包含敏感信息，可以保留
            
            return True
            
        except Exception as e:
            self.logger(f"完成登录流程失败: {str(e)}")
            return False

    def get_joined_groups(self) -> List[QQGroup]:
        """
        获取已加入的QQ群列表
        :return: QQ群列表
        """
        try:
            # 获取群列表的API接口
            url = f"{self.base_url}/cgi-bin/qun_mgr/get_group_list"
            bkn = self._get_g_tk()
            
            # 更新请求头
            headers = {
                "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
                "Origin": "https://qun.qq.com",
                "Referer": "https://qun.qq.com/member.html"
            }
            self.session.headers.update(headers)
            
            # 构造POST数据
            data = {
                "bkn": bkn
            }
            
            
            # 发送POST请求
            response = self.session.post(url, data=data)
            self.logger(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, str):
                        data = json.loads(data)
                        
                    self.logger(f"解析响应JSON成功")
                    
                    if data.get("ec") == 4:  # 未登录
                        self.logger("登录状态已失效，请重新登录")
                        return []
                        
                    groups = []
                    # 检查create、manage和join三种类型的群
                    for group_type in ['create', 'manage', 'join']:
                        group_list = data.get(group_type, [])
                        for group in group_list:
                            groups.append(QQGroup(
                                group_name=group.get("gn", ""),
                                group_id=str(group.get("gc", "")),
                                member_count=group.get("members", 0)
                            ))
                    self.logger(f"共找到 {len(groups)} 个群")
                    return groups
                except json.JSONDecodeError as e:
                    self.logger(f"JSON解析失败: {str(e)}")
                    return []
            return []
        except Exception as e:
            self.logger(f"获取群列表失败: {str(e)}")
            return []

    def _get_ptqrtoken(self, qrsig: str) -> int:
        """
        计算ptqrtoken
        :param qrsig: qrsig cookie值
        :return: ptqrtoken值
        """
        hash_val = 0
        for char in qrsig:
            hash_val += (hash_val << 5) + ord(char)
        return hash_val & 2147483647

    def _get_g_tk(self) -> str:
        """
        计算g_tk/bkn值，使用skey而不是p_skey
        :return: g_tk值
        """
        skey = self.session.cookies.get("skey", "")
        
        hash_val = 5381
        for char in skey:
            hash_val += (hash_val << 5) + ord(char)
        return str(hash_val & 2147483647)

    def get_cookies_dict(self) -> dict:
        """获取当前会话的Cookies字典"""
        return self.session.cookies.get_dict()
        
    def get_current_qq(self) -> str:
        """获取当前登录的QQ号"""
        return self.current_qq

# 导入主题配置
class Theme:
    # 主色
    PRIMARY = "#121317"  # 主背景色（深黑色）
    SECONDARY = "#1A1D23"  # 次要背景色（稍浅的黑色）
    ACCENT = "#2B9D7C"  # 暗绿色强调色（更暗的绿色）
    ACCENT_HOVER = "#34B892"  # 悬停时的强调色
    ACCENT_PRESSED = "#24856A"  # 按下时的强调色（更深的绿色）
    TEXT_PRIMARY = "#FFFFFF"  # 主要文本颜色
    TEXT_SECONDARY = "#9CA2AE"  # 次要文本颜色
    
    # 状态颜色
    SUCCESS = "#2B9D7C"  # 成功状态（暗绿色）
    WARNING = "#CBAF67"  # 警告状态（暗黄色）
    ERROR = "#BC4A59"  # 错误状态（暗红色）
    
    # 其他颜色
    CARD_BG = "#1E2128"  # 卡片背景
    BORDER = "#2A2E36"  # 边框颜色
    HOVER = "#252830"  # 悬停颜色
    
    # 卡片层级
    CARD_LEVEL_1 = "#1A1D23"  # 一级卡片
    CARD_LEVEL_2 = "#21252D"  # 二级卡片
    
    # 毛玻璃效果
    GLASS_BG = "rgba(30, 33, 40, 0.75)"  # 半透明背景
    GLASS_BORDER = "rgba(60, 63, 70, 0.35)"  # 半透明边框
    
    # 字体
    FONT_FAMILY = "'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif"
    FONT_SIZE_SMALL = "12px"
    FONT_SIZE_NORMAL = "14px"
    FONT_SIZE_TITLE = "18px"
    
    # 圆角
    BORDER_RADIUS = "14px"
    BORDER_RADIUS_SMALL = "10px"
    
    # 间距
    SPACING_SMALL = "8px"
    SPACING_MEDIUM = "16px"
    SPACING_LARGE = "24px"
    
    # 按钮样式
    BUTTON_PRIMARY_STYLE = f"""
        QPushButton {{
            background-color: {ACCENT};
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
            min-width: 80px;
        }}
        QPushButton:hover {{
            background-color: {ACCENT_HOVER};
        }}
        QPushButton:pressed {{
            background-color: {ACCENT_PRESSED};
        }}
        QPushButton:disabled {{
            background-color: #4D5057;
            color: #8A8D93;
        }}
    """
    
    BUTTON_SECONDARY_STYLE = f"""
        QPushButton {{
            background-color: {CARD_LEVEL_1};
            color: {TEXT_SECONDARY};
            border: 1px solid {BORDER};
            border-radius: 6px;
            padding: 8px 16px;
            min-width: 80px;
        }}
        QPushButton:hover {{
            background-color: {HOVER};
            color: {TEXT_PRIMARY};
        }}
        QPushButton:pressed {{
            background-color: {CARD_LEVEL_2};
        }}
    """

class SafeQWebEngineView(QWebEngineView):
    """安全的WebEngineView类，增加错误处理和超时检测"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.loadStarted.connect(self._on_load_started)
        self.loadFinished.connect(self._on_load_finished)
        self._load_timeout_timer = QTimer(self)
        self._load_timeout_timer.setSingleShot(True)
        self._load_timeout_timer.timeout.connect(self._on_load_timeout)
        
    def _on_load_started(self):
        # 页面开始加载时启动超时计时器
        self._load_timeout_timer.start(10000)  # 10秒超时
        
    def _on_load_finished(self, success):
        # 页面加载完成时停止超时计时器
        self._load_timeout_timer.stop()
        
    def _on_load_timeout(self):
        # 页面加载超时
        safe_print("WebEngine页面加载超时，可能是网络连接问题或渲染进程崩溃")
        self.stop()  # 停止当前页面加载
        # 通知父容器
        parent = self.parent()
        if parent and hasattr(parent, 'handle_webengine_timeout'):
            parent.handle_webengine_timeout()

class StyledMessageBox(QDialog):
    """自定义样式消息框"""
    
    INFORMATION = 0
    WARNING = 1
    CRITICAL = 2
    QUESTION = 3
    
    def __init__(self, parent=None, title="", text="", icon_type=0, buttons=None, update_url=None, show_update_url=True):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setModal(True)
        self.setFixedWidth(500)
        
        self.update_url = update_url

        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建背景框架
        self.bg_frame = QFrame(self)
        self.bg_frame.setObjectName("bg_frame")
        self.bg_frame.setStyleSheet(f"""
            #bg_frame {{
                background-color: {Theme.PRIMARY};
                border: 1px solid {Theme.GLASS_BORDER};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        # 背景框架布局
        self.bg_layout = QVBoxLayout(self.bg_frame)
        self.bg_layout.setContentsMargins(25, 25, 25, 25)
        self.bg_layout.setSpacing(15)
        
        # --- Title Area (Icon + Title) --- #
        title_area_layout = QHBoxLayout()
        title_area_layout.setSpacing(0) # Let addSpacing control space
        
        # 图标
        icon_label = QLabel()
        icon_style = "color: white; font-size: 18px; font-weight: bold;"
        icon_text = "i"
        icon_color = Theme.ACCENT
        
        if icon_type == self.WARNING:
            icon_text = "!"
            icon_color = Theme.WARNING
        elif icon_type == self.CRITICAL:
            icon_text = "✕"
            icon_color = Theme.ERROR
        elif icon_type == self.QUESTION:
            icon_text = "?"
            icon_color = Theme.ACCENT
        
        icon_label.setStyleSheet(f"""
            background-color: {icon_color};
            color: white;
            font-size: 18px; /* Smaller font for smaller icon */
            font-weight: bold;
            border-radius: 15px; /* Half of new size */
            min-width: 30px;   /* Reduced size */
            max-width: 30px;
            min-height: 30px;
            max-height: 30px;
            qproperty-alignment: AlignCenter;
        """)
        icon_label.setText(icon_text)
        
        title_area_layout.addWidget(icon_label)
        title_area_layout.addSpacing(12) # Space between icon and title
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_TITLE};
            font-weight: bold;
        """)
        title_area_layout.addWidget(title_label)
        title_area_layout.addStretch() # Push icon and title left
        
        # Add title area to main layout
        self.bg_layout.addLayout(title_area_layout)
        
        self.bg_layout.addSpacing(15) # Space after title row

        text_browser = QTextBrowser()
        text_browser.setReadOnly(True)
        text_browser.setText(text)
        text_browser.setStyleSheet(f"""
            QTextBrowser {{
                background-color: transparent;
                color: {Theme.TEXT_SECONDARY};
                border: none;
                font-size: {Theme.FONT_SIZE_NORMAL};
                selection-background-color: {Theme.ACCENT};
                selection-color: white;
            }}
            QScrollBar:vertical {{
                background: {Theme.CARD_LEVEL_1};
                width: 10px;
                margin: 0px;
            }}
            QScrollBar::handle:vertical {{
                background: {Theme.BORDER};
                min-height: 20px;
                border-radius: 5px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
            }}
        """)
        text_browser.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        text_browser.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        text_browser.setContentsMargins(0, 0, 0, 0)

        # --- Calculate and set dynamic height ---
        fm = QFontMetrics(text_browser.font())
        min_height = fm.height() # Minimum height for one line (no extra padding)
        max_height = 160 # User defined max height

        # Ensure layout is updated to get correct document size
        text_browser.document().adjustSize()
        # Use documentLayout for more accurate height
        doc_height = text_browser.document().documentLayout().documentSize().height()

        # Subtract a fixed offset empirically determined to compensate extra space
        fixed_offset = 35 # Increased offset
        target_height = doc_height - fixed_offset

        # Clamp height between min and max
        # Ensure final height is at least min_height even after subtracting offset
        final_height = max(min_height, min(target_height, max_height))

        text_browser.setFixedHeight(int(final_height))
        text_browser.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed) # Set fixed vertical policy
        # --- End dynamic height calculation ---

        # Add text browser directly to main layout
        self.bg_layout.addWidget(text_browser)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet(f"background-color: {Theme.BORDER}; border: none;")
        separator.setFixedHeight(1)
        self.bg_layout.addWidget(separator)
        
        self.bg_layout.addSpacing(15)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        # Add stretch first to push buttons to the right
        button_layout.addStretch(1)

        self.button_result = QDialog.DialogCode.Rejected
        
        # 默认按钮
        if not buttons:
            buttons = ["确定"]
        
        # Add Open URL button (will appear on the right, before standard buttons)
        if show_update_url and self.update_url:
            open_url_button = QPushButton("打开更新链接")
            open_url_button.setStyleSheet(Theme.BUTTON_SECONDARY_STYLE)
            open_url_button.clicked.connect(self._open_update_url)
            button_layout.addWidget(open_url_button)

        # Add standard buttons
        for i, button_text in enumerate(buttons):
            button = QPushButton(button_text)

            # Apply specific style for '退出' button or standard styles
            if button_text == "退出":
                # Apply primary style but with smaller width
                exit_button_style = Theme.BUTTON_PRIMARY_STYLE.replace("min-width: 80px;", "min-width: 60px;")
                button.setStyleSheet(exit_button_style)
                # '退出' button usually corresponds to rejecting or closing the dialog
                # Use reject() unless it's the only button (i=0)
                if i == 0 and len(buttons) == 1:
                    button.clicked.connect(self.accept) # If only button, treat as accept
                else:
                    button.clicked.connect(self.reject)
            elif i == 0:
                # First button (if not '退出') is primary (Accept)
                button.setStyleSheet(Theme.BUTTON_PRIMARY_STYLE)
                button.clicked.connect(self.accept)
            else:
                # Other buttons are secondary (Reject)
                button.setStyleSheet(Theme.BUTTON_SECONDARY_STYLE)
                button.clicked.connect(self.reject)

            # Add standard buttons to the right
            button_layout.addWidget(button)

        self.bg_layout.addLayout(button_layout)
        
        # 将背景框架添加到主布局
        self.main_layout.addWidget(self.bg_frame)
        
        # 初始化拖动相关变量
        self.drag_position = None
    
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        if event.buttons() & Qt.MouseButton.LeftButton and self.drag_position:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event):
        self.drag_position = None
        event.accept()
    
    def _open_update_url(self):
        if hasattr(self, 'update_url') and self.update_url:
            try:
                QDesktopServices.openUrl(QUrl(self.update_url))
            except Exception as e:
                print(f"打开更新链接时出错: {self.update_url}, 错误: {str(e)}")

    @staticmethod
    def showMessage(parent, title, text, icon=INFORMATION, buttons=None, update_url=None, show_update_url=True):
        """显示消息框"""
        dialog = StyledMessageBox(parent, title, text, icon, buttons, update_url=update_url, show_update_url=show_update_url)
        result = dialog.exec()
        return result == QDialog.DialogCode.Accepted

class DisclaimerDialog(QDialog):
    """免责声明对话框"""
    
    def __init__(self, parent=None, title="免责声明", content="", agree_text="我同意", cancel_text="退出"):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setModal(True)
        self.setFixedWidth(500)
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建背景框架
        self.bg_frame = QFrame(self)
        self.bg_frame.setObjectName("bg_frame")
        self.bg_frame.setStyleSheet(f"""
            #bg_frame {{
                background-color: {Theme.PRIMARY};
                border: 1px solid {Theme.GLASS_BORDER};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        # 背景框架布局
        self.bg_layout = QVBoxLayout(self.bg_frame)
        self.bg_layout.setContentsMargins(25, 25, 25, 25)
        self.bg_layout.setSpacing(20)
        
        # 标题标签
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-weight: bold;
            font-size: {Theme.FONT_SIZE_TITLE};
        """)
        self.bg_layout.addWidget(title_label)
        
        # 内容文本浏览器
        content_browser = QTextBrowser()
        content_browser.setStyleSheet(f"""
            QTextBrowser {{
                background-color: {Theme.PRIMARY};
                color: {Theme.TEXT_SECONDARY};
                border: none;
                font-size: {Theme.FONT_SIZE_NORMAL};
                selection-background-color: {Theme.ACCENT};
                selection-color: white;
            }}
            QScrollBar:vertical {{
                background: {Theme.CARD_LEVEL_1};
                width: 12px;
                margin: 0px;
            }}
            QScrollBar::handle:vertical {{
                background: {Theme.BORDER};
                min-height: 20px;
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
            }}
        """)
        # 直接使用HTML内容，不进行额外包装
        content_browser.setHtml(content)
        content_browser.setReadOnly(True)
        content_browser.setMinimumHeight(200)
        self.bg_layout.addWidget(content_browser)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet(f"background-color: {Theme.BORDER};")
        separator.setFixedHeight(1)
        self.bg_layout.addWidget(separator)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        button_layout.addStretch(1)
        
        # 取消按钮
        cancel_button = QPushButton(cancel_text)
        cancel_button.setStyleSheet(Theme.BUTTON_SECONDARY_STYLE)
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        # 同意按钮
        agree_button = QPushButton(agree_text)
        agree_button.setStyleSheet(Theme.BUTTON_PRIMARY_STYLE)
        agree_button.clicked.connect(self.accept)
        button_layout.addWidget(agree_button)
        
        self.bg_layout.addLayout(button_layout)
        
        # 将背景框架添加到主布局
        self.main_layout.addWidget(self.bg_frame)
        
        # 初始化拖动相关变量
        self.drag_position = None
    
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        if event.buttons() & Qt.MouseButton.LeftButton and self.drag_position:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event):
        self.drag_position = None
        event.accept()

class CustomWebEnginePage(QWebEnginePage):
    """自定义WebEngine页面，用于处理SSL错误和其他Web请求"""
    
    def __init__(self, profile, parent=None):
        super().__init__(profile, parent)
        # 确保连接渲染进程终止信号
        self.renderProcessTerminated.connect(self.on_render_process_terminated)
        
    def certificateError(self, error):
        """处理证书错误，允许继续访问"""
        safe_print(f"SSL错误: {error.errorDescription()} - URL: {error.url().toString()}")
        return True  # 忽略SSL错误并继续

    def on_render_process_terminated(self, terminationStatus, exitCode):
        """处理渲染进程终止事件"""
        termination_str = "正常终止" if terminationStatus == QWebEnginePage.TerminationStatus.NormalTermination else "异常终止/崩溃"
        safe_print(f"WebEngine渲染进程终止: 状态={termination_str}, 退出码={exitCode}")
        # 通知父窗口，让其处理渲染进程终止
        parent_widget = self.view()
        if parent_widget and hasattr(parent_widget, 'on_render_process_terminated'):
            parent_widget.on_render_process_terminated(terminationStatus, exitCode)
        elif parent_widget and parent_widget.parent() and hasattr(parent_widget.parent(), 'on_render_process_terminated'):
            # 如果视图本身没有处理方法，尝试获取其父级对象的方法
            parent_widget.parent().on_render_process_terminated(terminationStatus, exitCode)

class ButtonEventFilter(QObject):
    """用于处理按钮事件的过滤器，动态设置光标样式"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def eventFilter(self, obj, event):
        # 处理进入和离开事件以设置适当的光标
        if event.type() == event.Type.Enter:
            if not obj.isEnabled():
                obj.setCursor(Qt.CursorShape.ForbiddenCursor)
            else:
                obj.setCursor(Qt.CursorShape.PointingHandCursor)
        elif event.type() == event.Type.Leave:
            # 恢复默认光标
            obj.unsetCursor()
        
        # 传递事件给默认处理器
        return super().eventFilter(obj, event)

class VerificationCodeLineEdit(QLineEdit):
    code_entered = Signal(int, str)  # (index, text)
    backspace_on_empty = Signal(int) # (index)
    editing_started = Signal()

    def __init__(self, index, parent=None):
        super().__init__(parent)
        self.index = index
        self.setMaxLength(1)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.is_in_error_state = False # New member

        # Base style for the QLineEdit - ensure no text-shadow or color:transparent here
        self.base_style = f"""
            QLineEdit {{
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: 8px;
                font-size: 18px;
                font-weight: normal;
                padding-top: 8px;
                padding-right: 8px;
                padding-bottom: 10px;
                padding-left: 10px;
                selection-background-color: {Theme.CARD_LEVEL_1}; /* Hide selection highlight */
                selection-color: {Theme.TEXT_PRIMARY}; /* Ensure selected text has normal color */
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
            }}
        """
        self.setStyleSheet(self.base_style)
        # self.current_style_is_hidden = False # Removed this line

        self.textChanged.connect(self._handle_text_changed)

    def set_error_state(self, is_error: bool):
        if self.is_in_error_state == is_error:
            return # No change needed

        self.is_in_error_state = is_error
        if is_error:
            # Directly define the error style, ensuring Theme.ERROR is used for border
            error_stylesheet = f"""
                QLineEdit {{
                    background-color: {Theme.CARD_LEVEL_1};
                    color: {Theme.TEXT_PRIMARY};
                    border: 1px solid {Theme.ERROR}; /* Error border for normal state */
                    border-radius: 8px;
                    font-size: 18px;
                    font-weight: normal;
                    padding-top: 8px;
                    padding-right: 8px;
                    padding-bottom: 10px;
                    padding-left: 10px;
                    selection-background-color: {Theme.CARD_LEVEL_1};
                    selection-color: {Theme.TEXT_PRIMARY};
                }}
                QLineEdit:focus {{
                    border: 1px solid {Theme.ERROR}; /* Error border for focused state */
                    /* Re-state other base focus properties if they existed beyond border */
                    /* Since base_style QLineEdit:focus only had border, this is fine. */
                    /* If base_style QLineEdit:focus had e.g. background-color, add it here too. */
                }}
            """
            self.setStyleSheet(error_stylesheet)
        else:
            self.setStyleSheet(self.base_style)

    def _handle_text_changed(self, text: str):
        current_text = self.text()
        
        # Convert to uppercase if it's a single lowercase letter
        if len(current_text) == 1 and current_text.islower():
            current_text = current_text.upper()
            # setText will trigger _handle_text_changed again, so block signals
            self.blockSignals(True)
            self.setText(current_text)
            self.blockSignals(False)
            # After setText, current_text is now uppercase. Proceed with this updated text.

        if current_text: # Check current_text again, which is now guaranteed to be uppercase if it was a letter
            self._hide_cursor() 
            self.code_entered.emit(self.index, current_text)
        else:  # Text is empty
            self._show_cursor() 

    def _hide_cursor(self):
        self.setReadOnly(True)

    def _show_cursor(self):
        self.setReadOnly(False)

    def focusInEvent(self, event: QFocusEvent):
        super().focusInEvent(event)
        self.setReadOnly(False) 

        if self.is_in_error_state:
            self.set_error_state(True) 
        else:
            self.setStyleSheet(self.base_style) 

        if self.text():
            self.selectAll() 
            self._hide_cursor() 
        else:
            # Field is empty and writable, cursor will show due to setReadOnly(False)
            pass

    def focusOutEvent(self, event: QFocusEvent):
        super().focusOutEvent(event)
        self.setReadOnly(False) 
        
        if self.is_in_error_state:
            self.set_error_state(True) 
        else:
            self.setStyleSheet(self.base_style)

    def keyPressEvent(self, event: QKeyEvent):
        key = event.key()
        current_text = self.text()
        is_currently_readonly = self.isReadOnly()

        # Emit signal if a key press might lead to error correction
        if key == Qt.Key_Backspace or key == Qt.Key_Delete or event.text().isalnum():
            self.editing_started.emit()

        if is_currently_readonly:
            self.setReadOnly(False) # Temporarily make it writable for this event
            # After making it writable, re-select text if present, to ensure replacement
            if self.text(): # Check if there's still text after potential modification by other handlers
                self.selectAll()

        if key == Qt.Key_Backspace:
            if current_text:
                self.clear()
                # textChanged will call _show_cursor() making it writable and applying base style
            else:
                self.backspace_on_empty.emit(self.index)
            event.accept()
            # If it was made writable just for this, _handle_text_changed will make it read-only again if text remains.
            # If it became empty, _handle_text_changed will keep it writable.
            return
        elif key == Qt.Key_Delete:
            if current_text:
                self.clear()
            event.accept()
            # Similar to Backspace, rely on _handle_text_changed for final state.
            return
        
        super().keyPressEvent(event)
        # After super().keyPressEvent(event), the text might have changed.
        # The textChanged signal is connected to _handle_text_changed, which will then call
        # _hide_cursor() (making it read-only if text is present) or 
        # _show_cursor() (keeping it writable if empty).
        # So, no need to revert readOnly state here explicitly if it was temporarily changed.

class VerificationDialog(QDialog):
    """QQ群验证对话框"""
    
    def __init__(self, parent=None, verification_config=None, mode="code_verification", logger=None):
        super().__init__(parent)
        # 设置窗口属性
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setModal(True)
        self.setMinimumSize(450, 380)  # 减小窗口高度
        
        # 验证配置和模式
        self.verification_config = verification_config if verification_config else {}
        self.verification_mode = mode
        self.logger = logger if logger else safe_print
        
        # 创建QQ群API用于二维码登录和群列表获取
        self.qq_api = QQGroupAPI(self.logger)
        
        # 跟踪登录状态
        self.is_logged_in = False
        self.current_qq = ""  # 存储当前登录的QQ号
        self.verification_result = False  # 验证结果
        
        # QR码刷新和状态检查
        self.qr_retry_count = 0  # 二维码重试次数
        self.qr_status_timer = None  # 状态检查计时器
        self.qr_refresh_timer = None  # 二维码刷新计时器
        self.qrcode_expired = False  # 二维码是否已过期
        
        # 验证码输入框列表
        self.code_input_fields = []
        self.is_verifying = False # Add verification lock
        
        # 创建遮罩层
        self.overlay = QWidget(self)
        self.overlay.setStyleSheet(f"""
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: {Theme.BORDER_RADIUS};
        """
        )
        self.overlay.hide()  # 默认隐藏
        
        # 初始化UI
        self.init_ui()
        
        # 设置 QToolTip 全局样式
        # 确保 QApplication 实例存在
        app_instance = QApplication.instance()
        if app_instance:
            current_stylesheet = app_instance.styleSheet()
            tooltip_style = f"""
                QToolTip {{
                    background-color: {Theme.SECONDARY};
                    color: {Theme.TEXT_PRIMARY};
                    border: 1px solid {Theme.BORDER};
                    border-radius: 6px;
                    padding: 5px 8px;
                    opacity: 230;
                }}
            """
            # 追加样式，避免覆盖其他全局样式
            # 如果已有样式包含 QToolTip, 可能需要更复杂的合并逻辑，但通常这样追加是安全的
            if "QToolTip" not in current_stylesheet:
                 app_instance.setStyleSheet(current_stylesheet + tooltip_style)
            else:
                # 如果已经有 QToolTip 样式，这里简单替换，或者需要解析并合并
                # 为了简单起见，如果存在则先移除旧的，再添加新的（可能不完美）
                # 更稳妥的方法是确保只设置一次，或在应用层面统一管理
                import re
                current_stylesheet = re.sub(r"QToolTip\\s*{{[^}}]*}}", "", current_stylesheet, flags=re.DOTALL)
                app_instance.setStyleSheet(current_stylesheet + tooltip_style)
        
    def resizeEvent(self, event):
        """处理窗口大小改变事件，确保遮罩层覆盖整个窗口"""
        super().resizeEvent(event)
        self.overlay.setGeometry(self.rect())
        
    def showOverlay(self):
        """显示遮罩层"""
        self.overlay.setGeometry(self.rect())
        self.overlay.show()
        self.overlay.raise_()  # 确保遮罩在最上层
        
    def hideOverlay(self):
        """隐藏遮罩层"""
        self.overlay.hide()
        
    def showMessage(self, title, message, icon=StyledMessageBox.INFORMATION, buttons=None):
        """显示带遮罩的消息对话框"""
        # 先显示遮罩
        self.showOverlay()
        
        # 显示消息框
        result = StyledMessageBox.showMessage(
            self,
            title,
            message,
            icon,
            buttons
        )
        
        # 消息框关闭后隐藏遮罩
        self.hideOverlay()
        
        return result
    
    def init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建背景框架
        bg_frame = QFrame(self)
        bg_frame.setObjectName("bg_frame")
        bg_frame.setStyleSheet(f"""
            #bg_frame {{
                background-color: {Theme.PRIMARY};
                border: 1px solid {Theme.GLASS_BORDER};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """
        )
        
        # 背景框架布局
        bg_layout = QVBoxLayout(bg_frame)
        bg_layout.setContentsMargins(20, 15, 20, 15)  # 减少上下边距
        bg_layout.setSpacing(10)  # 减少组件间距
        
        # 创建水平布局，将内容分为左右两部分
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)  # 左右面板之间的间距
        
        # 左侧面板 - 放置文字说明和验证码输入框
        left_panel = QFrame()
        left_panel.setObjectName("left_panel")
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(10)  # 减少垂直间距
        
        # 添加顶部弹簧，减少上部留白
        left_layout.addStretch(0.5)
        
        # 状态提示标签 - 居中
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet(f"""
            font-size: 16px;  /* 增大字号提高可读性 */
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            padding: 5px 0;
            margin-bottom: 10px;
        """
        )
        left_layout.addWidget(self.status_label)
        
        # 验证码输入框布局
        code_input_layout = QHBoxLayout()
        code_input_layout.setSpacing(5)  # 验证码数字间距
        code_input_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        
        for i in range(6):
            line_edit = VerificationCodeLineEdit(index=i, parent=self)
            line_edit.setFixedSize(40, 40) # 调整大小
            line_edit.setMaxLength(1)
            line_edit.setAlignment(Qt.AlignmentFlag.AlignCenter)
            # 限制输入为数字和字母（大小写均可，后续转换为大写）
            from PySide6.QtGui import QRegularExpressionValidator
            from PySide6.QtCore import QRegularExpression
            validator = QRegularExpressionValidator(QRegularExpression("[0-9a-zA-Z]"), line_edit)
            line_edit.setValidator(validator)
            
            line_edit.code_entered.connect(self._handle_code_unit_entered)
            line_edit.backspace_on_empty.connect(self._handle_backspace_on_empty_unit)
            line_edit.editing_started.connect(self._clear_all_input_errors)
            
            self.code_input_fields.append(line_edit)
            code_input_layout.addWidget(line_edit)
            
        left_layout.addLayout(code_input_layout)
        
        # 底部信息标签（动态内容，稍后根据模式设置）
        self.info_text_label = QLabel()
        self.info_text_label.setWordWrap(True)
        self.info_text_label.setStyleSheet(f"""
            font-size: 12px;
            color: {Theme.TEXT_SECONDARY};
            line-height: 1.5;
            margin-top: 15px; /* 增加与验证码输入框的间距 */
        """
        )
        self.info_text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(self.info_text_label)
        
        # 添加底部弹簧，减少下部留白
        left_layout.addStretch(0.5)
        
        # 右侧面板 - 放置二维码
        right_panel = QFrame()
        right_panel.setObjectName("right_panel")
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(10)  # 减少垂直间距
        
        # 添加顶部弹簧，减少上部留白
        right_layout.addStretch(4)
        
        # 创建一个固定高度的组合容器，包含二维码和QQ标签
        combined_container = QFrame()
        combined_container.setObjectName("combined_container")
        combined_layout = QVBoxLayout(combined_container)
        combined_layout.setContentsMargins(0, 0, 0, 0)
        combined_layout.setSpacing(3)  # 减少二维码和QQ标签之间的间距
        
        # 创建二维码容器框架
        qr_container = QFrame()
        qr_container.setObjectName("qr_container")
        qr_container.setStyleSheet(f"""
            #qr_container {{
                background-color: {Theme.CARD_LEVEL_1};
                border: 2px solid {Theme.BORDER};
                border-radius: 18px;
                padding: 0px;
            }}
        """
        )
        qr_layout = QVBoxLayout(qr_container)
        qr_layout.setContentsMargins(12, 12, 12, 12)  # 保持12px的内边距
        qr_layout.setSpacing(0)
        qr_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 设置容器的固定宽度和高度
        qr_size = 180  # 二维码大小
        margin = 12 * 2  # 边距 * 2 (左右或上下)，增加到12px
        border = 2 * 2  # 边框宽度 * 2 (左右或上下)
        container_size = qr_size + margin + border
        qr_container.setFixedSize(container_size, container_size)
        
        # 存储对qr_container的引用
        self.qr_container = qr_container
        
        # 创建QR二维码标签
        self.qr_label = QLabel()
        self.qr_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.qr_label.setFixedSize(qr_size, qr_size)
        self.qr_label.setStyleSheet("background-color: transparent; border-radius: 12px;")  # 设置透明背景，与圆角效果一致
        qr_layout.addWidget(self.qr_label)
        
        # 将二维码容器添加到组合容器
        combined_layout.addWidget(qr_container)
        combined_layout.setAlignment(qr_container, Qt.AlignmentFlag.AlignHCenter)
        
        # 创建一个固定高度的QQ信息标签容器，但高度更小
        qq_info_container = QFrame()
        qq_info_container.setFixedHeight(16)  # 减小固定高度
        qq_info_layout = QVBoxLayout(qq_info_container)
        qq_info_layout.setContentsMargins(0, 0, 0, 0)
        qq_info_layout.setSpacing(0)
        
        # 添加QQ信息标签到QQ信息容器
        self.qq_info_label = QLabel(" ") # 默认留空，稍后更新
        self.qq_info_label.setStyleSheet(f"""
            font-size: 12px;
            color: {Theme.TEXT_SECONDARY};
            text-align: center;
            padding: 0px;
            margin: 0px;
        """
        )
        self.qq_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        qq_info_layout.addWidget(self.qq_info_label)
        qq_info_layout.setAlignment(self.qq_info_label, Qt.AlignmentFlag.AlignHCenter)
        
        # 将QQ信息容器添加到组合容器
        combined_layout.addWidget(qq_info_container)
        combined_layout.setAlignment(qq_info_container, Qt.AlignmentFlag.AlignHCenter)
        
        # 添加组合容器到右侧面板
        right_layout.addWidget(combined_container)
        right_layout.setAlignment(combined_container, Qt.AlignmentFlag.AlignHCenter)
        
        # 添加底部弹簧，减少下部留白
        right_layout.addStretch(3)
        
        # 添加左右面板到水平布局
        content_layout.addWidget(left_panel, 3)  # 左侧面板占比较大
        content_layout.addWidget(right_panel, 2)  # 右侧面板占较小
        
        # 将水平布局添加到背景框架
        bg_layout.addLayout(content_layout)
        
        # 按钮区域 - 保留在底部
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 退出按钮
        cancel_button = QPushButton("退出")
        cancel_button.setStyleSheet(Theme.BUTTON_SECONDARY_STYLE)
        cancel_button.clicked.connect(self.reject)
        
        self.check_button = None  # 兼容已有代码
        
        # 我是赞赏用户按钮
        self.vip_button = QPushButton("我是赞赏用户")
        self.vip_button.setStyleSheet(Theme.BUTTON_PRIMARY_STYLE) # 修改为绿色主要按钮样式
        self.vip_button.clicked.connect(self._switch_to_vip_mode)
        
        # 刷新按钮 (在init_ui中创建，但在验证码模式下默认隐藏)
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.setStyleSheet(Theme.BUTTON_SECONDARY_STYLE)
        self.refresh_button.clicked.connect(self.refresh_qrcode)
        self.refresh_button.hide() # 默认隐藏，由 _update_ui_for_mode 控制显示
        
        # 返回验证码验证按钮
        self.back_to_code_button = QPushButton("验证码验证")
        self.back_to_code_button.setStyleSheet(Theme.BUTTON_PRIMARY_STYLE) # 修改为绿色主要按钮样式
        self.back_to_code_button.clicked.connect(self._switch_to_code_mode)
        self.back_to_code_button.hide() # 默认隐藏，由 _update_ui_for_mode 控制显示
        
        button_layout.addStretch(1)
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(self.vip_button) # 添加赞赏用户按钮
        button_layout.addWidget(self.refresh_button)  # 刷新按钮放回退出按钮右侧
        button_layout.addWidget(self.back_to_code_button) # 添加返回验证码验证按钮
        
        bg_layout.addLayout(button_layout)
        
        # 将背景框架添加到主布局
        main_layout.addWidget(bg_frame)
        
        # 初始化拖动相关变量
        self.drag_position = None
    
        # 调整窗口大小以适应新的水平布局
        self.setMinimumSize(700, 350)  # 减小高度
        
        # 居中显示窗口
        self.center_on_screen()
        
        # 根据初始模式设置UI
        self._update_ui_for_mode() # 新增方法，用于根据模式更新UI
        
        # 开始二维码登录流程
        QTimer.singleShot(500, self.start_qr_login)
    
    def center_on_screen(self):
        """居中显示窗口"""
        if self.parent():
            center = self.parent().geometry().center()
        else:
            screen_geometry = self.screen().availableGeometry()
            center = screen_geometry.center()
            
        self.move(center.x() - self.width() // 2, center.y() - self.height() // 2)
    
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        if event.buttons() & Qt.MouseButton.LeftButton and self.drag_position:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event):
        self.drag_position = None
        event.accept()
    
    def _update_ui_for_mode(self):
        """根据当前验证模式更新UI显示"""
        self.qq_info_label.setText(" ") # 确保每次模式切换时都清空QQ号

        # 从配置中获取VIP QQ验证是否全局启用
        vip_qq_config = self.verification_config.get("vip_qq_verification", {})
        is_vip_globally_enabled = vip_qq_config.get("enabled", False)

        # 从配置中获取验证码验证是否全局启用
        code_verification_config = self.verification_config.get("code_verification", {})
        is_code_globally_enabled = code_verification_config.get("enabled", False)

        if self.verification_mode == "code_verification":
            self.status_label.setText('请微信扫码关注公众号获取验证码')
            self.status_label.setStyleSheet(f"""
                font-size: 16px;
                font-weight: bold;
                color: {Theme.TEXT_PRIMARY};
                padding: 5px 0;
                margin-bottom: 10px; /* Reduced margin */
            """)
            self.info_text_label.setText('YCursor为免费软件\n添加验证并非为了收费，只是为了防止泛滥和倒卖狗')
            self.info_text_label.setStyleSheet(f"""
                font-size: 12px;
                color: {Theme.TEXT_SECONDARY};
                line-height: 1.5;
                margin-top: 0px; /* Reduced margin */
            """)
            for field in self.code_input_fields:
                field.show()
            self.qr_label.show()
            self.qq_info_label.hide() # QQ号信息在验证码模式下隐藏
            
            self.vip_button.show()
            if not is_vip_globally_enabled:
                self.vip_button.setEnabled(False)
                self.vip_button.setToolTip("赞赏用户专属QQ验证通道当前未开启")
                self.vip_button.setCursor(Qt.CursorShape.ForbiddenCursor)
            else:
                self.vip_button.setEnabled(True)
                self.vip_button.setToolTip("") # 清除提示
                self.vip_button.setCursor(Qt.CursorShape.PointingHandCursor)

            self.refresh_button.hide() # 验证码模式下，刷新按钮隐藏，由用户点击我是赞赏用户后显示
            self.back_to_code_button.hide() # 验证码模式下隐藏返回按钮
        elif self.verification_mode == "vip_qq_verification":
            self.status_label.setText('请扫码登录QQ')
            self.status_label.setStyleSheet(f"""
                font-size: 16px;
                font-weight: bold;
                color: {Theme.TEXT_PRIMARY};
                padding: 5px 0;
                margin-bottom: 0px; /* Further reduced margin for VIP mode to 0px */
            """)
            self.info_text_label.setText('YCursor为免费软件，为了防止泛滥和倒卖狗，我添加验证码验证才能使用，但为了更方便支持我的各位老大老板们，只要赞赏超过100￥的，您可以直接QQ扫码通过验证，无需关注公众号获取验证码\n❤感谢您的支持❤')
            self.info_text_label.setStyleSheet(f"""
                font-size: 12px;
                color: {Theme.TEXT_SECONDARY};
                line-height: 1.5;
                margin-top: 0px; /* Further reduced margin for VIP mode to 0px */
            """)
            for field in self.code_input_fields:
                field.hide() # 隐藏验证码输入框
            self.qr_label.show()
            self.qq_info_label.show()
            self.vip_button.hide() # VIP模式下隐藏赞赏用户按钮
            self.refresh_button.show()
            
            self.back_to_code_button.show() # VIP模式下显示返回按钮
            if not is_code_globally_enabled:
                self.back_to_code_button.setEnabled(False)
                self.back_to_code_button.setToolTip("公众号验证码验证通道当前未开启")
                self.back_to_code_button.setCursor(Qt.CursorShape.ForbiddenCursor)
            else:
                self.back_to_code_button.setEnabled(True)
                self.back_to_code_button.setToolTip("")
                self.back_to_code_button.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 确保二维码容器可见
        self.qr_container.show()
        
    def _handle_code_unit_entered(self, index: int, text: str):
        """Handle when a character is entered in one of the code fields."""
        if len(text) == 1 and index < len(self.code_input_fields) - 1:
            next_field = self.code_input_fields[index + 1]
            next_field.setFocus()
            # next_field.selectAll() # Optional: select all for easy override on focus
        
        if all(len(field.text()) == 1 for field in self.code_input_fields):
            if not self.is_verifying: # Check lock before starting verification
                self.is_verifying = True # Set lock
                QTimer.singleShot(100, self.check_verification) # Delay to ensure UI updates

    def _handle_backspace_on_empty_unit(self, index: int):
        """Handle when backspace is pressed on an empty code field."""
        if index > 0:
            prev_field = self.code_input_fields[index - 1]
            prev_field.setFocus()
            prev_field.clear() # This will trigger its own textChanged, potentially _show_cursor

    def _get_entered_code(self):
        """获取用户输入的6位验证码"""
        return "".join([field.text() for field in self.code_input_fields])

    def start_qr_login(self):
        """开始二维码登录流程"""
        if self.verification_mode == "code_verification":
            # 显示公众号二维码
            qr_data = self._load_qrcode_image()
            if qr_data:
                self.display_qrcode(qr_data) # 传递图片数据或路径
                self.status_label.setText('请微信扫码关注公众号获取验证码')
                # 验证码模式下，刷新按钮隐藏
                self.refresh_button.hide()
                self.vip_button.show()
            else:
                self.status_label.setText('公众号二维码图片缺失')
                self.logger("错误: 无法加载公众号二维码图片")
            return

        elif self.verification_mode == "vip_qq_verification":
            self.status_label.setText('正在获取QQ登录二维码...')
            # 获取登录二维码
            qr_data = self.qq_api.get_login_qrcode()
            if not qr_data:
                self.status_label.setText('获取QQ登录二维码失败，请重试')
                self.refresh_button.setEnabled(True)
                return
                
            # 显示二维码
            self.display_qrcode(qr_data) # 传递二进制数据
            
            # 启动状态检查定时器
            self.start_status_check()
            
    def display_qrcode(self, qr_data_or_path):
        """显示二维码"""
        try:
            # 判断输入是文件路径还是二进制数据
            if isinstance(qr_data_or_path, str) and os.path.exists(qr_data_or_path):
                img = Image.open(qr_data_or_path)
            elif isinstance(qr_data_or_path, bytes):
                img = Image.open(io.BytesIO(qr_data_or_path))
            else:
                self.logger(f"错误: 无效的二维码数据或路径类型: {type(qr_data_or_path)}")
                self.status_label.setText('加载二维码失败: 无效图片数据')
                self.qr_label.clear() # 清除旧的二维码
                self.qr_container.hide() # 隐藏二维码容器
                return
            
            # 调整图像尺寸以确保它能在新的尺寸下正确显示
            img = img.resize((180, 180), Image.LANCZOS)  # 使用高质量的缩放
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='PNG')
            
            from PySide6.QtGui import QPixmap, QImage, QPainter, QPainterPath, QBrush, QColor
            qimg = QImage.fromData(img_bytes.getvalue())
            pixmap = QPixmap.fromImage(qimg)
            
            # 创建带圆角的二维码图片
            target_pixmap = QPixmap(pixmap.size())
            target_pixmap.fill(QColor(0, 0, 0, 0))  # 透明背景
            
            # 创建QPainter和QPainterPath
            painter = QPainter(target_pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)  # 使用抗锯齿
            
            # 创建圆角矩形路径
            rounded_rect = QPainterPath()
            rect = pixmap.rect()
            rounded_rect.addRoundedRect(rect, 12, 12)  # 设置圆角半径为12px
            
            # 应用路径作为裁剪区域
            painter.setClipPath(rounded_rect)
            
            # 绘制原始图片
            painter.drawPixmap(0, 0, pixmap)
            painter.end()
            
            # 设置QLabel显示圆角处理后的二维码
            self.qr_label.setPixmap(target_pixmap)
            self.qr_label.setScaledContents(True)  # 确保使用缩放内容以填充标签
            
            # 更新QLabel样式，移除背景色
            self.qr_label.setStyleSheet("background-color: transparent; border-radius: 12px;")
            
            # 启用刷新按钮
            self.refresh_button.setEnabled(True)
            
            # 更新状态
            self.status_label.setText('请扫码登录QQ')
            self.qrcode_expired = False
        
        except Exception as e:
            # 捕获所有可能出现的错误，包括 PIL.UnidentifiedImageError
            error_message = f"显示二维码时出错: {e}"
            self.logger(error_message)
            self.status_label.setText('显示二维码失败，请刷新重试。错误: 图片无法识别或文件损坏')
            self.qr_label.clear()  # 清除旧的二维码
            self.qr_container.hide() # 隐藏二维码容器，避免显示一个空框
            self.refresh_button.setEnabled(True) # 仍然允许用户刷新
            # 如果是由于图片无法识别导致的错误，提供更明确的提示
            if "cannot identify image file" in str(e).lower() or isinstance(e, Image.UnidentifiedImageError):
                self.status_label.setText('显示二维码失败：公众号图片文件损坏或缺失！')
                self.showMessage(
                    "图片文件错误",
                    f"未能加载公众号二维码图片文件 (icons/gzh.jpg)。\n请确保该文件是一个有效的图片文件且未损坏。\n错误详情: {e}",
                    StyledMessageBox.CRITICAL
                )
            
    def start_status_check(self):
        """开始二维码状态检查"""
        # 停止可能已有的定时器
        if self.qr_status_timer:
            self.qr_status_timer.stop()
            
        # 创建新的定时器，每2秒检查一次二维码状态
        self.qr_status_timer = QTimer(self)
        self.qr_status_timer.timeout.connect(self.check_qrcode_status)
        self.qr_status_timer.start(2000)  # 2秒检查一次
        
        # 创建自动刷新定时器，每2分钟刷新一次二维码
        if self.qr_refresh_timer:
            self.qr_refresh_timer.stop()
            
        self.qr_refresh_timer = QTimer(self)
        self.qr_refresh_timer.timeout.connect(self.auto_refresh_qrcode)
        self.qr_refresh_timer.start(120000)  # 2分钟自动刷新一次
    
    def check_qrcode_status(self):
        """检查二维码状态"""
        if self.verification_mode == "code_verification":
            return # 验证码模式下不检查QQ二维码状态
            
        try:
            # 调用API检查状态
            status, message, redirect_url = self.qq_api.check_qrcode_status()
            
            # 记录状态信息
            
            # 简化状态显示
            if status == 0:  # 二维码未失效
                self.status_label.setText("请扫码登录QQ")
            elif status == 1:  # 二维码认证中
                self.status_label.setText("扫码成功，请在手机上确认")
            elif status == 2:  # 登录成功
                self.status_label.setText("登录成功！")
                # 停止定时器
                if self.qr_status_timer:
                    self.qr_status_timer.stop()
                if self.qr_refresh_timer:
                    self.qr_refresh_timer.stop()
                    
                # 完成登录流程
                self.complete_login(redirect_url)
            elif status == 3:  # 二维码已失效
                if not self.qrcode_expired:
                    self.status_label.setText("二维码已失效，请刷新")
                    self.qrcode_expired = True
            else:  # 发生错误
                self.status_label.setText("请扫码登录QQ")  # 改为默认显示"请扫码登录QQ"而不是错误信息
        except Exception as e:
            self.logger(f"检查二维码状态出错: {str(e)}")
            self.status_label.setText("请扫码登录QQ")  # 发生异常时也显示"请扫码登录QQ"
    
    def refresh_qrcode(self):
        """手动刷新二维码"""
        if self.verification_mode == "code_verification":
            self.status_label.setText('验证码模式下无需刷新二维码')
            self.refresh_button.setEnabled(False)
            return
            
        self.refresh_button.setEnabled(False)
        self.status_label.setText('正在刷新二维码...')
        
        # 重置登录状态和清除QQ信息
        self.is_logged_in = False
        self.current_qq = ""
        self.verification_result = False
        
        # 重置QQ信息标签，但保持可见以维持布局
        self.qq_info_label.setText(" ")
        
        # 获取新的二维码
        self.start_qr_login()
    
    def auto_refresh_qrcode(self):
        """自动刷新二维码（每2分钟）"""
        if not self.is_logged_in:
            self.status_label.setText('二维码已超时，正在自动刷新...')
            self.refresh_qrcode()
    
    def complete_login(self, redirect_url):
        """完成登录流程"""
        if self.verification_mode == "code_verification":
            # 在验证码模式下，如果用户扫码进入，且二维码是公众号二维码，则不需要完成QQ登录流程
            # 此时应该直接进行验证码的验证逻辑（如果所有框都填满）
            self.status_label.setText('请微信扫码关注公众号获取验证码')
            self.refresh_button.hide()
            self.vip_button.show()
            return
            
        # 完成登录
        login_success = self.qq_api.finish_login(redirect_url)
        
        if login_success:
            # 获取当前登录的QQ号
            self.current_qq = self.qq_api.get_current_qq()
            
            # 更新QQ号显示
            if self.current_qq:
                self.qq_info_label.setText(f"QQ: {self.current_qq}")
            
            # 更新状态
            self.status_label.setText('登录成功！正在进行验证...')
            
            # 设置登录状态
            self.is_logged_in = True
            
            # 自动进行验证
            QTimer.singleShot(1000, self.check_verification)
        else:
            self.status_label.setText('登录完成出错，请重试')
            self.refresh_button.setEnabled(True)
    
    def check_verification(self):
        """检查是否通过QQ群验证"""
        if self.verification_mode == "code_verification":
            current_mode_config = self.verification_config.get(self.verification_mode, {})
            entered_code = self._get_entered_code()
            expected_code_original = current_mode_config.get("code")

            expected_code = None # Initialize expected_code
            if expected_code_original is not None: # Check if original is not None
                expected_code = expected_code_original.upper() # Convert to uppercase

            if expected_code is not None and entered_code == expected_code:
                self.verification_result = True
                self.current_qq = "CodeVerification" # 标识为验证码通过
                self.logger("验证码验证通过！")
                # self.status_label.setText('验证码验证通过！') # 由调用者 VersionChecker 统一处理成功提示
                self.accept() # This will close the dialog, lock release is implicitly handled.
                # self.is_verifying = False # Not strictly needed before accept(), but good for consistency if accept didn't close.
            else:
                self.verification_result = False
                self.status_label.setText('验证码不正确，请重试')
                # REMOVE self.showMessage CALL
                # self.showMessage(
                #     "验证失败",
                #     "您输入的验证码不正确，请重新输入公众号获取的验证码...",
                #     StyledMessageBox.WARNING
                # )
                for field in self.code_input_fields:
                    if hasattr(field, 'set_error_state'):
                        field.set_error_state(True) # Set error state for all fields
                
                # Keep focus on the first field (or remove if user prefers no focus change)
                #if self.code_input_fields: 
                #     self.code_input_fields[0].setFocus()

            self.is_verifying = False # Release lock whether success or fail (if not accepted)
            return
            
        elif self.verification_mode == "vip_qq_verification":
            if not self.is_logged_in:
                self.status_label.setText('请先登录QQ')
                self.showMessage("未登录", "请先完成QQ登录后再进行验证！", StyledMessageBox.WARNING)
                self.is_verifying = False # Release lock if logic flow ends here
                return
            
            # 显示状态
            self.status_label.setText('正在进行VIP QQ验证...')
            
            # 获取群列表
            groups = self.qq_api.get_joined_groups()
            
            if not groups:
                self.status_label.setText('获取QQ群列表失败，请重试')
                self.showMessage("验证失败", "获取QQ群列表失败，请重试。\n如果问题持续存在，请检查您的网络连接或重新启动程序。", StyledMessageBox.WARNING)
                self.is_verifying = False # Release lock for VIP path
                return
            
            # 获取VIP白名单
            # 首先获取当前模式 (vip_qq_verification) 的特定配置
            current_mode_config = self.verification_config.get(self.verification_mode, {})
            vip_whitelist = current_mode_config.get("whitelist", [])
            
            # 检查是否加入了指定的QQ群或在白名单中
            verified = False
            matched_qq = ""
            
            group_ids_list = []
            for group in groups:
                group_ids_list.append(group.group_id)
            
            # 检查当前登录的QQ号是否在VIP白名单中
            if self.current_qq and self.current_qq in vip_whitelist:
                verified = True
                matched_qq = self.current_qq
            
            # 保存验证结果
            self.verification_result = verified
            
            if verified:
                # 验证成功，关闭对话框
                # self.status_label.setText('VIP QQ验证通过！') # 由调用者 VersionChecker 统一处理成功提示
                self.accept()
            else:
                # 验证失败
                self.status_label.setText('验证不通过，您不是赞赏用户')
                self.showMessage(
                    "验证不通过",
                    f"抱歉，您的QQ号 {self.current_qq} 不在赞赏名单中\n\nYCursor 是免费使用的，您可以选择验证码验证\n\n如果您已经赞赏超过100￥，但 Yan 并没有把你加入赞赏名单里，请通过QQ或者邮件发送相关证明联系我\n\n如有疑问，请发送电子邮件给我 <EMAIL>\n\n",
                    StyledMessageBox.CRITICAL
                )
            self.is_verifying = False # Release lock for VIP path
            return
            
        # Fallback for unknown mode (should not happen with proper _update_ui_for_mode)
        self.status_label.setText('未知验证模式')
        self.showMessage("错误", "检测到未知的验证模式，请联系管理员。", StyledMessageBox.CRITICAL)
        self.is_verifying = False # Release lock for fallback path

    def _update_qq_info(self, qq):
        """统一更新QQ号信息"""
        if not self.current_qq:  # 如果还没有设置QQ号
            self.current_qq = qq
            if hasattr(self, 'qq_info_label') and self.qq_info_label:
                self.qq_info_label.setText(f"QQ: {qq}")
    
    def get_verification_info(self):
        """获取验证信息"""
        return {
            "success": self.verification_result,
            "qq": self.current_qq,
            "timestamp": int(time.time()),
            "user_type": "成员",  # 默认为成员，因为新API无法区分身份
            "completed_mode": self.verification_mode # 添加实际完成验证的模式
        }
        
    def closeEvent(self, event):
        """处理窗口关闭事件，确保资源正确释放"""
        self.logger("QQ群验证窗口关闭，清理资源...")
        
        # 停止定时器
        if hasattr(self, 'qr_status_timer') and self.qr_status_timer:
            self.qr_status_timer.stop()
            
        if hasattr(self, 'qr_refresh_timer') and self.qr_refresh_timer:
            self.qr_refresh_timer.stop()
        
        # 接受关闭事件
        event.accept()

    def accept(self):
        # 停止定时器
        if hasattr(self, 'qr_status_timer') and self.qr_status_timer:
            self.qr_status_timer.stop()
            
        if hasattr(self, 'qr_refresh_timer') and self.qr_refresh_timer:
            self.qr_refresh_timer.stop()
        
        super().accept()

    def reject(self):
        # 停止定时器
        if hasattr(self, 'qr_status_timer') and self.qr_status_timer:
            self.qr_status_timer.stop()
            
        if hasattr(self, 'qr_refresh_timer') and self.qr_refresh_timer:
            self.qr_refresh_timer.stop()
        
        super().reject()

    def _switch_to_vip_mode(self):
        """切换到VIP QQ验证模式"""
        self.logger("切换到VIP QQ验证模式...")
        self.verification_mode = "vip_qq_verification"
        self._update_ui_for_mode() # 更新UI
        self.start_qr_login() # 启动QQ登录流程

    def _switch_to_code_mode(self):
        """切换到验证码验证模式"""
        self.logger("切换到验证码验证模式...")
        self.verification_mode = "code_verification"
        self._update_ui_for_mode() # 更新UI
        
        # 清空所有验证码输入框
        for field in self.code_input_fields:
            field.clear()
            # Ensure fields are writable and correctly styled after clearing
            if hasattr(field, '_show_cursor'): # Check if it's our custom QLineEdit
                field._show_cursor() # This makes it writable and applies base style
            else: # Fallback for standard QLineEdit if type changes unexpectedly
                field.setReadOnly(False)

        self._clear_all_input_errors() # 清除所有输入框的错误状态

        # 确保第一个验证码输入框获得焦点，以便用户可以直接输入
        if self.code_input_fields:
            self.code_input_fields[0].setFocus()

        self.start_qr_login() # 启动二维码登录流程 (此时会显示公众号二维码)

    def _load_qrcode_image(self):
        """
        根据配置加载公众号二维码图片

        返回值:
            bytes/str: 图片数据(bytes)或本地路径(str)，失败返回None
        """
        # 获取二维码配置，如果没有配置则使用默认值
        code_verification_config = self.verification_config.get("code_verification", {})
        qrcode_config = code_verification_config.get("qrcode_config", {
            "use_remote": False,
            "remote_url": "",
            "local_path": "icons/gzh.jpg",
            "fallback_to_local": True
        })

        # 如果配置为优先使用网络图片
        if qrcode_config.get("use_remote", False):
            remote_url = qrcode_config.get("remote_url", "")
            if remote_url:
                try:
                    self.logger(f"正在从网络加载公众号二维码: {remote_url}")
                    response = requests.get(remote_url, timeout=10)
                    if response.status_code == 200:
                        self.logger("网络二维码加载成功")
                        return response.content  # 返回图片二进制数据
                    else:
                        self.logger(f"网络二维码加载失败，状态码: {response.status_code}")
                except Exception as e:
                    self.logger(f"网络二维码加载出错: {e}")

                # 如果网络加载失败且配置了回退到本地
                if qrcode_config.get("fallback_to_local", True):
                    self.logger("网络加载失败，回退到本地图片")
                else:
                    self.logger("网络加载失败且未配置回退，无法加载二维码")
                    return None

        # 使用本地图片
        local_path = qrcode_config.get("local_path", "icons/gzh.jpg")
        full_local_path = os.path.join(os.path.dirname(__file__), local_path)

        if os.path.exists(full_local_path):
            self.logger(f"使用本地公众号二维码: {local_path}")
            return full_local_path  # 返回本地文件路径
        else:
            self.logger(f"本地二维码图片不存在: {full_local_path}")
            return None

    def _clear_all_input_errors(self):
        """Clears the error state from all code input fields."""
        for field in self.code_input_fields:
            if hasattr(field, 'set_error_state'): # Check if it's our custom QLineEdit
                field.set_error_state(False)

class VersionChecker(QObject):
    """版本检查器类"""
    
    # --- 使用与加密脚本相同的密钥 ---
    DECRYPTION_KEY = b'F8T8d3XQy6j9wL4qA7gC2rX7pV5kM9nH0zK1lC3bE4h='
    # --- Key definition end ---

    AGREED_VERSION_FILENAME = "disclaimer_agreed_version"
    # 验证时效配置文件
    VERIFICATION_DATA_FILENAME = "grouptimeliness.json"

    # 认证时效检查配置
    AUTH_CHECK_INTERVAL_MINUTES = 180  # 默认每180分钟(3小时)检查一次
    REMOTE_CONFIG_RETRY_COUNT = 10     # 远程配置获取重试次数
    REMOTE_CONFIG_RETRY_INTERVAL = 3  # 重试间隔（秒）

    # 版本信息（按平台区分）
    CURRENT_VERSIONS = {
        "win": "4.8.5",
        "mac": "4.8.5",
        "linux": "4.8.5"
    }
    
    # 版本检查配置
    VERSION_CHECK_URL = "https://app.yan.vin/version/YCursor/version.json"  # 版本检查URL
    # VERSION_CHECK_URL = "http://y/test/versionplus.json"  # 版本检查URL
    ENABLE_VERSION_CHECK = True  # 是否启用版本检查
    HIDE_CONSOLE = True  # 是否隐藏终端窗口
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.platform = self._get_platform()
        self.current_version = self.CURRENT_VERSIONS.get(self.platform, "1.0.0")
        self.version_info = None # Initialize version_info
        
        # 验证码验证相关参数
        self.code_verification_enabled = True
        self.code_verification_code = ""
        self.code_verification_duration_hours = 2 # 默认1小时
        
        # VIP QQ验证相关参数
        self.vip_qq_verification_enabled = True
        self.vip_qq_whitelist = []
        self.vip_qq_duration_hours = 168 # 默认7天

        # 二维码配置参数
        self.qrcode_config = {
            "use_remote": False,
            "remote_url": "",
            "local_path": "icons/gzh.jpg",
            "fallback_to_local": True
        }

        # 加载验证配置
        self.machine_uuid = get_machine_uuid()

        # 初始化认证时效检查定时器
        self.auth_check_timer = None
        self.auth_check_interval_minutes = self.AUTH_CHECK_INTERVAL_MINUTES
        self.auth_expiry_dialog_shown = False  # 防止重复显示认证过期对话框
        safe_print(f"VersionChecker 初始化完成，认证检查间隔: {self.auth_check_interval_minutes}分钟")
    
    def _get_platform(self):
        """获取当前平台"""
        system = platform.system().lower()
        if system == "darwin":
            return "mac"
        elif system == "linux":
            return "linux"
        else:
            return "win"
    
    def check_version(self):
        """
        检查版本和免责声明
        
        返回值:
            bool: 是否可以继续运行
        """
        # 1. 初始化环境设置
        safe_print("开始版本检查流程...")
        
        # 延迟加载WebEngine路径设置和系统依赖检查
        # 这两项比较耗时，但对版本检查和QQ群验证是必要的
        # 尽量推迟到实际需要时再加载
        safe_print("正在初始化WebEngine环境...")
        lazy_setup_webengine_paths()
        
        # 在执行检查时加载系统依赖
        safe_print("正在检查系统依赖...")
        missing_deps = check_system_dependencies()
        if missing_deps:
            safe_print("程序可能因缺少系统依赖而无法正常运行，但将继续尝试执行")
        
        if not self.ENABLE_VERSION_CHECK:
            safe_print("版本检查已禁用，跳过")
            return True
            
        try:
                
            # 2. 获取远程版本信息
            safe_print("正在获取远程版本信息...")
            version_info = self._get_remote_version()
            if not version_info:
                # _get_remote_version 内部已处理连接错误或解密失败的消息，这里直接返回其结果
                # 如果 _get_remote_version 返回 None，表示已有严重错误发生，需要退出
                # 如果 _get_remote_version 返回 None，表示已有严重错误发生，需要退出
                safe_print("错误: 获取远程版本信息失败。") # 通用错误消息
                return self._show_connection_error() # 确保显示连接错误并返回 False
            
            safe_print("成功获取远程版本信息")
            self.version_info = version_info

            # 严格检查 global 和平台配置块
            global_config = self.version_info.get("global")
            if global_config is None:
                safe_print("错误: 远程配置格式不正确。") # 通用错误
                return self._show_critical_config_error("远程配置缺少 'global' 部分。")

            platform_specific_config = self.version_info.get(self.platform)
            if platform_specific_config is None:
                safe_print("错误: 远程配置格式不正确。") # 通用错误
                return self._show_critical_config_error(f"远程配置缺少针对平台 '{self.platform}' 的特定配置部分。")

            # 3. 免责声明检查 (严格)
            safe_print("正在检查免责声明...")
            if "disclaimer" not in global_config:
                safe_print("错误: 远程配置格式不正确。") # 通用错误
                return self._show_critical_config_error("远程配置的 'global' 部分缺少 'disclaimer' 配置。")
            disclaimer_config = global_config["disclaimer"]

            if not isinstance(disclaimer_config, dict):
                 safe_print("错误: 远程配置格式不正确。") # 通用错误
                 return self._show_critical_config_error("'disclaimer' 配置必须是一个字典。")

            show_disclaimer_flag = disclaimer_config.get("show")
            if show_disclaimer_flag is None: # 必须存在 show 字段
                 safe_print("错误: 远程配置格式不正确。") # 通用错误
                 return self._show_critical_config_error("'disclaimer' 配置缺少 'show' 字段。")
            
            latest_disclaimer_version = disclaimer_config.get("version")
            disclaimer_content = disclaimer_config.get("content")

            if show_disclaimer_flag:
                if latest_disclaimer_version is None:
                    safe_print("错误: 远程配置格式不正确。") # 通用错误
                    return self._show_critical_config_error("免责声明已启用但缺少 'version' 字段。")
                if disclaimer_content is None:
                     safe_print("错误: 远程配置格式不正确。") # 通用错误
                     return self._show_critical_config_error("免责声明已启用但缺少 'content' 字段。")
                
                # ... (原有的免责声明版本比较和显示逻辑保持不变，但现在基于已验证的字段) ...
                agreed_version = self._get_agreed_disclaimer_version()
                must_agree = disclaimer_config.get("must_agree", True) # must_agree 可以有默认值

                if self._compare_versions(agreed_version, latest_disclaimer_version) < 0:
                    user_agreed = self._show_disclaimer(disclaimer_config)
                    if user_agreed:
                        if not self._save_agreed_disclaimer_version(latest_disclaimer_version):
                            if must_agree:
                                safe_print("错误: 保存免责声明状态失败。") # 通用错误
                                return self._show_critical_config_error("保存同意的免责声明版本失败。")
                    elif must_agree:
                        return False # 用户不同意且必须同意，_show_disclaimer内部已处理退出消息
            
            # 4. 版本检查 (严格)
            safe_print("正在检查版本信息...")
            # platform_info 已经重命名为 platform_specific_config 并在上面检查过存在性

            latest_version = platform_specific_config.get("latest_version")
            min_version = platform_specific_config.get("min_version")

            if latest_version is None:
                safe_print("错误: 远程配置格式不正确。") # 通用错误
                return self._show_critical_config_error(f"平台 '{self.platform}' 配置缺少 'latest_version'。")
            if min_version is None:
                safe_print("错误: 远程配置格式不正确。") # 通用错误
                return self._show_critical_config_error(f"平台 '{self.platform}' 配置缺少 'min_version'。")
            
            force_update = platform_specific_config.get("force_update", False) # 可选，有默认值
            update_url = platform_specific_config.get("update_url", "")       # 可选，有默认值
            changes = platform_specific_config.get("changes", [])             # 可选，有默认值
            show_update_url = platform_specific_config.get("show_update_url", True) # 可选
            show_update_notes = platform_specific_config.get("show_update_notes", True) # 可选
            update_date = platform_specific_config.get("update_date", "")           # 可选
            
            safe_print(f"当前版本: {self.current_version}, 最新版本: {latest_version}, 最低要求版本: {min_version}")
            
            # ... (原有的版本比较和更新提示逻辑保持不变) ...
            if self._compare_versions(self.current_version, min_version) < 0:
                if force_update:
                    return self._show_force_update_message(latest_version, update_url, changes, show_update_url, show_update_notes, update_date)
                else:
                    return self._show_update_available_message(latest_version, update_url, changes, show_update_url, show_update_notes, update_date)

            # 5. 加载验证配置 (严格)
            safe_print("正在加载验证配置...")
            load_success, error_detail = self._load_verification_configs() # _load_verification_configs 内部会打印具体错误
            if not load_success:
                safe_print("错误: 加载程序验证配置失败。") # 通用错误消息
                # 如果加载失败，不再调用 _show_critical_config_error，因为 _load_verification_configs 内部已打印具体原因
                # 但我们仍然需要一个通用的配置错误消息来提示用户，所以调用它，但可以给一个通用消息
                return self._show_critical_config_error(error_detail if error_detail else "加载程序验证配置失败，详细原因未知。")
            
            # 6. 验证检查 (逻辑不变，依赖于 _load_verification_configs 的结果)
            # ... (原有的 _check_program_verification 调用逻辑保持不变) ...
            if self.code_verification_enabled or self.vip_qq_verification_enabled: # 检查旧的 qq_verification_enabled 也包含在内
                safe_print("验证已启用，正在检查验证状态...")
                if not self._check_program_verification():
                    return False
            else:
                safe_print("所有验证均未启用，跳过验证")
            
            # 7. 通知信息显示 (严格)
            if "notification" not in platform_specific_config:
                safe_print("错误: 远程配置格式不正确。") # 通用错误
                return self._show_critical_config_error(f"平台 '{self.platform}' 配置缺少 'notification' 部分。")
            notification = platform_specific_config["notification"]
            if not isinstance(notification, dict):
                safe_print("错误: 远程配置格式不正确。") # 通用错误
                return self._show_critical_config_error(f"平台 '{self.platform}' 的 'notification' 配置必须是一个字典。")

            show_notification = notification.get("show")
            if show_notification is None:
                safe_print("错误: 远程配置格式不正确。") # 通用错误
                return self._show_critical_config_error(f"平台 '{self.platform}' 的 'notification' 配置缺少 'show' 字段。")
            
            notification_message = notification.get("message")
            if show_notification and notification_message is None:
                safe_print("错误: 远程配置格式不正确。") # 通用错误
                return self._show_critical_config_error(f"平台 '{self.platform}' 的通知已启用但缺少 'message' 字段。")
            
            notification_level = notification.get("level", "info") # level 可以有默认值
            
            if show_notification and notification_message: # 确保 message 存在才显示
                safe_print(f"显示{notification_level}级别通知...")
                self._show_notification(notification_message, notification_level)
            
            safe_print("版本检查完成，允许继续运行")
            return True
            
        except Exception as e:
            try:
                safe_print(f"版本检查出错: {str(e)}")
            except UnicodeEncodeError:
                safe_print("版本检查出错（无法显示详细错误）")
            
            import traceback
            try:
                traceback.print_exc() # 打印详细错误堆栈
            except UnicodeEncodeError:
                safe_print("无法打印详细错误堆栈（编码错误）")
            
            return self._show_connection_error()
    
    def _load_qq_verification_config(self):
        """
        从version_info加载QQ群验证配置
        """
        if not self.version_info:
            return
            
        try:
            global_config = self.version_info.get("global", {})
            qq_config = global_config.get("qq_verification", {})
            
            # 更新QQ群验证配置
            self.qq_verification_enabled = qq_config.get("enabled", False)
            self.qq_verification_duration_hours = qq_config.get("duration_hours", 24)
            self.qq_group_ids = qq_config.get("group_ids", [])
            self.qq_advanced_whitelist = qq_config.get("advanced_whitelist", [])
            self.qq_normal_whitelist = qq_config.get("normal_whitelist", [])
            self.qq_blacklist = qq_config.get("blacklist", [])
            
            safe_print(f"已加载QQ群验证配置: 启用={self.qq_verification_enabled}, 时效={self.qq_verification_duration_hours}小时")
            safe_print(f"QQ群IDs: {self.qq_group_ids}")
            
        except Exception as e:
            safe_print(f"加载QQ群验证配置时出错: {e}")
            # 使用默认配置
            self.qq_verification_enabled = False
            self.qq_verification_duration_hours = 24
            self.qq_group_ids = []
            self.qq_advanced_whitelist = []
            self.qq_normal_whitelist = []
            self.qq_blacklist = []
    
    def _check_qq_verification(self):
        """
        检查QQ群验证
        
        返回值:
            bool: 是否通过验证
        """
        if not self.qq_verification_enabled:
            return True
        
        try:
            # 检查时效性文件是否存在及有效
            verification_data = self._load_verification_data()
            
            if verification_data:
                self.current_qq = verification_data.get("qq", "")
                
                # 检查是否在黑名单内
                if self.current_qq and self.current_qq in self.qq_blacklist:
                    safe_print(f"QQ号 {self.current_qq} 在黑名单中，拒绝访问")
                    self._show_blacklist_message()
                    return False
                
                # 检查是否在高级白名单内
                if self.current_qq and self.current_qq in self.qq_advanced_whitelist:
                    safe_print(f"QQ号 {self.current_qq} 在高级白名单中，无需验证")
                    return True
                
                # 检查是否在普通白名单内
                if self.current_qq and self.current_qq in self.qq_normal_whitelist:
                    # 普通白名单只跳过验证，但仍需要检查时效
                    timestamp = verification_data.get("timestamp", 0)
                    current_time = int(time.time())
                    expiration_time = timestamp + self.qq_verification_duration_hours * 3600
                    
                    if current_time < expiration_time:
                        safe_print(f"QQ号 {self.current_qq} 在普通白名单中且在有效期内，无需验证")
                        return True
                    else:
                        safe_print(f"QQ号 {self.current_qq} 在普通白名单中但已过期，需要更新时效")
                        # 直接更新时效，不需要再次验证
                        self._save_verification_data({
                            "qq": self.current_qq,
                            "timestamp": current_time,
                            "source": "normal_whitelist"
                        })
                        # 打印验证时效信息
                        message = f"白名单用户验证时效已更新，有效期为{self.qq_verification_duration_hours}小时"
                        safe_print(message)
                        print(f"\n[QQ验证] {message}\n")
                        return True
                
                # 检查是否在时效期内
                timestamp = verification_data.get("timestamp", 0)
                current_time = int(time.time())
                expiration_time = timestamp + self.qq_verification_duration_hours * 3600
                
                if current_time < expiration_time:
                    remaining_hours = (expiration_time - current_time) // 3600
                    message = f"QQ群验证还在有效期内，剩余时间: {remaining_hours}小时，总有效期: {self.qq_verification_duration_hours}小时"
                    safe_print(message)
                    print(f"\n[QQ验证] {message}\n")
                    return True
                else:
                    message = f"QQ群验证已过期，需要重新验证（有效期为{self.qq_verification_duration_hours}小时）"
                    safe_print(message)
                    print(f"\n[QQ验证] {message}\n")
            
            # 显示QQ群验证对话框
            return self._show_qq_verification_dialog()
            
        except Exception as e:
            safe_print(f"检查QQ群验证时出错: {e}")
            import traceback
            traceback.print_exc()
            
            # 出错时强制显示验证对话框
            return self._show_qq_verification_dialog()
    
    def _show_qq_verification_dialog(self):
        """
        显示QQ群验证对话框
        
        返回值:
            bool: 是否通过验证
        """
        if not self.qq_group_ids:
            safe_print("没有配置要验证的QQ群，无法进行验证")
            return self._show_verification_error("没有配置要验证的QQ群，无法进行验证")
        
        try:
            # 创建QQ群验证对话框
            dialog = VerificationDialog(self.parent, self.verification_config, self.verification_mode, safe_print)
            
            # 居中显示
            if self.parent:
                center = self.parent.geometry().center()
                dialog.move(center.x() - dialog.width() // 2, center.y() - dialog.height() // 2)
            
            # 显示对话框并等待结果
            result = dialog.exec()
            
            if result == QDialog.DialogCode.Accepted:
                # 获取验证信息
                verification_info = dialog.get_verification_info()
                
                # 获取当前QQ号
                self.current_qq = verification_info.get("qq", "")
                
                # 检查是否在黑名单内
                if self.current_qq and self.current_qq in self.qq_blacklist:
                    safe_print(f"QQ号 {self.current_qq} 在黑名单中，拒绝访问")
                    self._show_blacklist_message()
                    return False
                
                # 验证成功
                if verification_info.get("success", False):
                    # 保存验证信息
                    self._save_verification_data({
                        "qq": self.current_qq,
                        "timestamp": verification_info.get("timestamp", int(time.time())),
                        "source": "verification_success"
                    })
                    # 打印验证时效信息
                    message = f"QQ群验证通过，有效期为{self.qq_verification_duration_hours}小时"
                    safe_print(message)
                    print(f"\n[QQ验证] {message}\n")
                    # 尝试使用应用程序日志系统记录
                    try:
                        if self.logger and callable(self.logger):
                            self.logger(message)
                    except Exception:
                        pass
                    return True
                else:
                    # 验证失败
                    safe_print("QQ群验证失败")
                    return False
            else:
                # 用户取消
                safe_print("用户取消了QQ群验证")
                return False
        
        except Exception as e:
            safe_print(f"显示QQ群验证对话框时出错: {e}")
            return self._show_verification_error(f"无法显示QQ群验证: {str(e)}")
    
    def _show_verification_error(self, error_message):
        """显示验证错误信息"""
        StyledMessageBox.showMessage(
            self.parent,
            "验证错误",
            f"{error_message}\n\n无法完成QQ群验证，程序将退出。",
            StyledMessageBox.CRITICAL,
            ["退出"]
        )
        return False
    
    def _show_blacklist_message(self):
        """显示黑名单消息"""
        StyledMessageBox.showMessage(
            self.parent,
            "访问拒绝",
            f"您的QQ账号 {self.current_qq} 已被列入黑名单，无法使用YCursor。\n\n如有疑问，请发送电子邮件给我 <EMAIL>",
            StyledMessageBox.CRITICAL,
            ["退出"]
        )
    
    def _get_verification_filepath(self):
        """获取验证时效文件路径"""
        try:
            app_data_dir = get_app_data_dir()
            if not app_data_dir:
                safe_print("错误：无法获取应用数据目录")
                return None
                
            # 创建配置目录
            config_dir = os.path.join(app_data_dir, "config")
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
                
            return os.path.join(config_dir, self.VERIFICATION_DATA_FILENAME)
        except Exception as e:
            safe_print(f"获取验证时效文件路径时出错: {str(e)}")
            return None
    
    def _save_verification_data(self, data):
        """
        保存验证数据
        
        参数:
            data: 包含验证信息的字典
        
        返回值:
            bool: 是否保存成功
        """
        filepath = self._get_verification_filepath()
        if not filepath:
            return False
            
        try:
            # 加密数据
            encrypted_data = SecurityManager.encrypt_verification_data(
                self.machine_uuid, data
            )
            
            if not encrypted_data:
                safe_print("加密验证数据失败")
                return False
                
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
                
            # message = f"验证数据已保存到 {filepath}"
            # safe_print(message)
            # print(f"\n[QQ验证] {message}\n")
            return True
            
        except Exception as e:
            safe_print(f"保存验证数据时出错: {str(e)}")
            return False
    
    def _load_verification_data(self):
        """
        加载验证数据
        
        返回值:
            dict: 验证数据，如果加载失败则返回None
        """
        filepath = self._get_verification_filepath()
        if not filepath or not os.path.exists(filepath):
            return None
            
        try:
            # 读取文件
            with open(filepath, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
                
            # 解密数据
            decrypted_data = SecurityManager.decrypt_verification_data(
                self.machine_uuid, encrypted_data
            )
            
            if not decrypted_data:
                # safe_print("解密验证数据失败")
                return None
                
            return decrypted_data
            
        except Exception as e:
            safe_print(f"加载验证数据时出错: {str(e)}")
            return None
    
    def _show_disclaimer(self, disclaimer_config):
        """
        显示免责声明
        
        参数:
            disclaimer_config: 免责声明配置字典
        返回值:
            bool: 用户是否同意免责声明 (或不需要显示)
        """
        # 检查是否需要显示免责声明 (已在 check_version 中判断，但保留内部检查以防直接调用)
        if not disclaimer_config or not disclaimer_config.get("show", False):
            return True

        title = disclaimer_config.get("title", "免责声明")
        content_data = disclaimer_config.get("content", [])
        must_agree = disclaimer_config.get("must_agree", True)
        agree_button = disclaimer_config.get("agree_button", "我同意")
        cancel_button = disclaimer_config.get("cancel_button", "我不同意并退出")
        
        # 处理内容为字符串或数组的情况
        if isinstance(content_data, list):
            # 将数组内容转换为HTML，每项一行，不自动添加编号
            content = "<div style='margin: 5px;'>"
            for item in content_data:
                content += f"<p style='margin-bottom: 15px; line-height: 1.6;'>{item}</p>"
            content += "</div>"
        else:
            # 字符串内容直接使用，添加样式
            content = f"<div style='margin: 5px;'><p style='line-height: 1.6;'>{content_data}</p></div>"
            
        # 如果没有内容，直接返回
        if not content:
            return True
            
        # 显示免责声明对话框
        dialog = DisclaimerDialog(
            self.parent,
            title=title,
            content=content,
            agree_text=agree_button,
            cancel_text=cancel_button
        )
        
        # 居中显示
        if self.parent:
            center = self.parent.geometry().center()
            dialog.move(center.x() - dialog.width() // 2, center.y() - dialog.height() // 2)
        
        result = dialog.exec()
        
        # 如果必须同意且用户不同意，则返回False
        if must_agree and result != QDialog.DialogCode.Accepted:
            return False
            
        return True

    def _get_remote_version(self):
        """
        获取远程版本信息（现在是加密的），然后解密。
        解密流程: Base64 decode -> Fernet decrypt -> zlib decompress -> UTF-8 decode -> JSON parse
        
        返回值:
            dict: 解密后的版本信息字典，如果获取或解密失败则返回None
        """
        try:
            # 1. 获取远程 Base64 编码的加密文本
            safe_print("正在检查版本更新...")
            # 使用更低级的urllib3直接请求，更精确控制超时
            # 首先检查URL的有效性
            if not self.VERSION_CHECK_URL or not self.VERSION_CHECK_URL.startswith('http'):
                raise ValueError("无效的版本检查URL")
                
            # 使用requests库请求，但使用更细粒度的超时设置
            # 连接超时为5秒，读取超时为6秒
            response = requests.get(
                self.VERSION_CHECK_URL, 
                timeout=(6, 7),  # (连接超时, 读取超时)
                headers={'User-Agent': 'YCursor/Version_Checker'}
            )
            response.raise_for_status()  # 检查HTTP错误
            base64_content = response.text.strip()  # 获取文本内容并移除空白

            if not base64_content:
                safe_print("获取版本信息失败: 远程文件内容为空。")
                self.version_info = None
                return None

        except requests.exceptions.SSLError as e:
            # SSL证书相关错误
            safe_print(f"SSL证书错误: {str(e)}")
            self.version_info = None
            return None
        except requests.exceptions.ConnectionError as e:
            # 真正的连接错误
            safe_print(f"连接错误: {str(e)}")
            self.version_info = None
            return None
        except requests.RequestException as e:
            # 其他请求错误
            safe_print(f"请求错误: {str(e)}")
            # 检查是否与SSL相关
            if "SSL" in str(e) or "证书" in str(e) or "certificate" in str(e).lower():
                # 可能是SSL相关错误
                safe_print(f"检测到SSL相关错误，自动忽略并继续: {str(e)}")
                self.version_info = None
                return None
        except Exception as e:
            safe_print(f"获取版本信息时发生未知错误: {str(e)}")
            self.version_info = None
            return None

        try:
            # 2. Base64 解码
            encrypted_content = base64.urlsafe_b64decode(base64_content)
            # print(f"调试: Base64 解码成功，长度: {len(encrypted_content)}") # Debug

            # 3. Fernet 解密
            f = Fernet(self.DECRYPTION_KEY)
            compressed_content = f.decrypt(encrypted_content)
            # print(f"调试: Fernet 解密成功，长度: {len(compressed_content)}") # Debug

            # 4. zlib 解压缩
            byte_content = zlib.decompress(compressed_content)
            # print(f"调试: zlib 解压缩成功，长度: {len(byte_content)}") # Debug

            # 5. UTF-8 解码
            json_string = byte_content.decode('utf-8')
            # print("调试: UTF-8 解码成功") # Debug

            # 6. JSON 解析
            fetched_data = json.loads(json_string)
            # print("调试: JSON 解析成功") # Debug

            # 成功，返回解密后的数据
            return fetched_data

        except (binascii.Error, ValueError) as e: # Catch Base64 specific errors
            safe_print(f"解密版本信息失败(Base64解码错误): {str(e)}")
            self.version_info = None
            return None
        except InvalidToken:
            safe_print("解密版本信息失败(Fernet解密错误): 密钥不匹配或数据被篡改。")
            self.version_info = None
            return None
        except zlib.error as e:
            safe_print(f"解密版本信息失败(zlib解压缩错误): {str(e)}")
            self.version_info = None
            return None
        except UnicodeDecodeError as e:
            safe_print(f"解密版本信息失败(UTF-8解码错误): {str(e)}")
            self.version_info = None
            return None
        except json.JSONDecodeError as e:
            safe_print(f"解密版本信息失败(JSON解析错误): {str(e)}")
            self.version_info = None
            return None
        except Exception as e:
            # Catch any other unexpected errors during decryption
            safe_print(f"解密版本信息时发生未知错误: {str(e)}")
            import traceback
            traceback.print_exc() # Print stack trace for unexpected errors
            self.version_info = None
            return None
    
    def _compare_versions(self, version1, version2):
        """
        比较两个版本号
        
        参数:
            version1 (str): 版本号1 (可以是空字符串或None)
            version2 (str): 版本号2 (可以是空字符串或None)
            
        返回值:
            int: 如果version1 < version2返回-1，如果version1 > version2返回1，如果version1 == version2返回0
        """
        v1 = version1 if version1 else "0"
        v2 = version2 if version2 else "0"

        try:
            # 安全地解析版本号，确保所有部分都是数字
            v1_parts = []
            for part in v1.split('.'):
                try:
                    v1_parts.append(int(part))
                except ValueError:
                    # 非数字部分视为0
                    v1_parts.append(0)
                    
            v2_parts = []
            for part in v2.split('.'):
                try:
                    v2_parts.append(int(part))
                except ValueError:
                    # 非数字部分视为0
                    v2_parts.append(0)
            
        except Exception as e:
            safe_print(f"版本号格式错误: v1='{v1}', v2='{v2}'. 无法比较，视为相等。错误: {str(e)}")
            return 0 # 格式错误时，保守处理为相等

        # 确保两个版本号长度一致
        while len(v1_parts) < len(v2_parts):
            v1_parts.append(0)
        while len(v2_parts) < len(v1_parts):
            v2_parts.append(0)

        for i in range(len(v1_parts)):
            if v1_parts[i] < v2_parts[i]:
                return -1
            elif v1_parts[i] > v2_parts[i]:
                return 1

        return 0
    
    def _show_notification(self, message, level):
        """显示通知消息"""
        title = "通知"
        icon = StyledMessageBox.INFORMATION
        
        if level == "warning":
            title = "警告"
            icon = StyledMessageBox.WARNING
        elif level == "error":
            title = "错误"
            icon = StyledMessageBox.CRITICAL
            
        StyledMessageBox.showMessage(self.parent, title, message, icon)
    
    def _show_connection_error(self):
        """显示连接错误消息"""
        StyledMessageBox.showMessage(
            self.parent,
            "连接错误",
            "无法连接到验证服务器，请检查您的网络连接\n\nYCursor 需要连接上验证服务器才能正常使用 \n\n请尝试开关代理、更换节点、更换网络后进行重试 \n\n验证服务器在国外，内地网络不一定连的上 \n\n如果依旧无法连接，请查看官方文档是否是版本太低，旧的服务器已经弃用了 \n\n官方文档地址（往下滑）： \nhttps://docs.qq.com/aio/DV2VKUnNaeFRyRGRH?p=DKRZhtXI98ELAa724va8q8",
            StyledMessageBox.CRITICAL,
            ["退出"]
        )
        
        return False  # 始终返回False，表示不允许继续运行
        
    def _show_ssl_error_message(self, error_details):
        """显示SSL错误消息并询问用户是否继续
        
        参数:
            error_details: SSL错误的详细信息
            
        返回值:
            bool: 用户是否选择继续
        """
        result = StyledMessageBox.showMessage(
            self.parent,
            "SSL证书问题",
            f"连接到验证服务器时遇到SSL证书问题：\n\n{error_details}\n\n这可能是因为系统时间不正确、系统证书过期或网络环境问题。\n\n您可以选择忽略此错误并继续使用程序，但这可能存在安全风险。",
            StyledMessageBox.WARNING,
            ["忽略并继续", "退出"]
        )
        
        return result  # True表示"忽略并继续"，False表示"退出"
        
    
    def _format_update_message(self, latest_version, update_url, changes, show_update_url, show_update_notes, update_date):
        """格式化更新消息"""
        message = f"当前版本: {self.current_version}\n最新版本: {latest_version}\n"
        
        if update_date:
            message += f"更新日期: {update_date}\n"
            
        if show_update_notes and changes:
            message += "\n更新内容:\n"
            for i, change in enumerate(changes, 1):
                message += f"{i}. {change}\n"
            
        if show_update_url and update_url:
            message += f"\n更新链接: {update_url}"
            
        return message
    
    def _show_update_available_message(self, latest_version, update_url, changes, show_update_url, show_update_notes, update_date):
        """显示可更新消息"""
        message = self._format_update_message(
            latest_version, update_url, changes, 
            show_update_url, show_update_notes, update_date
        )
        
        result = StyledMessageBox.showMessage(
            self.parent,
            "发现新版本",
            message,
            StyledMessageBox.INFORMATION,
            ["立即更新", "稍后更新"],
            update_url=update_url,
            show_update_url=show_update_url
        )
        
        return True  # 继续运行程序
    
    def _show_force_update_message(self, latest_version, update_url, changes, show_update_url, show_update_notes, update_date):
        """显示强制更新消息"""
        message = self._format_update_message(
            latest_version, update_url, changes,
            show_update_url, show_update_notes, update_date
        )
        message += "\n\n您必须更新到最新版本才能继续使用。"
        
        StyledMessageBox.showMessage(
            self.parent,
            "需要更新",
            message,
            StyledMessageBox.CRITICAL,
            ["退出"],
            update_url=update_url,
            show_update_url=show_update_url
        )
        
        return False  # 不继续运行程序

    def _get_agreed_version_filepath(self):
        """获取存储同意版本的文件的完整路径"""
        try:
            app_data_dir = get_app_data_dir()
            if not app_data_dir:
                safe_print("错误：无法获取应用数据目录")
                return None
            return os.path.join(app_data_dir, self.AGREED_VERSION_FILENAME)
        except Exception as e:
            safe_print(f"获取同意版本文件路径时出错: {str(e)}")
            return None

    def _get_agreed_disclaimer_version(self):
        """读取已同意的免责声明版本号"""
        filepath = self._get_agreed_version_filepath()
        if not filepath:
            return ""

        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Check for and remove BOM
                    if content.startswith('\ufeff'):
                        content = content.lstrip('\ufeff')
                    version = content.strip() # Strip whitespace

                    safe_print(f"调试：读取到的版本字符串（repr）： {repr(version)}")

                    # 基本的版本号格式校验
                    if re.match(r"^\d+(\.\d+)*$", version): # Corrected regex pattern
                        safe_print(f"调试：版本号 '{version}' 通过格式校验。")
                        return version
                    else:
                        safe_print(f"警告：同意版本文件内容格式不正确: '{version}'，将忽略。")
                        return ""
            except UnicodeDecodeError as e:
                safe_print(f"读取同意版本文件时出错(编码错误): {str(e)}")
                # 尝试使用其他编码读取
                try:
                    with open(filepath, 'r', encoding='latin-1') as f:
                        content = f.read()
                        if re.match(r"^\d+(\.\d+)*$", content.strip()):
                            return content.strip()
                except Exception:
                    pass
                return ""
            except Exception as e:
                safe_print(f"读取同意版本文件时出错: {str(e)}")
                return ""
        else:
            return ""

    def _save_agreed_disclaimer_version(self, version):
        """保存同意的免责声明版本号"""
        filepath = self._get_agreed_version_filepath()
        if not filepath:
            return False # 无法获取路径，保存失败

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(version)
            safe_print(f"已保存同意的免责声明版本: {version} 到 {filepath}")
            return True
        except UnicodeEncodeError as e:
            safe_print(f"保存同意版本文件时出错(编码错误): {str(e)}")
            # 尝试以二进制方式写入
            try:
                with open(filepath, 'wb') as f:
                    f.write(version.encode('utf-8'))
                return True
            except Exception as e2:
                safe_print(f"尝试二进制写入失败: {str(e2)}")
                return False
        except Exception as e:
            safe_print(f"保存同意版本文件时出错: {str(e)}")
            return False # 保存失败

    def get_about_info(self):
        """获取关于页面的信息"""
        if self.version_info:
            return self.version_info.get("global", {}).get("about_info", {})
        return {} # 返回空字典，让调用者处理默认值

    def _load_verification_configs(self):
        """
        从version_info加载验证配置 (包含验证码和VIP QQ验证) - 严格模式
        返回 Tuple[bool, Optional[str]]: (成功状态, 错误详情或None)
        """
        if not self.version_info:
            # safe_print("错误: _load_verification_configs 调用时 self.version_info 未初始化。") # 不再打印详细错误到控制台
            return False, "_load_verification_configs 调用时 self.version_info 未初始化。"

        try:
            global_config = self.version_info.get("global")
            if global_config is None:
                # safe_print("错误: 远程配置缺少 'global' 部分。") # 不再打印详细错误到控制台
                return False, "远程配置缺少 'global' 部分。"

            # 加载验证码验证配置
            if "code_verification" not in global_config:
                # safe_print("错误: 远程配置的 'global' 部分缺少 'code_verification'。") # 不再打印详细错误到控制台
                return False, "远程配置的 'global' 部分缺少 'code_verification'。"
            code_config = global_config["code_verification"]

            if "enabled" not in code_config:
                # safe_print("错误: 'code_verification' 配置缺少 'enabled' 字段。") # 不再打印详细错误到控制台
                return False, "'code_verification' 配置缺少 'enabled' 字段。"
            self.code_verification_enabled = code_config["enabled"]

            if self.code_verification_enabled:
                if "code" not in code_config:
                    # safe_print("错误: 'code_verification' 已启用但缺少 'code' 字段。") # 不再打印详细错误到控制台
                    return False, "'code_verification' 已启用但缺少 'code' 字段。"
                self.code_verification_code = code_config["code"]

            if "duration_hours" not in code_config:
                # safe_print("错误: 'code_verification' 配置缺少 'duration_hours' 字段。") # 不再打印详细错误到控制台
                return False, "'code_verification' 配置缺少 'duration_hours' 字段。"
            self.code_verification_duration_hours = code_config["duration_hours"]

            # 加载二维码配置 (可选)
            self.qrcode_config = code_config.get("qrcode_config", {
                "use_remote": False,
                "remote_url": "",
                "local_path": "icons/gzh.jpg",
                "fallback_to_local": True
            })

            # 加载VIP QQ验证配置
            if "vip_qq_verification" not in global_config:
                # safe_print("错误: 远程配置的 'global' 部分缺少 'vip_qq_verification'。") # 不再打印详细错误到控制台
                return False, "远程配置的 'global' 部分缺少 'vip_qq_verification'。"
            vip_qq_config = global_config["vip_qq_verification"]

            if "enabled" not in vip_qq_config:
                # safe_print("错误: 'vip_qq_verification' 配置缺少 'enabled' 字段。") # 不再打印详细错误到控制台
                return False, "'vip_qq_verification' 配置缺少 'enabled' 字段。"
            self.vip_qq_verification_enabled = vip_qq_config["enabled"]
            
            if self.vip_qq_verification_enabled:
                if "whitelist" not in vip_qq_config:
                    # safe_print("错误: 'vip_qq_verification' 已启用但缺少 'whitelist' 字段。") # 不再打印详细错误到控制台
                    return False, "'vip_qq_verification' 已启用但缺少 'whitelist' 字段。"
                self.vip_qq_whitelist = vip_qq_config["whitelist"]
            
            if "duration_hours" not in vip_qq_config:
                # safe_print("错误: 'vip_qq_verification' 配置缺少 'duration_hours' 字段。") # 不再打印详细错误到控制台
                return False, "'vip_qq_verification' 配置缺少 'duration_hours' 字段。"
            self.vip_qq_duration_hours = vip_qq_config["duration_hours"]
            

            safe_print(f"验证配置已加载: 验证码启用={self.code_verification_enabled}, VIP QQ启用={self.vip_qq_verification_enabled}")
            safe_print(f"验证码时效={self.code_verification_duration_hours}小时, VIP QQ时效={self.vip_qq_duration_hours}小时")
            return True, None # 成功时返回True和None
            
        except KeyError as e:
            # safe_print(f"加载验证配置时键错误: 缺少键 {str(e)}") # 不再打印详细错误到控制台
            return False, f"加载验证配置时键错误: 缺少键 {str(e)}"
        except TypeError as e: # Catches issues like trying to access a field on a NoneType if a section is missing but not caught above
            # safe_print(f"加载验证配置时类型错误 (可能是某配置节缺失导致): {str(e)}") # 不再打印详细错误到控制台
            return False, f"加载验证配置时类型错误 (可能是某配置节缺失导致): {str(e)}"
        except Exception as e:
            # safe_print(f"加载验证配置时发生未知错误: {e}") # 不再打印详细错误到控制台
            import traceback
            # traceback.print_exc() # 终端不应打印堆栈
            return False, f"加载验证配置时发生未知错误: {str(e)}"
    
    def _check_program_verification(self):
        """
        检查程序验证 (包括验证码和VIP QQ)
        
        返回值:
            bool: 是否通过验证
        """
        # 如果验证码验证和VIP QQ验证都未启用，则允许继续
        if not self.code_verification_enabled and not self.vip_qq_verification_enabled:
            safe_print("所有验证均未启用，允许继续运行")
            return True
            
        verification_data = self._load_verification_data()

        if verification_data:
            source = verification_data.get("source")
            timestamp = verification_data.get("timestamp", 0)
            current_time = int(time.time())

            # 检查有效的验证码会话
            if source == "code_verification_success" and self.code_verification_enabled:
                expiration_time = timestamp + self.code_verification_duration_hours * 3600
                if current_time < expiration_time:
                    remaining_hours = (expiration_time - current_time) // 3600
                    message = f"验证码验证还在有效期内，剩余时间: {remaining_hours}小时，总有效期: {self.code_verification_duration_hours}小时"
                    safe_print(message)
                    print(f"\n[验证] {message}\n")
                    return True
                else:
                    message = f"验证码验证已过期，需要重新验证（有效期为{self.code_verification_duration_hours}小时）"
                    safe_print(message)
                    print(f"\n[验证] {message}\n")
            
            # 检查有效的VIP QQ会话 (使用 elif 确保在前一个条件不满足或过期时才检查)
            elif source == "vip_qq_verification_success" and self.vip_qq_verification_enabled:
                expiration_time = timestamp + self.vip_qq_duration_hours * 3600
                if current_time < expiration_time:
                    remaining_hours = (expiration_time - current_time) // 3600
                    message = f"VIP QQ验证还在有效期内，剩余时间: {remaining_hours}小时，总有效期: {self.vip_qq_duration_hours}小时"
                    safe_print(message)
                    print(f"\n[验证] {message}\n")
                    return True
                else:
                    message = f"VIP QQ验证已过期，需要重新验证（有效期为{self.vip_qq_duration_hours}小时）"
                    safe_print(message)
                    print(f"\n[验证] {message}\n")
            
            # 如果加载了数据，但source不匹配任何活动且启用的验证类型，或对应类型已过期，
            # 则会自然地落到下面的逻辑，以显示验证对话框。

        # 如果没有有效的已保存会话 (无数据、数据过期、source未知或对应模式禁用)
        # 则确定初始对话框模式并显示对话框
        initial_dialog_mode = None
        if self.code_verification_enabled:
            initial_dialog_mode = "code_verification"
        elif self.vip_qq_verification_enabled: # 仅在验证码验证未启用时，才将VIP设为初始模式
            initial_dialog_mode = "vip_qq_verification"

        if initial_dialog_mode:
            safe_print(f"未找到有效会话、会话已过期或数据源不匹配，将显示验证对话框 (初始模式: {initial_dialog_mode})...")
            return self._show_verification_dialog(initial_dialog_mode)
        else:
            # 此情况理论上不应发生，因为入口处检查了至少有一个验证模式被启用。
            # 但作为最后的保险措施：
            safe_print("配置错误：验证已启用，但无法确定初始对话框模式。程序将退出。")
            return self._show_verification_error("关键配置错误，无法确定有效的验证流程。")

    def _show_verification_dialog(self, mode):
        """
        显示验证对话框
        
        参数:
            mode (str): 验证模式 ("code_verification" 或 "vip_qq_verification")
            
        返回值:
            bool: 是否通过验证
        """
        # 传入对应的配置
        # 确保传递所有模式的完整配置给对话框
        config_to_pass = {
            "code_verification": {
                "enabled": self.code_verification_enabled,
                "code": self.code_verification_code,
                "duration_hours": self.code_verification_duration_hours,
                "qrcode_config": getattr(self, 'qrcode_config', {
                    "use_remote": False,
                    "remote_url": "",
                    "local_path": "icons/gzh.jpg",
                    "fallback_to_local": True
                })
            },
            "vip_qq_verification": {
                "enabled": self.vip_qq_verification_enabled,
                "whitelist": self.vip_qq_whitelist, # 确保这里的 self.vip_qq_whitelist 是最新的
                "duration_hours": self.vip_qq_duration_hours
            }
        }
            
        try:
            # 创建验证对话框
            # dialog 将接收完整的 config_to_pass，以及初始的 mode
            dialog = VerificationDialog(self.parent, config_to_pass, mode, safe_print)
            
            # 居中显示
            if self.parent:
                center = self.parent.geometry().center()
                dialog.move(center.x() - dialog.width() // 2, center.y() - dialog.height() // 2)
            
            # 显示对话框并等待结果
            result = dialog.exec()
            
            if result == QDialog.DialogCode.Accepted:
                # 获取验证信息
                verification_info = dialog.get_verification_info()
                
                # 获取当前QQ号 (仅在VIP QQ模式下有效)
                self.current_qq = verification_info.get("qq", "")
                
                
                # 验证成功
                if verification_info.get("success", False):
                    actual_completed_mode = verification_info.get("completed_mode", mode) # 获取实际完成的模式，默认为初始mode
                    
                    # 根据模式确定正确的source和时效
                    source_to_save = ""
                    duration_hours_to_display = 0
                    verification_type_display_name = ""

                    if actual_completed_mode == "code_verification":
                        source_to_save = "code_verification_success"
                        duration_hours_to_display = self.code_verification_duration_hours
                        verification_type_display_name = "验证码验证"
                    elif actual_completed_mode == "vip_qq_verification":
                        source_to_save = "vip_qq_verification_success"
                        duration_hours_to_display = self.vip_qq_duration_hours
                        verification_type_display_name = "VIP QQ 验证"
                    else: # 理论上不应发生，但作为保险
                        source_to_save = "unknown_verification_success"
                        duration_hours_to_display = 24 # 默认值
                        verification_type_display_name = f"未知验证({actual_completed_mode})"
                        
                    # 构建要保存的数据
                    data_to_save = {
                        "qq": self.current_qq, # self.current_qq 已被 verification_info 更新
                        "timestamp": verification_info.get("timestamp", int(time.time())),
                        "source": source_to_save 
                    }
                    
                    # 保存验证信息
                    self._save_verification_data(data_to_save)
                    
                    # 打印验证时效信息
                    message = f"{verification_type_display_name}通过，有效期为{duration_hours_to_display}小时"
                    safe_print(message)
                    print(f"\n[{verification_type_display_name}] {message}\n")
                    
                    # 尝试使用应用程序日志系统记录
                    try:
                        if self.logger and callable(self.logger):
                            self.logger(message)
                    except Exception:
                        pass
                    return True
                else:
                    # 验证失败
                    safe_print("QQ群验证失败")
                    return False
            else:
                # 用户取消
                safe_print("用户取消了QQ群验证")
                return False
        
        except Exception as e:
            safe_print(f"显示QQ群验证对话框时出错: {e}")
            return self._show_verification_error(f"无法显示QQ群验证: {str(e)}")
    
    def _show_verification_error(self, error_message):
        """显示验证错误信息"""
        StyledMessageBox.showMessage(
            self.parent,
            "验证错误",
            f"{error_message}\n\n无法完成QQ群验证，程序将退出。",
            StyledMessageBox.CRITICAL,
            ["退出"]
        )
        return False
    
    def _show_blacklist_message(self):
        """显示黑名单消息"""
        StyledMessageBox.showMessage(
            self.parent,
            "访问拒绝",
            f"您的QQ账号 {self.current_qq} 已被列入黑名单，无法使用YCursor。\n\n如有疑问，请发送电子邮件给我 <EMAIL>",
            StyledMessageBox.CRITICAL,
            ["退出"]
        )
    
    def _load_verification_data(self):
        """
        加载验证数据
        
        返回值:
            dict: 验证数据，如果加载失败则返回None
        """
        filepath = self._get_verification_filepath()
        if not filepath or not os.path.exists(filepath):
            return None
            
        try:
            # 读取文件
            with open(filepath, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
                
            # 解密数据
            decrypted_data = SecurityManager.decrypt_verification_data(
                self.machine_uuid, encrypted_data
            )
            
            if not decrypted_data:
                # safe_print("解密验证数据失败")
                return None
                
            return decrypted_data
            
        except Exception as e:
            safe_print(f"加载验证数据时出错: {str(e)}")
            return None
    
    def _show_disclaimer(self, disclaimer_config):
        """
        显示免责声明
        
        参数:
            disclaimer_config: 免责声明配置字典
        返回值:
            bool: 用户是否同意免责声明 (或不需要显示)
        """
        # 检查是否需要显示免责声明 (已在 check_version 中判断，但保留内部检查以防直接调用)
        if not disclaimer_config or not disclaimer_config.get("show", False):
            return True

        title = disclaimer_config.get("title", "免责声明")
        content_data = disclaimer_config.get("content", [])
        must_agree = disclaimer_config.get("must_agree", True)
        agree_button = disclaimer_config.get("agree_button", "我同意")
        cancel_button = disclaimer_config.get("cancel_button", "我不同意并退出")
        
        # 处理内容为字符串或数组的情况
        if isinstance(content_data, list):
            # 将数组内容转换为HTML，每项一行，不自动添加编号
            content = "<div style='margin: 5px;'>"
            for item in content_data:
                content += f"<p style='margin-bottom: 15px; line-height: 1.6;'>{item}</p>"
            content += "</div>"
        else:
            # 字符串内容直接使用，添加样式
            content = f"<div style='margin: 5px;'><p style='line-height: 1.6;'>{content_data}</p></div>"
            
        # 如果没有内容，直接返回
        if not content:
            return True
            
        # 显示免责声明对话框
        dialog = DisclaimerDialog(
            self.parent,
            title=title,
            content=content,
            agree_text=agree_button,
            cancel_text=cancel_button
        )
        
        # 居中显示
        if self.parent:
            center = self.parent.geometry().center()
            dialog.move(center.x() - dialog.width() // 2, center.y() - dialog.height() // 2)
        
        result = dialog.exec()
        
        # 如果必须同意且用户不同意，则返回False
        if must_agree and result != QDialog.DialogCode.Accepted:
            return False
            
        return True

    def _show_critical_config_error(self, detail_message):
        """显示严重配置错误消息并指示程序应退出"""
        # safe_print(f"严重配置错误: {detail_message}") # 移除终端的详细错误打印
        StyledMessageBox.showMessage(
            self.parent,
            "获取的配置错误",
            # f"无法加载配置，关键信息缺失或格式不正确 \n\n请更新 YCursor 版本后重试\n\n详细信息: {detail_message}",
            f"无法加载配置，关键信息缺失或格式不正确 \n\n请更换网络或更新 YCursor 版本后重试\n\n\n",
            StyledMessageBox.CRITICAL,
            ["退出"]
        )
        return False # 返回False以指示调用者应终止程序
    
    def _format_update_message(self, latest_version, update_url, changes, show_update_url, show_update_notes, update_date):
        """格式化更新消息"""
        message = f"当前版本: {self.current_version}\n最新版本: {latest_version}\n"
        
        if update_date:
            message += f"更新日期: {update_date}\n"
            
        if show_update_notes and changes:
            message += "\n更新内容:\n"
            for i, change in enumerate(changes, 1):
                message += f"{i}. {change}\n"
            
        if show_update_url and update_url:
            message += f"\n更新链接: {update_url}"

        return message

    def start_auth_check_timer(self):
        """启动认证时效检查定时器（全局运行，不受窗口状态影响）"""
        try:
            # 创建定时器 - 使用精确定时器类型确保全局运行
            self.auth_check_timer = QTimer(self)
            self.auth_check_timer.setTimerType(Qt.TimerType.PreciseTimer)
            self.auth_check_timer.timeout.connect(self._check_auth_expiry)

            # 设置定时器间隔（转换为毫秒）- 使用固定的本地配置
            interval_ms = self.auth_check_interval_minutes * 60 * 1000
            self.auth_check_timer.start(interval_ms)

        except Exception as e:
            safe_print(f"启动认证时效检查定时器时出错: {e}")

    def stop_auth_check_timer(self):
        """停止认证时效检查定时器（仅在程序退出时调用）"""
        if self.auth_check_timer:
            self.auth_check_timer.stop()
            self.auth_check_timer = None

    def is_auth_check_timer_running(self):
        """检查认证时效检查定时器是否正在运行"""
        return (hasattr(self, 'auth_check_timer') and
                self.auth_check_timer is not None and
                self.auth_check_timer.isActive())

    def restart_auth_check_timer_if_stopped(self):
        """如果认证时效检查定时器被意外停止，重新启动它"""
        if not self.is_auth_check_timer_running():
            self.start_auth_check_timer()



    def _get_remote_version_with_retry(self):
        """带重试机制的远程配置获取"""
        for attempt in range(self.REMOTE_CONFIG_RETRY_COUNT):
            try:
                version_info = self._get_remote_version()
                if version_info:
                    return version_info

                if attempt < self.REMOTE_CONFIG_RETRY_COUNT - 1:
                    time.sleep(self.REMOTE_CONFIG_RETRY_INTERVAL)

            except Exception:
                if attempt < self.REMOTE_CONFIG_RETRY_COUNT - 1:
                    time.sleep(self.REMOTE_CONFIG_RETRY_INTERVAL)

        return None

    def _check_verification_expiry_only(self):
        """
        仅检查认证时效是否过期，不显示验证对话框
        用于定时检查认证状态

        返回值:
            bool: True表示认证仍然有效，False表示认证已过期
        """
        # 如果验证码验证和VIP QQ验证都未启用，则认为认证有效
        if not self.code_verification_enabled and not self.vip_qq_verification_enabled:
            return True

        verification_data = self._load_verification_data()

        if verification_data:
            source = verification_data.get("source")
            timestamp = verification_data.get("timestamp", 0)
            current_time = int(time.time())

            # 检查验证码验证是否在有效期内
            if source == "code_verification_success" and self.code_verification_enabled:
                expiration_time = timestamp + self.code_verification_duration_hours * 3600
                return current_time < expiration_time

            # 检查VIP QQ验证是否在有效期内
            elif source == "vip_qq_verification_success" and self.vip_qq_verification_enabled:
                expiration_time = timestamp + self.vip_qq_duration_hours * 3600
                return current_time < expiration_time

        # 如果没有有效的验证数据，则认为认证已过期
        return False

    def _check_auth_expiry(self):
        """检查认证时效（全局运行，不受窗口状态影响）"""
        try:
            # 如果已经显示过认证过期对话框，则不再重复检查
            if self.auth_expiry_dialog_shown:
                return

            # 首先检查定时器是否还在运行，如果被意外停止则重新启动
            if not self.is_auth_check_timer_running():
                self.start_auth_check_timer()
                return

            # 获取远程配置以检查当前的认证时效规则
            version_info = self._get_remote_version_with_retry()
            if not version_info:
                self._exit_app_due_to_auth_expiry()
                return

            # 加载验证配置
            success, _ = self._load_verification_configs()
            if not success:
                self._exit_app_due_to_auth_expiry()
                return

            # 检查当前的认证状态（仅检查时效，不显示对话框）
            if not self._check_verification_expiry_only():
                self._exit_app_due_to_auth_expiry()
            else:
                # 确保定时器仍在运行
                if not self.is_auth_check_timer_running():
                    self.start_auth_check_timer()

        except Exception:
            self._exit_app_due_to_auth_expiry()

    def _exit_app_due_to_auth_expiry(self):
        """因认证时效过期而退出应用程序"""
        try:
            # 防止重复显示对话框
            if self.auth_expiry_dialog_shown:
                sys.exit(0)
                return

            # 标记已显示对话框
            self.auth_expiry_dialog_shown = True

            # 停止所有定时器
            self.stop_auth_check_timer()

            # 创建自定义样式的置顶对话框
            dialog = StyledMessageBox(
                self.parent,
                "认证时效过期",
                "您的认证时效已过期，请重新启动软件进行认证",
                StyledMessageBox.WARNING,
                ["确定"]
            )

            # 跨平台设置对话框置顶显示
            import sys
            if sys.platform == "win32":
                # Windows: 使用 WindowStaysOnTopHint
                dialog.setWindowFlags(
                    Qt.WindowType.FramelessWindowHint |
                    Qt.WindowType.Dialog |
                    Qt.WindowType.WindowStaysOnTopHint
                )
            elif sys.platform == "darwin":
                # macOS: 使用 WindowStaysOnTopHint + Tool
                dialog.setWindowFlags(
                    Qt.WindowType.FramelessWindowHint |
                    Qt.WindowType.Tool |
                    Qt.WindowType.WindowStaysOnTopHint
                )
            else:
                # Linux: 使用 WindowStaysOnTopHint + X11BypassWindowManagerHint
                dialog.setWindowFlags(
                    Qt.WindowType.FramelessWindowHint |
                    Qt.WindowType.Dialog |
                    Qt.WindowType.WindowStaysOnTopHint |
                    Qt.WindowType.X11BypassWindowManagerHint
                )

            # 确保对话框在屏幕中央显示
            try:
                screen_center = dialog.screen().geometry().center()
                dialog_center = dialog.rect().center()
                dialog.move(screen_center - dialog_center)
            except:
                # 如果居中失败，使用默认位置
                pass

            # 跨平台激活并置顶窗口
            try:
                dialog.show()  # 先显示
                dialog.activateWindow()  # 激活窗口
                dialog.raise_()  # 置顶

                # 额外的平台特定处理
                if sys.platform == "win32":
                    # Windows: 强制前置窗口
                    import ctypes
                    try:
                        hwnd = int(dialog.winId())
                        ctypes.windll.user32.SetForegroundWindow(hwnd)
                        ctypes.windll.user32.BringWindowToTop(hwnd)
                    except:
                        pass
                elif sys.platform == "darwin":
                    # macOS: 请求用户注意
                    try:
                        from PySide6.QtWidgets import QApplication
                        QApplication.instance().alert(dialog)
                    except:
                        pass

            except:
                # 如果激活失败，继续显示对话框
                pass

            # 显示模态对话框
            dialog.exec()

            # 强制退出应用程序，确保无论窗口状态如何都能退出
            try:
                # 尝试优雅关闭
                if self.parent:
                    self.parent.close()
                QCoreApplication.quit()
            except:
                pass

            # 强制退出，确保程序一定会关闭
            sys.exit(0)

        except Exception:
            # 强制退出
            sys.exit(1)

def check_version(parent=None):
    """
    检查版本的便捷函数
    
    返回值:
        bool: 是否可以继续运行
    """
    checker = VersionChecker(parent)
    return checker.check_version()


if __name__ == "__main__":
    # 测试版本检查功能
    app = QApplication([])
    result = check_version()
    safe_print(f"版本检查结果: {'可以继续运行' if result else '需要退出'}")
    sys.exit(0)
