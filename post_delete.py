import requests
import json

# cookie固定的前缀
prefix = 'user_01000000000000000000000000%3A%3A'
# 填写账号token部分
token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlNaWkI0UFpKNlNFRTQ1NkNBQzBaUlRHIiwidGltZSI6IjE3NDU5MDM1NDMiLCJyYW5kb21uZXNzIjoiYTU5N2UwNWItYjBiZi00ZjFmIiwiZXhwIjoxNzUxMDg3NTQzLCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20ifQ.SiIYDvXvSCCDsnRL0KPpGkqLq_iIHGBw97vyFkW3LbY'

# 拼接完整的token
full_token = prefix + token

cookies = {
    'WorkosCursorSessionToken': full_token
}

headers = {
    'authority': 'www.cursor.com',
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'content-type': 'application/json',
    'origin': 'https://www.cursor.com',
    'referer': 'https://www.cursor.com/cn/settings',
    'sec-ch-ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
}

data = {}  # 空JSON对象作为请求体

response = requests.post('https://www.cursor.com/api/dashboard/delete-account', cookies=cookies, headers=headers, json=data)

# 显示原始响应
print("API响应：", response.text)

# 添加判断逻辑
try:
    response_json = response.json()
    # 检查是否为空对象 {}，这表示成功
    if response_json == {}:
        print("\n✅ 删除账户成功！")
    else:
        # 检查是否包含错误信息
        if "error" in response_json:
            error_message = response_json["error"].get("message", "未知错误")
            details = response_json["error"].get("details", [])
            
            print(f"\n❌ 删除账户失败：{error_message}")
            
            # 显示详细错误信息
            if details:
                for detail in details:
                    error_type = detail.get("error", "")
                    if "details" in detail and isinstance(detail["details"], dict):
                        error_title = detail["details"].get("title", "")
                        error_detail = detail["details"].get("detail", "")
                        print(f"  - 错误类型: {error_type}")
                        print(f"  - 错误标题: {error_title}")
                        print(f"  - 错误详情: {error_detail}")
        else:
            print("\n❓ 未知响应格式")
except json.JSONDecodeError:
    # 响应不是有效的JSON
    if response.text.strip() == "{}":
        print("\n✅ 删除账户成功！")
    else:
        print("\n❌ 删除账户失败：响应格式无效")
except Exception as e:
    print(f"\n❌ 处理响应时出错：{str(e)}")
