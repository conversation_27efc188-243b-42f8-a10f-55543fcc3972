#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
平滑滚动动画模块
提供平滑滚动动画效果类
"""

import sys

from PySide6.QtCore import QObject, QPropertyAnimation, QEasingCurve, QAbstractAnimation, Signal, QTimer


class SmoothScrollAnimation(QObject):
    """平滑滚动动画类"""
    
    # 动画完成信号
    animation_finished = Signal()
    
    def __init__(self, scroll_bar, duration=500, easing_curve=QEasingCurve.Type.OutQuad):
        """初始化平滑滚动动画
        
        Args:
            scroll_bar: 滚动条对象
            duration: 动画持续时间(毫秒)
            easing_curve: 缓动曲线
        """
        super().__init__(scroll_bar)
        self._scroll_bar = scroll_bar
        # Directly animate the scroll bar's 'value' property
        self._animation = QPropertyAnimation(self._scroll_bar, b"value", self)
        self._animation.setDuration(duration)
        self._animation.setEasingCurve(easing_curve)
        self._animation.finished.connect(self.animation_finished) # Connect animation finish signal

        # 定时器用于平滑结束 (Keep timer logic for potential future use or removal)
        self._stop_timer = QTimer(self)
        self._stop_timer.setSingleShot(True)
        self._stop_timer.timeout.connect(self._smooth_stop)

    def scroll_to(self, position, duration=None, easing_curve=None):
        """滚动到指定位置
        
        Args:
            position: 目标滚动位置
            duration: 可选的动画持续时间，覆盖默认值
            easing_curve: 可选的缓动曲线，覆盖默认值
        """
        # 停止任何正在进行的动画
        if self._animation.state() == QAbstractAnimation.State.Running:
            self._animation.stop()
        
        # 应用自定义设置如果提供了的话
        if duration is not None:
            self._animation.setDuration(duration)
        
        if easing_curve is not None:
            self._animation.setEasingCurve(easing_curve)
        
        # 设置动画属性
        start_value = self._scroll_bar.value()
        self._animation.setStartValue(start_value)
        self._animation.setEndValue(position)
        
        # 如果起始位置和目标位置相同，直接发送完成信号
        if start_value == position:
            # Use QTimer to emit signal slightly later to avoid issues
            QTimer.singleShot(0, self.animation_finished.emit)
            return
            
        # 开始动画
        self._animation.start(QPropertyAnimation.DeletionPolicy.KeepWhenStopped)
    
    def _smooth_stop(self):
        """平滑停止动画（由定时器触发）"""
        if self._animation.state() == QAbstractAnimation.State.Running:
            self._animation.stop()
        self._stop_timer.stop() # Ensure timer is stopped
    
    def stop(self):
        """显式停止当前动画"""
        if self._animation.state() == QAbstractAnimation.State.Running:
            self._animation.stop()
        self._stop_timer.stop() # Also stop the timer if manual stop is called
    
    def set_duration(self, duration):
        """设置动画持续时间
        
        Args:
            duration: 动画持续时间(毫秒)
        """
        self._animation.setDuration(duration)
    
    def set_easing_curve(self, easing_curve):
        """设置缓动曲线
        
        Args:
            easing_curve: 缓动曲线
        """
        self._animation.setEasingCurve(easing_curve)