#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
动画管理器模块
提供中央动画管理，负责处理来自UI更新线程的动画请求
"""

from PySide6.QtCore import QObject, Slot, QPropertyAnimation, QEasingCurve
from PySide6.QtCore import QParallelAnimationGroup, QSequentialAnimationGroup, QPoint
from PySide6.QtWidgets import QWidget

from theme import Theme
from widgets.animated_widgets import AnimatedStackedWidget, AnimatedNumberLabel
from widgets.styled_widgets import StyledProgressBar
from smooth_scroll_animation import SmoothScrollAnimation


class AnimationManager(QObject):
    """动画管理器类
    
    负责接收动画请求并在主线程中执行动画
    作为中央动画控制系统，提供更好的动画管理和性能优化
    """
    
    def __init__(self, parent=None):
        """初始化动画管理器
        
        Args:
            parent: 父对象
        """
        super().__init__(parent)
        self.active_animations = {}  # 当前活动的动画
        self.animation_locked = False  # 动画锁定状态
        
        # 记录上次执行的动画，用于防止重复执行相同的动画
        self.last_animation_params = {}
    
    @Slot(str, dict)
    def handle_animation_request(self, animation_type, params):
        """处理动画请求
        
        Args:
            animation_type: 动画类型
            params: 动画参数
        """
        # 如果动画被锁定，则忽略请求
        if self.animation_locked:
            print(f"动画管理器: 动画已锁定，忽略请求 - {animation_type}")
            return
            
        try:
            # 检查是否是重复的动画请求（相同类型和参数）
            cache_key = f"{animation_type}_{hash(str(params))}"
            if cache_key in self.last_animation_params:
                last_time = self.last_animation_params[cache_key]['time']
                import time
                current_time = time.time()
                # 如果在0.5秒内收到相同的请求，则忽略
                if current_time - last_time < 0.5:
                    print(f"动画管理器: 忽略重复的动画请求 - {animation_type}")
                    return
                    
            # 更新上次执行的动画记录
            import time
            self.last_animation_params[cache_key] = {
                'time': time.time(),
                'params': params
            }
            
            # 根据动画类型调用对应的处理方法
            if animation_type == "page_transition":
                self._handle_page_transition(params)
            elif animation_type == "progress_bar":
                self._handle_progress_bar(params)
            elif animation_type == "number_label":
                self._handle_number_label(params)
            elif animation_type == "scroll":
                self._handle_scroll(params)
            else:
                print(f"动画管理器: 未知动画类型 - {animation_type}")
        except Exception as e:
            print(f"动画管理器: 处理动画请求时出错 - {str(e)}")
    
    def _handle_page_transition(self, params):
        """处理页面切换动画
        
        Args:
            params: 动画参数，包括:
                widget: AnimatedStackedWidget实例
                index: 目标页面索引
                direction: 动画方向
        """
        try:
            widget = params.get('widget')
            index = params.get('index')
            direction = params.get('direction')
            
            if not isinstance(widget, AnimatedStackedWidget) or index is None:
                print("动画管理器: 页面切换动画参数无效")
                return
                
            # 创建一个唯一ID用于跟踪此动画
            animation_id = f"page_transition_{id(widget)}_{index}"
            
            # 如果相同的动画已经在运行，则停止它
            if animation_id in self.active_animations:
                self.active_animations[animation_id].stop()
                del self.active_animations[animation_id]
            
            # 使用AnimatedStackedWidget提供的方法执行滑动动画
            widget.slideInIdx(index, direction)
            
            # 记录动画实例，虽然我们无法直接获取，但标记其为活动状态
            self.active_animations[animation_id] = True
            
            print(f"动画管理器: 执行页面切换动画 - 页面索引 {index}")
        except Exception as e:
            print(f"动画管理器: 处理页面切换动画时出错 - {str(e)}")
    
    def _handle_progress_bar(self, params):
        """处理进度条动画
        
        Args:
            params: 动画参数，包括:
                progress_bar: StyledProgressBar实例
                value: 目标值
                duration: 动画持续时间
        """
        try:
            progress_bar = params.get('progress_bar')
            value = params.get('value')
            duration = params.get('duration')
            
            if not isinstance(progress_bar, StyledProgressBar) or value is None:
                print("动画管理器: 进度条动画参数无效")
                return
                
            # 创建一个唯一ID用于跟踪此动画
            animation_id = f"progress_bar_{id(progress_bar)}_{value}"
            
            # 如果相同的动画已经在运行，则停止它
            if animation_id in self.active_animations:
                self.active_animations[animation_id].stop()
                del self.active_animations[animation_id]
            
            # 如果提供了持续时间，设置进度条的动画持续时间
            if duration is not None and hasattr(progress_bar, '_animation_duration'):
                original_duration = progress_bar._animation_duration
                progress_bar._animation_duration = duration
            
            # 设置值，触发动画
            progress_bar.setValue(value)
            
            # 还原原始持续时间
            if duration is not None and hasattr(progress_bar, '_animation_duration'):
                import time
                time.sleep(0.05)  # 短暂延迟确保动画开始
                progress_bar._animation_duration = original_duration
            
            # 记录动画实例，虽然我们无法直接获取，但标记其为活动状态
            self.active_animations[animation_id] = True
            
            print(f"动画管理器: 执行进度条动画 - 目标值 {value}")
        except Exception as e:
            print(f"动画管理器: 处理进度条动画时出错 - {str(e)}")
    
    def _handle_number_label(self, params):
        """处理数字标签动画
        
        Args:
            params: 动画参数，包括:
                label: AnimatedNumberLabel实例
                value: 目标值
                duration: 动画持续时间
                animate: 是否使用动画
        """
        try:
            label = params.get('label')
            value = params.get('value')
            duration = params.get('duration')
            animate = params.get('animate', True)
            
            if not isinstance(label, AnimatedNumberLabel) or value is None:
                print("动画管理器: 数字标签动画参数无效")
                return
                
            # 创建一个唯一ID用于跟踪此动画
            animation_id = f"number_label_{id(label)}_{value}"
            
            # 如果相同的动画已经在运行，则停止它
            if animation_id in self.active_animations:
                self.active_animations[animation_id].stop()
                del self.active_animations[animation_id]
            
            # 如果提供了持续时间，保存原始持续时间
            if duration is not None and hasattr(label, 'duration'):
                original_duration = label.duration
                label.duration = duration
            
            # 设置值，触发动画
            label.setValue(value, animate)
            
            # 还原原始持续时间
            if duration is not None and hasattr(label, 'duration'):
                import time
                time.sleep(0.05)  # 短暂延迟确保动画开始
                label.duration = original_duration
            
            # 记录动画实例，虽然我们无法直接获取，但标记其为活动状态
            self.active_animations[animation_id] = True
            
            print(f"动画管理器: 执行数字标签动画 - 目标值 {value}")
        except Exception as e:
            print(f"动画管理器: 处理数字标签动画时出错 - {str(e)}")
    
    def _handle_scroll(self, params):
        """处理滚动动画
        
        Args:
            params: 动画参数，包括:
                scroll_bar: 滚动条对象
                position: 目标位置
                duration: 动画持续时间
                easing_curve: 缓动曲线
        """
        try:
            scroll_bar = params.get('scroll_bar')
            position = params.get('position')
            duration = params.get('duration', 500)
            easing_curve = params.get('easing_curve', QEasingCurve.Type.OutCubic)
            
            if scroll_bar is None or position is None:
                print("动画管理器: 滚动动画参数无效")
                return
                
            # 创建一个唯一ID用于跟踪此动画
            animation_id = f"scroll_{id(scroll_bar)}_{position}"
            
            # 如果相同的动画已经在运行，则停止它
            if animation_id in self.active_animations and hasattr(self.active_animations[animation_id], 'stop'):
                self.active_animations[animation_id].stop()
                del self.active_animations[animation_id]
            
            # 创建平滑滚动动画
            scroll_animation = SmoothScrollAnimation(scroll_bar, duration)
            scroll_animation.set_easing_curve(easing_curve)
            
            # 记录动画实例
            self.active_animations[animation_id] = scroll_animation
            
            # 启动滚动动画
            scroll_animation.scroll_to(position)
            
            print(f"动画管理器: 执行滚动动画 - 目标位置 {position}")
        except Exception as e:
            print(f"动画管理器: 处理滚动动画时出错 - {str(e)}")
    
    def lock_animations(self):
        """锁定所有动画"""
        self.animation_locked = True
        print("动画管理器: 动画已锁定")
        
    def unlock_animations(self):
        """解锁所有动画并清理无效的动画对象"""
        self.animation_locked = False
        print("动画管理器: 动画已解锁")
        
        # 清理可能已无效的动画对象
        invalid_animations = []
        for anim_id, animation in self.active_animations.items():
            try:
                # 检查动画对象是否有效
                if animation is True:  # 特殊标记，不是实际动画对象
                    continue
                    
                # 检查动画目标是否已被销毁
                if hasattr(animation, 'targetObject') and animation.targetObject() is None:
                    invalid_animations.append(anim_id)
                    print(f"动画管理器: 检测到无效动画 {anim_id}，将被清理")
                elif hasattr(animation, 'target') and animation.target is None:
                    invalid_animations.append(anim_id)
                    print(f"动画管理器: 检测到无效动画 {anim_id}，将被清理")
            except Exception as e:
                # 如果访问动画属性出错，可能是已被销毁，添加到清理列表
                invalid_animations.append(anim_id)
                print(f"动画管理器: 检测无效动画 {anim_id} 时出错 - {str(e)}")
        
        # 清理无效动画
        for anim_id in invalid_animations:
            try:
                if hasattr(self.active_animations[anim_id], 'stop'):
                    self.active_animations[anim_id].stop()
                del self.active_animations[anim_id]
            except Exception as e:
                print(f"动画管理器: 清理无效动画 {anim_id} 时出错 - {str(e)}")
        
        if invalid_animations:
            print(f"动画管理器: 已清理 {len(invalid_animations)} 个无效动画")
        
    def cancel_all_animations(self):
        """取消所有正在进行的动画"""
        # 复制动画ID列表以避免在迭代时修改字典
        animation_ids = list(self.active_animations.keys())
        
        # 逐个停止和清理动画
        for anim_id in animation_ids:
            try:
                animation = self.active_animations.get(anim_id)
                
                # 确保动画是实际对象且有stop方法
                if animation and animation is not True and hasattr(animation, 'stop'):
                    try:
                        animation.stop()
                    except Exception as e:
                        print(f"动画管理器: 停止动画 {anim_id} 时出错 - {str(e)}")
                
                # 从活动动画字典中移除
                if anim_id in self.active_animations:
                    del self.active_animations[anim_id]
            except Exception as e:
                print(f"动画管理器: 取消动画 {anim_id} 时出错 - {str(e)}")
        
        # 清除所有动画记录
        self.active_animations.clear()
        
        print(f"动画管理器: 已取消所有动画")