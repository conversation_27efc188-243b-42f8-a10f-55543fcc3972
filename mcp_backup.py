#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP备份管理模块
提供MCP配置文件的备份和恢复功能
"""

import os
import sys
import json
import shutil
import platform
from logger import info, error
from utils import get_app_data_dir


class McpBackupManager:
    """MCP备份管理器，负责MCP配置文件的备份和恢复"""
    
    def __init__(self):
        """初始化MCP备份管理器"""
        # 创建备份目录
        self.backup_dir = self._create_backup_dir()
        info(f"MCP备份目录: {self.backup_dir}")
        
        # 获取MCP配置文件路径
        self.mcp_path = self._get_mcp_path()
        info(f"MCP配置文件路径: {self.mcp_path}")
    
    def _create_backup_dir(self):
        """创建备份目录"""
        app_data_dir = get_app_data_dir()
        backup_dir = os.path.join(app_data_dir, "backups", "mcp")
        
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
            info(f"创建MCP备份目录: {backup_dir}")
        
        return backup_dir
    
    def _get_mcp_path(self):
        """获取MCP配置文件路径"""
        system = platform.system().lower()
        if "windows" in system:
            # Windows: %USERPROFILE%\.cursor\mcp.json
            mcp_path = os.path.join(os.path.expanduser("~"), ".cursor", "mcp.json")
        elif "darwin" in system:
            # macOS: ~/.cursor/mcp.json
            mcp_path = os.path.expanduser("~/.cursor/mcp.json")
        else:
            # Linux: ~/.cursor/mcp.json
            mcp_path = os.path.expanduser("~/.cursor/mcp.json")
        
        return mcp_path
    
    def _get_backup_path(self, name):
        """获取备份文件路径"""
        # 确保名称不包含特殊字符
        name = self._sanitize_name(name)
        return os.path.join(self.backup_dir, f"{name}.json")
    
    def _sanitize_name(self, name):
        """清理文件名，移除不支持的字符"""
        # 移除不支持的文件名字符
        invalid_chars = ['\\', '/', ':', '*', '?', '"', '<', '>', '|']
        for char in invalid_chars:
            name = name.replace(char, '')
        return name
    
    def check_mcp_exists(self):
        """检查MCP配置文件是否存在"""
        return os.path.exists(self.mcp_path)
    
    def create_backup(self, name):
        """创建MCP配置文件备份
        
        Args:
            name: 备份名称
            
        Returns:
            tuple: (成功标志, 错误信息)
        """
        try:
            # 检查MCP文件是否存在
            if not self.check_mcp_exists():
                return False, "MCP配置文件不存在，无法创建备份"
            
            # 确保名称不包含特殊字符
            name = self._sanitize_name(name)
            
            # 检查名称是否为空
            if not name:
                return False, "备份名称不能为空"
            
            # A备份文件路径
            backup_path = self._get_backup_path(name)
            
            # 检查备份是否已存在
            if os.path.exists(backup_path):
                return False, f"备份'{name}'已存在，请使用其他名称"
            
            # 复制MCP文件到备份目录
            shutil.copy2(self.mcp_path, backup_path)
            info(f"创建MCP备份: {name}")
            
            return True, f"成功创建备份'{name}'"
        except Exception as e:
            error_msg = f"创建MCP备份时出错: {str(e)}"
            error(error_msg)
            return False, error_msg
    
    def restore_backup(self, name):
        """恢复MCP配置文件备份
        
        Args:
            name: 备份名称
            
        Returns:
            tuple: (成功标志, 错误信息)
        """
        try:
            # 备份文件路径
            backup_path = self._get_backup_path(name)
            
            # 检查备份是否存在
            if not os.path.exists(backup_path):
                return False, f"备份'{name}'不存在"
            
            # 备份当前MCP文件（如果存在）
            if self.check_mcp_exists():
                # 创建临时备份目录
                temp_dir = os.path.join(os.path.dirname(self.mcp_path), "temp_backup")
                if not os.path.exists(temp_dir):
                    os.makedirs(temp_dir)
                
                # 临时备份当前MCP文件
                temp_backup_path = os.path.join(temp_dir, "mcp_before_restore.json")
                shutil.copy2(self.mcp_path, temp_backup_path)
            
            # 复制备份文件到MCP路径
            mcp_dir = os.path.dirname(self.mcp_path)
            if not os.path.exists(mcp_dir):
                os.makedirs(mcp_dir)
            
            shutil.copy2(backup_path, self.mcp_path)
            info(f"恢复MCP备份: {name}")
            
            return True, f"成功恢复备份'{name}'"
        except Exception as e:
            error_msg = f"恢复MCP备份时出错: {str(e)}"
            error(error_msg)
            return False, error_msg
    
    def rename_backup(self, old_name, new_name):
        """重命名MCP配置文件备份
        
        Args:
            old_name: 原备份名称
            new_name: 新备份名称
            
        Returns:
            tuple: (成功标志, 错误信息)
        """
        try:
            # 确保名称不包含特殊字符
            new_name = self._sanitize_name(new_name)
            
            # 检查新名称是否为空
            if not new_name:
                return False, "新备份名称不能为空"
            
            # 原备份文件路径
            old_backup_path = self._get_backup_path(old_name)
            
            # 新备份文件路径
            new_backup_path = self._get_backup_path(new_name)
            
            # 检查原备份是否存在
            if not os.path.exists(old_backup_path):
                return False, f"备份'{old_name}'不存在"
            
            # 检查新备份是否已存在
            if os.path.exists(new_backup_path):
                return False, f"备份'{new_name}'已存在，请使用其他名称"
            
            # 重命名备份文件
            os.rename(old_backup_path, new_backup_path)
            info(f"重命名MCP备份: {old_name} -> {new_name}")
            
            return True, f"成功将备份'{old_name}'重命名为'{new_name}'"
        except Exception as e:
            error_msg = f"重命名MCP备份时出错: {str(e)}"
            error(error_msg)
            return False, error_msg
    
    def delete_backup(self, name):
        """删除MCP配置文件备份
        
        Args:
            name: 备份名称
            
        Returns:
            tuple: (成功标志, 错误信息)
        """
        try:
            # 备份文件路径
            backup_path = self._get_backup_path(name)
            
            # 检查备份是否存在
            if not os.path.exists(backup_path):
                return False, f"备份'{name}'不存在"
            
            # 删除备份文件
            os.remove(backup_path)
            info(f"删除MCP备份: {name}")
            
            return True, f"成功删除备份'{name}'"
        except Exception as e:
            error_msg = f"删除MCP备份时出错: {str(e)}"
            error(error_msg)
            return False, error_msg
    
    def get_all_backups(self):
        """获取所有MCP配置文件备份
        
        Returns:
            list: 备份名称列表
        """
        backups = []
        try:
            # 列出备份目录中的所有JSON文件
            for file in os.listdir(self.backup_dir):
                if file.endswith(".json"):
                    # 移除.json后缀
                    name = file[:-5]
                    backups.append(name)
            
            # 按名称排序
            backups.sort()
        except Exception as e:
            error_msg = f"获取MCP备份列表时出错: {str(e)}"
            error(error_msg)
        
        return backups 