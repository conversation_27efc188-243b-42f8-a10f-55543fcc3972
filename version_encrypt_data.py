import json
import zlib
import base64
import argparse
from cryptography.fernet import Fernet
import os

# --- 使用上面生成的密钥 ---
# 确保这个密钥与 version_checker.py 中的密钥完全一致
ENCRYPTION_KEY = b'F8T8d3XQy6j9wL4qA7gC2rX7pV5kM9nH0zK1lC3bE4h='

def encrypt_file(input_path, output_path):
    """
    读取JSON文件，执行多重加密，并将结果写入输出文件。
    流程: JSON -> UTF-8 -> zlib compress -> Fernet encrypt -> Base64 encode
    """
    try:
        # 1. 读取输入文件
        print(f"读取输入文件: {input_path}")
        with open(input_path, 'r', encoding='utf-8') as f:
            json_content = f.read()
        print("成功读取文件内容。")

        # 2. 编码为 UTF-8 字节
        byte_content = json_content.encode('utf-8')
        print(f"内容已编码为 UTF-8 字节，长度: {len(byte_content)}")

        # 3. zlib 压缩
        compressed_content = zlib.compress(byte_content)
        print(f"内容已压缩，压缩后长度: {len(compressed_content)}")

        # 4. Fernet 加密
        f = Fernet(ENCRYPTION_KEY)
        encrypted_content = f.encrypt(compressed_content)
        print(f"内容已加密，加密后长度: {len(encrypted_content)}")

        # 5. Base64 编码 (URL-safe)
        base64_encoded_content = base64.urlsafe_b64encode(encrypted_content)
        print(f"内容已进行 Base64 编码，最终长度: {len(base64_encoded_content)}")

        # 6. 写入输出文件
        print(f"准备写入加密内容到: {output_path}")
        with open(output_path, 'wb') as f:
            f.write(base64_encoded_content)
        print(f"加密后的内容已成功写入到: {output_path}")

    except FileNotFoundError:
        print(f"错误：输入文件未找到: {input_path}")
        exit(1)
    except Exception as e:
        print(f"加密过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="加密 version.json 文件。")
    parser.add_argument(
        "-i", "--input",
        default="version.json",
        help="输入的明文 JSON 文件路径 (默认为 version.json)"
    )
    parser.add_argument(
        "-o", "--output",
        default="versionplus.json",
        help="输出的加密文件路径 (默认为 versionplus.json)"
    )
    args = parser.parse_args()

    # 确保输入文件存在
    if not os.path.exists(args.input):
         print(f"错误：指定的输入文件不存在: {args.input}")
         exit(1)

    print("开始加密...")
    print(f"输入文件: {os.path.abspath(args.input)}")
    print(f"输出文件: {os.path.abspath(args.output)}")
    encrypt_file(args.input, args.output)
    print("加密完成！") 