"""
主页模块
提供应用程序主页的用户界面
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QFrame
from PySide6.QtCore import Qt
from PySide6.QtGui import QPixmap

# 导入主题
from theme import Theme

# 导入自定义组件
from widgets.styled_widgets import StyledFrame
from widgets.animated_widgets import AnimatedProgressBar, AnimatedNumberLabel
from widgets.clickable_copy_label import ClickableCopyLabel

class HomePage(QWidget):
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self._setup_ui()

    def _setup_ui(self):
        """设置主页UI"""
        self.setStyleSheet("background: transparent;")  # 设置整个页面背景为透明
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(25)
        
        # 添加当前账户信息卡片
        account_frame = StyledFrame(has_glass_effect=True)
        account_layout = QVBoxLayout(account_frame)
        account_layout.setContentsMargins(25, 25, 25, 25)
        
        # 修改为与模型使用情况区域一致的背景色
        account_frame.setStyleSheet(f"""
            StyledFrame {{
                background-color: {Theme.GLASS_BG};
                border-radius: {Theme.BORDER_RADIUS};
                border: 1px solid {Theme.GLASS_BORDER};
            }}
        """)
        
        # 当前登录账户标题改为透明背景
        account_title = QLabel("当前登录账户")
        account_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_TITLE}; 
            font-weight: bold; 
            color: {Theme.ACCENT}; 
            margin-bottom: 20px;
            background-color: transparent;
            padding: 8px 15px;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        account_layout.addWidget(account_title)
        
        # 邮箱和版本信息
        info_frame = QFrame()
        info_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 15px;
            }}
        """)
        
        info_layout = QVBoxLayout(info_frame)
        info_layout.setSpacing(15)
        
        self.main_window.email_label = QLabel("邮箱: 加载中...")
        self.main_window.email_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            padding: 8px 12px;
            background-color: transparent;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        
        # 添加注册时间
        self.main_window.register_time_label = QLabel("注册时间: 加载中...")
        self.main_window.register_time_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            padding: 8px 12px;
            background-color: transparent;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        
        # 添加剩余时间 - 使用动画标签
        self.main_window.remaining_days_label = AnimatedNumberLabel(prefix="剩余天数:", suffix="天", duration=2000)
        self.main_window.remaining_days_label.setSpecialText("加载中...")
        self.main_window.remaining_days_label.setColor(Theme.SUCCESS)
        # 不再需要单独设置样式，因为已在类中设置了与QLabel一致的样式
        
        info_layout.addWidget(self.main_window.email_label)
        info_layout.addWidget(self.main_window.register_time_label)  # 添加注册时间
        info_layout.addWidget(self.main_window.remaining_days_label)  # 添加剩余时间
        
        account_layout.addWidget(info_frame)
        
        layout.addWidget(account_frame)
        
        # 添加使用情况卡片
        usage_frame = StyledFrame(has_glass_effect=True)
        usage_frame.setMinimumWidth(400)  # 设置最小宽度，确保在小窗口下有足够空间
        usage_layout = QVBoxLayout(usage_frame)
        usage_layout.setContentsMargins(25, 25, 25, 25)
        
        usage_title = QLabel("模型使用情况")
        usage_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_TITLE}; 
            font-weight: bold; 
            color: {Theme.ACCENT}; 
            margin-bottom: 20px;
            background-color: transparent;
        """)
        usage_layout.addWidget(usage_title)
        
        # 模型使用情况容器
        models_frame = QFrame()
        models_frame.setMinimumWidth(350)  # 设置最小宽度，确保在小窗口下有足够空间
        models_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 20px;
            }}
        """)
        
        models_layout = QVBoxLayout(models_frame)
        models_layout.setSpacing(25)
        
        # 高级模型使用情况
        advanced_model_frame = QFrame()
        advanced_model_frame.setStyleSheet(f"""
            QFrame {{
                background-color: transparent;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px;
            }}
        """)
        
        advanced_model_layout = QVBoxLayout(advanced_model_frame)
        advanced_model_layout.setContentsMargins(5, 5, 5, 5)  # 减小内边距，为标签提供更多空间
        advanced_model_layout.setSpacing(8)  # 减小间距
        
        # 修改高级模型标签，增强跨平台可见性
        advanced_model_label = QLabel("高级模型使用量")
        # 确保标签文字完整显示
        advanced_model_label.setWordWrap(True)  # 允许文字换行
        advanced_model_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
            padding-left: 0px;  /* 移除左边距使文字左对齐 */
            padding-bottom: 5px; /* 增加底部边距 */
            min-width: 150px; /* 设置最小宽度确保文字显示 */
            qproperty-wordWrap: true; /* 允许在必要时换行 */
        """)
        # 确保标签总是可见
        advanced_model_label.setAutoFillBackground(False)
        advanced_model_label.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        # 设置固定宽度和对齐方式
        advanced_model_label.setMinimumWidth(150)
        advanced_model_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        # 设置最小高度以确保有足够空间显示可能的两行文本
        advanced_model_label.setMinimumHeight(40)
        advanced_model_label.raise_()
        
        # 使用支持数字动画的进度条
        self.main_window.advanced_progress = AnimatedProgressBar()
        self.main_window.advanced_progress.setMinimumHeight(25)
        self.main_window.advanced_progress.setMinimumWidth(200)  # 设置最小宽度
        self.main_window.advanced_progress.setRange(0, 100)
        self.main_window.advanced_progress.setValue(0)
        self.main_window.advanced_progress.setFormat("加载中...")
        
        advanced_model_layout.addWidget(advanced_model_label)
        advanced_model_layout.addWidget(self.main_window.advanced_progress)
        
        # 普通模型使用情况
        regular_model_frame = QFrame()
        regular_model_frame.setStyleSheet(f"""
            QFrame {{
                background-color: transparent;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px;
            }}
        """)
        
        regular_model_layout = QVBoxLayout(regular_model_frame)
        regular_model_layout.setContentsMargins(5, 5, 5, 5)  # 减小内边距，为标签提供更多空间
        regular_model_layout.setSpacing(8)  # 减小间距
        
        # 修改普通模型标签，增强跨平台可见性
        regular_model_label = QLabel("普通模型使用量")
        # 确保标签文字完整显示
        regular_model_label.setWordWrap(True)  # 允许文字换行
        regular_model_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
            padding-left: 0px;  /* 移除左边距使文字左对齐 */
            padding-bottom: 5px; /* 增加底部边距 */
            min-width: 150px; /* 设置最小宽度确保文字显示 */
            qproperty-wordWrap: true; /* 允许在必要时换行 */
        """)
        # 确保标签总是可见
        regular_model_label.setAutoFillBackground(False)
        regular_model_label.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        # 设置固定宽度和对齐方式
        regular_model_label.setMinimumWidth(150)
        regular_model_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        # 设置最小高度以确保有足够空间显示可能的两行文本
        regular_model_label.setMinimumHeight(40)
        regular_model_label.raise_()
        
        # 使用支持数字动画的进度条
        self.main_window.regular_progress = AnimatedProgressBar()
        self.main_window.regular_progress.setMinimumHeight(25)
        self.main_window.regular_progress.setMinimumWidth(200)  # 设置最小宽度
        self.main_window.regular_progress.setRange(0, 100)
        self.main_window.regular_progress.setValue(0)
        self.main_window.regular_progress.setFormat("加载中...")
        
        regular_model_layout.addWidget(regular_model_label)
        regular_model_layout.addWidget(self.main_window.regular_progress)
        
        # 添加到模型布局
        models_layout.addWidget(advanced_model_frame)
        models_layout.addWidget(regular_model_frame)
        
        usage_layout.addWidget(models_frame)
        
        layout.addWidget(usage_frame)
        
        layout.addStretch() 