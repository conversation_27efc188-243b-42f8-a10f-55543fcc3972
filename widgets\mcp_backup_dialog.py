#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP备份对话框模块
提供MCP配置文件备份和恢复的对话框界面
"""

import sys
import json
import os

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QScrollArea, 
    QWidget, QLineEdit, QFrame, QMessageBox, QSizePolicy, QApplication, QFileDialog
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon

from widgets.dialog import StyledDialog
from widgets.styled_widgets import StyledButton
from widgets.toast import ToastMessage
from theme import Theme
from mcp_backup import McpBackupManager
from logger import info, error


class BackupItemWidget(QFrame):
    """备份条目控件"""
    
    # 定义信号
    switch_backup = Signal(str)
    rename_backup = Signal(str)
    delete_backup = Signal(str)
    
    def __init__(self, name):
        super().__init__()
        self.name = name
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_3};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px;
                margin: 3px 0px;
            }}
            QFrame:hover {{
                background-color: {Theme.HOVER};
                border: 1px solid {Theme.BORDER};
            }}
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 8, 15, 8)
        
        # 备份名称区域
        name_layout = QHBoxLayout()
        name_layout.setSpacing(8)
        
        # Emoji作为图标
        icon_label = QLabel("📄")
        icon_label.setStyleSheet("""
            font-size: 16px;
            background-color: transparent;
        """)
        name_layout.addWidget(icon_label)
        
        # 备份名称
        name_label = QLabel(self.name)
        name_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
            background-color: transparent;
        """)
        name_layout.addWidget(name_label)
        name_layout.addStretch()
        
        # 操作按钮容器
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            background-color: transparent;
            border: none;
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(8)
        
        # 切换按钮 - 绿色
        switch_btn = QPushButton("切换")
        switch_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        switch_btn.setFixedHeight(30)
        switch_btn.setFixedWidth(60)
        switch_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px 10px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        switch_btn.clicked.connect(self._on_switch_clicked)
        
        # 重命名按钮 - 灰色
        rename_btn = QPushButton("重命名")
        rename_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        rename_btn.setFixedHeight(30)
        rename_btn.setFixedWidth(80)
        rename_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_2};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px 10px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
                background-color: {Theme.CARD_LEVEL_2};
            }}
            QPushButton:pressed {{
                background-color: {Theme.CARD_LEVEL_1};
            }}
        """)
        rename_btn.clicked.connect(self._on_rename_clicked)
        
        # 删除按钮 - 红色
        delete_btn = QPushButton("删除")
        delete_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        delete_btn.setFixedHeight(30)
        delete_btn.setFixedWidth(60)
        delete_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.WINDOW_CLOSE};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px 10px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #ef5350;
            }}
            QPushButton:pressed {{
                background-color: #c62828;
            }}
        """)
        delete_btn.clicked.connect(self._on_delete_clicked)
        
        # 添加按钮到布局
        buttons_layout.addWidget(switch_btn)
        buttons_layout.addWidget(rename_btn)
        buttons_layout.addWidget(delete_btn)
        
        # 添加到主布局
        layout.addLayout(name_layout)
        layout.addStretch()
        layout.addWidget(buttons_frame)
    
    def _on_switch_clicked(self):
        """切换按钮点击事件"""
        self.switch_backup.emit(self.name)
    
    def _on_rename_clicked(self):
        """重命名按钮点击事件"""
        self.rename_backup.emit(self.name)
    
    def _on_delete_clicked(self):
        """删除按钮点击事件"""
        self.delete_backup.emit(self.name)


class McpBackupDialog(StyledDialog):
    toast_request = Signal(str, str) # Signal to show toast message

    def __init__(self, parent=None):
        super().__init__(parent, width=700)
        self.backup_manager = McpBackupManager()
        self.backup_list_widget = None
        self.init_ui()
        self.refresh_backup_list()
        
        # Toast消息相关
        self.current_toast = None
    
    def show_toast(self, message, error=False, duration=2000):
        """显示Toast提示消息
        
        Args:
            message: 消息文本
            error: 是否为错误消息
            duration: 显示持续时间(毫秒)
        """
        # 如果有正在显示的Toast，先移除
        if self.current_toast:
            self.current_toast.hide()
            self.current_toast.deleteLater()
            self.current_toast = None
            
        # 创建并显示新的Toast
        self.current_toast = ToastMessage(
            self,
            message,
            duration,
            Theme.ERROR if error else None
        )
        self.current_toast.showToast()
    
    def init_ui(self):
        """初始化UI"""
        # 顶部区域：标题与状态
        header_frame = QFrame()
        header_frame.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_2};
            border-radius: {Theme.BORDER_RADIUS};
            margin-bottom: 15px;
        """)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题与描述
        title_layout = QHBoxLayout()
        
        # 图标（使用emoji）
        icon_label = QLabel("💾")
        icon_label.setStyleSheet(f"""
            font-size: 28px;
            background-color: transparent;
            padding-right: 10px;
        """)
        title_layout.addWidget(icon_label)
        
        # 标题文字区域
        title_text_layout = QVBoxLayout()
        
        title_label = QLabel("MCP配置文件备份管理")
        title_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: 18px;
            font-weight: bold;
            background-color: transparent;
        """)
        title_text_layout.addWidget(title_label)
        
        desc_label = QLabel("管理、创建、切换、重命名和删除您的MCP备份文件")
        desc_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: 13px;
            background-color: transparent;
        """)
        title_text_layout.addWidget(desc_label)
        
        title_layout.addLayout(title_text_layout)
        title_layout.addStretch()
        
        # 状态指示
        status_layout = QHBoxLayout()
        status_layout.setContentsMargins(0, 10, 0, 0)
        
        status_icon = QLabel("⚪")
        if self.backup_manager.check_mcp_exists():
            status_icon.setText("✅")
            status_text = "MCP文件存在，可备份"
            status_color = Theme.SUCCESS
        else:
            status_icon.setText("❌")
            status_text = "MCP文件不存在，无法备份"
            status_color = Theme.ERROR
        
        status_icon.setStyleSheet(f"""
            font-size: 16px;
            background-color: transparent;
        """)
        
        status_label = QLabel(status_text)
        status_label.setStyleSheet(f"""
            color: {status_color};
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
            background-color: transparent;
            padding-left: 5px;
        """)
        
        status_layout.addWidget(status_icon)
        status_layout.addWidget(status_label)
        status_layout.addStretch()
        
        # 添加到头部布局
        header_layout.addLayout(title_layout)
        header_layout.addLayout(status_layout)
        
        self.addWidget(header_frame)
        
        # 中间区域：备份功能与列表
        content_frame = QFrame()
        content_frame.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_2};
            border-radius: {Theme.BORDER_RADIUS};
        """)
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # 备份按钮区域
        backup_btn_layout = QHBoxLayout()
        
        list_title = QLabel("备份列表")
        list_title.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
            background-color: transparent;
        """)
        backup_btn_layout.addWidget(list_title)
        
        backup_btn_layout.addStretch()
        
        # 备份MCP按钮 - 自定义QPushButton而不是StyledButton
        backup_btn = QPushButton("备份MCP")
        backup_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        backup_btn.setFixedHeight(36)
        backup_btn.setMinimumWidth(100)
        backup_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        backup_btn.clicked.connect(self._on_create_backup)
        if not self.backup_manager.check_mcp_exists():
            backup_btn.setEnabled(False)
            backup_btn.setToolTip("MCP文件不存在，无法创建备份")
        
        backup_btn_layout.addWidget(backup_btn)
        
        content_layout.addLayout(backup_btn_layout)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet(f"""
            background-color: {Theme.BORDER};
            margin: 10px 0px;
        """)
        content_layout.addWidget(separator)
        
        # 备份列表区域
        list_frame = QFrame()
        list_frame.setStyleSheet("""
            background-color: transparent;
            border: none;
        """)
        list_frame.setMinimumHeight(300)
        list_frame_layout = QVBoxLayout(list_frame)
        list_frame_layout.setContentsMargins(0, 0, 0, 0)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background-color: transparent;
                width: 8px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #555;
                min-height: 30px;
                border-radius: 4px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """)
        
        # 备份列表容器
        list_container = QWidget()
        list_container.setStyleSheet("background-color: transparent;")
        self.backup_list_widget = QVBoxLayout(list_container)
        self.backup_list_widget.setContentsMargins(0, 0, 0, 0)
        self.backup_list_widget.setSpacing(10)
        
        # 设置滚动区域
        scroll_area.setWidget(list_container)
        list_frame_layout.addWidget(scroll_area)
        
        content_layout.addWidget(list_frame)
        
        self.addWidget(content_frame)
        
        # 底部按钮
        close_btn = self.addButtons("关闭", cancel_text=None)
        close_btn.clicked.connect(self.accept)  # 确保关闭按钮能正确关闭对话框
    
    def refresh_backup_list(self):
        """刷新备份列表"""
        # 清空列表
        while self.backup_list_widget.count():
            item = self.backup_list_widget.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 获取所有备份
        backups = self.backup_manager.get_all_backups()
        
        # 判断是否有备份
        if not backups:
            # 没有备份时显示提示
            empty_label = QLabel("还没有备份哦~")
            empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            empty_label.setStyleSheet(f"""
                color: {Theme.TEXT_SECONDARY};
                font-size: {Theme.FONT_SIZE_NORMAL};
                background-color: transparent;
                padding: 40px 20px;
            """)
            self.backup_list_widget.addWidget(empty_label)
        else:
            # 有备份时添加备份项
            for name in backups:
                backup_item = BackupItemWidget(name)
                backup_item.switch_backup.connect(self._on_switch_backup)
                backup_item.rename_backup.connect(self._on_rename_backup)
                backup_item.delete_backup.connect(self._on_delete_backup)
                self.backup_list_widget.addWidget(backup_item)
            
            # 添加弹性空间，确保备份项靠上对齐
            self.backup_list_widget.addStretch(1)
    
    def _on_create_backup(self):
        """创建备份按钮点击事件"""
        # 检查MCP文件是否存在
        if not self.backup_manager.check_mcp_exists():
            QMessageBox.warning(self, "创建备份失败", "MCP配置文件不存在，无法创建备份")
            return
        
        # 创建备份名称输入对话框
        backup_dialog = StyledDialog(self, None)
        
        # 主内容区域
        content_frame = QFrame()
        content_frame.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_2};
            border-radius: {Theme.BORDER_RADIUS};
            padding: 0px;
        """)
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(15)
        
        # 添加说明
        description = QLabel("请输入备份名称（不允许特殊符号）:")
        description.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            background-color: transparent;
        """)
        content_layout.addWidget(description)
        
        # 创建输入框
        name_input = QLineEdit()
        name_input.setPlaceholderText("请输入备份名称")
        name_input.setMaxLength(10)  # 限制最大输入长度为10个字符
        name_input.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_1};
            color: {Theme.TEXT_PRIMARY};
            border: 1px solid {Theme.BORDER};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            padding: 10px 15px;
            margin-top: 5px;
            margin-bottom: 5px;
            font-size: {Theme.FONT_SIZE_NORMAL};
            selection-background-color: {Theme.ACCENT};
        """)
        name_input.setMinimumHeight(40)
        content_layout.addWidget(name_input)
        
        # 添加提示信息
        hint_label = QLabel("💡 好的命名可以帮助您区分不同的配置（最多10个字符）")
        hint_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: {Theme.FONT_SIZE_SMALL};
            background-color: transparent;
            padding-bottom: 5px;
        """)
        content_layout.addWidget(hint_label)
        
        backup_dialog.addWidget(content_frame)
        
        # 添加确认和取消按钮
        button_layout = QHBoxLayout()
        
        # 确认按钮
        confirm_btn = QPushButton("确认")
        confirm_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        confirm_btn.setMinimumHeight(36)
        confirm_btn.setMinimumWidth(100)
        confirm_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        cancel_btn.setMinimumHeight(36)
        cancel_btn.setMinimumWidth(100)
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_2};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
            }}
            QPushButton:pressed {{
                background-color: {Theme.CARD_LEVEL_1};
            }}
        """)
        
        # 设置对话框按钮
        button_frame = QFrame()
        button_frame.setStyleSheet("background-color: transparent;")
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(confirm_btn)
        
        backup_dialog.addWidget(button_frame)
        
        # 连接确认按钮点击事件
        def on_confirm():
            name = name_input.text().strip()
            if not name:
                QMessageBox.warning(self, "创建备份失败", "备份名称不能为空")
                return
            
            # 创建备份
            success, message = self.backup_manager.create_backup(name)
            if success:
                info(message)
                backup_dialog.accept()
                # 刷新备份列表
                self.refresh_backup_list()
                # 显示Toast成功提示
                self.show_toast(f"成功创建备份 '{name}'")
            else:
                error(message)
                QMessageBox.warning(self, "创建备份失败", message)
        
        confirm_btn.clicked.connect(on_confirm)
        cancel_btn.clicked.connect(backup_dialog.reject)
        
        # 添加回车键确认功能
        name_input.returnPressed.connect(on_confirm)
        
        # 设置初始焦点
        name_input.setFocus()
        
        # 显示对话框
        backup_dialog.exec()
    
    def _on_switch_backup(self, name):
        """切换备份"""
        # 确认对话框
        if not StyledDialog.showConfirmDialog(
            self, 
            None, 
            f"确定要切换到备份'{name}'吗？\n这将替换您当前的MCP配置",
            "确认切换",
            "取消"
        ):
            return
        
        # 切换备份
        success, message = self.backup_manager.restore_backup(name)
        if success:
            info(message)
            # 显示Toast成功提示
            self.show_toast(f"成功切换到备份 '{name}'")
        else:
            error(message)
            QMessageBox.warning(self, "切换备份失败", message)
    
    def _on_rename_backup(self, old_name):
        """重命名备份"""
        # 创建重命名对话框
        rename_dialog = StyledDialog(self, None)
        
        # 主内容区域
        content_frame = QFrame()
        content_frame.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_2};
            border-radius: {Theme.BORDER_RADIUS};
            padding: 0px;
        """)
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(15)
        
        # 添加说明
        description = QLabel(f"将备份 '{old_name}' 重命名为:")
        description.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            background-color: transparent;
        """)
        content_layout.addWidget(description)
        
        # 创建输入框
        name_input = QLineEdit()
        name_input.setText(old_name)
        name_input.setMaxLength(10)  # 限制最大输入长度为10个字符
        name_input.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_1};
            color: {Theme.TEXT_PRIMARY};
            border: 1px solid {Theme.BORDER};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            padding: 10px 15px;
            margin-top: 5px;
            margin-bottom: 5px;
            font-size: {Theme.FONT_SIZE_NORMAL};
            selection-background-color: {Theme.ACCENT};
        """)
        name_input.setMinimumHeight(40)
        content_layout.addWidget(name_input)
        
        # 添加提示信息
        hint_label = QLabel("💡 不允许使用特殊符号，字符上限为10个")
        hint_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: {Theme.FONT_SIZE_SMALL};
            background-color: transparent;
            padding-bottom: 5px;
        """)
        content_layout.addWidget(hint_label)
        
        rename_dialog.addWidget(content_frame)
        
        # 添加确认和取消按钮
        button_layout = QHBoxLayout()
        
        # 确认按钮
        confirm_btn = QPushButton("确认")
        confirm_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        confirm_btn.setMinimumHeight(36)
        confirm_btn.setMinimumWidth(100)
        confirm_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        cancel_btn.setMinimumHeight(36)
        cancel_btn.setMinimumWidth(100)
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_2};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
            }}
            QPushButton:pressed {{
                background-color: {Theme.CARD_LEVEL_1};
            }}
        """)
        
        # 设置对话框按钮
        button_frame = QFrame()
        button_frame.setStyleSheet("background-color: transparent;")
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(confirm_btn)
        
        rename_dialog.addWidget(button_frame)
        
        # 连接确认按钮点击事件
        def on_confirm():
            new_name = name_input.text().strip()
            if not new_name:
                QMessageBox.warning(self, "重命名失败", "备份名称不能为空")
                return
            
            if new_name == old_name:
                rename_dialog.accept()
                return
            
            # 重命名备份
            success, message = self.backup_manager.rename_backup(old_name, new_name)
            if success:
                info(message)
                rename_dialog.accept()
                # 刷新备份列表
                self.refresh_backup_list()
                # 显示Toast成功提示
                self.show_toast(f"成功将备份 '{old_name}' 重命名为 '{new_name}'")
            else:
                error(message)
                QMessageBox.warning(self, "重命名失败", message)
        
        confirm_btn.clicked.connect(on_confirm)
        cancel_btn.clicked.connect(rename_dialog.reject)
        
        # 添加回车键确认功能
        name_input.returnPressed.connect(on_confirm)
        
        # 设置初始焦点并选中全部文本
        name_input.setFocus()
        name_input.selectAll()
        
        # 显示对话框
        rename_dialog.exec()
    
    def _on_delete_backup(self, name):
        """删除备份"""
        # 确认对话框
        if not StyledDialog.showConfirmDialog(
            self, 
            None, 
            f"确定要删除备份'{name}'吗？\n此操作不可恢复",
            "确认删除",
            "取消",
            confirm_color=Theme.WINDOW_CLOSE
        ):
            return
        
        # 删除备份
        success, message = self.backup_manager.delete_backup(name)
        if success:
            info(message)
            # 刷新备份列表
            self.refresh_backup_list()
            # 显示Toast成功提示
            self.show_toast(f"成功删除备份 '{name}'")
        else:
            error(message)
            QMessageBox.warning(self, "删除备份失败", message) 