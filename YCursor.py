#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
YCursor主程序
提供主应用程序类和程序入口点
"""

import os
import sys
import time
import json
import threading
import subprocess
import asyncio
import sqlite3
import tempfile
import platform  # 添加 platform 模块导入
from collections import deque
from datetime import datetime, timedelta, timezone
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QPushButton, QFrame, QStackedWidget, QScrollArea, 
    QSplashScreen, QSystemTrayIcon, QMenu, QFileDialog, QLineEdit,
    QGraphicsOpacityEffect, QGraphicsBlurEffect, QGraphicsDropShadowEffect,
    QComboBox, QCheckBox, QSizePolicy, QMessageBox, QSpacerItem, QSlider,
    QTabWidget, QToolButton, QProgressBar, Q<PERSON>ext<PERSON>rowser, QGridLayout,
    QTextEdit, QDialog, QComboBox, QGraphicsDropShadowEffect, QSizePolicy,
    QSpacerItem, QMessageBox, QLineEdit, QGridLayout, QPlainTextEdit,
    QTextEdit, QCheckBox, QStackedWidget, QFileDialog, QSplitter,
    QSystemTrayIcon, QStyle, QListWidget, QListWidgetItem, QButtonGroup,
    QRadioButton, QGroupBox, QTableWidget, QTableWidgetItem, QHeaderView
)
from PySide6.QtCore import (
    Qt, QSize, QTimer, QPropertyAnimation, QEasingCurve, QThread, Signal,
    QPoint, QCoreApplication, QAbstractAnimation, QSequentialAnimationGroup, 
    QParallelAnimationGroup, QEvent, QRect, QObject, QThread, Signal,
    QTimer, QSize, QPoint, QEvent, QUrl, QRect, QPropertyAnimation, QEasingCurve,
    QCoreApplication, QDateTime, QDate, QObject, QMetaObject, QFile, QIODevice,
    QTextStream
)
from PySide6.QtGui import (
    QPixmap, QIcon, QColor, QPalette, QDrag, QFontDatabase, QAction, 
    QFontMetrics, QCursor, QScreen, QFont, QTextDocument, QDesktopServices,
    QKeySequence, QShortcut, QTextCursor, QPainter, QTextCharFormat,
    QSyntaxHighlighter, QTextDocument, QIcon  # Added QIcon here if not present
)
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
from threading import Lock, Thread
import psutil
from PySide6.QtGui import QTextDocument
import traceback
import uuid
import ctypes
import warnings # 添加 warnings 模块

# 导入自定义模块
from theme import Theme
from utils import Utils, center_window, cleanup_on_exit, get_app_data_dir
from widgets.toast import ToastQueue
from widgets.dialog import StyledDialog
from widgets.account_widgets import AccountRowWidget
from widgets.clickable_copy_label import ClickableCopyLabel
from account.auth import CursorAuthManager
from account.account_data import AccountData
from account.quota import QuotaFetcher, AccountQuota
from account.account_actions import switch_account_action, delete_account_action
from widgets.styled_widgets import CustomTitleBar, StyledFrame, StyledProgressBar
from widgets.animated_widgets import AnimatedStackedWidget, AnimatedNumberLabel, AnimatedProgressBar
from logger import logger, info, warning, error, critical, debug
from core.quota_fetcher_manager import QuotaFetcherManager
from core.quota_manager import QuotaManager
from ui.pages.home_page import HomePage
from ui.pages.accounts_page import AccountsPage
from ui.pages.feature_page import FeaturePage
from ui.pages.log_page import LogPage
from ui.pages.settings_page import SettingsPage
from ui.dialogs.account_details_dialog import AccountDetailsDialog

# 新增导入
from ui.widget_helpers import create_copy_card, copy_text_to_clipboard, reset_copy_feedback
from ui.dialogs.delete_type_dialog import DeleteTypeDialog
from ui.dialogs.delete_quota_dialog import DeleteQuotaDialog

# Import the BatchDeleteDialog class
from ui.dialogs.batch_delete_dialog import BatchDeleteDialog
from ui.dialogs.import_accounts_dialog import ImportAccountsDialog

# Import VersionChecker
from version_checker import VersionChecker, check_version

# 新增导入
from widgets.pages import AdvancedBackupPage


# 主应用窗口
class CursorAccountManager(QMainWindow):
    """YCursor主窗口类"""
    
    # 定义信号
    refresh_data_changed_signal = Signal(str)  # 使用字符串参数传递邮箱
    temp_file_changed_signal = Signal(str)  # 使用字符串参数传递邮箱
    api_data_fetched_signal = Signal(str)  # 使用字符串参数传递邮箱
    
    def __init__(self, version_checker_instance):
        """初始化主窗口"""
        super().__init__()
        
        # Initialize VersionChecker using the passed instance
        self.version_checker = version_checker_instance
        self.version_checker.setParent(self) # Set parent for memory management
        self.app_version = self.version_checker.current_version # Store app version

        # 初始化数据管理器
        self.auth_manager = CursorAuthManager()
        self.account_data = AccountData()
        
        # 记录账户配置文件路径
        accounts_file = self.account_data.accounts_file
        # 移除检查文件是否存在并清空账户数据的逻辑
        # 因为保存功能现在会自动创建文件
        
        # 清理旧的临时文件
        self._cleanup_old_temp_files()
        
        # 当前账户信息
        self.current_email = self.auth_manager.get_current_email()
        self.account_rows = {}  # 邮箱 -> 行组件的映射
        self.account_quotas = {}  # 存储所有账户的额度数据，邮箱 -> 额度数据
        self.toast_queue = ToastQueue(self)  # 创建消息队列管理器
        self.entrance_anim = None  # 入场动画
        
        # 添加页面状态跟踪变量
        self.is_first_accounts_page_load = True  # 标记是否是首次加载账户管理页面
        
        # 初始化UI更新线程
        from ui_update_thread import UIUpdateThread
        self.ui_update_thread = UIUpdateThread()
        self.ui_update_thread.update_signal.connect(self._on_ui_update_signal)
        # 用于标记是否刚刚保存了账户
        self._last_saved_account = False
        
        # Initialize VersionChecker instance - REMOVED
        # self.version_checker = VersionChecker(self)
        # self.app_version = self.version_checker.current_version # Store app version
        
        # 初始化动画管理器
        from animation_manager import AnimationManager
        self.animation_manager = AnimationManager(self)
        self.ui_update_thread.animation_signal.connect(self.animation_manager.handle_animation_request)
        
        # 启动UI更新线程
        self.ui_update_thread.start()
        
        # 初始化日志页面管理器
        from log_page_manager import LogPageManager
        self.log_manager = LogPageManager(self, self.ui_update_thread)
        
        # 连接信号到槽
        self.refresh_data_changed_signal.connect(self._on_refresh_data_changed)
        self.temp_file_changed_signal.connect(self._on_temp_file_changed)
        self.api_data_fetched_signal.connect(self._on_api_data_fetched)
        
        # 设置窗口属性
        self.setWindowTitle("YCursor")
        self.setMinimumSize(1100, 780)  # 设置最小尺寸
        
        # 去掉默认窗口边框
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 初始化UI
        self.init_ui()
        
        # 运行入场动画
        self.run_entrance_animation()
        
        # 注意：不在构造函数中加载数据
        # 数据加载会在入场动画结束后进行
        
        # 创建自动刷新定时器 - 每30秒自动刷新一次当前账户额度
        self.quota_refresh_timer = QTimer(self)
        self.quota_refresh_timer.timeout.connect(self._auto_refresh_current_quota)

        # 创建认证定时器监控定时器 - 每分钟检查一次认证定时器状态
        self.auth_timer_monitor = QTimer(self)
        self.auth_timer_monitor.timeout.connect(self._monitor_auth_timer)
        self.auth_timer_monitor.start(60000)  # 每60秒检查一次
        # 读取settings.json的首页自动刷新时间
        import json
        import os
        from utils import get_app_data_dir
        settings_file = os.path.join(get_app_data_dir(), "settings.json")
        interval_sec = 30
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                val = int(settings.get("home_auto_refresh_interval", 30))
                if 5 <= val <= 600:
                    interval_sec = val
        except Exception as e:
            print(f"读取首页自动刷新时间配置异常: {e}")
        self.quota_refresh_timer.setInterval(interval_sec * 1000)  # 单位毫秒
        
        # 初始化智能线程管理器
        self.quota_manager = QuotaFetcherManager()
        # 创建配额管理器
        self.quota_mgr = QuotaManager(self)
        
        # 添加动画锁定标志
        self.animation_in_progress = False
        self.queued_updates = []  # 存储动画执行期间的更新请求
        
        self.page_history = []  # 页面历史堆栈
        self.page_forward_stack = []  # 前进堆栈
        
        # 添加首页初始化状态标志
        self.is_home_initialized = False
    
    def _cleanup_old_temp_files(self):
        """清理旧的临时文件"""
        try:
            # 获取应用数据目录中的临时文件目录
            app_data_dir = get_app_data_dir()
            temp_dir = os.path.join(app_data_dir, "temp_quota_data")
            
            # 如果目录不存在，直接返回
            if not os.path.exists(temp_dir):
                return
            
            # 查找所有以temp_开头的JSON文件
            for file in os.listdir(temp_dir):
                if file.startswith("temp_") and file.endswith(".json"):
                    try:
                        temp_file_path = os.path.join(temp_dir, file)
                        # 检查是否是当前使用的临时文件
                        if temp_file_path != self.account_data.temp_file:
                            # 删除过期的临时文件
                            os.remove(temp_file_path)
                            print(f"已删除旧的临时文件: {temp_file_path}")
                    except Exception as e:
                        print(f"删除临时文件出错: {str(e)}")
        except Exception as e:
            print(f"清理临时文件时出错: {str(e)}")
    
    def kill_cursor_process(self):
        """
        杀死Cursor进程
        返回：是否成功杀死Cursor进程的布尔值
        """
        try:
            cursor_killed = False
            
            # Windows系统
            if sys.platform == "win32":
                # 使用tasklist查找进程，仅匹配确切的进程名Cursor.exe
                find_cmd = 'tasklist /FI "IMAGENAME eq Cursor.exe" /FO CSV /NH'
                result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
                
                if "Cursor.exe" in result.stdout:
                    # 使用taskkill杀死进程
                    kill_cmd = 'taskkill /F /IM Cursor.exe'
                    subprocess.run(kill_cmd, shell=True, capture_output=True)
                    cursor_killed = True
                    print("Windows: 已结束Cursor进程")
            
            # macOS系统
            elif sys.platform == "darwin":
                # 查找进程ID，确保只找 /Applications/Cursor.app 的进程
                find_cmd = 'pgrep -f "^/Applications/Cursor.app/Contents/MacOS/Cursor"'
                result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
                
                if result.stdout.strip():
                    # 杀死进程
                    kill_cmd = f'kill -9 {result.stdout.strip()}'
                    subprocess.run(kill_cmd, shell=True)
                    cursor_killed = True
                    print("macOS: 已结束Cursor进程")
            
            # Linux系统
            elif sys.platform == "linux":
                # 查找进程ID，使用更精确的匹配
                find_cmd = 'pgrep -f "^/usr/bin/cursor|^/usr/local/bin/cursor|^/opt/cursor/cursor"'
                result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
                
                if result.stdout.strip():
                    # 杀死进程
                    kill_cmd = f'kill -9 {result.stdout.strip()}'
                    subprocess.run(kill_cmd, shell=True)
                    cursor_killed = True
                    print("Linux: 已结束Cursor进程")
            
            return cursor_killed
        except Exception as e:
            print(f"结束Cursor进程时出错: {str(e)}")
            return False
            
    def start_cursor_app(self):
        """
        启动Cursor应用程序，确保其作为独立进程运行且不显示终端窗口
        """
        try:
            # Windows系统
            if sys.platform == "win32":
                cursor_path = os.path.join(os.getenv("LOCALAPPDATA"), "Programs", "cursor", "Cursor.exe")
                if os.path.exists(cursor_path):
                    # 使用Windows内置的start命令启动程序，不显示命令提示符
                    # /B 参数表示不创建新的控制台窗口
                    # start命令会创建一个完全独立的进程
                    subprocess.run('start /B "" "' + cursor_path + '"', shell=True)
                    print("Windows: 已启动Cursor应用")
                    return True
            
            # macOS系统
            elif sys.platform == "darwin":
                # 使用open命令启动，-n参数强制创建新实例
                # -g参数使应用在后台启动而不获取焦点
                subprocess.run(["open", "-n", "-g", "-a", "Cursor"], shell=False)
                print("macOS: 已启动Cursor应用")
                return True
            
            # Linux系统
            elif sys.platform == "linux":
                # 尝试多个可能的路径
                for cursor_path in ["/usr/bin/cursor", "/usr/local/bin/cursor", "/opt/cursor/cursor"]:
                    if os.path.exists(cursor_path):
                        # 使用nohup启动，并在后台运行，禁止输出
                        subprocess.run(
                            f"nohup {cursor_path} >/dev/null 2>&1 & disown",
                            shell=True,
                            start_new_session=True
                        )
                        print("Linux: 已启动Cursor应用")
                        return True
            
            # 如果没有找到可执行文件
            self.show_toast("无法找到Cursor应用程序", error=True)
            return False
        except Exception as e:
            print(f"启动Cursor应用程序时出错: {str(e)}")
            self.show_toast(f"启动Cursor应用程序时出错: {str(e)}", error=True)
            return False
    
    def reload_progress_animations(self):
        """当页面切换完成后重载进度条动画"""
        try:
            # 再次确保asyncio事件循环策略正确设置
            from utils import setup_asyncio_event_loop_policy
            setup_asyncio_event_loop_policy()
            
            # 获取当前页面
            current_widget = self.content_stack.currentWidget()
            if not current_widget:
                return
                
            # 如果当前是日志页面，设置日志页面为可见，并处理特殊逻辑
            if self.content_stack.currentIndex() == 4 and hasattr(self, 'log_manager'):  # 日志页面
                # 设置滚动条事件处理
                self.log_manager.setup_scroll_bar_events()
                
                # 跟踪是否首次加载日志页面
                is_first_log_page_load = getattr(self, 'is_first_log_page_load', True)
                
                # 使用LogPageManager刷新日志
                if is_first_log_page_load:
                    # 首次加载时，在日志刷新后强制滚动到底部
                    QTimer.singleShot(300, lambda: self.log_manager.refresh_log_with_force_scroll())
                    # 设置标记，下次不再是首次加载
                    self.is_first_log_page_load = False
                else:
                    # 非首次加载时，依据当前的滚动位置决定是否自动滚动
                    QTimer.singleShot(300, self.log_manager.refresh_log)
            
            # 如果当前是账户页面，确保配额管理器正确初始化
            elif self.content_stack.currentIndex() == 1:  # 账户管理页面
                # 如果已经有账户数据，刷新账户状态
                # --- 移除此处的自动刷新逻辑 --- \
                # if hasattr(self, 'account_data') and self.account_data.accounts:
                #     QTimer.singleShot(300, lambda: self.fetch_all_accounts_quota(show_toast=False))
                pass # 保留空块以防未来需要添加逻辑
                    
            # 恢复当前页面中的所有动画
            self._restore_animations(current_widget)
            
        except Exception as e:
            print(f"重载动画时出错: {str(e)}")
    
    # 其他方法省略（这里需要将CursorAccountManager类的其他方法添加进来）
    # ...

    def init_ui(self):
        """初始化用户界面"""
        # 创建主窗口背景
        self.main_bg = QWidget(self)
        self.main_bg.setObjectName("mainBackground")
        self.main_bg.setStyleSheet(f"""
            QWidget#mainBackground {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                          stop:0 {Theme.GRADIENT_START}, 
                                          stop:1 {Theme.GRADIENT_END});
                border-radius: {Theme.BORDER_RADIUS};
                border: 1px solid {Theme.BORDER};
            }}
        """)
        
        # 初始时将背景设为半透明，用于淡入动画
        self.main_bg_effect = QGraphicsOpacityEffect(self.main_bg)
        self.main_bg_effect.setOpacity(0.5)
        self.main_bg.setGraphicsEffect(self.main_bg_effect)
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self.main_bg)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 添加自定义标题栏
        self.title_bar = CustomTitleBar(self)
        self.main_layout.addWidget(self.title_bar)
        
        # 创建内容区域
        self.content_widget = QWidget()
        self.content_widget.setObjectName("contentWidget")
        self.content_widget.setStyleSheet("""
            QWidget#contentWidget {
                background: transparent;
            }
        """)
        
        self.content_layout = QHBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(20, 10, 20, 20)
        self.content_layout.setSpacing(20)
        
        # 创建左侧菜单
        self.sidebar = self.create_sidebar()
        self.content_layout.addWidget(self.sidebar)
        
        # 创建内容区域
        self.content_stack = AnimatedStackedWidget()
        self.content_stack.setObjectName("contentStack")
        self.content_stack.setStyleSheet("""
            QStackedWidget#contentStack {
                background: transparent;
            }
        """)
        
        # 设置动画参数
        self.content_stack.setDirection(Qt.Orientation.Horizontal)  # 水平方向滑动
        self.content_stack.setSpeed(350)  # 动画持续时间，毫秒
        self.content_stack.setAnimation(QEasingCurve.Type.OutCubic)  # 动画曲线类型
        
        # 创建主页
        self.home_page = self.create_home_page()
        self.home_page.setWindowTitle("首页") # 添加标题
        self.content_stack.addWidget(self.home_page)
        
        # 创建账户管理页面
        self.accounts_page = self.create_accounts_page()
        self.accounts_page.setWindowTitle("账户管理") # 添加标题
        self.content_stack.addWidget(self.accounts_page)
        
        # 创建功能页面
        self.feature_page = self.create_feature_page()
        self.feature_page.setWindowTitle("功能") # 添加标题
        self.content_stack.addWidget(self.feature_page)
        
        # 创建设置页面
        self.settings_page = self.create_settings_page()
        self.settings_page.setWindowTitle("设置") # 添加标题
        self.content_stack.addWidget(self.settings_page)
        
        # 创建日志页面
        self.log_page = self.create_log_page()
        self.log_page.setWindowTitle("日志") # 添加标题
        self.content_stack.addWidget(self.log_page)
        
        # 创建关于页面
        self.about_page = self.create_about_page()
        self.about_page.setWindowTitle("关于") # 添加标题
        self.content_stack.addWidget(self.about_page)

        # ====== 注册高级自定义备份/恢复页面 ======
        self.advanced_backup_page = AdvancedBackupPage()
        self.advanced_backup_page.setWindowTitle("高级自定义备份/恢复") # 新增：设置页面标题
        self.advanced_backup_page.back_requested.connect(self._on_advanced_backup_back)
        self.content_stack.addWidget(self.advanced_backup_page)
        self.ADVANCED_BACKUP_PAGE_INDEX = self.content_stack.count() - 1
        
        self.content_layout.addWidget(self.content_stack)
        
        # 添加内容区域到主布局
        self.main_layout.addWidget(self.content_widget)
        
        # 连接页面切换动画完成信号，在切换后重新加载进度条动画
        self.content_stack.animationFinished.connect(self.reload_progress_animations)
        
        # 设置主窗口布局
        self.setCentralWidget(self.main_bg)

    def show_toast(self, message, error=False, copy_error=False):
        """显示Toast提示
        
        Args:
            message: 消息文本
            error: 是否是错误消息，默认False
            copy_error: 是否复制错误信息到剪贴板，默认False
        """
        # 只记录错误信息
        if error:
            critical(f"错误: {message}")
            
        # 直接使用消息队列添加消息
        self.toast_queue.add_message(message, duration=2000, error=error)
        
        # 如果是错误消息且需要复制
        if error and copy_error:
            # 收集错误信息
            error_info = f"\n=== YCursor 错误信息 ===\n"
            error_info += f"错误消息: {message}\n"
            error_info += f"操作系统: {platform.system()} {platform.version()}\n"
            error_info += f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            # 如果有原始错误信息，添加到复制内容中
            if hasattr(self, '_last_error_detail') and self._last_error_detail:
                error_info += f"\n原始错误:\n{self._last_error_detail}\n"
            
            # 复制到剪贴板
            self._copy_error_to_clipboard(error_info)

    def run_entrance_animation(self):
        """运行入场动画"""
        # 获取窗口原始尺寸和位置
        screen_geometry = QApplication.primaryScreen().availableGeometry()
        window_geometry = self.geometry()
        
        # 设置初始位置和尺寸（从中心点开始）
        center_x = screen_geometry.width() // 2
        center_y = screen_geometry.height() // 2
        initial_width = window_geometry.width() // 2
        initial_height = window_geometry.height() // 2
        
        initial_geometry = QRect(
            center_x - initial_width // 2,
            center_y - initial_height // 2,
            initial_width,
            initial_height
        )
        
        # 设置目标位置和尺寸
        target_geometry = window_geometry
        
        # 动画时长和曲线优化
        animation_duration = 300  # 更短的动画时间，避免卡顿感
        
        # 阻止窗口更新以避免动画中重绘引起的闪烁
        self.setUpdatesEnabled(False)
        
        # 创建窗口几何动画
        geo_anim = QPropertyAnimation(self, b"geometry")
        geo_anim.setDuration(animation_duration)
        geo_anim.setStartValue(initial_geometry)
        geo_anim.setEndValue(target_geometry)
        geo_anim.setEasingCurve(QEasingCurve.Type.OutQuint)  # 使用更平滑的曲线
        
        # 创建窗口不透明度动画
        opacity_anim = QPropertyAnimation(self, b"windowOpacity")
        opacity_anim.setDuration(animation_duration)
        opacity_anim.setStartValue(0.2)  # 从0.2开始，避免完全透明
        opacity_anim.setEndValue(1.0)
        opacity_anim.setEasingCurve(QEasingCurve.Type.OutQuint)
        
        # 创建背景淡入动画
        bg_opacity_anim = QPropertyAnimation(self.main_bg_effect, b"opacity")
        bg_opacity_anim.setDuration(animation_duration + 100)  # 稍长一点，让背景淡入更平滑
        bg_opacity_anim.setStartValue(0.5)
        bg_opacity_anim.setEndValue(1.0)
        bg_opacity_anim.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 创建动画组
        self.entrance_anim = QParallelAnimationGroup()
        self.entrance_anim.addAnimation(geo_anim)
        self.entrance_anim.addAnimation(opacity_anim)
        self.entrance_anim.addAnimation(bg_opacity_anim)
        
        # 连接动画完成信号，在动画结束后执行其他操作
        self.entrance_anim.finished.connect(self._on_entrance_animation_finished)
        
        # 延迟一点点再启动动画，确保窗口已完全渲染
        QTimer.singleShot(20, self._start_entrance_animation)
    
    def _start_entrance_animation(self):
        """实际启动入场动画"""
        try:
            # 在动画前确保所有部件已完全创建和布局
            self.update()
            QApplication.processEvents()
            
            # 一次性设置所有属性，减少可能的闪烁
            self.setUpdatesEnabled(True)
            
            # 启动动画
            self.entrance_anim.start()
        except Exception as e:
            print(f"启动入场动画时出错: {str(e)}")
            # 出现错误时尝试恢复正常状态
            self.setWindowOpacity(1.0)
            self.main_bg.setGraphicsEffect(None)
            self.setUpdatesEnabled(True)
    
    def _on_entrance_animation_finished(self):
        """入场动画完成后的回调"""
        # 清除所有特效，确保后续渲染正常
        self.main_bg.setGraphicsEffect(None)
        
        # 在动画完成后加载数据，避免动画过程中的干扰
        self.load_data()
        
        # 启动定时刷新的定时器
        self.quota_refresh_timer.start()
        print("自动刷新: 定时器已启动，每6秒自动刷新一次当前登录账户额度")

        # 启动认证时效检查定时器（全局运行，不受窗口状态影响）
        try:
            self.version_checker.start_auth_check_timer()
        except Exception as e:
            print(f"启动认证时效检查定时器时出错: {e}")
    
    def load_data(self):
        """加载数据
        
        在应用程序启动后，该方法负责加载所有需要的数据并初始化UI
        """
        # 使用定时器延迟加载，避免与UI初始化冲突
        QTimer.singleShot(100, self._delayed_data_loading)
    
    def _delayed_data_loading(self):
        """延迟加载数据，避免与UI初始化冲突"""
        try:
            # 加载当前账户信息
            self.load_current_account()
            
            # 加载所有账户列表
            self.load_accounts()
            
            # 标记首页已初始化
            self.is_home_initialized = True
            
        except Exception as e:
            error(f"加载数据时出错: {str(e)}")
            self.show_toast(f"加载数据时出错: {str(e)}", error=True)
    
    def _fetch_other_accounts_quota(self):
        """获取除当前账户外的所有其他账户额度
        
        由于当前账户的额度已经被优先获取，这个方法只负责获取其他账户的额度数据
        """
        # 调用配额管理器的方法
        self.quota_mgr._fetch_other_accounts_quota()
    
    def fetch_all_accounts_quota(self, is_manual_refresh=False, show_toast=True):
        """获取所有账户的额度信息"""
        # 调用配额管理器的方法
        self.quota_mgr.fetch_all_accounts_quota(is_manual_refresh, show_toast)
    
    def update_fetch_progress(self, current, total):
        """更新获取进度"""
        # 调用配额管理器的方法
        self.quota_mgr.update_fetch_progress(current, total)
    
    def on_all_quotas_fetched(self, is_manual_refresh=False, show_toast=True):
        """所有账户额度获取完成"""
        # 调用配额管理器的方法
        self.quota_mgr.on_all_quotas_fetched(is_manual_refresh, show_toast)
    
    def _auto_refresh_current_quota(self):
        """定时自动刷新当前账户额度数据"""
        # 调用配额管理器的方法
        self.quota_mgr._auto_refresh_current_quota()

    def _monitor_auth_timer(self):
        """监控认证时效检查定时器状态，确保其始终运行"""
        try:
            if hasattr(self, 'version_checker') and self.version_checker:
                # 如果已经显示过认证过期对话框，则停止监控
                if hasattr(self.version_checker, 'auth_expiry_dialog_shown') and self.version_checker.auth_expiry_dialog_shown:
                    return

                if not self.version_checker.is_auth_check_timer_running():
                    self.version_checker.restart_auth_check_timer_if_stopped()
        except Exception:
            pass
    
    def _background_refresh_quota(self, email_changed=False):
        """在后台线程中执行刷新操作"""
        # 调用配额管理器的方法
        self.quota_mgr._background_refresh_quota(email_changed)
    
    def _fetch_current_account_quota(self, silent=False):
        """单独获取当前账户额度数据，优先更新首页显示"""
        # 调用配额管理器的方法
        return self.quota_mgr._fetch_current_account_quota(silent)
    
    def _fetch_and_update_ui_on_main_thread(self):
        """在后台线程获取数据并更新UI"""
        # 调用配额管理器的方法
        self.quota_mgr._fetch_and_update_ui_on_main_thread()
    
    def _on_refresh_data_changed(self, email):
        """处理刷新数据变化信号，在主线程中更新UI"""
        # 使用统一的方法检查是否应该取消更新
        if self._should_cancel_update(email, "刷新更新"):
            return
        
        # 立即更新首页UI
        self._process_update(email)
        # 恢复动画效果
        self._restore_home_page_animations(force=True)
    
    def _compare_quota_data(self, old_quota, new_quota):
        """比较两个额度数据是否有关键字段的差异
        
        Returns:
            bool: 如果有差异返回True，否则返回False
        """
        from utils import compare_quota_data
        return compare_quota_data(old_quota, new_quota)

    def _check_temp_file_changes(self):
        """检查临时文件变化并更新UI"""
        try:
            # 重新加载临时文件中的账户数据
            temp_accounts = []
            temp_file = self.account_data.temp_file
            
            if not os.path.exists(temp_file):
                return False
            
            # 如果在主UI线程中执行，则先返回False，后台任务会处理
            if QThread.currentThread() is QApplication.instance().thread():
                # 创建线程执行文件读取操作
                thread = threading.Thread(target=self._background_check_temp_file)
                thread.daemon = True
                thread.start()
                return False
                
            with open(temp_file, 'r', encoding='utf-8') as f:
                temp_accounts = json.load(f)
                
            if not temp_accounts:
                return False
                
            # 查找当前账户的数据
            updated = False
            for temp_acc in temp_accounts:
                if temp_acc.get("email") == self.current_email:
                    # 获取额度数据
                    quota_data = temp_acc.get("quota_data")
                    if not quota_data:
                        continue
                        
                    # 检查额度数据是否有变化
                    old_quota = self.account_quotas.get(self.current_email, {})
                    
                    # 比较关键字段
                    if (self._compare_quota_data(old_quota, quota_data)):
                        # 更新内存中的数据
                        self.account_quotas[self.current_email] = quota_data
                        updated = True
                        print(f"从临时文件更新了账户 {self.current_email} 的额度数据")
                    break
                    
            if updated:
                # 使用信号通知主线程文件已变化
                self.temp_file_changed_signal.emit(self.current_email)
                
            return updated
        except Exception as e:
            print(f"检查临时文件变化时出错: {str(e)}")
            return False
            
    def _on_temp_file_changed(self, email):
        """处理临时文件变化信号，在主线程中更新UI"""
        # 使用统一的方法检查是否应该取消更新
        if self._should_cancel_update(email, "临时文件更新"):
            return
        
        # 更新UI
        self._safe_update_ui(email)
        # 恢复动画效果
        self._restore_home_page_animations()
    
    def _background_check_temp_file(self):
        """在后台线程中检查临时文件变化"""
        try:
            # 调用检查临时文件的方法（此时在后台线程中执行）
            self._check_temp_file_changes()
        except Exception as e:
            print(f"后台检查临时文件时出错: {str(e)}")
    
    def _on_api_data_fetched(self, email):
        """处理API数据获取完成信号，在主线程中更新UI"""
        # 更新UI
        self._safe_update_ui(email)
        # 恢复动画效果
        self._restore_home_page_animations()

    def create_sidebar(self):
        """创建左侧菜单栏"""
        # 创建侧边栏容器
        sidebar = StyledFrame(has_glass_effect=True)
        sidebar.setFixedWidth(240)
        sidebar.setObjectName("sidebar")
        
        # 应用玻璃效果样式到侧边栏
        sidebar.setStyleSheet(f"""
            #sidebar {{
                background-color: {Theme.GLASS_BG};
                border-radius: {Theme.BORDER_RADIUS};
                border: 1px solid {Theme.GLASS_BORDER};
            }}
        """)
        
        # 创建侧边栏布局
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(10, 25, 10, 20)
        layout.setSpacing(15)
        
        # 添加标题
        title_layout = QHBoxLayout()
        
        title_label = QLabel("YCursor")
        title_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_TITLE};
            font-weight: bold;
            color: {Theme.ACCENT};
            padding: 0px 10px 0px 10px;
            background-color: transparent;
        """)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        layout.addSpacing(30)
        
        # 创建首页按钮
        self.home_btn = QPushButton("   首页")
        self.home_btn.setCheckable(True)
        self.home_btn.setChecked(True)
        self.home_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.home_btn.clicked.connect(lambda: self.switch_page(0))
        
        # 创建账号管理按钮
        self.accounts_btn = QPushButton("   账户")
        self.accounts_btn.setCheckable(True)
        self.accounts_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.accounts_btn.clicked.connect(lambda: self.switch_page(1))
        
        # 创建功能页面按钮
        self.feature_btn = QPushButton("   功能")
        self.feature_btn.setCheckable(True)
        self.feature_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.feature_btn.clicked.connect(lambda: self.switch_page(2))  # 索引为2，对应第三个页面

        # 创建设置页面按钮
        self.settings_btn = QPushButton("   设置")
        self.settings_btn.setCheckable(True)
        self.settings_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.settings_btn.clicked.connect(lambda: self.switch_page(3))  # 索引为3，对应第四个页面

        # 创建关于页面按钮
        self.about_btn = QPushButton("   关于")
        self.about_btn.setCheckable(True)
        self.about_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.about_btn.clicked.connect(lambda: self.switch_page(5))  # 索引为5，对应第六个页面

        # 创建日志页面按钮
        self.log_btn = QPushButton("   日志")
        self.log_btn.setCheckable(True)
        self.log_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.log_btn.clicked.connect(lambda: self.switch_page(4))  # 索引为4，对应第五个页面
        
        # 设置按钮样式
        button_style = f"""
            QPushButton {{
                text-align: left;
                padding: 12px 20px;
                font-size: 16px;
                border: none;
                background-color: transparent;
                color: {Theme.TEXT_SECONDARY};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            QPushButton:hover {{
                background-color: {Theme.SECONDARY};
                color: {Theme.TEXT_PRIMARY};
            }}
            QPushButton:checked {{
                background-color: {Theme.ACCENT};
                color: {Theme.TEXT_PRIMARY};
                font-weight: bold;
            }}
        """
        
        # 为首页按钮添加特殊样式，确保无下划线
        self.home_btn.setStyleSheet(button_style + """
            QPushButton {
                border-bottom: none !important;
                text-decoration: none !important;
                outline: none !important;
            }
            QPushButton:checked {
                border-bottom: none !important;
                text-decoration: none !important;
                outline: none !important;
            }
            QPushButton:focus {
                border-bottom: none !important;
                text-decoration: none !important;
                outline: none !important;
            }
        """)
        
        # 为账号管理按钮使用基础样式
        self.accounts_btn.setStyleSheet(button_style)
        # 为功能按钮使用基础样式
        self.feature_btn.setStyleSheet(button_style)
        # 为设置按钮使用基础样式
        self.settings_btn.setStyleSheet(button_style)
        # 为关于按钮使用基础样式
        self.about_btn.setStyleSheet(button_style)
        # 为日志按钮使用基础样式
        self.log_btn.setStyleSheet(button_style)
        
        # 添加按钮到布局
        layout.addWidget(self.home_btn)
        layout.addWidget(self.accounts_btn)
        layout.addWidget(self.feature_btn)
        layout.addWidget(self.settings_btn)
        layout.addWidget(self.about_btn)
        layout.addWidget(self.log_btn)
        layout.addStretch()
        
        # 去掉多余的footer_frame，改为直接添加到主布局
        # 使用HBox布局直接添加到侧边栏底部
        version_container = QWidget()
        version_container.setStyleSheet("background-color: transparent;")
        version_layout = QHBoxLayout(version_container)
        version_layout.setContentsMargins(15, 8, 15, 8)
        version_layout.setSpacing(5)
        
        # 检查是否有管理员权限
        import ctypes
        is_admin = False
        try:
            if sys.platform == "win32":
                is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:  # macOS 和 Linux
                is_admin = os.geteuid() == 0
        except:
            is_admin = False
            
        # 获取版本信息
        current_version = Utils.get_cursor_version()
        from version_checker import VersionChecker
        checker = VersionChecker()
        # 将获取到的 YCursor 版本号赋值给 self.app_version
        self.app_version = checker.current_version
        
        # 创建权限状态指示器 - 使用小圆点而不是标签
        admin_status_color = Theme.SUCCESS if is_admin else Theme.ERROR
        
        # 创建彩色圆点指示器
        dot_size = 8
        status_dot = QLabel()
        status_dot.setFixedSize(dot_size, dot_size)
        status_dot.setStyleSheet(f"""
            background-color: {admin_status_color};
            border-radius: {dot_size/2}px;
        """)
        
        # 创建版本信息文本 - 使用单个标签节省空间
        version_text = f"Cursor {current_version} | YCursor {self.app_version}" # 使用 self.app_version
        # 将版本标签赋值给 self.cursor_version_label
        self.cursor_version_label = QLabel(version_text)
        self.cursor_version_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: {Theme.FONT_SIZE_SMALL};
        """)
        
        # 添加到布局
        version_layout.addWidget(status_dot)
        version_layout.addWidget(self.cursor_version_label) # 使用 self.cursor_version_label
        version_layout.addStretch()
        
        # Directly update the label with the already fetched app version
        # self._update_cursor_version_label(f"Cursor {current_version} | YCursor {self.app_version}") # Old incorrect call
        self._update_cursor_version_label(current_version) # Pass only the cursor version
        
        # 直接添加到sidebar布局
        layout.addStretch()  # 添加弹性空间，使版本信息始终位于底部
        layout.addWidget(version_container)
        
        return sidebar

    def create_home_page(self):
        """创建主页"""
        # 使用新的HomePage类
        return HomePage(self)

    def create_accounts_page(self):
        """创建账户管理页面"""
        # 使用新的AccountsPage类
        return AccountsPage(self)

    def create_feature_page(self):
        """创建功能页面"""
        # 使用新的FeaturePage类
        return FeaturePage(self)

    def create_settings_page(self):
        """创建设置页面"""
        from ui.pages.settings_page import SettingsPage
        
        settings_page = SettingsPage()
        
        # 连接设置保存信号
        settings_page.settings_saved.connect(self._on_settings_saved)
        
        return settings_page
    
    def _on_settings_saved(self, settings):
        """当设置保存时的回调"""
        from logger import info
        
        # 设置项的显示名称和值格式化映射
        settings_display = {
            "auto_hide_save_button": {
                "name": "自动隐藏保存账户按钮",
                "format": lambda value: "开启" if value else "关闭"
            },
            "use_custom_json_file": {
                "name": "自定义账户数据文件",
                "format": lambda value: "启用" if value else "关闭"
            },
            # 添加新设置的显示映射
            "auto_hide_custom_path": {
                "name": "自动隐藏自定义 Cursor 路径配置",
                "format": lambda value: "开启" if value else "关闭"
            },
            "auto_close_dialog_on_success": {
                "name": "操作成功时自动关闭对话框",
                "format": lambda value: "开启" if value else "关闭"
            },
            "auto_restart_cursor": {
                "name": "自动启动 Cursor",
                "format": lambda value: "开启" if value else "关闭"
            }
        }
        
        # 生成友好的设置描述信息
        settings_descriptions = []
        
        # 处理所有包含在映射中的设置项
        for key, value in settings.items():
            if key in settings_display:
                display = settings_display[key]
                formatted_value = display["format"](value)
                settings_descriptions.append(f"{display['name']}: {formatted_value}")
        
        # 输出日志
        if settings_descriptions:
            info(f"设置已更新: {', '.join(settings_descriptions)}")
        else:
            # 对于未在映射中的设置，也尝试记录原始值
            unmapped_settings = {k: v for k, v in settings.items() if k not in settings_display}
            if unmapped_settings:
                info(f"设置已更新(部分): {', '.join(settings_descriptions)} | 其他: {json.dumps(unmapped_settings, ensure_ascii=False)}")
            else:
                info(f"设置已更新: {', '.join(settings_descriptions)}")

        # 定义需要刷新功能页面的设置键
        feature_page_refresh_keys = ["auto_hide_save_button", "auto_hide_custom_path"]
        needs_feature_refresh = any(key in settings for key in feature_page_refresh_keys)

        # 处理需要刷新功能页面的设置
        if needs_feature_refresh:
            # 检查功能页面是否存在且当前是否显示
            if hasattr(self, 'content_stack') and hasattr(self, 'feature_page') and self.feature_page:
                 # 检查 feature_page 是否是 FeaturePage 实例
                if isinstance(self.feature_page, QWidget): # 基础检查
                    # 检查功能页面是否包含 functionality_page 实例
                    if hasattr(self.feature_page, 'main_window') and hasattr(self.feature_page.main_window, 'functionality_page'):
                        if self.content_stack.currentWidget() == self.feature_page:
                            self.feature_page.main_window.functionality_page.refresh()
                            info("功能页面相关设置已更新，已刷新功能页面")
                    elif hasattr(self, 'functionality_page'): # 兼容旧版直接引用
                         if self.content_stack.currentWidget() == self.feature_page:
                            self.functionality_page.refresh()
                            info("功能页面相关设置已更新，已刷新功能页面 (旧版引用)")
                else:
                    info("feature_page 实例类型不正确，无法刷新")
            else:
                 info("无法找到功能页面或堆栈，设置将在下次访问时生效")

        # 处理自定义账户数据文件路径设置
        if "use_custom_json_file" in settings or "custom_json_file_path" in settings:
            # 记录路径变更
            if "use_custom_json_file" in settings and "custom_json_file_path" in settings:
                path_info = settings["custom_json_file_path"] if settings["use_custom_json_file"] else "默认路径"
                info(f"账户数据文件路径已更改为: {path_info}")
            elif "use_custom_json_file" in settings:
                mode = "自定义路径" if settings["use_custom_json_file"] else "默认路径"
                info(f"账户数据文件模式已更改为: {mode}")
            elif "custom_json_file_path" in settings and settings["custom_json_file_path"]:
                info(f"自定义账户数据文件路径已更改为: {settings['custom_json_file_path']}")
            
            # 不立即重新初始化AccountData对象，而是设置一个标志
            # 表示需要在下次使用账户数据时重新加载
            if not hasattr(self, '_reload_account_data'):
                self._reload_account_data = True
            else:
                self._reload_account_data = True
            
            # 不再立即加载账户列表和获取额度信息
            # 仅在用户切换到账户管理页面时才会加载
            info("账户数据路径已更新，账户列表将在下次访问账户管理页面时刷新")
    
    def _handle_function_selected(self, func_id):
        """处理功能选择事件
        
        Args:
            func_id: 功能ID
        """
        try:
            if func_id == "save_current_account":
                self.save_current_account()
            elif func_id == "batch_delete":
                self.show_delete_type_dialog()
            elif func_id == "refresh_quota":
                self.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=True)
            elif func_id == "export_data":
                # 导出功能未实现
                self.show_toast("导出功能尚未实现")
            elif func_id == "import_data":
                # 导入功能未实现
                self.show_toast("导入功能尚未实现")
            elif func_id == "sort_accounts":
                # 排序功能
                self._sort_accounts_and_update_ui()
                self.show_toast("账户已按注册时间排序")
            elif func_id == "backup_restore":
                # 备份恢复功能未实现
                self.show_toast("备份恢复功能尚未实现")
            elif func_id == "system_settings":
                # 系统设置功能未实现
                self.show_toast("系统设置功能尚未实现")
            elif func_id == "about":
                # 关于功能未实现
                self.show_toast("关于功能尚未实现")
            elif func_id == "reset_machine_id":
                # 重置机器码功能由功能页面直接处理
                pass
            elif func_id == "mcp_backup":
                # MCP备份功能由功能页面直接处理
                pass
            else:
                self.show_toast(f"未知功能ID: {func_id}")
        except Exception as e:
            error(f"处理功能选择时出错: {str(e)}")
            self.show_toast(f"执行功能时出错: {str(e)}", error=True)
    
    def create_log_page(self):
        """创建日志页面"""
        # 使用新的LogPage类
        from ui.pages.log_page import LogPage
        return LogPage(self)
    
    def _toggle_log_filter(self, level_name, checked):
        """切换日志筛选器
        
        Args:
            level_name: 日志级别名称
            checked: 是否选中
        """
        # 日志级别名称与日志内容中的标记映射
        level_markers = {
            "信息": "INFO",
            "警告": "WARNING",
            "错误": "ERROR",
            "调试": "DEBUG",
            "严重": "CRITICAL"
        }
        
        # 更新激活的过滤器
        if checked:
            self.active_log_filters.add(level_markers[level_name])
        else:
            self.active_log_filters.discard(level_markers[level_name])
        
        # 使用LogPageManager应用过滤器
        self.log_manager.apply_filters(self.active_log_filters)
    
    def _refresh_log_display(self):
        """刷新日志显示内容"""
        # 使用LogPageManager刷新日志
        self.log_manager.refresh_log()
    
    def _update_after_fetch(self, is_manual_refresh=False, show_toast=True):
        """获取账户额度数据完成后的UI更新处理
        
        Args:
            is_manual_refresh: 是否是手动刷新
            show_toast: 是否显示提示
        """
        try:
            # 从数据库获取最新的当前登录邮箱
            db_email = self.auth_manager.get_current_email()
            
            # 如果邮箱发生变化，更新内存中的值
            if db_email != self.current_email:
                print(f"更新后处理: 检测到邮箱变化，从 {self.current_email} 变为 {db_email}")
                self.current_email = db_email
            
            # 获取当前页面
            current_widget = self.content_stack.currentWidget()
            
            # 账户管理页面特殊处理 - 不检查动画状态，始终执行
            if current_widget == self.accounts_page:
                # 对账户按注册时间进行排序
                self._sort_accounts_without_rebuild_ui()
                
                # 锁定动画状态
                self._lock_animations()
                
                # 检查是否已经显示了账户行
                accounts_visible = False
                for row in self.account_rows.values():
                    if row.isVisible():
                        accounts_visible = True
                        break
                
                # 如果账户已经可见，只更新数据而不重建UI
                if accounts_visible:
                    # 更新已有行的数据
                    from logger import info
                    info("账户管理页面: 检测到账户行已可见，只更新数据而不重建UI")
                    
                    # 更新每一行的数据
                    for email, row in self.account_rows.items():
                        # 确认：这里应该用 row，因为是从 self.account_rows.items() 迭代出来的
                        if row and row.isVisible(): # 确保行组件存在且可见
                            try:
                                # 使用 self.account_quotas.get() 避免 KeyError
                                quota_data = self.account_quotas.get(email)
                                if quota_data:
                                    row.update_quota(quota_data)
                                else:
                                    print(f"更新账户行 {email} 时未找到对应的额度数据")
                            except Exception as e:
                                print(f"更新账户行 {email} 数据时出错: {str(e)}")
                                # 如果更新时出错（可能表示组件状态异常），也尝试移除引用
                                self.account_rows.pop(email, None)
                        # else: # 调试信息
                        #     if row: print(f"调试: 尝试更新账户行 {email}，但行组件不可见")
                        #     else: print(f"调试: 尝试更新账户行 {email}，但行组件引用丢失")
                    
                    # 隐藏加载容器
                    if hasattr(self, 'loading_container'):
                        self.loading_container.setVisible(False)
                    
                    # 解锁动画
                    self._unlock_animations()
                    
                    # 显示提示
                    if show_toast:
                        self.show_toast("所有账户额度信息已更新")
                    return
                
                # 创建加载容器的淡出动画 - 增加安全检查
                loading_container_valid = (hasattr(self, 'loading_container') and 
                                          self.loading_container is not None and 
                                          not self.loading_container.isHidden() and
                                          self.loading_container.isVisible())
                
                if loading_container_valid:
                    try:
                        # 创建透明度动画
                        self.fadeout_anim = QPropertyAnimation(self.loading_container, b"windowOpacity")
                        self.fadeout_anim.setDuration(800)  # 延长动画时间为800毫秒
                        self.fadeout_anim.setStartValue(1.0)
                        self.fadeout_anim.setEndValue(0.0)
                        self.fadeout_anim.setEasingCurve(QEasingCurve.Type.OutCubic)  # 使用更平滑的缓动曲线
                        self.fadeout_anim.finished.connect(self._after_loading_fadeout)
                        
                        # 开始退场动画
                        self.fadeout_anim.start(QPropertyAnimation.DeletionPolicy.DeleteWhenStopped)
                    except Exception as e:
                        print(f"创建淡出动画时出错: {str(e)}")
                        # 出错时直接执行后续步骤
                        self._rebuild_accounts_ui_with_animation(show_toast)
                else:
                    # 如果加载容器已经隐藏或不可用，直接执行后续步骤
                    self._rebuild_accounts_ui_with_animation(show_toast)
            else:
                # 首先确保对账户按注册时间进行排序
                self._sort_accounts_without_rebuild_ui()
                
                # 对于所有账户，添加UI更新请求到UI更新线程
                for email in self.account_quotas.keys():
                    # 使用空数据字典，因为实际数据已经存储在self.account_quotas中
                    self.ui_update_thread.add_update_request(email, {})
                
                # 显示提示
                if show_toast:
                    self.show_toast("所有账户额度信息已更新")
        except Exception as e:
            print(f"更新数据后更新UI时出错: {str(e)}")
            # 确保解锁动画状态
            if hasattr(self, 'animation_in_progress') and self.animation_in_progress:
                self._unlock_animations()
            # 确保加载指示器隐藏 - 增加安全检查
            try:
                if hasattr(self, 'loading_container') and self.loading_container is not None:
                    self.loading_container.setVisible(False)
            except Exception as inner_e:
                print(f"隐藏加载指示器时出错: {str(inner_e)}")

    def _after_loading_fadeout(self):
        """加载容器淡出动画结束后执行"""
        try:
            # 安全检查，确保组件仍然有效
            if hasattr(self, 'loading_container') and self.loading_container is not None:
                # 隐藏加载容器
                self.loading_container.setVisible(False)
        except Exception as e:
            print(f"隐藏加载容器时出错: {str(e)}")
            
        # 完全重建账户UI，应用动画
        self._rebuild_accounts_ui_with_animation()
    
    def _rebuild_accounts_ui_with_animation(self, show_toast=True):
        """重建账户UI并添加动画效果"""
        try:
            # 锁定动画状态，防止更新中断动画
            self._lock_animations()
            
            # 再次从数据库获取最新的当前登录邮箱，确保UI显示最新状态
            db_email = self.auth_manager.get_current_email()
            
            # 如果邮箱发生变化，更新内存中的值
            if db_email != self.current_email:
                print(f"重建UI: 检测到邮箱变化，从 {self.current_email} 变为 {db_email}")
                self.current_email = db_email
            
            # 清空当前UI，重新构建，并在删除前移除字典引用
            while self.accounts_layout.count():
                item = self.accounts_layout.takeAt(0)
                if item and item.widget(): # 检查 item 和 widget 是否有效
                    widget = item.widget()
                    if widget != self.loading_container: # 不要删除加载容器
                        # 确保先清理任何现有效果
                        if hasattr(widget, '_opacity_effect'):
                            widget.setGraphicsEffect(None)
                            delattr(widget, '_opacity_effect')
                        
                        # 从 account_rows 字典中移除引用
                        if hasattr(widget, 'account_data') and 'email' in widget.account_data:
                            email_to_remove = widget.account_data.get('email')
                            if email_to_remove:
                                removed_widget = self.account_rows.pop(email_to_remove, None)
                                if removed_widget:
                                    print(f"重建UI: 同步移除了账户行引用 {email_to_remove}")
                        
                        # 将实际删除推迟到事件循环
                        widget.deleteLater()
            
            # 确保加载指示器仍在布局中（如果之前被移除则重新添加）
            if not self.accounts_layout.parentWidget().findChild(QWidget, "loadingContainer"): # 假设loading_container有objectName
                 if hasattr(self, 'loading_container'): # 检查是否存在
                      self.accounts_layout.addWidget(self.loading_container)

            # 确保完全清空account_rows字典
            self.account_rows.clear()
            print("重建UI: 清空 account_rows 字典")
            
            # 首先创建所有行但设为不可见
            for account in self.account_data.accounts:
                email = account.get("email", "")
                if not email: # 跳过没有邮箱的账户数据
                    print("重建UI: 跳过没有邮箱的账户数据")
                    continue
                
                is_current = email == self.current_email
                
                # 创建行组件
                row = AccountRowWidget(account, is_current)
                
                # 使用QGraphicsOpacityEffect设置透明度
                opacity_effect = QGraphicsOpacityEffect(row)
                opacity_effect.setOpacity(0.0)  # 初始完全透明
                row.setGraphicsEffect(opacity_effect)
                row._opacity_effect = opacity_effect  # 保存引用以供动画使用
                
                # 确保行组件初始时是不可见的
                row.setVisible(False)
                
                # 如果已经有该账户的额度数据，直接更新
                if email in self.account_quotas:
                    try:
                        row.update_quota(self.account_quotas[email])
                    except Exception as e:
                        print(f"重建UI: 更新账户 {email} 额度时出错: {str(e)}")
                
                # 连接信号
                row.switch_account_signal.connect(self.switch_account)
                row.delete_account_signal.connect(self.delete_account)
                row.show_details_signal.connect(self.show_account_details)  # 连接显示详情信号
                
                # 添加到布局
                self.accounts_layout.addWidget(row)
                self.account_rows[email] = row
            
            # 更新账户计数
            self._update_accounts_count()
            
            # 显示提示，但只在show_toast为True时
            if show_toast:
                self.show_toast("所有账户额度信息已更新")
            
            # 标记首次加载流程完成
            if self.is_first_accounts_page_load:
                print("账户页面首次加载流程开始，设置 is_first_accounts_page_load = False")
                self.is_first_accounts_page_load = False

            # 立即处理事件，确保UI已更新
            QApplication.processEvents()

            # 使所有行可见，但透明度仍为0
            for row in self.account_rows.values():
                row.setVisible(True)

            # 增加延迟，确保UI有时间刷新，再启动动画
            QTimer.singleShot(200, self._start_rows_animation)
            
        except Exception as e:
            print(f"重建账户UI时出错: {str(e)}")
            # 确保错误情况下也解锁动画状态
            if hasattr(self, 'animation_in_progress') and self.animation_in_progress:
                self._unlock_animations()

    def _start_rows_animation(self):
        """启动账户行的进场动画"""
        try:
            # 创建顺序动画组，而不是并行组
            self.row_animations = QSequentialAnimationGroup()
            
            # 添加每一行的透明度动画到顺序组中
            row_count = 0
            for i, row in enumerate(self.account_rows.values()):
                # 确保行是可见的，但透明度为0
                if not row.isVisible():
                    row.setVisible(True)
                
                # 确保行有opacity_effect
                if not hasattr(row, '_opacity_effect'):
                    # 为没有效果的行创建一个
                    opacity_effect = QGraphicsOpacityEffect(row)
                    opacity_effect.setOpacity(0.0)
                    row.setGraphicsEffect(opacity_effect)
                    row._opacity_effect = opacity_effect
                else:
                    # 确保效果的初始值是0
                    row._opacity_effect.setOpacity(0.0)
                
                # 创建透明度动画 - 使用QGraphicsOpacityEffect而不是windowOpacity
                anim = QPropertyAnimation(row._opacity_effect, b"opacity")
                anim.setDuration(150)  # 减短动画持续时间为150毫秒，加快行显示速度
                anim.setStartValue(0.0)
                anim.setEndValue(1.0)
                anim.setEasingCurve(QEasingCurve.Type.OutCubic)
                
                # 将动画添加到顺序组，它们会自动按顺序播放
                self.row_animations.addAnimation(anim)
                row_count += 1
            
            # 只有在有行要显示时才连接信号和开始动画
            if row_count > 0:
                # 连接动画完成信号
                self.row_animations.finished.connect(self._on_rows_animation_finished)
                
                # 开始动画
                self.row_animations.start(QPropertyAnimation.DeletionPolicy.DeleteWhenStopped)
            else:
                # 没有行要显示，直接完成
                self._on_rows_animation_finished()
            
        except Exception as e:
            print(f"启动账户行动画时出错: {str(e)}")
            # 确保错误情况下也解锁动画状态
            self._unlock_animations()

    def _on_rows_animation_finished(self):
        """账户行动画完成的处理"""
        try:
            # 启动数据刷新动画
            QTimer.singleShot(200, lambda: self._restore_animations(self.content_stack.currentWidget()))
            
            # 动画完成后，清除所有行的图形效果以防止性能问题
            for row in self.account_rows.values():
                if hasattr(row, '_opacity_effect'):
                    # 先设置为完全不透明
                    row._opacity_effect.setOpacity(1.0)
                    # 完全移除图形效果对象，释放资源
                    row.setGraphicsEffect(None)
                    # 删除引用
                    delattr(row, '_opacity_effect')
                
                # 确保所有行都是可见的
                if not row.isVisible():
                    row.setVisible(True)
            
            # 强制重绘账户页面
            if self.accounts_page:
                self.accounts_page.update()
            
            # 解锁动画状态
            self._unlock_animations()
            
        except Exception as e:
            print(f"账户行动画完成处理时出错: {str(e)}")
            # 确保错误情况下也解锁动画状态
            self._unlock_animations()
    
    def _on_ui_update_signal(self, email, data):
        """处理来自UI更新线程的更新信号
        
        Args:
            email: 需要更新的账户邮箱或操作类型
            data: 更新数据或配置信息
        """
        try:
            # 处理日志操作请求
            if email == "log_operation":
                # 如果是日志操作，通过log_update_signal将请求转发给日志页面管理器
                operation = data.get("operation", "")
                params = data.get("params", {})
                
                # 直接调用LogPageManager的处理方法，避免使用信号再次转发
                if hasattr(self, 'log_manager') and self.log_manager:
                    if operation == "refresh":
                        self.log_manager._refresh_log()
                    elif operation == "filter":
                        self.log_manager._apply_filters(params.get("filters", []))
                    elif operation == "scroll":
                        position = params.get("position")
                        animated = params.get("animated", True)
                        if position == "bottom":
                            self.log_manager._scroll_to_bottom(animated)
                        else:
                            self.log_manager._scroll_to_position(position, animated)
                return
                
            # 检查是否应取消更新(只检查邮箱匹配和账户数据存在)
            if not email or email not in self.account_quotas:
                return
            
            # 获取当前页面
            current_widget = self.content_stack.currentWidget()
            
            # 如果在主页且是当前账户，更新主页UI
            if email == self.current_email and current_widget == self.home_page:
                self._process_update(email)
            
            # 更新账户行数据 - 无论在哪个页面
            # 使用 .get() 获取行组件，如果账户已被删除或UI未同步，则返回 None
            row_widget = self.account_rows.get(email)
            if row_widget: # 确保行组件存在
                if row_widget.isVisible(): # 增加可见性检查
                    try:
                        # 修正变量名：使用 row_widget 而不是 row
                        row_widget.update_quota(self.account_quotas[email])
                    except Exception as e:
                        print(f"更新账户行 {email} 数据时出错: {str(e)}")
                        # 如果更新时出错（可能表示组件状态异常），也尝试移除引用
                        self.account_rows.pop(email, None)
                # else: # 调试信息，如果需要
                #     print(f"调试: 尝试更新账户行 {email}，但行组件不可见")
            
            # 更新账户统计信息 - 无论在哪个页面
            self._update_accounts_count()
            
        except Exception as e:
            print(f"处理UI更新信号时出错: {str(e)}")
    
    def _safe_update_ui(self, email):
        """安全地更新UI，确保UI已准备好
        
        Args:
            email: 要更新的账户邮箱
        """
        try:
            # 使用统一的方法检查是否应该取消更新
            if self._should_cancel_update(email, "UI更新"):
                return
                
            # 检查数据是否有效
            quota_data = self.account_quotas.get(email)
            if not quota_data:
                print(f"邮箱 {email} 的额度数据为空")
                return
            
            # 没有动画在进行，直接处理更新
            self._process_update(email)
            
        except Exception as e:
            print(f"安全更新UI出错: {str(e)}")

    def _safe_delayed_restore(self, restore_func, widget, *args, **kwargs):
        """安全地调用延迟恢复函数，捕获对象已删除的错误"""
        try:
            # --- 更安全的检查和调用 --- \
            # 1. 检查 Python 引用是否存在
            if widget is None:
                # print(f"延迟恢复: Python widget 引用为 None，跳过") # Debug
                return

            # 2. 尝试调用恢复函数如果 C++ 对象已删除，
            #    调用 restore_func 时访问 widget 的方法或属性会触发 RuntimeError
            # print(f"延迟恢复: 尝试恢复 {widget} 使用 {restore_func.__name__}") # Debug
            restore_func(widget, *args, **kwargs)
            # print(f"延迟恢复: 成功恢复 {widget}") # Debug

        except RuntimeError as e:
            # 捕获 "Internal C++ object already deleted" 错误
            # 检查错误消息以增加确定性（可选）
            if "Internal C++ object" in str(e) and "already deleted" in str(e):
                # print(f"延迟恢复: 目标 widget {widget} C++ 对象已被删除 (RuntimeError)，跳过恢复") # Debug
                pass # 静默处理，因为这在页面快速切换时是预期行为
            else:
                # 其他类型的 RuntimeError
                print(f"延迟恢复: 发生意外的 RuntimeError: {e}")
        except Exception as e:
            # 捕获其他可能的错误 (例如 restore_func 内部的逻辑错误)
            print(f"延迟恢复函数 {restore_func.__name__} 对 widget {widget} 执行时出错: {e}")
            import traceback
            # traceback.print_exc() # Uncomment for detailed debugging

    def _restore_animations(self, current_widget):
        """在页面切换完成后恢复所有动画"""
        try:
            # 锁定动画状态，防止更新中断动画
            self._lock_animations()
            
            # 查找当前页面中的所有进度条
            progress_bars = current_widget.findChildren(StyledProgressBar)
            
            # 等待数据加载的时间，确保API数据已返回
            animation_delay_base = 50  # 基础延迟
            
            # 对每个进度条重新应用值以触发动画，使用错开的延迟时间
            for i, progress_bar in enumerate(progress_bars):
                try:
                    # 计算每个进度条的递增延迟，避免所有动画同时启动
                    pb_delay = animation_delay_base + (i * 50)  # 每个增加50ms
                    
                    # 使用延迟触发恢复，确保数据先加载完成，并添加安全检查
                    QTimer.singleShot(pb_delay, lambda bar=progress_bar: self._safe_delayed_restore(self._delayed_restore_bar, bar))
                except Exception as e:
                    print(f"设置进度条动画延迟恢复时出错: {str(e)}")
            
            # 查找当前页面中的所有数字动画标签
            animated_labels = current_widget.findChildren(AnimatedNumberLabel)
            
            # 对每个数字标签重置并重新启动动画，使用更长的延迟
            for i, label in enumerate(animated_labels):
                try:
                    # 添加递增延迟，避免所有动画同时启动造成卡顿
                    label_delay = animation_delay_base + 100 + (i * 50)  # 比进度条多100ms基础延迟
                    # 使用延迟触发恢复，确保数据先加载完成，并添加安全检查
                    QTimer.singleShot(label_delay, lambda lbl=label: self._safe_delayed_restore(self._delayed_restore_label, lbl))
                except Exception as e:
                    print(f"设置数字标签动画延迟恢复时出错: {str(e)}")
                
            # 确保动画完成后解锁
            # 计算总延迟时间，使用最长的动画持续时间
            max_animation_duration = 800  # 假设最长动画持续800ms
            total_animations = len(progress_bars) + len(animated_labels)
            if total_animations > 0:
                unlock_delay = animation_delay_base + (total_animations * 50) + max_animation_duration
                # 确保至少有最小解锁延迟
                unlock_delay = max(unlock_delay, 500)
                QTimer.singleShot(unlock_delay, self._unlock_animations)
            else:
                # 如果没有动画，立即解锁
                QTimer.singleShot(100, self._unlock_animations)
        except Exception as e:
            print(f"恢复动画时出错: {str(e)}")
            # 确保错误情况下也解锁
            QTimer.singleShot(500, self._unlock_animations)

    def _delayed_restore_bar(self, progress_bar, force=False):
        """延迟恢复进度条动画，确保数据已加载
        
        Args:
            progress_bar: 要恢复的进度条
            force: 是否强制恢复，即使短时间内已恢复过，默认False
        """
        try:
            # 检查进度条是否已经正在运行动画，如果是则不重复触发
            if not force and hasattr(progress_bar, '_animation') and progress_bar._animation.state() == QAbstractAnimation.State.Running:
                return
            
            # 记录上一次恢复时间，避免短时间内多次恢复
            current_time = time.time()
            if not force and hasattr(progress_bar, '_last_restore_time'):
                # 如果两次恢复间隔小于1秒，则跳过
                if current_time - progress_bar._last_restore_time < 1.0:
                    return
            
            # 更新最后恢复时间
            progress_bar._last_restore_time = current_time
            
            # 增加动画持续时间，使其更平滑
            original_duration = getattr(progress_bar, '_animation_duration', 300)
            # 临时将动画时间设置为更长，确保动画效果明显
            progress_bar._animation_duration = 600
            
            # 恢复进度条的原始值并触发动画
            progress_bar.restore_original_value()
            
            # 如果是AnimatedProgressBar，确保数字动画也正确恢复
            if isinstance(progress_bar, AnimatedProgressBar):
                # 延迟一小段时间后恢复数字动画，避免同时触发导致的卡顿
                QTimer.singleShot(50, progress_bar.reset_animation)
                
            # 动画结束后恢复原始持续时间
            QTimer.singleShot(700, lambda: setattr(progress_bar, '_animation_duration', original_duration))
        except Exception as e:
            # 降低日志级别，仅在调试时输出
            # print(f"延迟恢复进度条动画时出错: {str(e)}")
            pass

    def _delayed_restore_label(self, label, force=False):
        """延迟恢复数字标签动画，确保数据已加载
        
        Args:
            label: 要恢复的数字标签
            force: 是否强制恢复，即使短时间内已恢复过，默认False
        """
        try:
            # 记录上一次恢复时间，避免短时间内多次恢复
            current_time = time.time()
            if not force and hasattr(label, '_last_restore_time'):
                # 如果两次恢复间隔小于1秒，则跳过
                if current_time - label._last_restore_time < 1.0:
                    return
            
            # 更新最后恢复时间
            label._last_restore_time = current_time
            
            # 增加动画持续时间，使其更平滑
            original_duration = getattr(label, '_animation_duration', 400)
            # 临时将动画时间设置为更长，确保动画效果明显
            label._animation_duration = 700
            
            # 重置并启动数字标签动画
            label.reset_animation()
            
            # 动画结束后恢复原始持续时间
            QTimer.singleShot(800, lambda: setattr(label, '_animation_duration', original_duration))
        except Exception as e:
            # 降低日志级别，仅在调试时输出
            # print(f"延迟恢复数字标签动画时出错: {str(e)}")
            pass

    def _process_update(self, email):
        """处理实际的更新请求"""
        # 实际更新UI逻辑
        if email in self.account_quotas:
            quota_data = self.account_quotas[email]
            # 更新主页UI
            self._update_usage_ui_impl(quota_data)
            # 更新账户行
            if email in self.account_rows:
                try:
                    self.account_rows[email].update_quota(quota_data)
                except Exception as e:
                    print(f"更新账户行数据时出错: {str(e)}")
            
    def _update_usage_ui_impl(self, quota_data):
        """在主线程中实际更新UI"""
        try:
            # 检测平台
            is_macos = platform.system() == 'Darwin'
            
            # 更新注册时间和剩余天数
            start_of_month = quota_data.get("startOfMonth")
            
            # 如果有真实注册时间，更新注册时间显示
            if start_of_month:
                try:
                    # 解析ISO 8601格式的时间字符串
                    from datetime import datetime, timezone, timedelta
                    from dateutil import parser

                    # 使用dateutil解析器兼容多种时间格式
                    reg_date = parser.parse(start_of_month)

                    # 时区处理 - 转换为中国时间 (UTC+8)
                    china_tz = timezone(timedelta(hours=8))
                    reg_date_china = reg_date.astimezone(china_tz)

                    # 格式化注册时间显示 (使用中国时间)
                    formatted_time = reg_date_china.strftime("%Y-%m-%d %H:%M:%S")
                    self.register_time_label.setText(f"注册时间: {formatted_time}")

                except Exception as e:
                    print(f"解析注册时间时出错: {str(e)}")
                    self.register_time_label.setText("注册时间: --")
            else:
                # 没有真实注册时间，显示默认值
                self.register_time_label.setText("注册时间: --")
            
            # 高级模型 (GPT-4) - 适配新的JSON格式
            gpt4_data = quota_data.get("gpt-4", {})
            usage = gpt4_data.get("numRequests", 0)  # 使用numRequests而不是numRequestsTotal
            max_usage = gpt4_data.get("maxRequestUsage")  # 保持不变

            # 使用新的账户类型API获取账户类型信息
            account_type_info = quota_data.get("account_type_info")
            if account_type_info:
                membership_type = account_type_info.get("membershipType")
                days_remaining = account_type_info.get("daysRemainingOnTrial")

                # 使用AccountType类格式化显示
                from account.account_type import AccountType
                display_text, label_text = AccountType.format_account_type_display(membership_type, days_remaining)
                color = AccountType.get_account_type_color(membership_type)

                if membership_type == "free_trial" and days_remaining is not None:
                    # 试用账户显示剩余天数
                    self.remaining_days_label.prefix = "剩余天数:"
                    self.remaining_days_label.suffix = "天"
                    self.remaining_days_label.setValue(days_remaining, animate=True)
                    self.remaining_days_label.setColor(color)
                else:
                    # 其他类型显示账户类型
                    self.remaining_days_label.prefix = ""
                    self.remaining_days_label.suffix = ""
                    self.remaining_days_label.setSpecialText(display_text)
                    self.remaining_days_label.setColor(color)
            else:
                # 如果没有账户类型信息，显示获取失败
                self.remaining_days_label.prefix = ""
                self.remaining_days_label.suffix = ""
                self.remaining_days_label.setSpecialText("获取失败")
                self.remaining_days_label.setColor(Theme.TEXT_SECONDARY)
            
            if max_usage is not None and isinstance(max_usage, (int, float)) and not isinstance(max_usage, bool) and max_usage > 0:
                # 有具体的使用限制
                self.advanced_progress.setRange(0, max_usage)
                self.advanced_progress.setValue(usage)  # 显示已使用量

                # 设置格式字符串 - 在macOS上特殊处理
                if is_macos:
                    # 确保进度条组件文本可见
                    self.advanced_progress.setTextVisible(True)
                    # 强制更新文本格式并立即触发重绘
                    self.advanced_progress.setFormat(f"{usage}/{max_usage}")
                    self.advanced_progress.update()
                else:
                    # 其他平台正常处理
                    self.advanced_progress.setFormat(f"{usage}/{max_usage}")

                # 根据使用情况设置不同颜色
                usage_percent = usage / max_usage
                if usage_percent > 0.9:
                    # 已用超过90%，显示红色警告
                    self.advanced_progress.setChunkColor(Theme.ERROR)
                elif usage_percent > 0.7:
                    # 已用超过70%，显示黄色警告
                    self.advanced_progress.setChunkColor(Theme.WARNING)
                else:
                    # 默认绿色
                    self.advanced_progress.setChunkColor(Theme.ACCENT)
            else:
                # maxRequestUsage为null或0，表示无限制
                self.advanced_progress.setRange(0, 100)
                self.advanced_progress.setValue(100)
                self.advanced_progress.setFormat("无限制")
                self.advanced_progress.setChunkColor(Theme.ACCENT)  # 使用绿色显示无限制
            
            # 普通模型 (GPT-3.5) - 适配新的JSON格式
            gpt35_data = quota_data.get("gpt-3.5-turbo", {})
            usage = gpt35_data.get("numRequests", 0)  # 使用numRequests而不是numRequestsTotal
            max_usage = gpt35_data.get("maxRequestUsage")
            
            if max_usage is not None and isinstance(max_usage, (int, float)) and not isinstance(max_usage, bool) and max_usage > 0:
                # 有具体的使用限制
                self.regular_progress.setRange(0, max_usage)
                self.regular_progress.setValue(usage)  # 显示已使用量

                # 设置格式字符串 - 在macOS上特殊处理
                if is_macos:
                    # 确保进度条组件文本可见
                    self.regular_progress.setTextVisible(True)
                    # 强制更新文本格式并立即触发重绘
                    self.regular_progress.setFormat(f"{usage}/{max_usage}")
                    self.regular_progress.update()
                else:
                    # 其他平台正常处理
                    self.regular_progress.setFormat(f"{usage}/{max_usage}")

                # 根据使用情况设置不同颜色
                usage_percent = usage / max_usage
                if usage_percent > 0.9:
                    # 已用超过90%，显示红色警告
                    self.regular_progress.setChunkColor(Theme.ERROR)
                elif usage_percent > 0.7:
                    # 已用超过70%，显示黄色警告
                    self.regular_progress.setChunkColor(Theme.WARNING)
                else:
                    # 默认绿色
                    self.regular_progress.setChunkColor(Theme.ACCENT)
            else:
                # maxRequestUsage为null或0，表示无限制
                self.regular_progress.setRange(0, 100)
                self.regular_progress.setValue(100)
                self.regular_progress.setFormat("无限制")
                self.regular_progress.setChunkColor(Theme.ACCENT)  # 使用绿色显示无限制
        except Exception as e:
            print(f"更新使用额度UI时出错: {str(e)}")
            self.advanced_progress.setFormat("无法获取")
            self.regular_progress.setFormat("无法获取")
            self.register_time_label.setText("注册时间: --")
            self.remaining_days_label.setSpecialText("未知")
            self.remaining_days_label.setColor(Theme.TEXT_SECONDARY)
    
    def _restore_home_page_animations(self, force=False):
        """在数据更新后恢复首页动画效果
        
        Args:
            force: 是否强制恢复，即使短时间内已恢复过
        """
        try:
            # 锁定动画状态，防止更新中断动画
            self._lock_animations()
            
            # 确保首页是当前页面
            if self.content_stack.currentWidget() != self.home_page:
                print("非首页，跳过动画恢复")
                # 非首页时立即解锁
                QTimer.singleShot(50, self._unlock_animations)
                return
                
            # 检查上次恢复时间，避免频繁刷新动画
            current_time = time.time()
            if not force and hasattr(self, '_last_home_page_restore_time'):
                # 如果两次恢复间隔小于1秒，则跳过
                if current_time - self._last_home_page_restore_time < 1.0:
                    print("距离上次恢复时间过短，跳过")
                    # 短时间内跳过时立即解锁
                    QTimer.singleShot(50, self._unlock_animations)
                    return
            
            # 更新最后恢复时间
            self._last_home_page_restore_time = current_time
            
            # 恢复首页所有动画
            self._restore_animations(self.home_page)
            
            # 注意：此处不需要解锁，_restore_animations会在完成后自动解锁
        except Exception as e:
            print(f"恢复首页动画时出错: {str(e)}")
            # 确保出错时也解锁
            QTimer.singleShot(100, self._unlock_animations)
    
    def load_current_account(self):
        """加载当前账号信息并立即获取其额度"""
        # 从数据库重新获取当前登录邮箱
        db_email = self.auth_manager.get_current_email()
        
        # 检查邮箱是否有变化
        if db_email != self.current_email:
            print(f"加载当前账户: 检测到邮箱变化，从 {self.current_email} 变为 {db_email}")
            self.current_email = db_email
            
            # 如果账户列表已加载，更新行的当前状态
            for email, row in self.account_rows.items():
                row.set_current(email == self.current_email)
        
        # 更新UI显示
        if self.current_email:
            self.email_label.setText(f"邮箱: {self.current_email}")
        else:
            self.email_label.setText("邮箱: 未登录")
        
        # 设置更明显的加载状态
        self.register_time_label.setText("注册时间: 加载中...")
        self.remaining_days_label.setSpecialText("加载中...")
        self.remaining_days_label.setColor(Theme.ACCENT)
        
        # 重置进度条状态并显示加载状态
        self.advanced_progress.reset_without_animation()
        self.advanced_progress.setFormat("加载中...")
        self.regular_progress.reset_without_animation()
        self.regular_progress.setFormat("加载中...")
        
        # 如果有当前账户，立即获取其额度数据
        if self.current_email:
            # 立即启动一个线程获取当前账户数据
            # 独立获取当前账户数据，优先更新首页
            QTimer.singleShot(0, lambda: self._fetch_current_account_quota())

    def load_accounts(self):
        """加载账户列表到UI"""
        # 清除之前的行
        for row in self.account_rows.values():
            row.setParent(None)
            row.deleteLater()
        self.account_rows.clear()
        
        # 检查账户文件是否存在
        if not os.path.exists(self.account_data.accounts_file):
            # 更新账户总数标签
            self.accounts_count_label.setText(f"账户总数：0个")
            
            # 显示错误信息
            self._show_account_file_error()
            return
        
        # 检查账户数据是否为空
        if not self.account_data.accounts:
            # 更新账户总数标签
            self.accounts_count_label.setText(f"账户总数：0个")
            
            # 显示账户数据为空的错误
            self._show_empty_account_data_error()
            return
        
        # 定义排序键函数
        def get_register_time(account):
            # 尝试获取所有可能的注册时间字段
            api_register_time = account.get("api_register_time")
            register_time = account.get("register_time")
            
            # 优先使用API返回的注册时间，因为它是最准确的
            if api_register_time:
                try:
                    from dateutil import parser
                    dt = parser.parse(api_register_time)
                    # 确保日期时间对象没有时区信息
                    return dt.replace(tzinfo=None)
                except:
                    pass
            
            # 其次使用保存的注册时间
            if register_time:
                try:
                    from dateutil import parser
                    dt = parser.parse(register_time)
                    # 确保日期时间对象没有时区信息
                    return dt.replace(tzinfo=None)
                except:
                    pass
            
            # 如果都没有，返回一个很旧的日期
            from datetime import datetime
            return datetime(2000, 1, 1)
        
        # 加载排序后的账户数据
        sorted_accounts = sorted(
            self.account_data.accounts, 
            key=lambda acc: get_register_time(acc), 
            reverse=True
        )
        
        # 检查是否存在账户
        if not sorted_accounts:
            # 显示账户数据为空的错误
            self._show_empty_account_data_error()
            return
        
        # 为每个账户创建一行
        for account in sorted_accounts:
            email = account.get("email", "")
            is_current = email == self.current_email
            row = AccountRowWidget(account, is_current)
            
            # 连接行信号
            row.switch_account_signal.connect(self.switch_account)
            row.delete_account_signal.connect(self.delete_account)
            row.show_details_signal.connect(self.show_account_details)  # 连接显示详情信号
            
            # 添加到布局
            self.accounts_layout.addWidget(row)
            
            # 保存到行字典中
            self.account_rows[email] = row
        
        # 更新账户总数标签
        self.accounts_count_label.setText(f"账户总数：{len(sorted_accounts)}个")
    
    def _show_account_file_error(self):
        """显示账户文件错误"""
        # 显示友好的错误信息
        error_widget = QWidget()
        error_widget.setObjectName("errorWidget")
        error_widget.setStyleSheet(f"""
            #errorWidget {{
                background-color: rgba(43, 157, 124, 0.1);
                border-radius: {Theme.BORDER_RADIUS};
                border: 1px solid {Theme.SUCCESS};
                padding: 40px;
                margin: 20px 0px;
            }}
        """)
        
        error_layout = QVBoxLayout(error_widget)
        error_layout.setContentsMargins(40, 40, 40, 40)
        error_layout.setSpacing(10)
        
        # 错误标题
        error_title = QLabel("没有找到账户文件")
        error_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        error_title.setStyleSheet(f"""
            color: {Theme.SUCCESS};
            font-size: {Theme.FONT_SIZE_TITLE};
            font-weight: bold;
        """)
        error_layout.addWidget(error_title)
        
        # 文件路径
        path_label = QLabel(f"路径：{self.account_data.accounts_file}")
        path_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        path_label.setWordWrap(True)
        path_label.setStyleSheet(f"""
            color: {Theme.SUCCESS};
            font-size: {Theme.FONT_SIZE_NORMAL};
            margin: 10px 0px;
        """)
        error_layout.addWidget(path_label)
        
        # 提示信息
        hint_label = QLabel("放心，这不是什么错误，你可以运行 保存当前登录账户 功能，就会创建账户文件\n如果你有账户数据JSON文件，可以直接导入或在设置里修改使用你的账户数据JSON路径")
        hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        hint_label.setWordWrap(True)
        hint_label.setStyleSheet(f"""
            color: {Theme.SUCCESS};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        error_layout.addWidget(hint_label)
        
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addStretch()
        
        error_layout.addLayout(button_layout)
        
        # 添加到布局
        self.accounts_layout.addWidget(error_widget)
    
    def _show_empty_account_data_error(self):
        """显示账户数据为空的错误"""
        # 显示友好的错误信息
        error_widget = QWidget()
        error_widget.setObjectName("errorWidget")
        error_widget.setStyleSheet(f"""
            #errorWidget {{
                background-color: rgba(43, 157, 124, 0.1);
                border-radius: {Theme.BORDER_RADIUS};
                border: 1px solid {Theme.SUCCESS};
                padding: 40px;
                margin: 20px 0px;
            }}
        """)
        
        error_layout = QVBoxLayout(error_widget)
        error_layout.setContentsMargins(40, 40, 40, 40)
        error_layout.setSpacing(10)
        
        # 错误标题
        error_title = QLabel("啊哦，账户数据是空的！")
        error_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        error_title.setStyleSheet(f"""
            color: {Theme.SUCCESS};
            font-size: {Theme.FONT_SIZE_TITLE};
            font-weight: bold;
        """)
        error_layout.addWidget(error_title)
        
        # 文件路径
        path_label = QLabel(f"文件路径：\n{self.account_data.accounts_file}")
        path_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        path_label.setWordWrap(True)
        path_label.setStyleSheet(f"""
            color: {Theme.SUCCESS};
            font-size: {Theme.FONT_SIZE_NORMAL};
            margin: 10px 0px;
        """)
        error_layout.addWidget(path_label)
        
        # 提示信息
        hint_label = QLabel("放心，这不是什么错误，你可以运行 保存当前登录账或自动注册 功能，就会有账户数据了\n如果你有账户数据JSON文件，可以直接导入或在设置里修改使用你的账户数据JSON路径")
        hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        hint_label.setStyleSheet(f"""
            color: {Theme.SUCCESS};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        error_layout.addWidget(hint_label)
        
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addStretch()
        
        error_layout.addLayout(button_layout)
        
        # 添加到布局
        self.accounts_layout.addWidget(error_widget)
    
    def switch_account(self, account):
        """切换到指定账户"""
        switch_account_action(self, account)
    
    def delete_account(self, email):
        """删除指定邮箱的账户"""
        # 确保在实际删除动作发生前或后，同步移除 UI 引用
        # self.account_rows.pop(email, None) # <<< 删除此行
        delete_account_action(self, email)
    

    
    def show_delete_type_dialog(self):
        """显示按账户类型删除的对话框"""
        dialog = DeleteTypeDialog(self, self)
        dialog.exec()

    def delete_accounts_by_type(self, dialog):
        """此方法已被DeleteTypeDialog类替代，保留空方法用于向后兼容"""
        pass

    def _execute_delete_by_type(self, account_type, dialog):
        """此方法已被DeleteTypeDialog类替代，保留空方法用于向后兼容"""
        pass
    
    def show_delete_quota_dialog(self):
        """显示删除额度账户的对话框"""
        dialog = DeleteQuotaDialog(self, self)
        dialog.exec()
    
    def reset_accounts_list_state(self):
        """重置账户列表状态"""
        try:
            # 记录调用来源，用于调试
            from logger import info
            
            # 获取是否首次加载标志
            is_first = self.is_first_accounts_page_load

            already_fetching = False
            # --- 修改状态检查：直接检查 quota_mgr 的状态 --- \
            if hasattr(self.quota_mgr, 'is_fetching_quotas') and self.quota_mgr.is_fetching_quotas: # 条件2
                already_fetching = True
                if is_first:
                    info("账户管理页面: 首次加载且检测到后台 quota_mgr 正在获取数据，跳过重复触发")
                else:
                    info("账户管理页面: 非首次加载，检测到后台 quota_mgr 正在获取数据，但不再显示加载状态")

            from_save_account = False
            if hasattr(self, '_last_saved_account') and self._last_saved_account:
                from_save_account = True
                self._last_saved_account = False  # 重置标记
                info("从保存账户操作切换至账户管理页面，将强制刷新")
                is_first = True # 强制执行首次加载/刷新逻辑
            elif is_first:
                 info("首次切换至账户管理页面，将加载数据")
            else:
                 info("切换回已加载的账户管理页面")

            # 无论是否首次加载，都隐藏所有账户行，保证一致的体验
            # 这样可以避免奇偶次数切换行为不一致的问题
            for email, row in self.account_rows.items():
                row.setVisible(False)
            
            # 根据是否首次加载（或需要强制刷新）执行不同逻辑
            if is_first:
                # --- 首次加载或需要强制刷新的逻辑 ---
                info("执行首次加载/强制刷新逻辑")
                
                # 显示加载指示器
                if hasattr(self, 'loading_container'):
                    self.loading_container.setVisible(True)
                    
                    # 重置进度条
                    if hasattr(self, 'loading_progress') and self.loading_progress:
                        self.loading_progress.setFormat("正在加载中")
                        self.loading_progress.setValue(1)
                        # 尝试获取账户数，如果 account_data 还未加载则使用默认值
                        num_accounts = len(self.account_data.accounts) if hasattr(self, 'account_data') and self.account_data.accounts else 100
                        self.loading_progress.setMaximum(num_accounts or 100)
                
                # 如果已经在获取数据，不需要重复加载，只需等待并重建UI
                if already_fetching:
                    # 等待数据获取完成的信号即可，UI会在 _update_after_fetch 中重建
                    info("账户管理页面: 等待后台数据获取完成...")
                    # QTimer.singleShot(100, lambda: self._rebuild_accounts_ui_with_animation(show_toast=False)) # 不再需要，由信号触发
                else:
                    # 正常首次加载流程：重新加载数据
                    QTimer.singleShot(100, self._safe_refresh_all_data)  # 使用安全的刷新方法
            else:
                # --- 切换回已加载页面的逻辑 ---
                info("执行切换回页面的逻辑，不显示加载状态")
                # 确保加载指示器是隐藏的
                if hasattr(self, 'loading_container'):
                    self.loading_container.setVisible(False)
                
                # 修改：不再默认显示所有行，而是重建UI并应用动画
                QTimer.singleShot(100, lambda: self._rebuild_accounts_ui_with_animation(show_toast=False))
                
                # 已不再需要，因为会在重建UI时处理
                # for row in self.account_rows.values():
                #     if not row.isVisible():
                #         row.setVisible(True)

        except Exception as e:
            print(f"重置账户列表状态时出错: {str(e)}")
            # 确保解锁动画状态
            if hasattr(self, 'animation_in_progress') and self.animation_in_progress:
                self._unlock_animations()

    def _safe_refresh_all_data(self):
        """安全地刷新所有数据，确保不会覆盖文件中的新账户数据"""
        try:
            # 显示正在刷新的提示
            # self.show_toast("正在刷新所有账户数据...")
            
            # 检查是否需要重新初始化AccountData
            if hasattr(self, '_reload_account_data') and self._reload_account_data:
                # 重新初始化AccountData实例
                self.account_data = AccountData()
                # 重置标志
                self._reload_account_data = False
                print("检测到JSON自定义路径已更改，已重新初始化AccountData实例")
            else:
                # 仅重新从文件加载账户数据
                self.account_data.accounts = self.account_data.load_accounts()
            
            # 加载完账户数据后，获取所有账户的额度信息
            # 但这里不要保存数据，只读取
            self.fetch_all_accounts_quota(is_manual_refresh=True)
        except Exception as e:
            print(f"安全刷新所有数据时出错: {str(e)}")
            self.show_toast("刷新数据时出错", error=True)
    
    def switch_page(self, index):
        """切换页面"""
        # 导入日志函数
        from logger import info
        
        # 更新按钮状态 - 将这部分逻辑移到条件判断前，确保无论是否切换页面都会更新按钮状态
        self.home_btn.setChecked(index == 0)
        self.accounts_btn.setChecked(index == 1)
        self.feature_btn.setChecked(index == 2)
        self.settings_btn.setChecked(index == 3)
        self.log_btn.setChecked(index == 4)
        self.about_btn.setChecked(index == 5)
        
        # 如果当前已经在这个页面，不需要再次切换
        if self.content_stack.currentIndex() == index:
            return

        # --- 修改：移除或注释掉 page_names 字典 ---
        # page_names = {
        #     0: "首页",
        #     1: "账户管理页面",
        #     2: "功能页面",
        #     3: "设置页面",
        #     4: "日志页面",
        #     5: "关于页面"
        # }
        # --- 修改结束 ---

        current_index = self.content_stack.currentIndex()

        # --- 修改：直接获取 windowTitle ---
        current_widget = self.content_stack.widget(current_index)
        new_widget = self.content_stack.widget(index)
        current_page_name = current_widget.windowTitle() if current_widget and current_widget.windowTitle() else f"未知页面({current_index})"
        new_page_name = new_widget.windowTitle() if new_widget and new_widget.windowTitle() else f"未知页面({index})"
        info(f"切换页面: 从 {current_page_name} 到 {new_page_name}")
        # --- 修改结束 ---

        # --- Shutdown managers of the page *being left* --- \
        if current_index == 1: # Leaving Accounts page
            # if hasattr(self, 'quota_manager') and self.quota_manager and hasattr(self.quota_manager, 'shutdown'):
            #     print("Leaving Accounts page, shutting down QuotaFetcher...")
            #     self.quota_manager.shutdown(wait=False) # Non-blocking shutdown
            # else:
            #     print("Leaving Accounts page, quota_manager not found or no shutdown method.")
            pass # Add pass to ensure the block is syntactically valid if everything else is commented
        elif current_index == 4: # Leaving Log page
            if hasattr(self, 'log_manager') and self.log_manager and hasattr(self.log_manager, 'shutdown'):
                print("Leaving Log page, shutting down LogPageManager...")
                self.log_manager.shutdown()
            else:
                print("Leaving Log page, log_manager not found or no shutdown method.")

        # --- Removed problematic event loop handling during switch --- \
        # try:
        #     # 在切换页面前尝试确保所有asyncio相关操作已完成
        #     # ... (Old shutdown logic removed as it's handled above) ...
        #
        #     # 这里不进行循环检查，因为上面的shutdown应该已经处理了事件循环
        #     import asyncio
        #     from utils import setup_asyncio_event_loop_policy
        #
        #     # 重新设置事件循环策略，确保后续操作使用正确的策略
        #     # --- This call is suspect and might cause issues, commenting out --- \
        #     # print("Switch Page: Re-applying asyncio event loop policy...")
        #     # setup_asyncio_event_loop_policy()
        # except Exception as e:
        #     print(f"处理异步资源或策略时出错，将继续页面切换: {str(e)}")

        # 立即打断所有正在进行的动画 (Should be safe, handles UI animations)
        self._cancel_all_animations()

        # 切换到账户管理页面前，重置账户列表状态
        if index == 1:  # Entering Accounts page
            self.reset_accounts_list_state()
            # Maybe re-initialize or start quota_manager if needed here?
            # For now, assuming fetch_all_accounts_quota handles initialization.
        # 检查是否是切换到首页且首页需要重新初始化
        elif index == 0 and not self.is_home_initialized:  # Entering Home page
            from logger import info
            info("检测到首页需要重新初始化，开始重新加载数据")
            # 执行首页数据加载
            self.load_current_account()
            # 标记首页已初始化
            self.is_home_initialized = True

        # 获取目标页面，并预处理其进度条
        target_widget = self.content_stack.widget(index)
        if target_widget:
            # 查找目标页面中的所有进度条
            progress_bars = target_widget.findChildren(StyledProgressBar)

            # 将所有进度条值设为0，预先隐藏已填充状态，但跳过loading_progress
            for progress_bar in progress_bars:
                try:
                    # 跳过加载进度条
                    if hasattr(self, 'loading_progress') and progress_bar == self.loading_progress:
                        continue

                    # 使用专用方法重置进度条，同时存储原始值
                    progress_bar.reset_without_animation(store_original=True)
                except Exception as e:
                    error(f"预处理进度条时出错: {str(e)}")

        # 使用动画效果切换页面
        self.content_stack.slideInIdx(index)

        # --- Set visibility for the page *being entered* --- \
        if index == 4 and hasattr(self, 'log_manager') and self.log_manager:  # Entering Log page
            print("Entering Log page, setting visible...")
            self.log_manager.set_log_page_visible(True)
        # Note: The logic previously set log_manager visible=False when *entering* other pages.
        # Now, the shutdown logic handles cleanup when *leaving* the log page.
        # We might not need the `elif hasattr(self, 'log_manager'): self.log_manager.set_log_page_visible(False)` anymore,
        # but let's keep it for now as `set_log_page_visible` also saves scroll state.
        elif hasattr(self, 'log_manager') and self.log_manager: # Entering other pages
             self.log_manager.set_log_page_visible(False)

    def _cancel_all_animations(self):
        """打断并取消所有正在进行的动画"""
        try:
            # 强制解锁动画状态
            if hasattr(self, 'animation_in_progress') and self.animation_in_progress:
                self._unlock_animations()
                # 清空排队的更新请求
                if hasattr(self, 'queued_updates'):
                    self.queued_updates.clear()
            
            # 使用动画管理器取消所有动画
            if hasattr(self, 'animation_manager'):
                self.animation_manager.cancel_all_animations()
            else:
                # 拦截方式：直接取消动画
                # 获取当前页面
                current_widget = self.content_stack.currentWidget()
                if not current_widget:
                    return
                    
                # 停止当前页面中所有进度条的动画
                progress_bars = current_widget.findChildren(StyledProgressBar)
                for progress_bar in progress_bars:
                    try:
                        # 停止进度条的动画
                        if hasattr(progress_bar, '_animation'):
                            if progress_bar._animation.state() == QAbstractAnimation.State.Running:
                                progress_bar._animation.stop()
                    except Exception as e:
                        print(f"停止进度条动画时出错: {str(e)}")
                
                # 停止当前页面中所有数字标签的动画
                animated_labels = current_widget.findChildren(AnimatedNumberLabel)
                for label in animated_labels:
                    try:
                        # 停止数字标签的动画
                        if hasattr(label, '_animation'):
                            if label._animation.state() == QAbstractAnimation.State.Running:
                                label._animation.stop()
                    except Exception as e:
                        print(f"停止数字标签动画时出错: {str(e)}")
            
            # 取消延迟执行的定时器，但保留关键定时器
            protected_timers = [self.quota_refresh_timer]

            # 保护认证时效检查定时器
            if hasattr(self, 'version_checker') and hasattr(self.version_checker, 'auth_check_timer'):
                if self.version_checker.auth_check_timer:
                    protected_timers.append(self.version_checker.auth_check_timer)

            # 保护认证定时器监控器
            if hasattr(self, 'auth_timer_monitor'):
                protected_timers.append(self.auth_timer_monitor)

            for timer in self.findChildren(QTimer):
                if timer.isActive() and timer not in protected_timers:
                    timer.stop()
                    
            print("已取消所有正在运行的动画")
        except Exception as e:
            print(f"取消动画时出错: {str(e)}")

    def _sort_accounts_and_update_ui(self):
        """根据注册时间重新排序账户列表并更新UI"""
        # 如果没有账户数据，直接返回
        if not self.account_data.accounts:
            self.show_toast("未找到账户数据，请添加账户")
            self._update_accounts_count()
            return
        
        # 定义排序函数，按注册时间从新到旧排序
        def get_register_time(account):
            try:
                # 尝试获取所有可能的注册时间字段
                register_time = (account.get("startOfMonth") or 
                               account.get("real_register_time") or 
                               account.get("api_register_time") or
                               account.get("register_time"))
                
                if not register_time:
                    # 尝试获取注册时间显示字段(可能已经格式化为本地日期格式)
                    if "注册时间" in account:
                        register_time = account["注册时间"]
                    else:
                        # 如果没有任何注册时间，将其排在最后
                        from datetime import datetime
                        return datetime(1970, 1, 1)
                
                # 使用dateutil解析日期时间并去除时区信息
                from dateutil import parser
                dt = parser.parse(register_time)
                return dt.replace(tzinfo=None)
            except Exception:
                # 解析失败时返回一个默认的旧日期
                from datetime import datetime
                return datetime(1970, 1, 1)
        
        # 对账户列表按注册时间从新到旧排序
        sorted_accounts = sorted(self.account_data.accounts, key=get_register_time, reverse=True)
        
        # 更新账户列表
        self.account_data.accounts = sorted_accounts
        
        # 移除保存操作，只在内存中保持排序后的数据
        # self.account_data.save_accounts()
        
        # 清空当前UI
        while self.accounts_layout.count():
            item = self.accounts_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 重新创建账户行
        self.account_rows = {}
        for account in sorted_accounts:
            email = account.get("email", "")
            is_current = email == self.current_email
            
            # 创建行组件
            row = AccountRowWidget(account, is_current)
            
            # 如果已经有该账户的额度数据，直接更新
            if email in self.account_quotas:
                try:
                    row.update_quota(self.account_quotas[email])
                except Exception as e:
                    print(f"更新账户额度时出错: {str(e)}")
            else:
                try:
                    row.set_loading_state(True)
                except Exception as e:
                    print(f"设置行加载状态时出错: {str(e)}")
            
            # 连接信号
            row.switch_account_signal.connect(self.switch_account)
            row.delete_account_signal.connect(self.delete_account)
            row.show_details_signal.connect(self.show_account_details)  # 连接显示详情信号
            
            # 添加到布局
            self.accounts_layout.addWidget(row)
            self.account_rows[email] = row
        
        # 更新账户计数
        self._update_accounts_count()

    def _sort_accounts_without_rebuild_ui(self):
        """只排序账户列表，但不重新创建UI组件"""
        # 如果没有账户数据，直接返回
        if not self.account_data.accounts:
            self._update_accounts_count()
            return
        
        # 定义排序函数，按注册时间从新到旧排序
        def get_register_time(account):
            try:
                # 尝试获取所有可能的注册时间字段
                register_time = (account.get("startOfMonth") or 
                               account.get("real_register_time") or 
                               account.get("api_register_time") or
                               account.get("register_time"))
                
                if not register_time:
                    # 尝试获取注册时间显示字段(可能已经格式化为本地日期格式)
                    if "注册时间" in account:
                        register_time = account["注册时间"]
                    else:
                        # 如果没有任何注册时间，将其排在最后
                        from datetime import datetime
                        return datetime(1970, 1, 1)
                
                # 使用dateutil解析日期时间并去除时区信息
                from dateutil import parser
                dt = parser.parse(register_time)
                return dt.replace(tzinfo=None)
            except Exception:
                # 解析失败时返回一个默认的旧日期
                from datetime import datetime
                return datetime(1970, 1, 1)
        
        # 对账户列表按注册时间从新到旧排序
        sorted_accounts = sorted(self.account_data.accounts, key=get_register_time, reverse=True)
        
        # 更新账户列表
        self.account_data.accounts = sorted_accounts
        
        # 移除保存操作，只在内存中保持排序后的数据
        # self.account_data.save_accounts()
        
        # 更新账户计数
        self._update_accounts_count()

    def refresh_accounts_data(self):
        """刷新所有账户数据"""
        # 显示Toast提示
        self.show_toast("正在获取账户数据...")
        
        # 清空账户列表
        while self.accounts_layout.count():
            item = self.accounts_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 在新线程中加载数据
        thread = threading.Thread(target=self._load_accounts_data)
        thread.daemon = True
        thread.start()
    
    def _load_accounts_data(self):
        """在线程中加载账户数据"""
        try:
            # 检查是否需要重新初始化AccountData
            if hasattr(self, '_reload_account_data') and self._reload_account_data:
                # 重新初始化AccountData实例
                self.account_data = AccountData()
                # 重置标志
                self._reload_account_data = False
                print("检测到JSON自定义路径已更改，已重新初始化AccountData实例")
            else:
                # 仅重新从文件加载账户数据
                self.account_data = AccountData()
            
            # 检查账户文件是否存在
            if not os.path.exists(self.account_data.accounts_file):
                # 在主线程中更新UI，显示文件不存在的错误
                error_message = f"账户文件不存在: {self.account_data.accounts_file}"
                print(error_message)
                QTimer.singleShot(0, lambda: self.show_toast(error_message, error=True))
                # 确保在主线程中更新UI
                QTimer.singleShot(0, self._update_accounts_ui_with_error)
                return
            
            # 在主线程中更新UI
            QTimer.singleShot(0, self._update_accounts_ui)
        except Exception as e:
            print(f"加载账户数据时出错: {str(e)}")
            QTimer.singleShot(0, lambda: self.show_toast("加载账户数据失败", error=True))
    
    def _update_accounts_ui_with_error(self):
        """在账户数据加载失败时更新UI"""
        # 清空账户列表
        for row in self.account_rows.values():
            row.setParent(None)
            row.deleteLater()
        self.account_rows.clear()
        
        # 确保加载指示器隐藏
        if hasattr(self, 'loading_container'):
            self.loading_container.setVisible(False)
        
        # 显示错误信息
        self._show_account_file_error()
        
        # 更新账户总数标签
        self.accounts_count_label.setText(f"账户总数：0个")
    
    def _update_accounts_ui(self, show_toast=True):
        """在主线程中更新账户列表UI
        
        Args:
            show_toast: 是否显示Toast提示，默认显示
        """
        # 清空当前账户列表UI
        while self.accounts_layout.count():
            item = self.accounts_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 重新加载当前账号信息
        self.load_current_account()
        
        # 加载账户列表
        self.load_accounts()
        
        # 更新账户计数显示
        self._update_accounts_count()
        
        # 获取所有账户额度
        if self.account_data.accounts:
            self.fetch_all_accounts_quota(show_toast=show_toast)
        else:
            # 不再添加无数据提示，只在状态栏显示
            # no_data_label = QLabel("没有找到账户数据")
            # no_data_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            # no_data_label.setStyleSheet(f"""
            #     color: {Theme.TEXT_SECONDARY};
            #     font-size: {Theme.FONT_SIZE_NORMAL};
            #     padding: 20px;
            # """)
            # self.accounts_layout.addWidget(no_data_label)
            self.show_toast("未找到账户数据，请添加账户")
            self._update_accounts_count()

    def _copy_error_to_clipboard(self, error_info):
        """将错误信息复制到剪贴板
        
        Args:
            error_info: 错误信息字符串
        """
        from utils import copy_error_to_clipboard
        copy_error_to_clipboard(error_info, self.toast_queue)

    def _load_current_account_quota_from_storage(self):
        """从存储的数据中加载当前账户的额度信息，而不是请求API
        
        Returns:
            bool: 加载成功返回True，否则返回False
        """
        try:
            # 获取当前邮箱
            if not self.current_email:
                print("加载存储数据失败: 当前无邮箱")
                return False
            
            # 检查是否有存储的配额数据
            if self.current_email in self.account_quotas:
                # 直接使用已存储的数据更新UI
                QTimer.singleShot(0, lambda: self._safe_update_ui(self.current_email))
                print(f"从存储加载配额成功: {self.current_email}")
                return True
            else:
                print(f"存储中没有该账户的配额数据: {self.current_email}")
                return False
        except Exception as e:
            print(f"从存储加载配额数据时发生错误: {str(e)}")
            return False

    def _update_accounts_count(self):
        """更新账户计数信息"""
        # 获取账户总数
        total_accounts = len(self.account_data.accounts)

        # 计算不同类型账户数
        pro_accounts = 0
        free_accounts = 0
        trial_accounts = 0
        unknown_accounts = 0

        for account in self.account_data.accounts:
            # 优先使用新的账户类型API信息
            quota_data = account.get("quota_data", {})
            account_type_info = quota_data.get("account_type_info")

            if account_type_info:
                membership_type = account_type_info.get("membershipType")
                days_remaining = account_type_info.get("daysRemainingOnTrial")

                if membership_type == "pro":
                    pro_accounts += 1
                elif membership_type == "free":
                    free_accounts += 1
                elif membership_type == "free_trial":
                    trial_accounts += 1
                else:
                    unknown_accounts += 1
            else:
                # 如果没有账户类型信息，归为未知
                unknown_accounts += 1

        # 更新显示 - 只显示总数和账户类型统计，不显示有效/过期统计
        self.accounts_count_label.setText(f"共 {total_accounts} 个账户 （ {pro_accounts} 个Pro, {trial_accounts} 个试用, {free_accounts} 个免费 ）")

    def cleanup(self):
        """执行清理任务"""
        try:
            # 保存临时文件中的数据到主文件，但不自动创建文件
            if hasattr(self, 'account_data'):
                # 检查账户文件是否存在，只有存在时才保存
                if os.path.exists(self.account_data.accounts_file):
                    # 先重新从文件加载账户数据，确保不会覆盖新注册但未显示在UI上的账户
                    from logger import info
                    current_accounts = self.account_data.accounts.copy()  # 保存当前内存中的账户
                    
                    # 从文件重新加载账户，确保获取最新数据
                    loaded_accounts = self.account_data.load_accounts()
                    
                    # 记录添加的新账户数量
                    added_accounts_count = 0
                    
                    # 检查内存中的每个账户是否存在于文件列表中
                    for memory_account in current_accounts:
                        memory_email = memory_account.get('email')
                        if not memory_email:
                            continue
                            
                        # 检查该邮箱是否存在于文件列表中
                        found = False
                        for file_account in loaded_accounts:
                            if file_account.get('email') == memory_email:
                                found = True
                                break
                        
                        # 如果未找到，将账户添加到文件列表中
                        if not found:
                            info(f"文件中未找到账户 {memory_email}，将其添加到内存中")
                            loaded_accounts.insert(0, memory_account)  # 添加到列表开头
                            added_accounts_count += 1
                    
                    # 如果有添加新账户，记录日志
                    if added_accounts_count > 0:
                        info(f"共添加了 {added_accounts_count} 个新注册但未显示在UI上的账户")
                    
                    # 更新内存中的账户数据
                    self.account_data.accounts = loaded_accounts
                    
                    # 保存到文件
                    self.account_data.save_accounts(allow_create=False)
                else:
                    print("账户文件不存在，跳过保存")
            
            # 断开智能线程管理器的连接
            if hasattr(self, 'quota_manager'):
                with warnings.catch_warnings(): # 添加 warnings 上下文管理器
                    warnings.simplefilter("ignore", RuntimeWarning) # 忽略 RuntimeWarning
                    try:
                        self.quota_manager.quota_fetched.disconnect()
                        self.quota_manager.all_quotas_fetched.disconnect()
                        self.quota_manager.progress_updated.disconnect()
                        # 关闭 QuotaFetcherManager (using self.quota_manager)
                        if hasattr(self, 'quota_manager') and self.quota_manager and hasattr(self.quota_manager, 'shutdown'):
                            print("关闭 QuotaFetcherManager...")
                            # Use wait=True here for graceful exit, timeout is handled by join/wait internally if needed
                            self.quota_manager.shutdown(wait=True) # Pass wait=True, remove timeout
                        else:
                            print("QuotaFetcherManager 未找到或无 shutdown 方法，跳过关闭")
                    except Exception as e:
                        print(f"断开智能线程管理器连接时出错: {str(e)}")
            
            # 停止UI更新线程
            if hasattr(self, 'ui_update_thread'):
                try:
                    self.ui_update_thread.stop()
                except Exception as e:
                    print(f"停止UI更新线程时出错: {str(e)}")
            
            # 删除临时目录和文件
            self._cleanup_temp_files()
            
            # 停止自动刷新定时器
            if hasattr(self, 'quota_refresh_timer'):
                self.quota_refresh_timer.stop()

            # 停止认证定时器监控器
            if hasattr(self, 'auth_timer_monitor'):
                self.auth_timer_monitor.stop()

            # 停止认证时效检查定时器
            if hasattr(self, 'version_checker'):
                try:
                    self.version_checker.stop_auth_check_timer()
                except Exception:
                    pass
        except Exception as e:
            print(f"执行清理任务时出错: {str(e)}")
            traceback.print_exc()
    
    def _show_log_context_menu(self, position):
        """显示日志文本区域的自定义上下文菜单"""
        try:
            # 创建自定义上下文菜单
            context_menu = QMenu(self)
            
            # 设置菜单样式与应用的暗色主题匹配
            context_menu.setStyleSheet(f"""
                QMenu {{
                    background-color: #1A1D24;
                    color: {Theme.TEXT_PRIMARY};
                    border: 1px solid {Theme.BORDER};
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    padding: 5px;
                    font-weight: bold;
                }}
                QMenu::item {{
                    padding: 5px 15px;
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                }}
                QMenu::item:selected {{
                    background-color: {Theme.ACCENT};
                    color: white;
                }}
                QMenu::separator {{
                    height: 1px;
                    background-color: {Theme.BORDER};
                    margin: 5px;
                }}
            """)
            
            # 添加菜单项
            copy_action = context_menu.addAction("复制")
            select_all_action = context_menu.addAction("全选")
            
            context_menu.addSeparator()
            
            copy_all_action = context_menu.addAction("复制全部日志")
            refresh_action = context_menu.addAction("刷新日志")
            
            context_menu.addSeparator()
            
            # 添加打开日志文件夹选项
            from logger import open_log_dir
            open_folder_action = context_menu.addAction("打开日志文件夹")
            
            # 获取选中的文本
            has_selection = False
            if isinstance(self.log_text, QTextBrowser):
                has_selection = bool(self.log_text.textCursor().hasSelection())
            else:
                # 兼容旧的QLabel实现
                has_selection = hasattr(self.log_text, 'selectedText') and bool(self.log_text.selectedText())
            
            # 根据是否有选中文本启用/禁用复制操作
            copy_action.setEnabled(has_selection)
            
            # 显示菜单并处理选择
            action = context_menu.exec(self.log_text.mapToGlobal(position))
            
            # 处理动作
            if action == copy_action and has_selection:
                # 复制选中文本
                clipboard = QApplication.clipboard()
                if isinstance(self.log_text, QTextBrowser):
                    clipboard.setText(self.log_text.textCursor().selectedText())
                else:
                    # 兼容旧的QLabel实现
                    clipboard.setText(self.log_text.selectedText())
            elif action == select_all_action:
                # 全选
                self.log_text.selectAll()
            elif action == copy_all_action:
                # 复制全部文本（不含HTML标签）
                clipboard = QApplication.clipboard()
                # 根据组件类型获取内容
                if hasattr(self.log_text, 'toHtml'):  # QTextBrowser
                    doc = QTextDocument()
                    doc.setHtml(self.log_text.toHtml())
                    clipboard.setText(doc.toPlainText())
                else:  # QLabel
                    doc = QTextDocument()
                    doc.setHtml(self.log_text.text())
                    clipboard.setText(doc.toPlainText())
                self.show_toast("已复制全部日志内容")
            elif action == refresh_action:
                # 使用LogPageManager刷新日志
                self.log_manager.refresh_log()
                self.show_toast("日志已刷新")
            elif action == open_folder_action:
                # 打开日志文件夹
                open_log_dir()
                
        except Exception as e:
            print(f"显示上下文菜单时出错: {str(e)}")
    
    def _should_cancel_update(self, email=None, operation_name="更新"):
        """检查是否应该取消更新操作
        
        Args:
            email: 可选，要更新的账户邮箱
            operation_name: 操作名称，用于日志显示
            
        Returns:
            bool: 如果应该取消更新，返回True；否则返回False
        """
        # 检查是否有进度条或数字标签动画在进行中
        if self.animation_in_progress:
            message = f"动画正在进行中，取消{operation_name}操作"
            if email:
                message += f": {email}"
            print(message)
            return True
            
        # 如果传入了邮箱，检查是否为当前邮箱
        if email and email != self.current_email:
            print(f"邮箱 {email} 不是当前登录邮箱 {self.current_email}")
            return True
            
        # 如果传入了邮箱，检查该邮箱是否存在额度数据
        if email and email not in self.account_quotas:
            print(f"错误: 邮箱 {email} 不存在额度数据")
            return True
            
        # 不需要取消更新
        return False
    
    # 添加动画锁定与解锁方法
    def _lock_animations(self):
        """锁定进度条和数字标签动画，防止其他操作中断这些动画"""
        print("进度条和数字标签动画锁定")
        self.animation_in_progress = True
        if hasattr(self, 'animation_manager'):
            self.animation_manager.lock_animations()
        
    def _unlock_animations(self):
        """解锁进度条和数字标签动画状态"""
        print("进度条和数字标签动画解锁")
        self.animation_in_progress = False
        
        # 使用动画管理器解锁动画
        if hasattr(self, 'animation_manager'):
            self.animation_manager.unlock_animations()
        
        # 强制重绘当前页面
        current_widget = self.content_stack.currentWidget()
        if current_widget:
            current_widget.update()
            
        # 主动请求垃圾回收 - REMOVED

    def _cleanup_temp_files(self):
        """清理临时文件和目录"""
        try:
            # 获取应用数据目录中的临时文件目录
            app_data_dir = get_app_data_dir()
            temp_dir = os.path.join(app_data_dir, "temp_quota_data")
            
            # 如果目录不存在，直接返回
            if not os.path.exists(temp_dir):
                return
            
            # 检查临时文件是否存在
            if hasattr(self.account_data, 'temp_file') and os.path.exists(self.account_data.temp_file):
                try:
                    # 临时文件已经在其他地方保存，可以安全删除
                    os.remove(self.account_data.temp_file)
                    print(f"已删除临时文件: {self.account_data.temp_file}")
                except Exception as e:
                    print(f"删除临时文件出错: {str(e)}")
        except Exception as e:
            print(f"清理临时文件时出错: {str(e)}")

    def start_processing(self, accounts):
        """开始处理账户列表（在后台线程中调用）"""
        try:
            # 重置进度计数器
            self.processed_count = 0
            self.total_count = len(accounts)
            
            # 发送初始进度信号，确保UI显示"正在加载中"
            self.progress_updated.emit(0, self.total_count)
            
            # 创建账户分组，保证组数=线程数
            account_groups = self.create_account_groups(accounts)
            
            # 创建future列表
            futures = []
            
            # 提交每个分组的任务到线程池，每个组对应一个线程
            for group in account_groups:
                future = self.thread_pool.submit(self.process_group, group)
                futures.append(future)
            
            # 处理每个分组的结果
            for future in as_completed(futures):
                try:
                    group_results = future.result()
                    for email, quota_data in group_results:
                        with self.lock:
                            self.results[email] = quota_data
                            # 发送单个账户配额获取完成的信号
                            self.quota_fetched.emit(email, quota_data)
                except Exception as e:
                    print(f"处理账户组结果时出错: {str(e)}")
                
                # 平台特定的延迟处理
                if self.platform == "darwin":
                    time.sleep(0.1)
                elif self.platform != "win32":
                    time.sleep(0.05)
            
            # 发送所有账户处理完成的信号
            self.all_quotas_fetched.emit()
            
        except Exception as e:
            print(f"处理账户列表时出错: {str(e)}")
            # 发送完成信号，确保UI不会永远处于加载状态
            self.all_quotas_fetched.emit()
    
    def process_accounts(self, accounts):
        """处理账户列表（在主线程中调用）"""
        # 创建后台线程来处理数据
        worker_thread = Thread(target=self.start_processing, args=(accounts,))
        worker_thread.daemon = True  # 将线程设为守护线程，确保主程序退出时线程也会终止
        worker_thread.start()
    
    def shutdown(self):
        """关闭线程池"""
        try:
            self.thread_pool.shutdown(wait=True)
            
            # 如果需要额外的清理，在平台特定的延迟后进行
            if self.platform == "win32":
                time.sleep(0.5)  # Windows平台稍长的延迟
            else:
                time.sleep(0.2)  # 其他平台标准延迟
        except Exception as e:
            print(f"关闭线程池时出错: {str(e)}")

    def eventFilter(self, obj, event):
        """重载事件过滤器，处理自定义事件
        
        Args:
            obj: 事件源对象
            event: 事件对象
            
        Returns:
            bool: 是否已处理事件
        """
        # 处理日志滚动条的点击事件
        if hasattr(self, '_handle_scrollbar_click'):
            if self._handle_scrollbar_click(obj, event):
                return True
                
        # 调用父类方法处理其他事件
        return super().eventFilter(obj, event)

    def save_current_account(self):
        """将当前使用的账户保存到账户列表中"""
        # 导入日志模块
        from logger import info, warning, error
        
        # 记录操作开始
        info("开始保存当前登录账户操作")
        
        try:
            # 从数据库重新获取当前登录邮箱，确保最新状态
            db_email = self.auth_manager.get_current_email()
            
            # 如果数据库中的邮箱与当前缓存的邮箱不一致（不区分大小写比较），记录并使用数据库中的邮箱
            if db_email is None and self.current_email is None:
                error("没有当前登录邮箱，无法保存")
                self.show_toast("没有当前登录邮箱，无法保存", error=True)
                return
            elif db_email is None:
                info(f"数据库中未找到邮箱信息，将继续使用当前缓存的邮箱: {self.current_email}")
            elif self.current_email is None:
                info(f"当前无缓存邮箱，使用数据库中的邮箱: {db_email}")
                self.current_email = db_email
            elif db_email.lower() != self.current_email.lower():
                info(f"数据库中的邮箱({db_email})与当前缓存的邮箱({self.current_email})不一致，将使用数据库中的邮箱")
                self.current_email = db_email
            else:
                # 邮箱内容相同但大小写可能不同，使用数据库中的格式
                if db_email != self.current_email:
                    info(f"数据库中的邮箱({db_email})与当前缓存的邮箱({self.current_email})大小写不同，将使用数据库中的格式")
                    self.current_email = db_email
                else:
                    info(f"数据库中的邮箱与当前缓存的邮箱完全一致: {self.current_email}")
            
            # 检查是否有当前账户
            if not self.current_email:
                error("没有选择当前账户，无法保存")
                self.show_toast("没有选择当前账户，无法保存", error=True)
                return
                
            info(f"准备保存当前登录账户: {self.current_email}")
                
            # 获取当前账户信息
            from datetime import datetime
            import json
            import os
            import platform
            import subprocess
            import sqlite3
                
            # 从系统获取必要信息
            current_system = platform.system()
            info(f"当前系统类型: {current_system}")
            
            # 确定系统类型
            system_type = ""
            if "windows" in current_system.lower():
                system_type = "windows"
            elif "darwin" in current_system.lower():
                system_type = "mac"
            else:
                system_type = "linux"
                
            info(f"系统类型标识: {system_type}")
                
            # 构建账户信息结构，只包含必要字段，使用数据库中的原始邮箱格式
            new_account_data = {
                "email": self.current_email,  # 使用数据库中的格式
                "auth_info": {
                    "cursorAuth/cachedSignUpType": "Auth_0",
                    "cursorAuth/cachedEmail": self.current_email,  # 使用数据库中的格式
                    "cursorAuth/accessToken": "",
                    "cursorAuth/refreshToken": ""
                },
                "machine_info": {},
                "system_type": system_type
            }
            
            # 检查是否已存在该账户，使用不区分大小写的比较
            existing_account = None
            existing_account_index = -1
            for i, account in enumerate(self.account_data.accounts):
                if account.get("email", "").lower() == self.current_email.lower():
                    existing_account = account
                    existing_account_index = i
                    break
            
            # 标记是否为新账户
            is_new_account = existing_account is None
            
            if existing_account:
                info(f"发现账户已存在(忽略大小写): {self.current_email}，询问是否更新")
                # 如果已存在，询问是否更新
                from utils import Utils
                if not Utils.confirm_message(
                    self,
                    "更新账户",
                    f"账户 {self.current_email} 已存在，是否更新？"
                ):
                    info(f"用户取消更新已存在账户: {self.current_email}")
                    return
                    
                info(f"用户确认更新账户: {self.current_email}")
                # 将新账户数据设置为现有账户的完整副本，但更新邮箱字段为数据库中的格式
                new_account_data = existing_account.copy()
                # 更新邮箱字段，确保使用数据库中的格式
                new_account_data["email"] = self.current_email
                if "auth_info" in new_account_data:
                    new_account_data["auth_info"]["cursorAuth/cachedEmail"] = self.current_email
            else:
                info(f"账户不存在，将创建新账户: {self.current_email}")
            
            # 从SQLite数据库获取token
            info("开始从SQLite数据库获取token信息")
            conn = None
            access_token = None
            refresh_token = None
            
            try:
                conn = sqlite3.connect(self.auth_manager.db_path)
                cursor = conn.cursor()
                
                info(f"成功连接到SQLite数据库: {self.auth_manager.db_path}")
                
                # 查询当前登录的accessToken
                cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/accessToken'")
                result = cursor.fetchone()
                
                if result is not None:
                    access_token = result[0]
                    token_length = len(access_token) if access_token else 0
                    info(f"成功从数据库获取accessToken，长度: {token_length}")
                    # 只更新auth_info中的accessToken
                    new_account_data["auth_info"]["cursorAuth/accessToken"] = access_token
                else:
                    warning("未能从数据库获取accessToken")
                
                # 查询refreshToken
                cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/refreshToken'")
                result = cursor.fetchone()
                
                if result is not None:
                    refresh_token = result[0]
                    token_length = len(refresh_token) if refresh_token else 0
                    info(f"成功从数据库获取refreshToken，长度: {token_length}")
                    # 只更新auth_info中的refreshToken
                    new_account_data["auth_info"]["cursorAuth/refreshToken"] = refresh_token
                else:
                    warning("未能从数据库获取refreshToken")
                    
            except sqlite3.Error as e:
                error_msg = str(e)
                error(f"获取token时数据库错误: {error_msg}")
                print(f"获取token时数据库错误: {error_msg}")
            finally:
                if conn:
                    conn.close()
                    info("关闭SQLite数据库连接")
            
            # 如果数据库获取失败，尝试从auth.json文件获取
            if not access_token or not refresh_token:
                info("数据库获取token不完整，尝试从auth.json文件获取")
                from utils import Utils
                cursor_cache_dir = Utils.get_cursor_cache_dir()
                if cursor_cache_dir and os.path.exists(cursor_cache_dir):
                    info(f"找到Cursor缓存目录: {cursor_cache_dir}")
                    # 获取auth.json中的token信息
                    token_file = os.path.join(cursor_cache_dir, "auth.json")
                    if os.path.exists(token_file):
                        info(f"找到auth.json文件: {token_file}")
                        try:
                            with open(token_file, "r", encoding="utf-8") as f:
                                auth_data = json.load(f)
                                info("成功读取auth.json文件内容")
                                
                            # 更新token信息
                            if "cursorAuth/accessToken" in auth_data:
                                token = auth_data["cursorAuth/accessToken"]
                                token_length = len(token) if token else 0
                                # 只更新auth_info中的accessToken
                                new_account_data["auth_info"]["cursorAuth/accessToken"] = token
                                info(f"从auth.json获取到accessToken，长度: {token_length}")
                                
                            if "cursorAuth/refreshToken" in auth_data:
                                token = auth_data["cursorAuth/refreshToken"]
                                token_length = len(token) if token else 0
                                # 只更新auth_info中的refreshToken
                                new_account_data["auth_info"]["cursorAuth/refreshToken"] = token
                                info(f"从auth.json获取到refreshToken，长度: {token_length}")
                        except Exception as e:
                            error_msg = str(e)
                            error(f"读取auth.json token信息时出错: {error_msg}")
                            print(f"读取token信息时出错: {error_msg}")
                else:
                    warning(f"未找到Cursor缓存目录，无法从auth.json获取token")
                
            # 准备一个临时字典来存储机器码信息，稍后会更新到主账户中
            machine_info_updates = {}
            
            # 获取telemetry和系统机器码信息
            info("开始获取telemetry和系统机器码信息")
            storage_path = ""
            if system_type == "windows":
                storage_path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "globalStorage", "storage.json")
            elif system_type == "mac":
                storage_path = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")
            elif system_type == "linux":
                storage_path = os.path.expanduser("~/.config/Cursor/User/globalStorage/storage.json")
            
            info(f"storage.json路径: {storage_path}")
            
            # 读取storage.json文件获取telemetry信息
            if os.path.exists(storage_path):
                info(f"找到storage.json文件，准备读取")
                try:
                    with open(storage_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)
                        info("成功读取storage.json文件内容")
                        
                        # 提取telemetry相关的机器码
                        machine_id = storage_data.get("telemetry.machineId", "")
                        if machine_id:
                            machine_info_updates["telemetry.machineId"] = machine_id
                            info(f"获取到telemetry.machineId: {machine_id[:8]}...")
                        else:
                            warning("未找到telemetry.machineId")
                            
                        mac_machine_id = storage_data.get("telemetry.macMachineId", "")
                        if mac_machine_id:
                            machine_info_updates["telemetry.macMachineId"] = mac_machine_id
                            info(f"获取到telemetry.macMachineId: {mac_machine_id[:8]}...")
                        else:
                            warning("未找到telemetry.macMachineId")
                            
                        dev_device_id = storage_data.get("telemetry.devDeviceId", "")
                        if dev_device_id:
                            machine_info_updates["telemetry.devDeviceId"] = dev_device_id
                            info(f"获取到telemetry.devDeviceId: {dev_device_id[:8]}...")
                        else:
                            warning("未找到telemetry.devDeviceId")
                            
                        sqm_id = storage_data.get("telemetry.sqmId", "")
                        if sqm_id:
                            machine_info_updates["telemetry.sqmId"] = sqm_id
                            info(f"获取到telemetry.sqmId: {sqm_id[:8]}...")
                        else:
                            warning("未找到telemetry.sqmId")
                except Exception as e:
                    error_msg = str(e)
                    error(f"读取storage.json失败: {error_msg}")
                    print(f"读取storage.json失败: {error_msg}")
            else:
                warning(f"未找到storage.json文件: {storage_path}")
            
            # 根据系统类型获取特定的机器码
            info(f"开始获取{system_type}系统特定的机器码")
            
            if system_type == "windows":
                info("尝试获取Windows MachineGuid")
                try:
                    import winreg
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography", 0, winreg.KEY_READ) as key:
                        machine_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
                        machine_info_updates["system.machineGuid"] = machine_guid
                        info(f"通过winreg API获取到Windows MachineGuid: {machine_guid}")
                except Exception as e:
                    error_msg = str(e)
                    warning(f"通过winreg API获取Windows MachineGuid失败: {error_msg}")
                    # 如果使用Python注册表API失败，尝试使用reg命令
                    try:
                        info("尝试使用reg命令获取Windows MachineGuid")
                        output = subprocess.check_output(
                            ['reg', 'query', 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography', '/v', 'MachineGuid'], 
                            universal_newlines=True
                        )
                        for line in output.splitlines():
                            if "MachineGuid" in line:
                                # 提取GUID值
                                machine_guid = line.split()[-1]
                                machine_info_updates["system.machineGuid"] = machine_guid
                                info(f"通过reg命令获取到Windows MachineGuid: {machine_guid}")
                    except Exception as e:
                        error_msg = str(e)
                        error(f"通过reg命令获取Windows MachineGuid也失败: {error_msg}")
                        print(f"获取Windows MachineGuid失败: {error_msg}")
            
            elif system_type == "linux":
                # 读取/etc/machine-id
                info("尝试读取Linux /etc/machine-id")
                try:
                    if os.path.exists("/etc/machine-id"):
                        with open("/etc/machine-id", 'r') as f:
                            machine_id = f.read().strip()
                            machine_info_updates["system.machineId"] = machine_id
                            info(f"获取到Linux machine-id: {machine_id}")
                    else:
                        warning("Linux系统上未找到 /etc/machine-id 文件")
                except Exception as e:
                    error_msg = str(e)
                    error(f"读取/etc/machine-id失败: {error_msg}")
                    print(f"读取/etc/machine-id失败: {error_msg}")
                
                # 读取/var/lib/dbus/machine-id
                info("尝试读取Linux /var/lib/dbus/machine-id")
                try:
                    if os.path.exists("/var/lib/dbus/machine-id"):
                        with open("/var/lib/dbus/machine-id", 'r') as f:
                            dbus_id = f.read().strip()
                            machine_info_updates["system.dbusId"] = dbus_id
                            info(f"获取到Linux dbus machine-id: {dbus_id}")
                    else:
                        warning("Linux系统上未找到 /var/lib/dbus/machine-id 文件")
                except Exception as e:
                    error_msg = str(e)
                    error(f"读取/var/lib/dbus/machine-id失败: {error_msg}")
                    print(f"读取/var/lib/dbus/machine-id失败: {error_msg}")
            
            elif system_type == "mac":
                # 尝试获取nvram SystemUUID
                info("尝试获取Mac nvram SystemUUID")
                try:
                    nvram_output = subprocess.check_output(
                        ["nvram", "SystemUUID"],
                        universal_newlines=True,
                        stderr=subprocess.DEVNULL
                    ).strip()
                    
                    if '\t' in nvram_output:  # 确保输出格式正确
                        nvram_uuid = nvram_output.split('\t')[-1]
                        machine_info_updates["system.nvramSystemUUID"] = nvram_uuid
                        info(f"获取到Mac nvram SystemUUID: {nvram_uuid}")
                    else:
                        warning(f"nvram命令输出格式不正确: {nvram_output}")
                except Exception as e:
                    error_msg = str(e)
                    error(f"读取Mac nvram SystemUUID失败: {error_msg}")
                    print(f"读取Mac nvram SystemUUID失败: {error_msg}")
            
            # 如果是新账户，创建machine_info字段
            if is_new_account:
                new_account_data["machine_info"] = machine_info_updates
            else:
                # 如果是已有账户，仅更新machine_info中的新值，不影响原有值
                if "machine_info" not in new_account_data:
                    new_account_data["machine_info"] = {}
                
                # 更新machine_info中的字段
                for key, value in machine_info_updates.items():
                    new_account_data["machine_info"][key] = value
                    
                info("已更新账户的机器码信息")
            
            # 如果是已存在的账户，更新它；否则添加到列表
            if is_new_account:
                # 将账户添加到列表开头，因为AccountData类加载时会反转列表顺序
                info(f"准备将新账户添加到账户列表: {self.current_email}")
                self.account_data.accounts.insert(0, new_account_data)
            else:
                # 更新现有账户
                info(f"更新账户在列表中的数据: {self.current_email}")
                self.account_data.accounts[existing_account_index] = new_account_data
            
            # 保存账户列表
            info("开始保存账户列表到文件")
            file_existed = os.path.exists(self.account_data.accounts_file)
            if self.account_data.save_accounts(allow_create=True):
                if not file_existed:
                    info(f"成功创建账户文件并保存账户: {self.current_email}")
                    self.show_toast(f"成功创建账户文件并保存账户 {self.current_email}")
                else:
                    operation_type = "更新" if not is_new_account else "保存"
                    info(f"账户 {self.current_email} 已成功{operation_type}")
                    self.show_toast(f"账户 {self.current_email} 已{operation_type}")
                # 设置标记，表示刚刚保存了账户
                self._last_saved_account = True
                
                # 记录保存的账户信息摘要
                token_saved = bool(new_account_data["auth_info"].get("cursorAuth/accessToken"))
                machine_codes_saved = len(new_account_data["machine_info"]) > 0
                info(f"账户保存结果摘要 - 邮箱: {self.current_email}, Token已保存: {token_saved}, 机器码已保存: {machine_codes_saved}, 系统类型: {system_type}")
                
            else:
                error(f"保存账户 {self.current_email} 到文件失败")
                self.show_toast(f"保存账户 {self.current_email} 失败", error=True)
                
            # 完成操作
            info("保存当前登录账户操作完成")
                
        except Exception as e:
            error_msg = str(e)
            error(f"保存当前账户时出错: {error_msg}")
            self.show_toast(f"保存当前账户时出错: {error_msg}", error=True)

    def refresh_all_data(self):
        """刷新所有数据，包括重新读取账户文件和获取额度信息"""
        try:
            # 显示正在刷新的提示
            # self.show_toast("正在刷新所有账户数据...")
            
            # 将首页标记为未初始化，需要重新加载
            self.is_home_initialized = False
            
            # 立即初始化首页数据，避免切换到首页时看到"加载中"状态
            self.load_current_account()
            
            # 标记首页已初始化
            self.is_home_initialized = True
            
            # 在重新加载前记录当前账户数量和最后一个账户
            current_account_count = len(self.account_data.accounts)
            last_account_before = None
            if current_account_count > 0:
                last_account_before = self.account_data.accounts[0]  # 因为列表已反转，第一个是最新的
                from logger import info
                info(f"刷新前的账户数量: {current_account_count}, 最新账户: {last_account_before.get('email', 'unknown')}")
            
            # 检查是否需要重新初始化AccountData，以应用新的文件路径设置
            if hasattr(self, '_reload_account_data') and self._reload_account_data:
                # 重新初始化AccountData实例
                self.account_data = AccountData()
                # 重置标志
                self._reload_account_data = False
                info("检测到JSON自定义路径可能已更改，已重新初始化AccountData实例")
                # 重新初始化后直接加载
                loaded_accounts = self.account_data.load_accounts()
            else:
                # 否则，仅重新从文件加载账户数据 (使用现有实例的文件路径)
                loaded_accounts = self.account_data.load_accounts()
            
            # 检查账户数量是否减少，可能表示新注册的账户丢失
            if current_account_count > 0 and len(loaded_accounts) < current_account_count and last_account_before:
                # 检查最新账户是否在加载的列表中
                last_email = last_account_before.get('email')
                found = False
                if last_email:
                    for account in loaded_accounts:
                        if account.get('email') == last_email:
                            found = True
                            break
                    
                    # 如果未找到最新账户，可能是加载出错，添加回列表
                    if not found:
                        from logger import warning
                        warning(f"加载数据丢失了最新账户: {last_email}，添加回列表")
                        loaded_accounts.insert(0, last_account_before)  # 添加到列表开头
            
            # 更新内存中的账户数据
            self.account_data.accounts = loaded_accounts
            
            # 加载完账户数据后，获取所有账户的额度信息，但不主动保存文件
            self.fetch_all_accounts_quota(is_manual_refresh=True)
        except Exception as e:
            print(f"刷新所有数据时出错: {str(e)}")
            self.show_toast("刷新数据时出错", error=True)

    def show_account_details(self, account_data):
        """显示账户详情对话框
        
        Args:
            account_data: 账户数据字典
        """
        dialog = AccountDetailsDialog(account_data, self, self) # 父窗口设为 self
        dialog.exec() # 使用模态执行
    
    def _create_copy_card(self, icon_text, icon_image, value, is_copyable=True, container_id=None, label=None):
        """创建统一样式的复制卡片
        
        Args:
            icon_text: 图标文本（当图片不可用时）
            icon_image: 图标图片路径
            value: 显示和复制的值
            is_copyable: 是否可复制
            container_id: 容器ID
            label: 标签文本
            
        Returns:
            QFrame: 创建的卡片
        """
        return create_copy_card(self, icon_text, icon_image, value, is_copyable, container_id, label)
        
    def _copy_text_to_clipboard(self, event, text, container, text_label=None, copy_icon=None):
        """复制文本到剪贴板并显示反馈
        
        Args:
            event: 鼠标事件
            text: 要复制的文本
            container: 容器组件
            text_label: 文本标签（可为None）
            copy_icon: 复制图标（可为None）
        """
        copy_text_to_clipboard(self, event, text, container, text_label, copy_icon)
    
    def _reset_copy_feedback(self, container, text_label, copy_icon):
        """重置复制反馈，恢复原始显示"""
        reset_copy_feedback(container, text_label, copy_icon)

    def show_batch_delete_dialog(self):
        """显示批量删除账户的对话框"""
        dialog = BatchDeleteDialog(self, self)
        dialog.exec()

    def show_import_accounts_dialog(self):
        """显示导入账户对话框"""
        from ui.dialogs.import_select_dialog import ImportSelectDialog
        dialog = ImportSelectDialog(self, self)
        dialog.exec()

    def update_account_quota(self, email, quota_data):
        """更新账户额度UI"""
        # 保存账户额度数据
        self.account_quotas[email] = quota_data
        
        # 更新账户数据中的注册时间
        start_of_month = quota_data.get("startOfMonth")
        if start_of_month:
            # 使用新方法更新账户信息，只更新临时文件中的配额数据
            self.account_data.update_account_info(
                email=email,
                register_time=start_of_month,
                quota_data=quota_data
            )
        
        # 将UI更新请求添加到UI更新线程的队列中
        self.ui_update_thread.add_update_request(email, quota_data)

    def create_about_page(self):
        """创建关于页面"""
        # 使用新的AboutPage类
        from widgets.pages.about_page import AboutPage # Corrected import path
        # Fetch data from VersionChecker instance
        about_data = self.version_checker.get_about_info()
        # Pass app_version and about_data to constructor
        return AboutPage(app_version=self.app_version, about_info=about_data)

    def _update_cursor_version_label(self, version_string):
        """使用新的版本信息更新光标版本标签"""
        if hasattr(self, 'cursor_version_label') and hasattr(self, 'app_version'):
            # 增加检查，确保实例属性不为 None
            if self.cursor_version_label is not None and self.app_version is not None:
                text = f"Cursor {version_string} | YCursor {self.app_version}"
                self.cursor_version_label.setText(text)
                info(f"状态栏版本更新为: {text}")
            else:
                # 提供更详细的警告
                warning_msg = "尝试更新版本标签失败，实例属性为 None:"
                if self.cursor_version_label is None:
                    warning_msg += " self.cursor_version_label is None."
                if self.app_version is None:
                    warning_msg += " self.app_version is None."
                warning(warning_msg)
        else:
            # 提供更详细的警告，指明哪个属性缺失
            warning_msg = "尝试更新版本标签失败，属性不存在:"
            if not hasattr(self, 'cursor_version_label'):
                 warning_msg += " self.cursor_version_label attribute missing."
            if not hasattr(self, 'app_version'):
                 warning_msg += " self.app_version attribute missing."
            warning(warning_msg)

    def is_cursor_version_known(self):
        """检查左下角标签是否显示已知 Cursor 版本号"""
        if hasattr(self, 'cursor_version_label') and self.cursor_version_label is not None:
            label_text = self.cursor_version_label.text()
            if "|" in label_text:
                cursor_part = label_text.split("|")[0].strip()
                # 检查版本部分是否包含数字，并且不包含"未知"
                # 使用更健壮的检查，例如查找版本号模式
                import re
                if re.search(r"\d+\.\d+\.\d+", cursor_part) and "未知" not in cursor_part:
                    return True
        return False

    def show_advanced_backup_page(self):
        self._push_page_history(self.content_stack.currentIndex())
        self.switch_page(self.ADVANCED_BACKUP_PAGE_INDEX)

    def _on_advanced_backup_back(self):
        self._go_back()

    def _push_page_history(self, index):
        if not self.page_history or self.page_history[-1] != index:
            self.page_history.append(index)
        self.page_forward_stack.clear()

    def _go_back(self):
        if self.page_history:
            prev_index = self.page_history.pop()
            self.page_forward_stack.append(self.content_stack.currentIndex())
            self.switch_page(prev_index)
        else:
            self.switch_page(2)  # 默认返回功能页面

    def _go_forward(self):
        if self.page_forward_stack:
            next_index = self.page_forward_stack.pop()
            self._push_page_history(self.content_stack.currentIndex())
            self.switch_page(next_index)

    def event(self, event):
        # 支持鼠标侧键前进/后退
        if event.type() == QEvent.Type.MouseButtonPress:
            if hasattr(event, 'button'):
                if event.button() == Qt.MouseButton.BackButton:
                    self._go_back()
                    return True
                elif event.button() == Qt.MouseButton.ForwardButton:
                    self._go_forward()
                    return True
        return super().event(event)


# QuotaFetcherManager类已移至core/quota_fetcher_manager.py

# 应用程序主函数
def main():
    """应用程序主入口点"""
    try:
        # 在Windows上启用高DPI支持 (已被移除，因为在Qt6中已弃用)

        # 确保正确处理命令行参数，以处理PyInstaller打包后的情况
        # 并修复macOS中QFileDialog等问题
        sys.argv = [arg for arg in sys.argv if not arg.startswith('-psn_')]

        # 创建QApplication必须放在开头，因为很多其他操作依赖它
        app = QApplication(sys.argv)
        
        # 优先初始化日志系统，确保后续操作可以记录日志
        from logger import logger, info, warning, error
        info("YCursor已启动")
        
        # 设置asyncio事件循环策略，解决Windows Python 3.11+兼容性问题
        from utils import setup_asyncio_event_loop_policy
        setup_asyncio_event_loop_policy()
        info(f"Python版本: {sys.version}")
        info(f"平台信息: {sys.platform}")
        if sys.platform == "win32" and sys.version_info >= (3, 11):
            info("已为Windows Python 3.11+配置asyncio事件循环策略")

        # ---- 进程锁检查 ----
        # 由于前面已经创建了QApplication并初始化了日志，所以这里可以直接进行锁检查
        try:
            # Import both the lock function and the new message box class
            from utils_pkg.process_lock import acquire_lock, ProcessLockMessageBox
            
            locked, pid = acquire_lock()
            if not locked:
                # 更专业的提示信息
                pid_info = f"（进程 ID: {pid}）" if pid else ""
                professional_message = (
                    f"\nYCursor 应用程序已经在运行啦{pid_info}"
                )

                # 显示错误消息并退出
                dialog = ProcessLockMessageBox(
                    None, # 没有父窗口
                    "应用程序已运行",
                    professional_message,
                    buttons=["退出"],
                    pid=pid  # 传递PID参数，用于"就要运行"按钮功能
                )
                result = dialog.exec()
                
                # 如果用户点击了"退出"按钮，则退出应用程序
                # 如果用户点击了"就要运行"按钮，则继续运行（通过对话框的accept/reject状态判断）
                if result == QDialog.DialogCode.Rejected:
                    sys.exit(1) # 用户点击了"退出"按钮，退出应用程序
                # 否则，继续正常运行程序

        except ImportError as import_err:
             print(f"ERROR: Failed to import process lock module: {import_err}. Single instance check skipped.", file=sys.stderr)
        except Exception as lock_err:
             print(f"ERROR: Failed during process lock check: {lock_err}. Single instance check skipped.", file=sys.stderr)
        # ---- 进程锁检查结束 ----

        # 设置多进程启动方法，避免冻结时的问题
        if sys.platform == 'darwin':  # macOS
            multiprocessing.set_start_method('spawn')

        # --- 设置应用程序图标 ---
        try:
            # 获取脚本所在目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            icons_dir = os.path.join(script_dir, 'icons')

            icon_path = ""
            if sys.platform == "win32":
                icon_path = os.path.join(icons_dir, "yan.ico")
            elif sys.platform == "darwin":
                icon_path = os.path.join(icons_dir, "yan32x32.icns") # Using .icns for macOS
            else:  # Linux and other platforms
                icon_path = os.path.join(icons_dir, "yan.png")

            if os.path.exists(icon_path):
                app_icon = QIcon(icon_path)
                app.setWindowIcon(app_icon)
                info(f"应用程序图标已设置为: {icon_path}")
            else:
                warning(f"图标文件未找到，无法设置应用程序图标: {icon_path}")
        except Exception as e:
            error(f"设置应用程序图标时出错: {str(e)}")
        # --- 图标设置结束 ---
            
        app.setQuitOnLastWindowClosed(True)
        
        # 应用暗色主题
        from utils import Utils
        Utils.set_dark_theme(app)
        
        # 现在创建VersionChecker实例并执行版本检查
        # 注意：这部分可能比较耗时，但至少前面的UI初始化已完成
        temp_checker = VersionChecker()
        if not temp_checker.check_version():
            return 1 # Exit if check fails
            
        # 创建主窗口
        manager = CursorAccountManager(version_checker_instance=temp_checker)
        manager.show()
        
        # 启动入场动画
        manager.run_entrance_animation()
        
        # 创建退出时的清理函数
        temp_file = None
        if hasattr(manager, 'account_data') and hasattr(manager.account_data, 'temp_file'):
            temp_file = manager.account_data.temp_file
            
        # 注册退出时的清理函数
        from utils import cleanup_on_exit
        app.aboutToQuit.connect(lambda: cleanup(manager))
        
        # 运行应用程序事件循环
        return_code = app.exec()
        
        # 返回应用程序的退出代码
        return return_code
        
    except Exception as e:
        error(f"应用程序运行时出错: {str(e)}")
        # 捕获并记录未处理的异常
        import traceback
        error_details = traceback.format_exc()
        error(f"异常详情: {error_details}")
        
        # 显示错误消息框
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        error_msg = QMessageBox()
        error_msg.setIcon(QMessageBox.Icon.Critical)
        error_msg.setWindowTitle("应用程序错误")
        error_msg.setText("YCursor发生了致命错误，请查看日志获取详细信息")
        error_msg.setDetailedText(error_details)
        error_msg.exec()
        
        return 1  # 返回错误退出代码


def cleanup(manager):
    """程序退出时的操作"""
    try:
        from logger import info
        info("YCursor正在关闭...")
        
        # 清理临时配额数据文件，不清理日志文件
        if hasattr(manager, 'account_data') and hasattr(manager.account_data, 'temp_file'):
            temp_file = manager.account_data.temp_file
            from utils import cleanup_on_exit
            cleanup_on_exit(temp_file)
        
        # 如果主窗口有清理方法，调用它释放资源
        if hasattr(manager, 'cleanup'):
            manager.cleanup()
        
        info("YCursor已关闭")
    except Exception as e:
        from logger import error
        error(f"程序退出记录时出错: {str(e)}")
        print(f"程序退出记录时出错: {str(e)}")


if __name__ == "__main__":
    # 直接调用main()函数，它会处理隐藏终端窗口和程序初始化
    main()