#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
工具函数模块 - 提供各种通用工具函数
"""

import os
import sys
import time
import inspect
import json
import base64
import tempfile
from pathlib import Path

def get_executable_dir():
    """
    获取程序真正的执行目录，而不是临时目录
    """
    # 尝试导入STARTUP_DIRECTORY
    try:
        # 首先检查是否已经导入了STARTUP_DIRECTORY
        try:
            # 从主模块导入
            from __main__ import STARTUP_DIRECTORY
            result = STARTUP_DIRECTORY
            # print(f"[utils] 从主模块获取启动目录: {result}")
            return result
        except ImportError:
            # 从cursor_pro_keep_alive导入
            try:
                from cursor_pro_keep_alive import STARTUP_DIRECTORY
                result = STARTUP_DIRECTORY
                # print(f"[utils] 从cursor_pro_keep_alive获取启动目录: {result}")
                return result
            except ImportError:
                # 如果无法导入，检查环境变量
                if 'REAL_STARTUP_DIRECTORY' in os.environ:
                    result = os.environ['REAL_STARTUP_DIRECTORY']
                    # print(f"[utils] 从环境变量获取启动目录: {result}")
                    return result
    except Exception as e:
        # print(f"[utils] 获取STARTUP_DIRECTORY失败: {e}")
        pass
    
    # 如果无法导入STARTUP_DIRECTORY，使用以下方法尝试获取真实路径
    result = ""
    
    # 方法1: 检查是否在冻结环境中
    if getattr(sys, 'frozen', False):
        # 如果是打包的可执行文件，使用sys.executable的目录
        result = os.path.dirname(sys.executable)
        # print(f"[utils] 从sys.executable获取路径: {result}")
    else:
        # 方法2: 使用当前文件的路径
        result = os.path.dirname(os.path.abspath(__file__))
        # print(f"[utils] 从当前文件获取路径: {result}")
    
    # 如果结果指向临时目录，尝试其他方法
    if any(temp in result.upper() for temp in ["TEMP", "TMP", "ONEFILE", "~1"]):
        # print(f"[utils] 检测到临时路径: {result}，尝试替代方法")
        
        # 方法3: 检查命令行参数
        if len(sys.argv) > 0 and os.path.exists(sys.argv[0]):
            path = os.path.dirname(os.path.abspath(sys.argv[0]))
            if not any(temp in path.upper() for temp in ["TEMP", "TMP", "ONEFILE", "~1"]):
                result = path
                # print(f"[utils] 从命令行参数获取路径: {result}")
        
        # 方法4: 使用当前工作目录
        if any(temp in result.upper() for temp in ["TEMP", "TMP", "ONEFILE", "~1"]):
            current_dir = os.getcwd()
            if not any(temp in current_dir.upper() for temp in ["TEMP", "TMP", "ONEFILE", "~1"]):
                result = current_dir
                # print(f"[utils] 使用当前工作目录: {result}")
    
    # 最后验证目录是否可写
    try:
        test_file = os.path.join(result, ".utils_test_write")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        # print(f"[utils] 已验证路径可写: {result}")
    except Exception as e:
        print(f"[警告] 路径不可写: {result}，使用用户主目录")
        # 回退到用户主目录
        result = os.path.expanduser("~")
        # print(f"[utils] 使用用户主目录: {result}")
    
    return result

def get_full_path(relative_path):
    """基于可执行文件目录获取完整路径"""
    return os.path.join(get_executable_dir(), relative_path)

def ensure_base_dir(true_original_dir=None):
    """确保工作目录设置为程序所在目录
    
    Args:
        true_original_dir: 可选，真正的原始工作目录，优先使用
    """
    try:
        # 如果提供了真正的原始目录，优先检查它
        if true_original_dir is not None:
            # 检查提供的原始目录是否为临时目录
            true_original_is_temp = any(temp in true_original_dir.upper() for temp in ["TEMP", "TMP", "~1"])
            if not true_original_is_temp:
                # 如果不是临时目录，直接使用它
                # 减少日志输出
                # print(f"[信息] 使用提供的非临时原始目录: {true_original_dir}")
                try:
                    os.chdir(true_original_dir)
                    # print(f"[信息] 已切换工作目录到真正的原始目录: {os.getcwd()}")
                    return
                except Exception as e:
                    print(f"[警告] 切换到提供的原始目录失败: {e}")
        
        # 如果没有提供原始目录或切换失败，继续默认逻辑
        # 获取原始工作目录（用于记录）
        original_cwd = os.getcwd()
        # print(f"[信息] 原始工作目录: {original_cwd}")
        
        # 获取可执行文件所在目录
        base_dir = get_executable_dir()
        # print(f"[信息] 程序所在目录: {base_dir}")
        
        # 移除调试信息
        # print(f"[调试] 原始路径类型: {type(original_cwd)}, 程序路径类型: {type(base_dir)}")
        
        # 统一路径格式和大小写，避免比较问题
        original_cwd_upper = original_cwd.upper()
        base_dir_upper = base_dir.upper()
        
        # 判断哪个路径是临时目录 - 更精确的检测
        temp_indicators = ["TEMP", "TMP", "ONEFILE", "~1"]
        
        # 移除详细的匹配信息
        # for indicator in temp_indicators:
        #     print(f"[调试] 检查原始目录是否包含'{indicator}': {indicator in original_cwd_upper}")
        #     print(f"[调试] 检查程序目录是否包含'{indicator}': {indicator in base_dir_upper}")
        
        # 重新实现检测逻辑，更精确地检测
        original_is_temp = any(temp in original_cwd_upper for temp in temp_indicators)
        base_is_temp = any(temp in base_dir_upper for temp in temp_indicators)
        
        # print(f"[调试] 原始目录是临时目录: {original_is_temp}")
        # print(f"[调试] 程序目录是临时目录: {base_is_temp}")
        
        # 检查真正的工作目录D:3是否直接被传递进来
        current_cwd = os.getcwd().upper()
        if "D:\\3" in current_cwd and "TEMP" not in current_cwd and "TMP" not in current_cwd:
            # print(f"[信息] 当前目录是非临时目录 {os.getcwd()}，保持不变")
            return
        
        # 强制执行分支 - 如果程序在临时目录，原始目录在非临时目录
        if ("TEMP" in base_dir_upper or "TMP" in base_dir_upper or "~1" in base_dir_upper) and \
           not ("TEMP" in original_cwd_upper or "TMP" in original_cwd_upper):
            # print(f"[信息] 检测到程序在临时目录，强制保留原始工作目录: {original_cwd}")
            
            # 不检查可写性，直接尝试切换
            try:
                # 如果当前目录不是原始目录，切换回原始目录
                if os.getcwd() != original_cwd:
                    os.chdir(original_cwd)
                    # print(f"[信息] 已强制切换回原始工作目录: {os.getcwd()}")
                # else:
                    # print(f"[信息] 已在原始工作目录中: {os.getcwd()}")
                return
            except Exception as e:
                print(f"[警告] 强制切换到原始工作目录失败: {e}")
        
        # 保留原始的条件判断逻辑作为备份
        if base_is_temp and not original_is_temp:
            # print(f"[信息] 检测到程序在临时目录，保留原始工作目录: {original_cwd}")
            # 检查原始目录是否可写
            try:
                test_file = os.path.join(original_cwd, ".test_write")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                # print(f"[信息] 原始工作目录可写，继续使用: {original_cwd}")
                
                # 重要：如果已经在临时目录中，需要切换回原始目录
                if os.getcwd() != original_cwd:
                    os.chdir(original_cwd)
                    # print(f"[信息] 已切换回原始工作目录: {os.getcwd()}")
                return
            except Exception as e:
                print(f"[警告] 原始工作目录不可写: {e}")
                # 如果原始目录不可写，会继续使用程序所在目录
        
        # 如果原始目录是临时目录，而程序所在目录不是，则切换到程序所在目录
        if original_is_temp and not base_is_temp:
            # print(f"[信息] 检测到在临时目录中运行，切换到程序所在目录: {base_dir}")
            if os.path.exists(base_dir):
                os.chdir(base_dir)
                # print(f"[信息] 已切换工作目录到: {os.getcwd()}")
            return
            
        # 如果两个都不是临时目录，优先使用原始工作目录
        if not original_is_temp and not base_is_temp:
            # print(f"[信息] 保持原始工作目录: {original_cwd}")
            return
            
        # 如果两个都是临时目录，使用程序所在目录（可能包含更多依赖文件）
        if original_is_temp and base_is_temp:
            # print(f"[信息] 两个目录都是临时目录，使用程序所在目录: {base_dir}")
            if os.path.exists(base_dir) and original_cwd != base_dir:
                os.chdir(base_dir)
                # print(f"[信息] 已切换工作目录到: {os.getcwd()}")
            return
            
        # 所有条件都不满足时的默认行为 - 强制切换回原始目录
        # print(f"[信息] 条件判断未命中，强制切换回原始工作目录: {original_cwd}")
        try:
            os.chdir(original_cwd)
            # print(f"[信息] 已切换回原始工作目录: {os.getcwd()}")
        except Exception as e:
            print(f"[警告] 强制切换到原始工作目录失败: {e}")
    except Exception as e:
        print(f"[警告] 切换工作目录失败: {e}")

# 提供文件路径操作的工具函数
def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
    return directory

def is_running_from_temp():
    """检查是否从临时目录运行"""
    current_path = os.getcwd().lower()
    temp_indicators = ['temp', 'tmp', 'cache', '缓存']
    
    for indicator in temp_indicators:
        if indicator in current_path:
            return True
    
    return False

# 用于macOS系统的管理员权限检查和提升
def is_admin_mac():
    """检查macOS系统是否有管理员权限"""
    try:
        return os.geteuid() == 0
    except:
        return False

def check_and_elevate_privileges_mac():
    """在macOS上检查并提升权限"""
    # 如果已经是管理员，直接返回
    if is_admin_mac():
        # print("已获得管理员权限")
        return True
    
    print("需要管理员权限，正在提升...")
    
    # 获取当前脚本路径
    script = os.path.abspath(sys.argv[0])
    script_dir = os.path.dirname(script)
    
    # 创建临时AppleScript文件
    temp_script_path = os.path.join(script_dir, "elevate_temp.scpt")
    
    try:
        # 使用固定的系统Python路径而非sys.executable
        # 确保使用的是系统Python而不是临时目录中的Python
        system_python = "/usr/bin/python3"
        if not os.path.exists(system_python):
            system_python = "/usr/bin/python"
        if not os.path.exists(system_python):
            # 尝试使用环境变量中的Python
            import subprocess
            try:
                system_python = subprocess.check_output("which python3", shell=True).decode().strip()
            except:
                try:
                    system_python = subprocess.check_output("which python", shell=True).decode().strip()
                except:
                    system_python = "python3"  # 回退到PATH中的Python
        
        print(f"[信息] 使用系统Python: {system_python}")
        
        # 创建AppleScript内容 - 直接使用do shell script执行命令
        applescript_content = """
tell application "Terminal"
    do shell script "cd \\\"" & "%s" & "\\\" && %s \\\"" & "%s" & "\\\"" with administrator privileges
end tell
""" % (script_dir.replace('"', '\\\\"'), system_python, script.replace('"', '\\\\"'))
        
        # 写入临时AppleScript文件
        with open(temp_script_path, 'w') as f:
            f.write(applescript_content)
        
        # 使用osascript执行AppleScript
        print(f"执行权限提升脚本: {temp_script_path}")
        print(f"执行命令: cd \"{script_dir}\" && {system_python} \"{script}\"")
        os.system(f"osascript {temp_script_path}")
        
        # 删除临时脚本文件
        if os.path.exists(temp_script_path):
            os.remove(temp_script_path)
        
        # 当前进程不再需要继续运行
        sys.exit(0)
    except Exception as e:
        print(f"权限提升失败: {e}")
        
        # 尝试备用方法 - 使用shell脚本
        try:
            # 创建临时shell脚本文件
            temp_sh_path = os.path.join(script_dir, "elevate_temp.sh")
            with open(temp_sh_path, 'w') as f:
                f.write(f"""#!/bin/bash
cd "{script_dir}" || exit 1
echo "请输入管理员密码以获取权限..."
sudo "{script}"
""")
            
            # 添加执行权限
            os.chmod(temp_sh_path, 0o755)
            
            # 打开终端并执行脚本
            print(f"尝试使用备用方法: {temp_sh_path}")
            os.system(f"open -a Terminal {temp_sh_path}")
            
            # 删除临时脚本文件
            time.sleep(1)  # 给一点时间启动Terminal
            if os.path.exists(temp_sh_path):
                os.remove(temp_sh_path)
        except Exception as e2:
            print(f"备用权限提升方法也失败: {e2}")
        
        return False 