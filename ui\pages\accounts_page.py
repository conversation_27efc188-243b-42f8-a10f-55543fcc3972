"""
账户管理页面模块
提供应用程序账户管理页面的用户界面
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, 
    QPushButton, QScrollArea, QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy, QComboBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor

# 导入主题
from theme import Theme

# 导入自定义组件
from widgets.styled_widgets import StyledFrame
from widgets.animated_widgets import AnimatedProgressBar

class AccountsPage(QWidget):
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self._setup_ui()

    def _setup_ui(self):
        """设置账户管理页面UI"""
        self.setStyleSheet("background: transparent;")  # 设置整个页面背景为透明
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题行
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(20)
        
        # 账户总数统计
        self.main_window.accounts_count_label = QLabel("加载中...")
        self.main_window.accounts_count_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            margin-bottom: 10px;
        """)
        title_layout.addWidget(self.main_window.accounts_count_label)
        title_layout.addStretch()
        
        # 添加批量删除按钮
        batch_delete_btn = QPushButton("批量删除")
        batch_delete_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        batch_delete_btn.setStyleSheet(f"""
            QPushButton {{
                color: {Theme.TEXT_PRIMARY};
                background-color: {Theme.CARD_LEVEL_2};
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                background-color: {Theme.ERROR};
                color: white;
            }}
            QPushButton:pressed {{
                background-color: #A8414E;
                color: white;
            }}
        """)
        batch_delete_btn.clicked.connect(self.main_window.show_batch_delete_dialog)
        title_layout.addWidget(batch_delete_btn)
        
        # 添加导入账户按钮
        import_btn = QPushButton("导入账户")
        import_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        import_btn.setStyleSheet(f"""
            QPushButton {{
                color: {Theme.TEXT_PRIMARY};
                background-color: {Theme.CARD_LEVEL_2};
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT};
                color: white;
            }}
            QPushButton:pressed {{
                background-color: #249070;
                color: white;
            }}
        """)
        import_btn.clicked.connect(self.main_window.show_import_accounts_dialog)
        title_layout.addWidget(import_btn)
        
        # 创建刷新和删除按钮
        refresh_btn = QPushButton("刷新状态")
        refresh_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        refresh_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: {Theme.TEXT_PRIMARY};
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        
        # 连接刷新按钮到获取所有账户额度的方法
        refresh_btn.clicked.connect(lambda: self.main_window.refresh_all_data())
        title_layout.addWidget(refresh_btn)
        
        main_layout.addLayout(title_layout)
        
        # 添加分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        line.setStyleSheet(f"""
            color: {Theme.BORDER};
            background-color: {Theme.BORDER};
        """)
        main_layout.addWidget(line)
        
        # 创建列表标题
        header_frame = QFrame()
        header_frame.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 0px;
            margin: 4px 2px;
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 12, 15, 12)
        header_layout.setSpacing(5)  # 减少组件间的间距，与行组件一致
        
        # 左侧 - 账户信息区域
        left_header = QWidget()
        left_header.setStyleSheet("background: transparent;")
        left_layout = QVBoxLayout(left_header)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(6)
        
        email_header = QLabel("账户信息")
        email_header.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY}; 
            font-weight: bold;
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        left_layout.addWidget(email_header)
        
        # 中间部分 - 包含剩余天数和进度条
        center_header = QWidget()
        center_header.setStyleSheet("background: transparent;")
        center_layout = QVBoxLayout(center_header)
        center_layout.setContentsMargins(0, 0, 0, 0)
        center_layout.setSpacing(6)
        
        status_header = QLabel("账户状态")
        status_header.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY}; 
                font-weight: bold; 
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        center_layout.addWidget(status_header)
        
        # 右侧 - 操作按钮
        right_header = QWidget()
        right_header.setStyleSheet("background: transparent;")
        right_layout = QHBoxLayout(right_header)
        right_layout.setContentsMargins(5, 0, 0, 0)  # 减少左侧边距为5像素
        right_layout.setSpacing(5)  # 减少间距
        right_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)  # 左对齐
        
        actions_header = QLabel("操作")
        actions_header.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY}; 
            font-weight: bold;
            font-size: {Theme.FONT_SIZE_NORMAL};
            padding-left: 0px;
            text-align: left;
        """)
        actions_header.setAlignment(Qt.AlignmentFlag.AlignLeft)  # 设置文本左对齐
        right_layout.addWidget(actions_header)
        
        # 添加到主布局，使用与行组件相同的比例
        header_layout.addWidget(left_header, 3)
        header_layout.addWidget(center_header, 3)
        header_layout.addWidget(right_header, 2)
        
        main_layout.addWidget(header_frame)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollArea > QWidget {{
                background: transparent;
            }}
            QScrollBar:vertical {{
                border: none;
                background: transparent;
                width: 12px;
                margin: 2px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #282C36;
                min-height: 30px;
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
                background: none;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
            }}
            QScrollBar:horizontal {{
                border: none;
                background: transparent;
                height: 12px;
                margin: 2px;
            }}
            QScrollBar::handle:horizontal {{
                background-color: #282C36;
                min-width: 30px;
                border-radius: 6px;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                width: 0px;
                background: none;
            }}
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                background: none;
            }}
        """)
        
        # 创建账户列表区域
        self.main_window.accounts_container = QWidget()
        self.main_window.accounts_container.setStyleSheet(f"""
            background-color: transparent;
            border: none;
        """)
        self.main_window.accounts_layout = QVBoxLayout(self.main_window.accounts_container)
        self.main_window.accounts_layout.setContentsMargins(0, 0, 0, 0)
        self.main_window.accounts_layout.setSpacing(10)
        self.main_window.accounts_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        # 创建加载指示器容器
        self.main_window.loading_container = QWidget()
        self.main_window.loading_container.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_1};
            border-radius: {Theme.BORDER_RADIUS};
            padding: 30px;
            margin: 20px 0px;
        """)
        loading_layout = QVBoxLayout(self.main_window.loading_container)
        loading_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 创建加载中标签
        loading_label = QLabel("正在获取账户配额数据...")
        loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        loading_label.setStyleSheet(f"""
            color: {Theme.ACCENT};
            font-size: {Theme.FONT_SIZE_TITLE};
            font-weight: bold;
            margin-bottom: 15px;
        """)
        loading_layout.addWidget(loading_label)
        
        # 创建加载进度条
        self.main_window.loading_progress = AnimatedProgressBar()
        self.main_window.loading_progress.setRange(0, 100)
        self.main_window.loading_progress.setValue(1)  # 设置为1而不是0，确保有基本样式显示
        self.main_window.loading_progress.setFormat("正在加载中")  # 初始状态显示"正在加载中"
        self.main_window.loading_progress.setMinimumWidth(300)
        self.main_window.loading_progress.setMaximumWidth(500)
        self.main_window.loading_progress.setMinimumHeight(25)  # 增加高度以容纳文字
        self.main_window.loading_progress.setMaximumHeight(25)  # 增加高度以容纳文字
        self.main_window.loading_progress.setTextVisible(True)  # 启用文字显示
        self.main_window.loading_progress.setStyleSheet(f"""
            QProgressBar {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: 5px;
                text-align: center;
                color: {Theme.TEXT_PRIMARY};
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_SMALL};
                border: 2px solid {Theme.SUCCESS};
                margin: 2px;
                padding: 1px;
            }}
            QProgressBar::chunk {{
                background-color: {Theme.SUCCESS};
                border-radius: 3px;
                min-width: 5px;
            }}
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect(self.main_window.loading_progress)
        shadow.setOffset(0, 0)
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 80))  # 半透明黑色阴影
        self.main_window.loading_progress.setGraphicsEffect(shadow)
        
        # 添加说明文本
        info_label = QLabel("获取完成后将自动按照账户注册时间进行排序")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: {Theme.FONT_SIZE_SMALL};
            margin-top: 10px;
        """)
        loading_layout.addWidget(self.main_window.loading_progress)
        loading_layout.addWidget(info_label)
        
        # 添加到账户列表布局
        self.main_window.accounts_layout.addWidget(self.main_window.loading_container)
        
        # 初始化时根据账户数据情况决定是否显示加载指示器
        if not self.main_window.account_data.accounts:
            # 如果没有账户数据，立即隐藏加载指示器
            self.main_window.loading_container.setVisible(False)
        else:
            # 有账户数据，显示加载指示器
            self.main_window.loading_container.setVisible(True)
        
        scroll_area.setWidget(self.main_window.accounts_container)
        main_layout.addWidget(scroll_area, 1) 