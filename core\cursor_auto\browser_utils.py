from DrissionPage import ChromiumOptions, Chromium
import sys
import os
from colorama import Fore, Style
from dotenv import load_dotenv
import random
import platform
import string
from typing import List, Dict
import time
import json
import tempfile
import traceback

# 获取当前文件所在的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 指定.env文件路径
dotenv_path = os.path.join(current_dir, ".env")
# 只加载指定路径的.env文件
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path)

# 颜色定义
class Colors:
    """统一的颜色定义"""
    RED = ''      # 错误信息
    GREEN = ''    # 成功信息
    YELLOW = ''   # 警告/提示信息
    BLUE = ''     # 框架/标题
    PURPLE = ''   # 重要数据
    CYAN = ''     # 进度信息
    WHITE = ''    # 普通文本
    NC = ''       # 结束颜色

def print_box(title="", content=None, footer=None):
    """打印消息，不带框"""
    # 在开始前添加一个空行
    print()
    
    # 如果只有标题，直接显示标题
    if title and not content and not footer:
        print(title)
        return
    
    # 显示标题
    if title:
        print(title)
    
    # 显示内容
    if content:
        if isinstance(content, str):
            if content.strip():
                print(content)
        elif isinstance(content, list):
            for line in content:
                print(line)
    
    # 显示页脚
    if footer:
        print(footer)

class BrowserManager:
    def __init__(self):
        self.browser = None
        self.user_agent = None
        # 保存生成的指纹数据以便验证
        self.current_fingerprint = None

    def _generate_random_hardware_info(self) -> Dict[str, any]:
        """生成随机的硬件信息"""
        return {
            'hardwareConcurrency': random.choice([2, 4, 6, 8, 12, 16]),
            'deviceMemory': random.choice([2, 4, 8, 16, 32]),
            'devicePixelRatio': random.choice([1, 1.25, 1.5, 2, 2.5, 3]),
            'screenResolution': random.choice([
                [1366, 768],
                [1920, 1080],
                [2560, 1440],
                [3840, 2160]
            ]),
            'colorDepth': random.choice([24, 30, 32])
        }

    def _generate_random_browser_info(self) -> Dict[str, any]:
        """生成随机的浏览器信息"""
        # 修改时区为美国、新加坡、日本、韩国，移除中国地区
        # 对应时区和语言进行匹配
        region_settings = [
            # 美国
            {
                'timezone': 'America/New_York',  # 美国东部时间
                'languages': ['en-US', 'en-GB'],
                'geo': 'US'
            },
            {
                'timezone': 'America/Los_Angeles',  # 美国西部时间
                'languages': ['en-US', 'en-GB'],
                'geo': 'US'
            },
            {
                'timezone': 'America/Chicago',  # 美国中部时间
                'languages': ['en-US', 'en-GB'],
                'geo': 'US'
            },
            # 日本
            {
                'timezone': 'Asia/Tokyo',
                'languages': ['ja-JP', 'en-US'],
                'geo': 'JP'
            },
            # 韩国
            {
                'timezone': 'Asia/Seoul',
                'languages': ['ko-KR', 'en-US'],
                'geo': 'KR'
            },
            # 新加坡
            {
                'timezone': 'Asia/Singapore',
                'languages': ['en-SG', 'zh-SG', 'en-US'],
                'geo': 'SG'
            }
        ]

        # 随机选择一个地区设置
        selected_region = random.choice(region_settings)
        
        return {
            'doNotTrack': random.choice(['1', '0', 'unspecified']),
            'timezone': selected_region['timezone'],
            'languages': selected_region['languages'],
            'geo': selected_region['geo'],  # 增加地理位置标识，用于后续扩展
            'plugins': random.randint(0, 5),
            'maxTouchPoints': random.choice([0, 1, 2, 5, 10])
        }

    def _generate_random_media_info(self) -> Dict[str, any]:
        """生成随机的媒体信息"""
        codecs = {
            'audio': ['aac', 'mp3', 'opus', 'vorbis'],
            'video': ['h264', 'vp8', 'vp9', 'av1']
        }
        return {
            'audioCodecs': random.sample(codecs['audio'], random.randint(2, 4)),
            'videoCodecs': random.sample(codecs['video'], random.randint(2, 4)),
            'webrtcEnabled': random.random() > 0.1,  # 90%概率启用
            'audioContext': {
                'sampleRate': random.choice([44100, 48000, 96000]),
                'channelCount': random.choice([2, 4, 6, 8])
            }
        }

    def _generate_random_feature_info(self) -> Dict[str, any]:
        """生成随机的功能特征信息"""
        return {
            'battery': random.random() > 0.2,  # 80%概率启用
            'bluetooth': random.random() > 0.3,  # 70%概率启用
            'gamepad': random.random() > 0.8,   # 20%概率启用
            'storage': {
                'quota': random.randint(100, 1000) * 1024 * 1024,
                'persistent': random.random() > 0.3
            }
        }

    def _generate_random_webgl_vendor(self) -> str:
        """生成随机的 WebGL 供应商信息"""
        # 使用更真实的显卡供应商和型号组合
        vendors = [
            'Google Inc. (NVIDIA)',
            'Google Inc. (Intel)',
            'Google Inc. (AMD)',
            'Intel Inc. (Intel(R) UHD Graphics)',
            'NVIDIA Corporation (NVIDIA GeForce GTX)',
            'NVIDIA Corporation (NVIDIA RTX)',
            'AMD (AMD Radeon Graphics)',
            'Apple Inc. (Apple M1)',
            'Apple Inc. (Apple M2)'
        ]
        return random.choice(vendors)

    def _generate_random_platform(self) -> str:
        """生成随机的平台信息"""
        system = platform.system().lower()
        if system == 'windows':
            # Windows 平台更倾向于使用 Win64
            return random.choice(['Win64', 'Win64', 'Win64', 'Win32'])
        elif system == 'darwin':
            # macOS 平台主要使用 MacIntel 和 MacM1
            return random.choice(['MacIntel', 'MacIntel', 'MacM1', 'MacM2'])
        else:
            # Linux 平台主要使用 x86_64
            return 'Linux x86_64'

    def get_user_agent(self):
        """获取user_agent - 根据模式选择不同的UA获取策略"""
        # 判断是否处于无头模式
        is_headless = os.getenv('BROWSER_HEADLESS', 'False').lower() == 'true'
        
        if is_headless:
            # 无头模式下，强制更新并优先从系统获取真实Chrome UA 
            print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[UA信息]{Colors.NC} 无头模式下强制更新系统UA缓存...")
            real_ua = self._get_real_chrome_ua_from_system(force_update=True, no_browser=True)
            if real_ua:
                self.user_agent = real_ua
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[UA信息]{Colors.NC} 无头模式下使用更新后的系统Chrome真实UA")
                return real_ua
            
            # 如果无法从系统获取UA，尝试从环境变量获取
            env_ua = self._get_latest_chrome_ua()
            if env_ua:
                self.user_agent = env_ua
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[UA信息]{Colors.NC} 无头模式下使用环境变量配置的UA")
                return env_ua
            
            # 如果环境变量也未配置，抛出异常
            raise ValueError("无法自动获取真实UA，请在设置页面 > 自动注册配置里设置 浏览器User-Agent")
        else:
            # 非无头模式下，优先使用 DrissionPage 的方式从浏览器获取UA
            v6_ua = self._get_ua_from_browser_v6()
            if v6_ua:
                return v6_ua
                
            # 如果  DrissionPage  方式失败，尝试从系统获取
            real_ua = self._get_real_chrome_ua_from_system()
            if real_ua:
                self.user_agent = real_ua
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[UA信息]{Colors.NC} 使用系统Chrome真实UA作为备选")
                return real_ua
                
            # 如果从系统获取失败，尝试使用当前版本的方式从浏览器获取UA
            try:
                # 使用简化的浏览器设置，加快启动速度
                self._get_fast_ua_from_browser()
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[UA信息]{Colors.NC} 非无头模式下使用浏览器真实UA")
                return self.user_agent
            except Exception as e:
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.YELLOW}[UA信息]{Colors.NC} 浏览器获取UA失败: {str(e)}")
                
            # 所有方法都失败，尝试从环境变量获取
            env_ua = self._get_latest_chrome_ua()
            if env_ua:
                self.user_agent = env_ua
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[UA信息]{Colors.NC} 使用环境变量配置的UA")
                return env_ua
            
            # 如果环境变量也未配置，抛出异常
            raise ValueError("无法自动获取真实UA，请在设置页面 > 自动注册配置里设置 浏览器User-Agent")

    def _get_fast_ua_from_browser(self):
        """从浏览器快速获取UA (优化版)"""
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        import time
        
        # 判断是否处于无头模式
        is_headless = os.getenv('BROWSER_HEADLESS', 'False').lower() == 'true'
        
        print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[UA信息]{Colors.NC} 使用{'轻量级' if is_headless else '真实'}浏览器获取UA...")
        start_time = time.time()
        
        # 创建极简配置的Chrome选项
        options = Options()
        
        # 只在无头模式下添加 headless 参数
        if is_headless:
            options.add_argument('--headless=new')
            
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-logging')
        options.add_argument('--log-level=3')
        options.add_argument('--disable-infobars')
        options.add_argument('--window-size=800,600')  # 最小窗口
        
        # 尝试使用selenium直接获取UA，速度更快
        try:
            # 快速打开浏览器
            browser = webdriver.Chrome(options=options)
            
            # 获取UA
            user_agent = browser.execute_script("return navigator.userAgent")
            
            # 立即关闭浏览器
            browser.quit()
            
            # 只在无头模式下剔除"HeadlessChrome"
            if is_headless:
                user_agent = user_agent.replace("HeadlessChrome", "Chrome")
            
            end_time = time.time()
            print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[UA信息]{Colors.NC} 快速获取UA成功 ({(end_time-start_time):.2f}秒)")
            
            # 保存结果到实例变量
            self.user_agent = user_agent
            return user_agent
            
        except Exception as e:
            print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.RED}[UA信息]{Colors.NC} 浏览器获取UA失败: {str(e)}")
            raise

    def _get_real_chrome_ua_from_system(self, force_update=False, no_browser=False):
        """
        从系统安装的Chrome获取真实UA (优化版，更可靠)
        :param force_update: 是否强制更新缓存
        :param no_browser: 是否禁止打开浏览器获取版本
        """
        try:
            import subprocess
            import re
            import json
            
            system = platform.system()
            
            # 方法0：优先从缓存文件获取（除非强制更新）
            cache_path = os.path.join(os.path.expanduser("~"), ".chrome_ua_cache")
            if not force_update and os.path.exists(cache_path) and os.path.getsize(cache_path) > 0:
                try:
                    with open(cache_path, "r") as f:
                        cache_data = json.load(f)
                        # 确认缓存不超过7天
                        if time.time() - cache_data.get("timestamp", 0) < 7 * 24 * 3600:
                            return cache_data.get("user_agent")
                except:
                    pass  # 忽略缓存读取错误
            
            # 方法1: 使用特定文件路径
            chrome_paths = []
            if system == "Windows":
                chrome_paths = [
                    os.path.join(os.environ.get('PROGRAMFILES', ''), 'Google', 'Chrome', 'Application', 'chrome.exe'),
                    os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), 'Google', 'Chrome', 'Application', 'chrome.exe'),
                    os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Google', 'Chrome', 'Application', 'chrome.exe')
                ]
            elif system == "Darwin":  # macOS
                chrome_paths = [
                    '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
                    os.path.expanduser('~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome')
                ]
            else:  # Linux
                chrome_paths = [
                    '/usr/bin/google-chrome',
                    '/usr/bin/chrome',
                    '/usr/bin/chromium',
                    '/usr/bin/chromium-browser'
                ]
                
            # 查找可用的Chrome路径
            chrome_path = None
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_path = path
                    break
            
            # 获取Chrome版本信息
            chrome_version = None
            
            # 方法2: 使用"chrome://version"信息获取 (Windows特有，通过powershell获取)
            if not chrome_version and system == "Windows":
                try:
                    # 使用PowerShell获取Chrome版本
                    cmd = ['powershell', '-command', 
                           "(Get-Item (Get-ItemProperty 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\App Paths\\chrome.exe').'(Default)').VersionInfo.ProductVersion"]
                    result = subprocess.check_output(cmd, stderr=subprocess.STDOUT, text=True, timeout=2)
                    chrome_version = result.strip()
                except:
                    pass
            
            # 方法2.1: macOS特有 - 从Info.plist获取版本
            if not chrome_version and system == "Darwin" and chrome_path:
                try:
                    # 从Info.plist文件获取版本
                    info_plist_path = '/Applications/Google Chrome.app/Contents/Info.plist'
                    if os.path.exists(info_plist_path):
                        cmd = ['defaults', 'read', info_plist_path, 'CFBundleShortVersionString']
                        result = subprocess.check_output(cmd, stderr=subprocess.STDOUT, text=True, timeout=2)
                        chrome_version = result.strip()
                except:
                    pass
                    
            # 方法2.2: macOS特有 - 使用mdls获取版本
            if not chrome_version and system == "Darwin":
                try:
                    cmd = ['mdls', '-name', 'kMDItemVersion', '/Applications/Google Chrome.app']
                    result = subprocess.check_output(cmd, stderr=subprocess.STDOUT, text=True, timeout=2)
                    version_match = re.search(r'kMDItemVersion = "([^"]+)"', result.strip())
                    if version_match:
                        chrome_version = version_match.group(1)
                except:
                    pass
            
            # 方法3: 使用--version参数 (可能会闪现浏览器窗口)
            if not chrome_version and chrome_path and not no_browser:
                try:
                    cmd = [chrome_path, '--version']
                    result = subprocess.check_output(cmd, stderr=subprocess.STDOUT, text=True, timeout=2)
                    version_match = re.search(r'(\d+\.\d+\.\d+\.\d+)|(\d+\.\d+\.\d+)', result.strip())
                    if version_match:
                        chrome_version = version_match.group(0)
                except:
                    pass
            
            # 方法4: 从注册表获取 (Windows特有)
            if not chrome_version and system == "Windows":
                try:
                    import winreg
                    with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r'Software\Google\Chrome\BLBeacon') as key:
                        chrome_version = winreg.QueryValueEx(key, 'version')[0]
                except:
                    pass
                    
            # 方法5: 获取本地已安装应用列表 (Windows特有)
            if not chrome_version and system == "Windows":
                try:
                    cmd = ['wmic', 'product', 'where', "name like '%Google Chrome%'", 'get', 'version']
                    result = subprocess.check_output(cmd, stderr=subprocess.STDOUT, text=True, timeout=3)
                    lines = [line.strip() for line in result.split('\n') if line.strip()]
                    if len(lines) > 1:  # 第一行是'Version'标题
                        chrome_version = lines[1]
                except:
                    pass
            
            # 方法5.1: macOS特有 - 使用system_profiler获取应用信息
            if not chrome_version and system == "Darwin":
                try:
                    cmd = ['system_profiler', 'SPApplicationsDataType', '-xml']
                    result = subprocess.check_output(cmd, stderr=subprocess.STDOUT, text=True, timeout=5)
                    
                    # 在XML输出中查找Chrome版本
                    import plistlib
                    from io import BytesIO
                    
                    plist_data = plistlib.loads(result.encode('utf-8'))
                    for app_dict in plist_data[0]['_items']:
                        if 'Google Chrome' in app_dict.get('_name', ''):
                            chrome_version = app_dict.get('version', '')
                            break
                except:
                    pass
                    
            # 如果找不到版本号，返回None
            if not chrome_version:
                return None
                
            # 构建UA字符串
            ua = self._create_ua_string(system, chrome_version)
            
            # 保存到缓存文件
            try:
                with open(cache_path, "w") as f:
                    json.dump({"user_agent": ua, "timestamp": time.time()}, f)
            except:
                pass  # 忽略缓存写入错误
                
            return ua
            
        except Exception:
            return None
    
    def _create_ua_string(self, system, chrome_version):
        """根据系统和版本创建UA字符串"""
        if system == "Windows":
            os_info = "Windows NT 10.0"
            platform_suffix = "Win64; x64"
            ua = f"Mozilla/5.0 ({os_info}; {platform_suffix}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36"
        elif system == "Darwin":
            # 获取macOS版本
            mac_ver = platform.mac_ver()[0]
            os_info = f"Macintosh; Intel Mac OS X {mac_ver.replace('.', '_')}"
            ua = f"Mozilla/5.0 ({os_info}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36"
        else:  # Linux
            ua = f"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36"
        
        return ua

    def _get_latest_chrome_ua(self):
        """获取最新的Chrome UA（优先从设置获取，其次从环境变量获取）"""
        import json
        import os
        from utils import get_app_data_dir
        
        # 首先尝试从设置文件读取
        settings_file = os.path.join(get_app_data_dir(), "settings.json")
        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    custom_ua = settings.get("auto_register_browser_user_agent", "")
                    if custom_ua and custom_ua.strip():
                        return custom_ua.strip()
            except Exception:
                pass
        
        # 其次从环境变量获取
        env_ua = os.getenv('BROWSER_USER_AGENT')
        if env_ua and env_ua.strip():
            return env_ua.strip()
            
        # 提示用户配置
        print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.YELLOW}[UA信息]{Colors.NC} 无法自动获取真实UA，请在设置页面 > 自动注册配置里设置 浏览器User-Agent")
        print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.YELLOW}[UA信息]{Colors.NC} 例如: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        
        # 如果环境变量未配置，返回None
        return None

    def _check_chrome_browser(self) -> str:
        """
        检查浏览器安装路径
        :return: 找到的浏览器路径或None
        """
        # 获取浏览器类型，默认为Chrome
        browser_type = os.getenv('BROWSER_TYPE', 'chrome').lower()
        is_edge = browser_type == 'edge'
        is_custom_chrome = browser_type == 'custom_chrome'
        
        # 检查自定义路径
        if is_custom_chrome:
            custom_path = os.getenv('BROWSER_CUSTOM_PATH')
            if custom_path and os.path.exists(custom_path):
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[浏览器]{Colors.NC} 使用自定义路径Chrome浏览器: {custom_path}")
                return custom_path
            else:
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.YELLOW}[浏览器]{Colors.NC} 警告: 自定义Chrome路径不存在或未设置，尝试查找默认路径")
        
        system = platform.system()
        default_paths = []
        
        if system == "Windows":
            program_files = os.environ.get('PROGRAMFILES', 'C:\\Program Files')
            program_files_x86 = os.environ.get('PROGRAMFILES(X86)', 'C:\\Program Files (x86)')
            local_app_data = os.environ.get('LOCALAPPDATA', '')
            
            if browser_type == 'edge':
                # Edge浏览器路径
                default_paths = [
                    os.path.join(program_files, 'Microsoft', 'Edge', 'Application', 'msedge.exe'),
                    os.path.join(program_files_x86, 'Microsoft', 'Edge', 'Application', 'msedge.exe'),
                    os.path.join(local_app_data, 'Microsoft', 'Edge', 'Application', 'msedge.exe')
                ]
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[浏览器]{Colors.NC} 使用Edge浏览器")
            else:
                # Chrome浏览器路径
                default_paths = [
                    os.path.join(program_files, 'Google', 'Chrome', 'Application', 'chrome.exe'),
                    os.path.join(program_files_x86, 'Google', 'Chrome', 'Application', 'chrome.exe'),
                    os.path.join(local_app_data, 'Google', 'Chrome', 'Application', 'chrome.exe')
                ]
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[浏览器]{Colors.NC} 使用Chrome浏览器")
        elif system == "Darwin":  # macOS
            if browser_type == 'edge':
                default_paths = ['/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge']
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[浏览器]{Colors.NC} 使用Edge浏览器")
            else:
                default_paths = ['/Applications/Google Chrome.app/Contents/MacOS/Google Chrome']
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[浏览器]{Colors.NC} 使用Chrome浏览器")
        else:  # Linux
            if browser_type == 'edge':
                default_paths = [
                    '/usr/bin/microsoft-edge',
                    '/usr/bin/microsoft-edge-stable'
                ]
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[浏览器]{Colors.NC} 使用Edge浏览器")
            else:
                default_paths = [
                    '/usr/bin/google-chrome',
                    '/usr/bin/google-chrome-stable'
                ]
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[浏览器]{Colors.NC} 使用Chrome浏览器")
            
        # 检查所有可能的路径
        for path in default_paths:
            if os.path.exists(path):
                return path
                
        # 如果没有找到浏览器，打印详细的错误信息
        browser_name = "Edge" if browser_type == 'edge' else "Chrome"
        print_box(
            f"{Colors.RED}{browser_name}浏览器检查失败{Colors.NC}",
            [
                (Colors.RED, f"未在默认位置找到{browser_name}浏览器！"),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "请按照以下步骤解决："),
                (Colors.WHITE, f"1. 请确保已安装{browser_name}浏览器"),
                (Colors.WHITE, "2. 浏览器必须安装在默认位置："),
                *[(Colors.CYAN, f"   • {path}") for path in default_paths],
                (Colors.WHITE, ""),
                (Colors.WHITE, "3. 如果已安装但仍然报错："),
                (Colors.WHITE, f"   • 请完全卸载{browser_name}后重新安装"),
                (Colors.WHITE, ""),
                (Colors.YELLOW, f"下载地址：{'https://www.microsoft.com/edge' if browser_type == 'edge' else 'https://www.google.com/chrome/'}")
            ]
        )
        return None

    def _create_fingerprint_injection_script(self):
        """创建指纹注入脚本"""
        import json
        
        # 生成所有随机指纹数据
        hardware_info = self._generate_random_hardware_info()
        browser_info = self._generate_random_browser_info()
        media_info = self._generate_random_media_info()
        feature_info = self._generate_random_feature_info()
        webgl_vendor = self._generate_random_webgl_vendor()
        platform_info = self._generate_random_platform()
        
        # 保存当前指纹数据供验证使用
        self.current_fingerprint = {
            'hardware': hardware_info,
            'browser': browser_info,
            'media': media_info,
            'feature': feature_info,
            'webgl': webgl_vendor,
            'platform': platform_info
        }
        
        # 创建注入脚本 - 使用更强大的方法覆盖属性
        script = """
        (function() {
            console.log('[指纹修改] 开始应用指纹修改...');
            
            // 创建不可修改的属性描述符
            function createConstantGetter(value) {
                return {
                    get: function() { return value; },
                    set: function() { },
                    configurable: false,
                    enumerable: true
                };
            }
            
            // 应用属性并捕获任何错误
            function applyProperty(obj, prop, value) {
                try {
                    // 首先尝试删除任何现有的属性定义
                    try { delete obj[prop]; } catch (e) { }
                    
                    // 然后设置新的属性
                    Object.defineProperty(obj, prop, createConstantGetter(value));
                    return true;
                } catch (e) {
                    console.warn('[指纹修改] 无法修改属性:', prop, e);
                    return false;
                }
            }
            
            // 硬件和设备信息修改
            applyProperty(navigator, 'hardwareConcurrency', %d);
            applyProperty(navigator, 'deviceMemory', %d);
            applyProperty(window, 'devicePixelRatio', %f);
            
            // 屏幕信息修改 - 使用多种方法确保成功
            var screenProps = {
                width: %d,
                height: %d,
                colorDepth: %d,
                pixelDepth: %d,
                availWidth: %d,
                availHeight: %d,
                availLeft: 0,
                availTop: 0
            };
            
            // 方法1: 使用Proxy对象重定义screen
            try {
                var originalScreen = window.screen;
                var screenProxyHandler = {
                    get: function(target, prop) {
                        if (prop in screenProps) {
                            return screenProps[prop];
                        }
                        return originalScreen[prop];
                    },
                    set: function() { return true; }
                };
                
                window.screen = new Proxy(originalScreen, screenProxyHandler);
                console.log('[指纹修改] 屏幕代理创建成功');
            } catch (e) {
                console.warn('[指纹修改] 屏幕代理创建失败:', e);
                
                // 方法2: 使用defineProperty逐个设置属性
                for (var prop in screenProps) {
                    applyProperty(screen, prop, screenProps[prop]);
                }
                
                // 方法3: 最后使用古老但有效的__defineGetter__
                try {
                    for (var prop in screenProps) {
                        screen.__defineGetter__(prop, (function(p) {
                            return function() { return screenProps[p]; };
                        })(prop));
                    }
                } catch(e) { 
                    console.warn('[指纹修改] __defineGetter__失败:', e);
                }
            }
            
            // 平台和用户代理信息
            applyProperty(navigator, 'platform', '%s');
            
            // 时区修改 - 使用多种方法
            try {
                // 方法1: Proxy方法
                var originalIntl = window.Intl;
                var intlHandler = {
                    get: function(target, prop) {
                        if (prop === 'DateTimeFormat') {
                            return function() { 
                                return { 
                                    resolvedOptions: function() { 
                                        return { timeZone: '%s' }; 
                                    } 
                                }; 
                            };
                        }
                        return originalIntl[prop];
                    }
                };
                window.Intl = new Proxy(originalIntl, intlHandler);
                
                // 方法2: 覆盖DateTimeFormat.prototype.resolvedOptions
                var originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
                Intl.DateTimeFormat.prototype.resolvedOptions = function() {
                    var result = originalResolvedOptions.call(this);
                    result.timeZone = '%s';
                    return result;
                };
                console.log('[指纹修改] 时区修改成功');
            } catch (e) {
                console.warn('[指纹修改] 时区修改失败:', e);
            }
            
            // 语言设置
            applyProperty(navigator, 'languages', %s);
            applyProperty(navigator, 'doNotTrack', '%s');
            
            // WebGL指纹修改 - 使用多层保护
            try {
                // WebGL供应商信息修改
                var webglVendor = '%s';
                
                // 方法1: 使用Proxy拦截getParameter调用
                var getParameterProxy = new Proxy(WebGLRenderingContext.prototype.getParameter, {
                    apply: function(target, thisArg, args) {
                        var parameter = args[0];
                        // UNMASKED_VENDOR_WEBGL
                        if (parameter === 37445) {
                            return webglVendor;
                        }
                        // UNMASKED_RENDERER_WEBGL
                        else if (parameter === 37446) {
                            return webglVendor + ' OpenGL Engine';
                        }
                        return target.apply(thisArg, args);
                    }
                });
                WebGLRenderingContext.prototype.getParameter = getParameterProxy;
                
                // 对WebGL2RenderingContext也应用相同的修改
                if (typeof WebGL2RenderingContext !== 'undefined') {
                    WebGL2RenderingContext.prototype.getParameter = getParameterProxy;
                }
                console.log('[指纹修改] WebGL修改成功');
            } catch (e) {
                console.warn('[指纹修改] WebGL修改失败:', e);
            }
            
            // 媒体功能控制
            if (%s === false) {
                // WebRTC禁用
                try {
                    // 多种方法移除WebRTC功能
                    applyProperty(window, 'RTCPeerConnection', undefined);
                    applyProperty(window, 'webkitRTCPeerConnection', undefined);
                    applyProperty(window, 'mozRTCPeerConnection', undefined);
                    applyProperty(window, 'RTCDataChannel', undefined);
                    applyProperty(window, 'RTCSessionDescription', undefined);
                    applyProperty(window, 'RTCIceCandidate', undefined);
                } catch (e) { 
                    console.warn('[指纹修改] WebRTC禁用失败:', e);
                }
            }
            
            // 电池API控制
            if (%s === false) {
                applyProperty(navigator, 'getBattery', undefined);
            }
            
            // 蓝牙API控制
            if (%s === false) {
                applyProperty(navigator, 'bluetooth', undefined);
            }
            
            // 游戏手柄API控制
            if (%s === false) {
                applyProperty(navigator, 'getGamepads', undefined);
            }
            
            // Canvas指纹保护 - 增强版
            try {
                // 保存原始方法
                var originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                var originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
                var originalGetData = ImageData.prototype.data;
                
                // 修改toDataURL方法
                HTMLCanvasElement.prototype.toDataURL = new Proxy(originalToDataURL, {
                    apply: function(target, thisArg, args) {
                        var canvas = thisArg;
                        try {
                            // 应用微小扰动
                            var ctx = canvas.getContext('2d');
                            if (ctx) {
                                try {
                                    var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                                    if (imageData && imageData.data && imageData.data.some(val => val !== 0)) {
                                        // 随机噪声位置
                                        var noiseX = Math.max(0, canvas.width - 2);
                                        var noiseY = Math.max(0, canvas.height - 2);
                                        ctx.fillStyle = `rgba(${Math.floor(Math.random()*10)},${Math.floor(Math.random()*10)},${Math.floor(Math.random()*10)},0.0001)`;
                                        ctx.fillRect(noiseX, noiseY, 2, 2);
                                    }
                                } catch (e) { /* 忽略错误 */ }
                            }
                        } catch (e) { /* 忽略错误 */ }
                        
                        return target.apply(thisArg, args);
                    }
                });
                
                // 修改getImageData方法，在读取时添加微小扰动
                CanvasRenderingContext2D.prototype.getImageData = new Proxy(originalGetImageData, {
                    apply: function(target, thisArg, args) {
                        var result = target.apply(thisArg, args);
                        
                        try {
                            // 只在非空画布上修改
                            if (result.data && result.data.some(val => val !== 0)) {
                                // 微小修改一个随机像素的随机通道
                                var index = Math.floor(Math.random() * result.data.length / 4) * 4;
                                var channel = Math.floor(Math.random() * 3);
                                if (result.data[index + channel] > 0) {
                                    // 微小偏移，最多±1
                                    result.data[index + channel] += (Math.random() > 0.5 ? 1 : -1);
                                }
                            }
                        } catch (e) { /* 忽略错误 */ }
                        
                        return result;
                    }
                });
                console.log('[指纹修改] Canvas保护成功');
            } catch (e) {
                console.warn('[指纹修改] Canvas保护失败:', e);
            }
            
            // 检测Headless模式检测保护
            try {
                // 阻止一些常见的headless检测
                // navigator.webdriver
                applyProperty(navigator, 'webdriver', false);
                
                // plugins和mimeTypes增加一些假数据
                if (navigator.plugins.length === 0) {
                    var pluginsData = [
                        { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
                        { name: 'Chrome PDF Viewer', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
                        { name: 'Native Client', filename: 'internal-nacl-plugin', description: 'Native Client' }
                    ];
                    
                    // 定义PluginArray的长度属性getter
                    Object.defineProperty(navigator.plugins, 'length', { 
                        get: function() { return pluginsData.length; },
                        configurable: false,
                        enumerable: true 
                    });
                    
                    // 添加索引和named属性访问
                    for (var i = 0; i < pluginsData.length; i++) {
                        Object.defineProperty(navigator.plugins, i, { 
                            get: function(j) { 
                                return function() {
                                    return { 
                                        name: pluginsData[j].name,
                                        filename: pluginsData[j].filename,
                                        description: pluginsData[j].description
                                    };
                                }
                            }(i), 
                            enumerable: true,
                            configurable: true
                        });
                        
                        Object.defineProperty(navigator.plugins, pluginsData[i].name, { 
                            get: function(j) { 
                                return function() {
                                    return { 
                                        name: pluginsData[j].name,
                                        filename: pluginsData[j].filename,
                                        description: pluginsData[j].description
                                    };
                                }
                            }(i), 
                            enumerable: true,
                            configurable: true
                        });
                    }
                }
                
                console.log('[指纹修改] Headless模式检测保护成功');
            } catch (e) {
                console.warn('[指纹修改] Headless模式检测保护失败:', e);
            }
            
            console.log('[指纹修改] 浏览器指纹已成功修改');
            
            // 设置一个延迟检查，确保修改持续有效
            var checkCount = 0;
            var maxChecks = 3;
            
            function verifyFingerprint() {
                var actualConcurrency = navigator.hardwareConcurrency;
                var actualMemory = navigator.deviceMemory;
                var actualPlatform = navigator.platform;
                var actualWidth = screen.width;
                var actualHeight = screen.height;
                
                var matches = (
                    actualConcurrency === %d &&
                    actualMemory === %d &&
                    actualPlatform === '%s' &&
                    actualWidth === %d &&
                    actualHeight === %d
                );
                
                console.log('[指纹验证] 自检结果 #' + (checkCount + 1) + ':', 
                    matches ? '成功' : '失败', 
                    {
                        hardwareConcurrency: actualConcurrency,
                        deviceMemory: actualMemory,
                        platform: actualPlatform,
                        width: actualWidth,
                        height: actualHeight
                    }
                );
                
                if (!matches) {
                    console.log('[指纹修改] 检测到指纹被重置，尝试重新应用...');
                    
                    // 硬件信息再次应用
                    applyProperty(navigator, 'hardwareConcurrency', %d);
                    applyProperty(navigator, 'deviceMemory', %d);
                    applyProperty(navigator, 'platform', '%s');
                    
                    // 屏幕属性再次应用
                    for (var prop in screenProps) {
                        try {
                            delete screen[prop];
                            Object.defineProperty(screen, prop, createConstantGetter(screenProps[prop]));
                        } catch(e) {
                            try {
                                // 尝试使用老式方法
                                screen.__defineGetter__(prop, function() { return screenProps[prop]; });
                            } catch(e2) { }
                        }
                    }
                }
                
                // 递归检查，直到达到最大次数
                checkCount++;
                if (checkCount < maxChecks) {
                    setTimeout(verifyFingerprint, 2000);  // 每2秒检查一次，最多检查3次
                }
            }
            
            // 启动验证循环
            setTimeout(verifyFingerprint, 1000);
        })();
        """ % (
            hardware_info['hardwareConcurrency'],
            hardware_info['deviceMemory'],
            hardware_info['devicePixelRatio'],
            hardware_info['screenResolution'][0],
            hardware_info['screenResolution'][1],
            hardware_info['colorDepth'],
            hardware_info['colorDepth'],
            hardware_info['screenResolution'][0],  # availWidth
            hardware_info['screenResolution'][1],  # availHeight
            platform_info,
            browser_info['timezone'],
            browser_info['timezone'],  # 重复时区设置用于多种方法
            json.dumps(browser_info['languages']),
            browser_info['doNotTrack'],
            webgl_vendor,
            'true' if media_info['webrtcEnabled'] else 'false',
            'true' if feature_info['battery'] else 'false',
            'true' if feature_info['bluetooth'] else 'false',
            'true' if feature_info['gamepad'] else 'false',
            # 重复值用于自检和修复
            hardware_info['hardwareConcurrency'],
            hardware_info['deviceMemory'],
            platform_info,
            hardware_info['screenResolution'][0],
            hardware_info['screenResolution'][1],
            # 再次重复值用于二次修复
            hardware_info['hardwareConcurrency'],
            hardware_info['deviceMemory'],
            platform_info
        )
        
        return script

    def verify_fingerprint(self, tab):
        """验证指纹是否成功应用"""
        if not self.current_fingerprint:
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.YELLOW}[指纹]{Colors.NC} 没有可验证的指纹数据")
            return False
            
        try:
            # 先等待一段时间，确保脚本有足够时间执行
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[指纹]{Colors.NC} 等待指纹注入脚本完成...")
            time.sleep(1.5)  # 增加等待时间到1.5秒
            
            # 执行JavaScript获取当前指纹信息
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[指纹]{Colors.NC} 正在验证浏览器指纹...")
            result = tab.run_js("""
            return {
                // 硬件信息
                hardwareConcurrency: navigator.hardwareConcurrency,
                deviceMemory: navigator.deviceMemory,
                devicePixelRatio: window.devicePixelRatio,
                
                // 屏幕信息
                screenWidth: screen.width,
                screenHeight: screen.height,
                colorDepth: screen.colorDepth,
                pixelDepth: screen.pixelDepth,
                availWidth: screen.availWidth, 
                availHeight: screen.availHeight,
                
                // 平台信息
                platform: navigator.platform,
                
                // 浏览器信息
                userAgent: navigator.userAgent,
                languages: JSON.stringify(navigator.languages),
                doNotTrack: navigator.doNotTrack,
                webdriver: navigator.webdriver,
                
                // 功能特性
                hasWebRTC: (typeof RTCPeerConnection !== 'undefined'),
                hasBattery: (typeof navigator.getBattery !== 'undefined'),
                hasBluetooth: (typeof navigator.bluetooth !== 'undefined'),
                hasGamepads: (typeof navigator.getGamepads !== 'undefined'),
                
                // 插件信息
                pluginsLength: navigator.plugins ? navigator.plugins.length : 0,
                
                // 时区信息
                timezone: (function() {
                    try {
                        return Intl.DateTimeFormat().resolvedOptions().timeZone;
                    } catch(e) {
                        return "unknown";
                    }
                })()
            }
            """)
            
            # 检查指纹是否与生成的值匹配
            hardware_info = self.current_fingerprint['hardware']
            browser_info = self.current_fingerprint['browser']
            platform_info = self.current_fingerprint['platform']
            feature_info = self.current_fingerprint['feature']
            
            # 输出详细的验证信息用于诊断（增加了更多验证项）
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[指纹验证]{Colors.NC} 硬件线程: 预期={hardware_info['hardwareConcurrency']}, 实际={result['hardwareConcurrency']} → {('✓' if result['hardwareConcurrency'] == hardware_info['hardwareConcurrency'] else '✗')}")
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[指纹验证]{Colors.NC} 内存: 预期={hardware_info['deviceMemory']}, 实际={result['deviceMemory']} → {('✓' if result['deviceMemory'] == hardware_info['deviceMemory'] else '✗')}")
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[指纹验证]{Colors.NC} 平台: 预期={platform_info}, 实际={result['platform']} → {('✓' if result['platform'] == platform_info else '✗')}")
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[指纹验证]{Colors.NC} 分辨率: 预期={hardware_info['screenResolution'][0]}x{hardware_info['screenResolution'][1]}, 实际={result['screenWidth']}x{result['screenHeight']} → {('✓' if result['screenWidth'] == hardware_info['screenResolution'][0] and result['screenHeight'] == hardware_info['screenResolution'][1] else '✗')}")
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[指纹验证]{Colors.NC} 时区: 预期={browser_info['timezone']}, 实际={result['timezone']} → {('✓' if result['timezone'] == browser_info['timezone'] else '✗')}")
            
            # 定义关键属性列表及权重，用于判断总体验证结果
            critical_checks = [
                {'name': 'hardwareConcurrency', 'expected': hardware_info['hardwareConcurrency'], 'actual': result['hardwareConcurrency'], 'weight': 2},
                {'name': 'deviceMemory', 'expected': hardware_info['deviceMemory'], 'actual': result['deviceMemory'], 'weight': 2},
                {'name': 'platform', 'expected': platform_info, 'actual': result['platform'], 'weight': 2},
                {'name': 'screenWidth', 'expected': hardware_info['screenResolution'][0], 'actual': result['screenWidth'], 'weight': 2},
                {'name': 'screenHeight', 'expected': hardware_info['screenResolution'][1], 'actual': result['screenHeight'], 'weight': 2}
            ]
            
            # 次要属性列表，这些失败不会导致整体失败
            secondary_checks = [
                {'name': 'timezone', 'expected': browser_info['timezone'], 'actual': result['timezone'], 'weight': 1},
                {'name': 'devicePixelRatio', 'expected': hardware_info['devicePixelRatio'], 'actual': result['devicePixelRatio'], 'weight': 1}
            ]
            
            # 计算验证得分
            total_weight = sum(check['weight'] for check in critical_checks)
            current_score = sum(check['weight'] for check in critical_checks if check['expected'] == check['actual'])
            secondary_weight = sum(check['weight'] for check in secondary_checks)
            secondary_score = sum(check['weight'] for check in secondary_checks if check['expected'] == check['actual'])
            
            # 计算验证成功率
            critical_success_rate = current_score / total_weight if total_weight > 0 else 0
            secondary_success_rate = secondary_score / secondary_weight if secondary_weight > 0 else 0
            overall_rate = (current_score + secondary_score) / (total_weight + secondary_weight) if (total_weight + secondary_weight) > 0 else 0
            
            # 保存初始验证结果
            initial_matches = critical_success_rate >= 0.8
            
            # 检查是否需要进行强制注入
            if not initial_matches:  # 如果关键属性成功率低于80%
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.YELLOW}[指纹]{Colors.NC} 指纹验证失败（成功率: {critical_success_rate:.0%}），尝试强制注入...")
                
                force_script = f"""
                try {{
                    console.log('[指纹修改] 开始强制注入关键属性...');
                    
                    // 创建不可修改的属性描述符
                    function createConstantGetter(value) {{
                        return {{
                            get: function() {{ return value; }},
                            configurable: true,
                            enumerable: true
                        }};
                    }}
                    
                    // 强制设置关键属性
                    Object.defineProperties(navigator, {{
                        'hardwareConcurrency': {{ value: {hardware_info['hardwareConcurrency']}, configurable: true, writable: false }},
                        'deviceMemory': {{ value: {hardware_info['deviceMemory']}, configurable: true, writable: false }},
                        'platform': {{ value: '{platform_info}', configurable: true, writable: false }}
                    }});
                    
                    // 强制设置屏幕属性
                    Object.defineProperties(screen, {{
                        'width': {{ value: {hardware_info['screenResolution'][0]}, configurable: true, writable: false }},
                        'height': {{ value: {hardware_info['screenResolution'][1]}, configurable: true, writable: false }},
                        'availWidth': {{ value: {hardware_info['screenResolution'][0]}, configurable: true, writable: false }},
                        'availHeight': {{ value: {hardware_info['screenResolution'][1]}, configurable: true, writable: false }}
                    }});
                    
                    // 使用旧式getter方法再次尝试
                    try {{
                        screen.__defineGetter__('width', function() {{ return {hardware_info['screenResolution'][0]}; }});
                        screen.__defineGetter__('height', function() {{ return {hardware_info['screenResolution'][1]}; }});
                        screen.__defineGetter__('availWidth', function() {{ return {hardware_info['screenResolution'][0]}; }});
                        screen.__defineGetter__('availHeight', function() {{ return {hardware_info['screenResolution'][1]}; }});
                    }} catch(e) {{ console.warn('[指纹修改] __defineGetter__失败:', e); }}
                    
                    // 强制设置时区
                    try {{
                        var originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
                        Intl.DateTimeFormat.prototype.resolvedOptions = function() {{
                            var result = originalResolvedOptions.call(this);
                            result.timeZone = '{browser_info['timezone']}';
                            return result;
                        }};
                    }} catch(e) {{ console.warn('[指纹修改] 时区强制设置失败:', e); }}
                    
                    return "强制注入完成";
                }} catch(e) {{
                    return "强制注入失败: " + e.message;
                }}
                """
                
                result = tab.run_js(force_script)
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[指纹]{Colors.NC} 强制注入结果: {result}")
                
                # 再次等待并验证
                time.sleep(0.5)
                result = tab.run_js("""
                return {
                    hardwareConcurrency: navigator.hardwareConcurrency,
                    deviceMemory: navigator.deviceMemory,
                    platform: navigator.platform,
                    screenWidth: screen.width,
                    screenHeight: screen.height,
                    timezone: (function() {
                        try {
                            return Intl.DateTimeFormat().resolvedOptions().timeZone;
                        } catch(e) {
                            return "unknown";
                        }
                    })()
                }
                """)
                
                # 再次检查关键指纹是否匹配
                critical_checks = [
                    {'name': 'hardwareConcurrency', 'expected': hardware_info['hardwareConcurrency'], 'actual': result['hardwareConcurrency']},
                    {'name': 'deviceMemory', 'expected': hardware_info['deviceMemory'], 'actual': result['deviceMemory']},
                    {'name': 'platform', 'expected': platform_info, 'actual': result['platform']},
                    {'name': 'screenWidth', 'expected': hardware_info['screenResolution'][0], 'actual': result['screenWidth']},
                    {'name': 'screenHeight', 'expected': hardware_info['screenResolution'][1], 'actual': result['screenHeight']}
                ]
                
                # 计算二次验证通过的项目数
                passed_checks = sum(1 for check in critical_checks if check['expected'] == check['actual'])
                total_checks = len(critical_checks)
                
                # 更新成功率计算 - 使用二次验证结果
                new_critical_success_rate = passed_checks / total_checks if total_checks > 0 else 0
                new_overall_rate = new_critical_success_rate  # 简化计算，只考虑关键属性
                
                # 输出二次验证结果
                for check in critical_checks:
                    print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[指纹验证]{Colors.NC} 二次验证 - {check['name']}: 预期={check['expected']}, 实际={check['actual']} → {('✓' if check['expected'] == check['actual'] else '✗')}")
                
                # 检查二次验证是否通过
                matches = passed_checks >= (total_checks * 0.8)  # 80%的关键项通过即视为成功
                
                # 使用新的成功率替换旧的
                if matches:
                    critical_success_rate = new_critical_success_rate
                    overall_rate = new_critical_success_rate
                
                if not matches and passed_checks >= (total_checks * 0.6):  # 如果60%~80%的项目通过，仍视为部分成功
                    print(f"{Colors.BLUE}│{Colors.NC} {Colors.YELLOW}[指纹]{Colors.NC} 二次验证部分通过 ({passed_checks}/{total_checks} = {passed_checks/total_checks:.0%})")
                    matches = True  # 视为成功，但会在日志中标记
                    critical_success_rate = new_critical_success_rate
                    overall_rate = new_critical_success_rate
            else:
                # 关键属性验证通过，计算总体成功率
                matches = True
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[指纹]{Colors.NC} 关键指纹项验证通过 (成功率: {critical_success_rate:.0%})")
                if secondary_success_rate < 1.0:
                    print(f"{Colors.BLUE}│{Colors.NC} {Colors.YELLOW}[指纹]{Colors.NC} 部分次要指纹项未通过 (次要项成功率: {secondary_success_rate:.0%})")
            
            if matches:
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[指纹]{Colors.NC} 指纹验证通过 (总体成功率: {overall_rate:.0%})")
                return True
            else:
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.RED}[指纹]{Colors.NC} 指纹验证失败，属性未能成功修改 (总体成功率: {overall_rate:.0%})")
                return False
        except Exception as e:
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.RED}[指纹]{Colors.NC} 指纹验证过程发生错误: {str(e)}")
            traceback_info = sys.exc_info()[2]
            if traceback_info:
                line_no = traceback_info.tb_lineno
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.RED}[指纹]{Colors.NC} 错误位置: 行 {line_no}")
            return False

    def init_browser(self, use_temp=False):
        """初始化浏览器"""
        # 获取浏览器类型，默认为Chrome
        browser_type = os.getenv('BROWSER_TYPE', 'chrome').lower()
        is_edge = browser_type == 'edge'
        is_custom_chrome = browser_type == 'custom_chrome'
        
        # 检查自定义路径或指定类型的浏览器
        browser_path = self._check_chrome_browser()
        if not browser_path:
            browser_name = "Edge" if is_edge else "Chrome"
            if is_custom_chrome:
                browser_name = "自定义路径Chrome"
            raise FileNotFoundError(f"未找到{browser_name}浏览器，请按照提示安装或重新安装{browser_name}")
            
        if not use_temp and not self.user_agent:
            self.user_agent = self.get_user_agent()
            
        co = self._get_browser_options(self.user_agent, browser_path)
        
        # 指纹注入脚本
        fingerprint_script = None
        
        # 总是生成并添加指纹注入脚本（除非是临时浏览器）
        if not use_temp:
            try:
                # 生成指纹注入脚本
                fingerprint_script = self._create_fingerprint_injection_script()
                
                # 简化注入方法，不再尝试使用CDP API，而是直接使用更简单可靠的方法
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[指纹]{Colors.NC} 准备指纹注入脚本...")
                
                # 创建浏览器实例 - 先不尝试注入
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[指纹]{Colors.NC} 已配置随机浏览器指纹")
                
                # 显示指纹基本信息
                hardware_info = self.current_fingerprint['hardware']
                webgl_vendor = self.current_fingerprint['webgl']
                platform_info = self.current_fingerprint['platform']
                
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[指纹]{Colors.NC} 平台: {platform_info}")
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[指纹]{Colors.NC} 硬件线程: {hardware_info['hardwareConcurrency']}核, 内存: {hardware_info['deviceMemory']}GB")
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[指纹]{Colors.NC} 分辨率: {hardware_info['screenResolution'][0]}x{hardware_info['screenResolution'][1]}")
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[指纹]{Colors.NC} WebGL供应商: {webgl_vendor}")
                
            except Exception as e:
                browser_name = "Edge" if is_edge else "Chrome"
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.RED}[浏览器]{Colors.NC} 配置指纹失败: {str(e)}，使用默认指纹")
        
        # 创建浏览器实例
        self.browser = Chromium(co)
        
        # 如果不是临时使用，显示一次当前使用的UA
        if not use_temp and self.user_agent and not os.getenv('DISABLE_UA_PRINT', 'False').lower() == 'true':
            # 简化的UA打印
            browser_name = "Edge" if is_edge else "Chrome"
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[浏览器]{Colors.NC} 使用UA: {self.user_agent}")
            
        # 如果不是临时浏览器，在启动后直接注入指纹脚本
        if not use_temp and fingerprint_script and hasattr(self, 'current_fingerprint') and self.current_fingerprint:
            try:
                # 获取并等待第一个标签页
                tab = self.browser.latest_tab
                
                # 直接注入指纹脚本
                tab.run_js(fingerprint_script)
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[指纹]{Colors.NC} 指纹脚本已直接注入页面")
            except Exception as e:
                print(f"{Colors.BLUE}│{Colors.NC} {Colors.RED}[指纹]{Colors.NC} 直接注入指纹脚本失败: {str(e)}")

        return self.browser

    def _get_browser_options(self, user_agent=None, browser_path=None):
        """获取浏览器配置"""
        # 获取浏览器类型，默认为Chrome
        browser_type = os.getenv('BROWSER_TYPE', 'chrome').lower()
        is_edge = browser_type == 'edge'
        
        co = ChromiumOptions()
        
        # 设置浏览器路径
        if browser_path and os.path.exists(browser_path):
            co.set_browser_path(browser_path)
            browser_name = "Edge" if is_edge else "Chrome"
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[浏览器]{Colors.NC} 设置{browser_name}路径: {browser_path}")
        
        try:
            extension_path = self._get_extension_path()
            co.add_extension(extension_path)
        except FileNotFoundError as e:
            print(f"{Colors.BLUE}│{Colors.NC} {Colors.YELLOW}警告: {e}{Colors.NC}")

        co.set_pref("credentials_enable_service", False)
        co.set_argument("--hide-crash-restore-bubble")
        proxy = os.getenv("BROWSER_PROXY")
        if proxy:
            co.set_proxy(proxy)
        
        # Edge浏览器专有设置
        if is_edge:
            # 添加Edge特有的启动参数
            co.set_argument("--edge-ensure-enable-user-agent-client-hints")
            # 设置Edge特有的偏好设置
            co.set_pref("msEdge.ensure-microsoft-edge", True)

        co.auto_port()
        if user_agent:
            co.set_user_agent(user_agent)
        else:
            # 如果没有提供UA，尝试获取
            ua = self.get_user_agent()
            if ua:
                co.set_user_agent(ua)
            else:
                # 如果无法获取UA，提示用户配置
                browser_name = "Edge" if is_edge else "Chrome"
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.RED}[UA信息]{Colors.NC} 无法获取有效的User-Agent，请在设置页面 > 自动注册配置里设置UA")
                raise ValueError("无法获取有效的User-Agent，请在设置页面 > 自动注册配置里设置UA")

        co.headless(
            os.getenv("BROWSER_HEADLESS", "False").lower() == "true"
        )

        # Mac 系统特殊处理
        if sys.platform == "darwin":
            co.set_argument("--no-sandbox")
            co.set_argument("--disable-gpu")
            
        # Edge浏览器的附加参数
        if is_edge:
            if sys.platform == "win32":
                # Windows平台Edge专有参数
                co.set_argument("--disable-features=msImplicitSignin")
            
        return co

    def _get_extension_path(self):
        """获取插件路径，支持多种环境"""
        # 可能的路径列表，按优先级排序
        possible_paths = []
        
        # 1. 检查当前工作目录
        root_dir = os.getcwd()
        possible_paths.append(os.path.join(root_dir, "turnstilePatch"))
        
        # 2. 检查可能的 STARTUP_DIRECTORY（如果已定义）
        try:
            # 尝试从主模块获取
            import sys
            if 'STARTUP_DIRECTORY' in globals():
                possible_paths.append(os.path.join(globals()['STARTUP_DIRECTORY'], "turnstilePatch"))
            elif hasattr(sys.modules.get('__main__', None), 'STARTUP_DIRECTORY'):
                startup_dir = sys.modules['__main__'].STARTUP_DIRECTORY
                possible_paths.append(os.path.join(startup_dir, "turnstilePatch"))
            elif 'REAL_STARTUP_DIRECTORY' in os.environ:
                possible_paths.append(os.path.join(os.environ['REAL_STARTUP_DIRECTORY'], "turnstilePatch"))
        except Exception:
            pass
        
        # 3. 检查PyInstaller环境
        if hasattr(sys, "_MEIPASS"):
            possible_paths.append(os.path.join(sys._MEIPASS, "turnstilePatch"))
        
        # 4. 检查Nuitka环境 - 可执行文件所在目录
        if getattr(sys, 'frozen', False):
            exe_dir = os.path.dirname(os.path.abspath(sys.executable))
            possible_paths.append(os.path.join(exe_dir, "turnstilePatch"))
        
        # 5. 检查脚本所在目录（非打包环境）
        script_dir = os.path.dirname(os.path.abspath(__file__))
        possible_paths.append(os.path.join(script_dir, "turnstilePatch"))
        
        # 6. 检查上级目录（某些项目结构中可能需要）
        parent_dir = os.path.dirname(script_dir)
        possible_paths.append(os.path.join(parent_dir, "turnstilePatch"))
        
        # 搜索策略1: 逐个检查可能的路径
        for path in possible_paths:
            if os.path.exists(path) and os.path.isdir(path):
                # print(f"[信息] 找到turnstilePatch: {path}")
                return path
        
        # 搜索策略2: 在文件系统中广泛搜索（有限深度）
        try:
            search_dirs = [root_dir]
            if getattr(sys, 'frozen', False):
                search_dirs.append(os.path.dirname(os.path.abspath(sys.executable)))
            
            for base_dir in search_dirs:
                for root, dirs, files in os.walk(base_dir, topdown=True):
                    # 限制搜索深度，避免过深遍历
                    if root.count(os.sep) - base_dir.count(os.sep) > 2:
                        dirs.clear()  # 不再深入
                        continue
                    
                    if "turnstilePatch" in dirs:
                        found_path = os.path.join(root, "turnstilePatch")
                        print(f"[信息] 通过搜索找到turnstilePatch: {found_path}")
                        return found_path
        except Exception as e:
            print(f"[警告] 搜索turnstilePatch目录时出错: {e}")
        
        # 如果找不到，尝试创建临时的turnstilePatch目录
        try:
            temp_dir = os.path.join(tempfile.gettempdir(), "turnstilePatch")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir, exist_ok=True)
                # 创建基本文件
                manifest_path = os.path.join(temp_dir, "manifest.json")
                with open(manifest_path, 'w') as f:
                    f.write('''{
    "manifest_version": 3,
    "name": "Turnstile Patcher",
    "version": "2.1",
    "content_scripts": [
        {
            "js": ["./script.js"],
            "matches": ["<all_urls>"],
            "run_at": "document_start",
            "all_frames": true,
            "world": "MAIN"
        }
    ]
}''')
                
                script_path = os.path.join(temp_dir, "script.js")
                with open(script_path, 'w') as f:
                    f.write('''function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

let screenX = getRandomInt(800, 1200);
let screenY = getRandomInt(400, 600);

Object.defineProperty(MouseEvent.prototype, 'screenX', { value: screenX });
Object.defineProperty(MouseEvent.prototype, 'screenY', { value: screenY });''')
                
                print(f"[信息] 创建了临时的turnstilePatch目录: {temp_dir}")
                return temp_dir
        except Exception as e:
            print(f"[警告] 创建临时turnstilePatch目录失败: {e}")
        
        # 如果仍然找不到，抛出异常
        available_paths = "\n".join(possible_paths)
        raise FileNotFoundError(f"插件不存在: {os.path.join(root_dir, 'turnstilePatch')}\n已尝试以下路径:\n{available_paths}")

    def quit(self):
        """关闭浏览器"""
        if self.browser:
            try:
                self.browser.quit()
            except:
                pass

    def _get_ua_from_browser_v6(self):
        """使用  DrissionPage  版本的方式从浏览器快速获取UA"""
        from DrissionPage import ChromiumOptions, Chromium
        import time
        
        print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[UA信息]{Colors.NC} 使用 DrissionPage 获取UA...")
        start_time = time.time()
        
        try:
            # 获取浏览器类型和路径
            browser_type = os.getenv('BROWSER_TYPE', 'chrome').lower()
            browser_path = self._check_chrome_browser()
            
            # 创建极简配置的Chrome选项
            co = ChromiumOptions()
            
            # 设置浏览器路径
            if browser_path and os.path.exists(browser_path):
                co.set_browser_path(browser_path)
                browser_name = "Edge" if browser_type == 'edge' else "Chrome"
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[UA信息]{Colors.NC} 使用{browser_name}路径: {browser_path}")
                
            co.set_argument("--headless=new")  # 使用无头模式但获取真实UA
            co.set_argument("--disable-gpu")
            co.set_argument("--no-sandbox")
            co.set_argument("--disable-dev-shm-usage")
            co.set_argument("--disable-extensions")
            co.set_argument("--disable-logging")
            co.set_argument("--log-level=3")
            co.set_argument("--disable-infobars")
            co.set_argument("--window-size=800,600")  # 最小窗口
            co.auto_port()
            
            # 快速打开浏览器
            temp_browser = Chromium(co)
            
            # 获取UA
            user_agent = temp_browser.latest_tab.run_js("return navigator.userAgent")
            
            # 立即关闭浏览器
            temp_browser.quit()
            
            # 剔除user_agent中的"HeadlessChrome"
            if user_agent:
                user_agent = user_agent.replace("HeadlessChrome", "Chrome")
                
                end_time = time.time()
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[UA信息]{Colors.NC}  DrissionPage 获取UA成功 ({(end_time-start_time):.2f}秒)")
                
                # 保存结果到实例变量
                self.user_agent = user_agent
                return user_agent
            else:
                end_time = time.time()
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.RED}[UA信息]{Colors.NC}  DrissionPage 获取UA失败: 返回空值 ({(end_time-start_time):.2f}秒)")
                return None
            
        except Exception as e:
            end_time = time.time()
            print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.RED}[UA信息]{Colors.NC}  DrissionPage 获取UA失败: {str(e)} ({(end_time-start_time):.2f}秒)")
            return None

    def update_system_ua_cache(self, no_browser=True):
        """
        手动更新系统UA缓存
        :param no_browser: 是否禁止打开浏览器获取版本
        :return: 更新后的UA或None
        """
        print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[UA信息]{Colors.NC} 正在手动更新系统UA缓存{'' if no_browser else ''}...")
        
        # 删除现有缓存文件
        cache_path = os.path.join(os.path.expanduser("~"), ".chrome_ua_cache")
        if os.path.exists(cache_path):
            try:
                os.remove(cache_path)
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.CYAN}[UA信息]{Colors.NC} 已删除旧的UA缓存文件")
            except Exception as e:
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.RED}[UA信息]{Colors.NC} 删除缓存文件失败: {str(e)}")
        
        # 强制更新系统UA
        ua = self._get_real_chrome_ua_from_system(force_update=True, no_browser=no_browser)
        if ua:
            print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[UA信息]{Colors.NC} 系统UA缓存更新成功: {ua}")
            return ua
        else:
            # 尝试从环境变量获取
            env_ua = self._get_latest_chrome_ua()
            if env_ua:
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.GREEN}[UA信息]{Colors.NC} 使用环境变量配置的UA: {env_ua}")
                return env_ua
            else:
                print(f"\n{Colors.BLUE}│{Colors.NC} {Colors.RED}[UA信息]{Colors.NC} 系统UA缓存更新失败，请在设置页面 > 自动注册配置里设置UA")
                return None
