#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 脚本用于从main.py中删除ClickableCopyLabel类并添加导入语句

with open('main.py', 'r', encoding='utf-8') as f:
    lines = f.readlines()

# 找到类定义的开始和结束
start_line = -1
end_line = -1

for i, line in enumerate(lines):
    if '# 可点击复制的标签类' in line:
        start_line = i
    elif start_line != -1 and '# 应用程序主函数' in line:
        end_line = i
        break

if start_line != -1 and end_line != -1:
    # 添加导入语句
    new_lines = lines[:start_line] + ['from widgets.clickable_copy_label import ClickableCopyLabel\n\n'] + lines[end_line:]
    
    # 写回文件
    with open('main.py', 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print(f"成功删除ClickableCopyLabel类并添加导入语句: 从第{start_line+1}行到第{end_line}行")
else:
    print("找不到ClickableCopyLabel类或者main函数的定义")