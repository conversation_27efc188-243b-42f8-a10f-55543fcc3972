```python
import sys
import os
import platform
import logging
import re
import traceback
import subprocess
import ctypes

# 设置日志 - 记录到文件，方便调试
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'qq_verify.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 记录启动信息
logger.info("=" * 50)
logger.info("程序开始启动")
logger.info(f"Python版本: {sys.version}")
logger.info(f"当前工作目录: {os.getcwd()}")
logger.info(f"脚本路径: {__file__}")

# 检测操作系统
CURRENT_OS = platform.system()
logger.info(f"当前操作系统: {CURRENT_OS}")

# 禁用QtWebEngine的沙箱模式，这可能会在某些环境下提高兼容性
os.environ['QTWEBENGINE_DISABLE_SANDBOX'] = '1'
# 禁用硬件加速，使用软件渲染，更兼容但性能较低
os.environ['QTWEBENGINE_CHROMIUM_FLAGS'] = '--disable-gpu --disable-gpu-compositing --disable-accelerated-2d-canvas --disable-accelerated-video-decode --disable-gpu-rasterization --log-level=3 --disable-logging'

# 平台特定配置
if CURRENT_OS == 'Windows':
    os.environ['OPENSSL_CONF'] = ''
    os.environ['QT_LOGGING_RULES'] = "qt.webenginecontext.warning=false;qt.webengine.warning=false"
    logger.info("已应用Windows平台特定配置")
elif CURRENT_OS == 'Darwin':
    os.environ['QT_MAC_WANTS_LAYER'] = '1'
    logger.info("已应用macOS平台特定配置")
elif CURRENT_OS == 'Linux':
    os.environ['QT_X11_NO_MITSHM'] = '1' # 尝试解决共享内存问题
    logger.info("已应用Linux平台特定配置")
else:
    logger.info(f"未知操作系统: {CURRENT_OS}，使用通用Chromium标志")

try:
    #############################################
    ##           QQ群验证配置                  ##
    #############################################
    
    # 需要验证的QQ群号列表
    # 用户只需加入以下任意一个群即可通过验证
    QQ_GROUP_IDS = [
        "565745991",  # 示例：测试群1
        "752439445",  # 示例：测试群2
        "631250950",  # 示例：测试群3
        "2157041667", # 示例：测试群4
    ]
    
    # 系统依赖检查
    def check_system_dependencies():
        """检查系统是否满足WebEngine运行的基本要求"""
        missing_deps = []
        
        # Windows平台特定检查
        if platform.system() == 'Windows':
            # 检查VC++运行库
            try:
                # 尝试加载一些VC++ DLL
                ctypes.WinDLL('vcruntime140.dll')
                ctypes.WinDLL('msvcp140.dll')
                logger.info("Visual C++ Redistributable检查通过")
            except Exception:
                missing_deps.append("Microsoft Visual C++ Redistributable (可能需要安装最新版本)")
                logger.warning("未找到Visual C++ Redistributable 2015-2022")
            
            # 检查DirectX
            try:
                # 尝试运行dxdiag检查DirectX版本
                result = subprocess.run(['dxdiag', '/t', 'dxdiag_output.txt'], 
                                      stdout=subprocess.PIPE, 
                                      stderr=subprocess.PIPE,
                                      shell=True,
                                      timeout=3)
                if result.returncode != 0:
                    missing_deps.append("DirectX (可能需要更新)")
            except Exception:
                # 失败则假设可能需要更新DirectX
                missing_deps.append("DirectX (无法检查，可能需要更新)")
        
        # 检查Python和PySide6版本
        python_version = sys.version.split()[0]
        try:
            import PySide6
            pyside_version = PySide6.__version__
            logger.info(f"Python版本: {python_version}, PySide6版本: {pyside_version}")
        except (ImportError, AttributeError):
            logger.warning("无法确定PySide6版本")
        
        return missing_deps

    # 尝试导入PySide6，稍后再导入特定的类，以确保基础导入成功
    try:
        import PySide6
        has_pyside6 = True
    except ImportError as e:
        logger.critical(f"无法导入PySide6: {e}")
        has_pyside6 = False
        
    # 检查是否有VC++ Redistributable
    has_vcredist = True
    try:
        if CURRENT_OS == 'Windows':
            ctypes.WinDLL('vcruntime140.dll')
            ctypes.WinDLL('msvcp140.dll')
    except Exception:
        has_vcredist = False
        logger.warning("未找到Visual C++ Redistributable，应用可能无法正常运行")
        
    # 检查是否可以使用WebEngine
    can_use_webengine = has_pyside6
    
    # 有条件地导入PySide6模块
    if has_pyside6:
        from PySide6.QtCore import QUrl, Qt, QTimer, QCoreApplication, QStandardPaths
        from PySide6.QtGui import QGuiApplication # For newer DPI settings
        from PySide6.QtWidgets import (QApplication, QWidget, QVBoxLayout, QPushButton, 
                                      QMessageBox, QHBoxLayout, QLabel, QDialog, QTextEdit)
        from PySide6.QtGui import QIcon, QDesktopServices
        try:
            from PySide6.QtWebEngineWidgets import QWebEngineView
            from PySide6.QtWebEngineCore import QWebEnginePage, QWebEngineProfile, QWebEngineSettings
            can_use_webengine = True
            logger.info("成功导入WebEngine模块")
        except ImportError as e:
            can_use_webengine = False
            logger.error(f"无法导入WebEngine模块: {e}")
    
    # 错误对话框
    class ErrorDialog(QDialog):
        def __init__(self, title, message, additional_info=None, parent=None):
            super().__init__(parent)
            self.setWindowTitle(title)
            self.setMinimumWidth(500)
            
            layout = QVBoxLayout()
            
            # 错误消息
            message_label = QLabel(message)
            message_label.setWordWrap(True)
            layout.addWidget(message_label)
            
            # 附加信息
            if additional_info:
                text_edit = QTextEdit()
                text_edit.setPlainText(additional_info)
                text_edit.setReadOnly(True)
                layout.addWidget(text_edit)
            
            # 确定按钮
            ok_button = QPushButton("确定")
            ok_button.clicked.connect(self.accept)
            layout.addWidget(ok_button)
            
            self.setLayout(layout)
    
    class VCRedistDialog(QDialog):
        """提示安装Visual C++ Redistributable的对话框"""
        def __init__(self, parent=None):
            super().__init__(parent)
            self.setWindowTitle("需要安装系统组件")
            self.setMinimumSize(500, 300)
            
            layout = QVBoxLayout()
            
            # 提示文本
            label = QLabel("您的系统缺少运行本程序所需的组件: Visual C++ Redistributable 2015-2022")
            label.setWordWrap(True)
            layout.addWidget(label)
            
            # 详细说明
            details = QTextEdit()
            details.setReadOnly(True)
            details.setPlainText(
                "错误原因:\n"
                "本程序依赖于Visual C++ Redistributable运行库，但您的计算机上尚未安装此组件。\n\n"
                "解决方法:\n"
                "1. 点击下方的「下载组件」按钮前往官方下载页面\n"
                "2. 下载并安装适用于您系统的x86或x64版本\n"
                "3. 安装完成后重启本程序\n\n"
                "官方下载页面：\n"
                "https://learn.microsoft.com/zh-cn/cpp/windows/latest-supported-vc-redist"
            )
            layout.addWidget(details)
            
            # 按钮区域
            button_layout = QHBoxLayout()
            
            download_button = QPushButton("下载组件")
            download_button.clicked.connect(self.open_download_page)
            button_layout.addWidget(download_button)
            
            close_button = QPushButton("关闭")
            close_button.clicked.connect(self.reject)
            button_layout.addWidget(close_button)
            
            layout.addLayout(button_layout)
            self.setLayout(layout)
            
        def open_download_page(self):
            """打开VC++运行库下载页面"""
            QDesktopServices.openUrl(QUrl("https://learn.microsoft.com/zh-cn/cpp/windows/latest-supported-vc-redist"))
    
    class FallbackVerifyDialog(QDialog):
        """当WebEngine不可用时的备用验证对话框"""
        def __init__(self, group_ids, parent=None):
            super().__init__(parent)
            self.setWindowTitle("QQ群验证")
            self.setMinimumSize(400, 300)
            self.group_ids = group_ids
            
            layout = QVBoxLayout()
            
            # 提示文本
            title = QLabel("QQ群验证 - 备用模式")
            title.setStyleSheet("font-size: 18px; font-weight: bold;")
            title.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)
            
            info = QLabel("您的系统不支持自动验证，请使用手动验证模式")
            info.setWordWrap(True)
            info.setAlignment(Qt.AlignCenter)
            layout.addWidget(info)
            
            # 群号列表
            groups_label = QLabel("请确认您已加入以下QQ群之一:")
            layout.addWidget(groups_label)
            
            group_text = QTextEdit()
            group_text.setReadOnly(True)
            group_text.setMaximumHeight(100)
            group_list = "\n".join([f"• {gid}" for gid in self.group_ids])
            group_text.setPlainText(group_list)
            layout.addWidget(group_text)
            
            # 操作说明
            help_label = QLabel("手动验证方法:")
            layout.addWidget(help_label)
            
            help_text = QTextEdit()
            help_text.setReadOnly(True)
            help_text.setPlainText(
                "1. 打开QQ，进入您已加入的群聊页面\n"
                "2. 确认您加入了上面列出的任意一个QQ群\n"
                "3. 点击「我已加入」按钮继续使用软件\n\n"
                "如果您尚未加入上述群聊，请先加入再验证。"
            )
            layout.addWidget(help_text)
            
            # 按钮区域
            button_layout = QHBoxLayout()
            
            no_button = QPushButton("我未加入")
            no_button.clicked.connect(self.reject)
            button_layout.addWidget(no_button)
            
            yes_button = QPushButton("我已加入")
            yes_button.clicked.connect(self.accept)
            yes_button.setStyleSheet("background-color: #1E90FF; color: white;")
            button_layout.addWidget(yes_button)
            
            layout.addLayout(button_layout)
            self.setLayout(layout)
    
    # 安全的WebEngineView类，增加错误处理
    class SafeQWebEngineView(QWebEngineView):
        """安全的WebEngineView类，增加错误处理"""
        def __init__(self, parent=None):
            super().__init__(parent)
            self.loadStarted.connect(self._on_load_started)
            self.loadFinished.connect(self._on_load_finished)
            self._load_timeout_timer = QTimer(self)
            self._load_timeout_timer.setSingleShot(True)
            self._load_timeout_timer.timeout.connect(self._on_load_timeout)
            
        def _on_load_started(self):
            # 页面开始加载时启动超时计时器
            self._load_timeout_timer.start(10000)  # 10秒超时
            
        def _on_load_finished(self, success):
            # 页面加载完成时停止超时计时器
            self._load_timeout_timer.stop()
            
        def _on_load_timeout(self):
            # 页面加载超时
            logger.warning("WebEngine页面加载超时，可能是网络连接问题或渲染进程崩溃")
            self.stop()  # 停止当前页面加载
            # 通知父容器
            parent = self.parent()
            if parent and hasattr(parent, 'handle_webengine_timeout'):
                parent.handle_webengine_timeout()
    
    class CustomWebEnginePage(QWebEnginePage):
        def __init__(self, profile, parent=None):
            super().__init__(profile, parent)
            # 确保view()返回的是QQGroupLogin实例或其WebEngineView子控件
            self.renderProcessTerminated.connect(self.on_render_process_terminated)
            
        def certificateError(self, error):
            # 记录证书错误但允许继续
            logger.warning(f"SSL错误: {error.errorDescription()} - URL: {error.url().toString()}")
            return True  # 忽略SSL错误并继续

        def on_render_process_terminated(self, terminationStatus, exitCode):
            termination_str = "正常终止" if terminationStatus == QWebEnginePage.TerminationStatus.NormalTermination else "异常终止/崩溃"
            logger.error(f"WebEngine渲染进程终止: 状态={termination_str}, 退出码={exitCode}")
            # 尝试获取父窗口来显示消息框
            parent_widget = self.view()
            if not parent_widget:
                logger.error("无法获取父窗口来显示渲染进程错误消息。")
                # 如果无法获取父窗口，可以考虑创建一个临时的QMessageBox或者不显示
                # 为了简单起见，这里不创建临时窗口，但可以根据需要调整
                return

            QMessageBox.critical(parent_widget, "渲染进程错误", 
                                f"WebEngine渲染进程意外终止 ({termination_str})。请尝试重启程序。如果问题持续，可能需要检查系统兼容性或更新图形驱动程序。")
    
    class QQGroupLogin(QWidget):
        def __init__(self, profile=None, group_ids=None):
            super().__init__()
            # 如果没有提供群号列表，则使用全局配置的群号
            if group_ids is None:
                self.group_ids = QQ_GROUP_IDS
            else:
                self.group_ids = group_ids
            
            # 追踪资源状态
            self.browser = None
            self.profile = profile # 使用传入的profile
            self.page = None
            
            # 跟踪登录状态
            self.is_logged_in = False
            self.last_url = ""
            self.current_qq = ""  # 存储当前登录的QQ号
            
            self.use_webengine = can_use_webengine
            
            if self.use_webengine:
                self.init_ui()
            else:
                # WebEngine不可用时使用备用验证模式
                self.init_fallback_ui()
        
        def init_fallback_ui(self):
            """当WebEngine不可用时的简化界面"""
            self.setWindowTitle('QQ群验证登录 - 简化模式')
            self.setMinimumSize(500, 300)
            
            # 创建主布局
            layout = QVBoxLayout()
            
            # 标题
            title = QLabel('QQ群验证登录')
            title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 10px;")
            title.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)
            
            # 说明文本
            message = QLabel('您的系统不支持浏览器组件，请使用备用验证方式')
            message.setStyleSheet("font-size: 14px; color: #666; margin-bottom: 20px;")
            message.setAlignment(Qt.AlignCenter)
            layout.addWidget(message)
            
            # 群组信息标签
            group_info = QLabel(f'本程序要求您必须加入以下QQ群之一：')
            group_info.setStyleSheet("font-size: 14px; color: #333;")
            group_info.setAlignment(Qt.AlignCenter)
            layout.addWidget(group_info)
            
            # 群号列表
            group_list = QTextEdit()
            group_list.setReadOnly(True)
            group_list.setMaximumHeight(150)
            group_list.setStyleSheet("font-size: 16px;")
            group_list_text = "\n".join([f"• {gid}" for gid in self.group_ids])
            group_list.setPlainText(group_list_text)
            layout.addWidget(group_list)
            
            # 验证按钮
            self.verify_button = QPushButton('手动验证')
            self.verify_button.setStyleSheet("font-size: 16px; height: 40px; padding: 0 30px; background-color: #1E90FF; color: white; border-radius: 4px;")
            self.verify_button.clicked.connect(self.manual_verify)
            
            # 按钮水平居中布局
            button_layout = QHBoxLayout()
            button_layout.addStretch(1)
            button_layout.addWidget(self.verify_button)
            button_layout.addStretch(1)
            
            layout.addLayout(button_layout)
            self.setLayout(layout)
            
            # 居中显示窗口
            self.center_on_screen()
            
        def init_ui(self):
            """标准WebEngine界面"""
            self.setWindowTitle('QQ群验证登录')
            self.setMinimumSize(800, 600)
            
            # 创建主布局
            layout = QVBoxLayout()
            
            # 状态提示标签
            self.loading_label = QLabel('页面加载中，请稍候...')
            self.loading_label.setAlignment(Qt.AlignCenter)
            self.loading_label.setStyleSheet("font-size: 14px; color: #666;")
            layout.addWidget(self.loading_label)
            
            # 添加QQ号显示标签
            self.qq_info_label = QLabel('')
            self.qq_info_label.setAlignment(Qt.AlignCenter)
            self.qq_info_label.setStyleSheet("font-size: 14px; color: #333; font-weight: bold;")
            self.qq_info_label.setVisible(False)
            layout.addWidget(self.qq_info_label)
            
            # 群组信息标签
            group_info = QLabel(f'本程序将验证您是否加入了以下QQ群之一：{", ".join(self.group_ids)}')
            group_info.setStyleSheet("font-size: 12px; color: #333; margin-bottom: 10px;")
            group_info.setAlignment(Qt.AlignCenter)
            layout.addWidget(group_info)
            
            try:
                # 使用安全的WebEngineView
                self.browser = SafeQWebEngineView(self)
                
                # 如果没有提供profile，则创建一个新的
                if self.profile is None:
                    self.profile = QWebEngineProfile("qq_login_profile")
                    self.profile.setHttpUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                
                # 使用自定义页面以处理SSL错误
                self.page = CustomWebEnginePage(self.profile, self.browser)
                self.browser.setPage(self.page)
                self.browser.setMinimumSize(600, 400)
                
                # 加载QQ登录页面
                login_url = "https://xui.ptlogin2.qq.com/cgi-bin/xlogin?pt_disable_pwd=1&appid=715030901&daid=73&hide_close_icon=1&pt_no_auth=1&s_url=https%3A%2F%2Fqun.qq.com%2Fmember.html%23"
                logger.info(f"正在加载URL: {login_url}")
                self.browser.load(QUrl(login_url))
                
                # 连接页面加载信号
                self.browser.loadStarted.connect(self.on_load_started)
                self.browser.loadFinished.connect(self.on_load_finished)
                
                layout.addWidget(self.browser)
            except Exception as e:
                # WebEngine初始化失败
                error_msg = QLabel(f'浏览器组件初始化失败: {str(e)}')
                error_msg.setStyleSheet("color: red;")
                error_msg.setWordWrap(True)
                layout.addWidget(error_msg)
                
                logger.error(f"WebEngine初始化失败: {e}")
                logger.error(traceback.format_exc())
                
                # 添加转换到备用模式的按钮
                fallback_button = QPushButton("使用备用验证模式")
                fallback_button.clicked.connect(self.switch_to_fallback)
                layout.addWidget(fallback_button)
            
            # 创建单个验证按钮
            self.check_button = QPushButton('检查群验证')
            self.check_button.setStyleSheet("font-size: 16px; height: 40px; padding: 0 30px; background-color: #1E90FF; color: white; border-radius: 4px;")
            self.check_button.clicked.connect(self.check_verification)
            
            # 按钮水平居中布局
            button_layout = QHBoxLayout()
            button_layout.addStretch(1)
            button_layout.addWidget(self.check_button)
            button_layout.addStretch(1)
            
            layout.addLayout(button_layout)
            self.setLayout(layout)
            
            # 居中显示窗口
            self.center_on_screen()
            
            # 设置超时监视器
            self.timeout_timer = QTimer(self)
            self.timeout_timer.setSingleShot(True)
            self.timeout_timer.timeout.connect(self.check_initialization)
            self.timeout_timer.start(5000)  # 5秒后检查初始化是否完成
        
        def check_initialization(self):
            """检查应用初始化是否成功"""
            # 如果5秒后仍在加载中，可能存在问题
            if self.browser and not hasattr(self.browser, 'url') or not self.browser.url():
                logger.warning("WebEngine初始化可能失败，未能正确加载页面")
                # 显示可能的解决方案
                deps = check_system_dependencies()
                error_msg = "WebEngine组件可能未正确初始化，这通常是由于系统环境问题导致。"
                additional_info = ""
                
                if deps:
                    additional_info += "检测到以下可能缺失的系统依赖:\n"
                    additional_info += "\n".join([f"- {dep}" for dep in deps])
                    additional_info += "\n\n请尝试安装或更新这些组件后再试。"
                
                additional_info += "\n\n其他可能的解决方案:\n"
                additional_info += "1. 更新或重新安装图形驱动程序\n"
                additional_info += "2. 确保已安装Microsoft Visual C++ Redistributable\n"
                additional_info += "3. 尝试在命令行使用管理员权限运行程序\n"
                additional_info += "4. 重新安装PySide6: pip uninstall pyside6 && pip install pyside6"
                
                # 显示错误对话框
                dlg = ErrorDialog("初始化失败", error_msg, additional_info, self)
                dlg.exec()
        
        def handle_webengine_timeout(self):
            """处理WebEngine加载超时"""
            logger.warning("处理WebEngine加载超时")
            error_msg = "页面加载超时。这可能是网络连接问题，或WebEngine渲染进程崩溃。"
            additional_info = "可能的解决方案:\n"
            additional_info += "1. 检查网络连接\n"
            additional_info += "2. 更新图形驱动\n"
            additional_info += "3. 关闭其他占用资源的应用\n"
            additional_info += "4. 重新启动计算机"
            
            dlg = ErrorDialog("加载超时", error_msg, additional_info, self)
            dlg.exec()
        
        def center_on_screen(self):
            # 获取屏幕几何信息
            available_geometry = self.screen().availableGeometry()
            # 计算窗口居中位置
            x = (available_geometry.width() - self.width()) // 2
            y = (available_geometry.height() - self.height()) // 2
            # 移动窗口到居中位置
            self.move(x, y)
        
        def on_load_started(self):
            # 页面开始加载时显示加载提示
            self.loading_label.setText('页面加载中，请稍候...')
            self.loading_label.setVisible(True)
            logger.info("开始加载页面")
        
        def on_load_finished(self, ok):
            # 获取当前URL
            current_url = self.browser.url().toString()
            
            # 页面加载完成后隐藏加载提示
            if ok:
                logger.info(f"页面加载成功: {current_url}")
                
                # 判断是否是登录页面
                if 'xui.ptlogin2.qq.com' in current_url or '登录' in current_url:
                    self.loading_label.setText('请扫码登录QQ')
                    self.is_logged_in = False  # 确保登录状态被重置
                    logger.info("检测到登录页面，请扫码登录")
                # 宽松判断：只要是群页面就检查是否登录成功
                elif 'qun.qq.com/member.html' in current_url and not self.is_logged_in:
                    logger.info("检测到群页面加载完成，准备进行验证...")
                    self.loading_label.setText('群页面加载完成，准备验证...')
                    self.loading_label.setVisible(True)
                    
                    # 延迟2秒执行验证，确保页面完全加载
                    QTimer.singleShot(2000, self.check_verification)
                    self.is_logged_in = True  # 标记为已登录状态
                else:
                    self.loading_label.setText('页面加载完成')
                
                # 更新上一次URL
                self.last_url = current_url
            else:
                self.loading_label.setText('页面加载失败，请刷新')
                logger.error(f"页面加载失败: {current_url}")
            
            # 3秒后隐藏提示
            QTimer.singleShot(3000, lambda: self.loading_label.setVisible(False))
        
        def verify_login_status(self, is_logged_in):
            """验证是否真正登录成功"""
            if is_logged_in:
                logger.info("JS确认已登录QQ并进入群页面，即将自动验证...")
                self.loading_label.setText('检测到登录成功，正在自动验证...')
                self.loading_label.setVisible(True)
                self.is_logged_in = True
                # 延迟2秒后自动进行验证，确保页面完全加载
                QTimer.singleShot(2000, self.check_verification)
            else:
                logger.info("页面加载完成，但尚未登录")
                self.is_logged_in = False
        
        def check_verification(self):
            current_url = self.browser.url().toString()
            logger.info(f"检查验证，当前URL: {current_url}")
            
            # 增强登录状态判断
            if 'xui.ptlogin2.qq.com' in current_url:
                QMessageBox.warning(self, '验证失败', '您尚未登录，请先扫码登录QQ')
                logger.warning("尝试验证但用户尚未登录")
                return
            
            # 检查是否已经登录到QQ群页面
            if 'qun.qq.com' in current_url:
                # 使用多个简单的JavaScript脚本分别获取不同信息，减少复杂性
                
                # 1. 获取Cookie
                cookie_script = """
                (function() {
                    try {
                        return document.cookie;
                    } catch(e) {
                        return "";
                    }
                })()
                """
                self.browser.page().runJavaScript(cookie_script, self.process_cookie_info)
                
                # 2. 获取页面文本
                text_script = """
                (function() {
                    try {
                        return document.body.innerText;
                    } catch(e) {
                        return "";
                    }
                })()
                """
                self.browser.page().runJavaScript(text_script, self.process_text_info)
                
                # 3. 检查特定元素
                elements_script = """
                (function() {
                    let result = {};
                    try {
                        // 常见可能包含QQ号的元素
                        let selectors = ['.my-info', '.user-info', '.account-info', '#userInfo'];
                        
                        selectors.forEach(function(selector) {
                            let elements = document.querySelectorAll(selector);
                            if (elements.length > 0) {
                                result[selector] = [];
                                for (let i = 0; i < elements.length; i++) {
                                    result[selector].push(elements[i].innerText || elements[i].textContent);
                                }
                            }
                        });
                        
                        return result;
                    } catch(e) {
                        return {error: e.toString()};
                    }
                })()
                """
                self.browser.page().runJavaScript(elements_script, self.process_elements_info)
                
                # 获取HTML内容进行验证
                self.browser.page().toHtml(self.process_verification_result)
            else:
                QMessageBox.warning(self, '验证失败', '请先完成QQ登录后再进行验证！')
        
        def process_cookie_info(self, cookie_str):
            """处理获取到的Cookie信息"""
            logger.info(f"获取到Cookie: {cookie_str[:100]}..." if len(cookie_str) > 100 else cookie_str)
            
            try:
                # 尝试从Cookie中提取QQ号
                uin_match = re.search(r'uin=o0*(\d+)', cookie_str)
                p_uin_match = re.search(r'p_uin=o0*(\d+)', cookie_str)
                
                if uin_match:
                    qq = uin_match.group(1)
                    logger.info(f"从Cookie中找到QQ号(uin): {qq}")
                    self._update_qq_info(qq)
                elif p_uin_match:
                    qq = p_uin_match.group(1)
                    logger.info(f"从Cookie中找到QQ号(p_uin): {qq}")
                    self._update_qq_info(qq)
            except Exception as e:
                logger.info(f"处理Cookie时出错: {e}")
        
        def process_text_info(self, text):
            """处理获取到的页面文本信息"""
            if not text:
                logger.info("获取页面文本为空")
                return
            
            logger.info(f"获取页面文本(部分): {text[:100]}..." if len(text) > 100 else text)
            
            try:
                # 在文本中查找可能的QQ号（5-11位数字）
                qq_matches = re.findall(r'[^\d](\d{5,11})[^\d]', text)
                if qq_matches:
                    logger.info(f"在文本中找到可能的QQ号: {qq_matches[:5]}")
                    # 如果还没有找到QQ号，使用第一个匹配项
                    if not self.current_qq and qq_matches:
                        self._update_qq_info(qq_matches[0])
            except Exception as e:
                logger.info(f"处理页面文本时出错: {e}")
        
        def process_elements_info(self, elements_info):
            """处理获取到的DOM元素信息"""
            if not isinstance(elements_info, dict):
                logger.info(f"获取DOM元素信息格式错误: {type(elements_info)}")
                return
            
            if 'error' in elements_info:
                logger.info(f"获取DOM元素时出错: {elements_info['error']}")
                return
            
            logger.info(f"获取DOM元素信息: {elements_info}")
            
            try:
                # 在所有文本中查找可能的QQ号
                for selector, texts in elements_info.items():
                    for text in texts:
                        if not text:
                            continue
                        
                        qq_match = re.search(r'(\d{5,11})', text)
                        if qq_match:
                            qq = qq_match.group(1)
                            logger.info(f"在元素 {selector} 中找到QQ号: {qq}")
                            self._update_qq_info(qq)
                            return
            except Exception as e:
                logger.info(f"处理DOM元素时出错: {e}")
        
        def _update_qq_info(self, qq):
            """统一更新QQ号信息"""
            if not self.current_qq:  # 如果还没有设置QQ号
                self.current_qq = qq
                logger.info(f"成功获取到QQ号: {qq}")
                self.qq_info_label.setText(f"当前登录QQ: {qq}")
                self.qq_info_label.setVisible(True)
        
        def process_verification_result(self, html):
            if not html:
                QMessageBox.warning(self, '验证失败', '无法获取QQ群信息，请确认已正确登录')
                logger.warning("无法获取QQ群信息")
                return
            
            # 记录获取的HTML前100个字符（避免日志过长）
            logger.info(f"获取的页面HTML（部分）: {html[:100]}...")
            
            # 检查是否包含任一指定群号的data-id属性
            verified = False
            matched_group = ""
            
            for group_id in self.group_ids:
                # 检查多种可能的HTML模式
                patterns = [
                    f'data-id=\"{group_id}\"',
                    f'data-groupid=\"{group_id}\"',
                    f'groupid=\"{group_id}\"',
                    f'group-id=\"{group_id}\"',
                    f'群号：{group_id}',
                    f'群号:{group_id}'
                ]
                
                for pattern in patterns:
                    if pattern in html:
                        verified = True
                        matched_group = group_id
                        logger.info(f"找到匹配的群: {matched_group}，匹配模式: {pattern}")
                        break
                
                if verified:
                    break
            
            # 如果仍未验证成功，尝试通过页面文本进行匹配
            if not verified:
                for group_id in self.group_ids:
                    if group_id in html:
                        verified = True
                        matched_group = group_id
                        logger.info(f"通过文本找到匹配的群: {matched_group}")
                        break
            
            if verified:
                # 创建更友好的成功消息
                success_msg = QMessageBox(self)
                success_msg.setWindowTitle("验证成功")
                success_msg.setIcon(QMessageBox.Information)
                success_msg.setText(f"验证通过")
                
                # 添加QQ号信息到验证成功消息
                info_text = "感谢您的使用和支持"
                if self.current_qq:
                    info_text = f"您的QQ: {self.current_qq}\n{info_text}"
                
                success_msg.setInformativeText(info_text)
                success_msg.setStandardButtons(QMessageBox.Ok)
                success_msg.exec()
            else:
                QMessageBox.warning(self, '验证失败', 
                                   f'抱歉您并不是本软件交流群的成员，认证失败\n请加入窗口上任意一个群聊后再试')

        def debug_page_content(self):
            """调试函数，显示当前页面内容以帮助分析"""
            current_url = self.browser.url().toString()
            
            if 'qun.qq.com' in current_url:
                self.browser.page().toHtml(self.show_debug_info)
            else:
                QMessageBox.information(self, '调试信息', f'当前URL: {current_url}\n不是QQ群页面，请先登录')
            
        def show_debug_info(self, html):
            """显示页面内容的前500个字符，帮助调试"""
            preview = html[:500] if html else "无法获取HTML内容"
            
            for group_id in self.group_ids:
                # 检查群号是否在内容中，如果存在则高亮显示
                if group_id in html:
                    preview += f"\n\n找到群号: {group_id}"
            
            # 创建一个可滚动的消息框来显示HTML预览
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("页面内容预览")
            msg_box.setText("页面HTML前500个字符:")
            msg_box.setDetailedText(preview)
            msg_box.exec()

        def closeEvent(self, event):
            """处理窗口关闭事件，确保资源正确释放"""
            logger.info("窗口关闭，清理资源...")
            
            # 显式删除WebEngine相关对象，避免资源泄漏
            if self.browser:
                self.browser.setPage(None)
                self.browser.deleteLater()
                self.browser = None
            
            if self.page:
                self.page.deleteLater()
                self.page = None
            
            if self.profile:
                self.profile.deleteLater()
                self.profile = None
            
            # 确保页面相关资源被释放
            QApplication.processEvents()
            
            # 接受关闭事件
            event.accept()

        def manual_verify(self):
            """手动验证模式"""
            dialog = FallbackVerifyDialog(self.group_ids, self)
            if dialog.exec() == QDialog.Accepted:
                # 用户确认已加入群
                QMessageBox.information(self, "验证成功", "您已确认加入了指定的QQ群，验证通过！")
            else:
                # 用户未加入群
                QMessageBox.warning(self, "验证失败", 
                                   f"您尚未加入指定的QQ群，请加入以下群之一后重试：\n{', '.join(self.group_ids)}")
        
        def switch_to_fallback(self):
            """切换到备用验证模式"""
            # 清理现有UI
            if self.layout():
                QWidget().setLayout(self.layout())
            
            # 初始化备用UI
            self.init_fallback_ui()
            self.show()  # 重新显示窗口

    if __name__ == "__main__":
        try:
            # 首先检查系统依赖
            missing_deps = check_system_dependencies()
            if missing_deps:
                print("警告: 检测到可能缺失的系统依赖:")
                for dep in missing_deps:
                    print(f" - {dep}")
                print("程序可能无法正常运行。请安装或更新这些组件后再试。")
                # 不退出，继续尝试运行
            
            # 更新高DPI设置API
            QGuiApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough) # PySide6 6.2+
            logger.info("已设置高DPI舍入策略。")

            # 显式添加PySide6的Qt插件路径
            try:
                import PySide6
                pyside6_dir = os.path.dirname(PySide6.__file__)
                logger.info(f"PySide6 安装目录: {pyside6_dir}")

                # 尝试几种可能的插件路径结构
                possible_plugin_paths = [
                    os.path.join(pyside6_dir, "Qt", "plugins"),                # 结构1: PySide6/Qt/plugins
                    os.path.join(pyside6_dir, "plugins"),                       # 结构2: PySide6/plugins (常见于某些wheels)
                    os.path.join(pyside6_dir, "..", "..", "Qt", "plugins"), # 结构3: 如果PySide6在site-packages/PySide6/PySide6这种结构
                    pyside6_dir, # 直接在PySide6目录 (不太可能，但为了完整性)
                ]
                
                # 对于conda环境，插件可能在不同的位置
                if 'CONDA_PREFIX' in os.environ:
                    conda_qt_plugins = os.path.join(os.environ['CONDA_PREFIX'], 'Library', 'plugins') # Windows Conda
                    if os.path.isdir(conda_qt_plugins):
                        possible_plugin_paths.append(conda_qt_plugins)
                    conda_qt_plugins_unix = os.path.join(os.environ['CONDA_PREFIX'], 'lib', 'qt', 'plugins') # Unix Conda
                    if os.path.isdir(conda_qt_plugins_unix):
                        possible_plugin_paths.append(conda_qt_plugins_unix)
                    logger.info(f"Conda 环境检测到，已添加Conda特定插件路径尝试: {os.environ['CONDA_PREFIX']}")

                plugin_path_found = False
                for path_to_try in possible_plugin_paths:
                    normalized_path = os.path.normpath(path_to_try)
                    if os.path.isdir(normalized_path):
                        QCoreApplication.addLibraryPath(normalized_path)
                        logger.info(f"尝试添加Qt插件路径: {normalized_path}")
                        # 简单验证：检查WebEngine核心进程是否存在于相关目录
                        # 这只是一个启发式检查，不能保证完全正确
                        if CURRENT_OS == 'Windows':
                            process_exe = os.path.join(normalized_path, "..", "bin", "QtWebEngineProcess.exe") # 可能在 plugins/../bin
                            alt_process_exe = os.path.join(pyside6_dir, "QtWebEngineProcess.exe") # 或者直接在 PySide6 目录 (旧版本?)
                            if os.path.exists(process_exe) or os.path.exists(alt_process_exe):
                                logger.info(f"找到可能的QtWebEngineProcess.exe (Windows) 在 {process_exe} 或 {alt_process_exe}")
                                plugin_path_found = True
                                # break # 找到一个看似合理的就停止，或继续添加所有可能的
                        elif CURRENT_OS == 'Darwin':
                            framework_path = os.path.join(normalized_path, "..", "Frameworks", "QtWebEngineCore.framework")
                            if os.path.exists(framework_path):
                                logger.info(f"找到可能的QtWebEngineCore.framework (macOS) 在 {framework_path}")
                                plugin_path_found = True
                                # break
                        elif CURRENT_OS == 'Linux':
                            libexec_process = os.path.join(normalized_path, "..", "libexec", "QtWebEngineProcess")
                            alt_libexec_process = os.path.join(pyside6_dir, "libexec", "QtWebEngineProcess")
                            if os.path.exists(libexec_process) or os.path.exists(alt_libexec_process):
                                logger.info(f"找到可能的QtWebEngineProcess (Linux) 在 {libexec_process} 或 {alt_libexec_process}")
                                plugin_path_found = True
                                # break 
                    else:
                        logger.info(f"路径不存在，跳过: {normalized_path}")

                if not plugin_path_found:
                    logger.warning("未能自动定位到有效的Qt插件路径或WebEngine核心进程。WebEngine可能无法正常工作。")
                    logger.warning("请检查您的PySide6安装，并确保WebEngine组件已正确安装。")
                
                # 对于打包后的应用，可能需要添加可执行文件所在目录的plugins
                if getattr(sys, 'frozen', False):
                    exe_plugin_path = os.path.join(sys._MEIPASS, 'PySide6', 'Qt', 'plugins')
                    if os.path.isdir(exe_plugin_path):
                        QCoreApplication.addLibraryPath(exe_plugin_path)
                        logger.info(f"已为打包应用添加Qt插件路径: {exe_plugin_path}")
                    else:
                        logger.warning(f"打包应用Qt插件路径不存在: {exe_plugin_path}")

            except Exception as e:
                logger.error(f"添加Qt插件路径时出错: {e}", exc_info=True)
            
            app = QApplication(sys.argv)
            app.setQuitOnLastWindowClosed(True) # 确保应用在最后一个窗口关闭时退出
            
            # 创建默认的WebEngineProfile，确保其生命周期与app一致
            default_profile = QWebEngineProfile.defaultProfile()
            if not default_profile:
                logger.error("无法获取默认的QWebEngineProfile！WebEngine可能无法正常工作。")
                # 可以选择在这里退出或尝试创建一个新的profile
                # default_profile = QWebEngineProfile("my_profile", app) # app作为父对象
            else:
                logger.info(f"默认QWebEngineProfile已获取: {default_profile}")
                default_profile.setHttpUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                logger.info("已设置默认Profile的User-Agent。")

                # 设置WebEngine的一些附加选项，提高兼容性
                settings = default_profile.settings()
                if settings:
                    # 禁用WebGL，这是一个潜在的崩溃来源
                    settings.setAttribute(QWebEngineSettings.WebGLEnabled, False)
                    # 禁用JavaScript JIT编译，可能提高稳定性
                    settings.setAttribute(QWebEngineSettings.JavascriptEnabled, True)  # 仍需启用JavaScript
                    # 其他可能有用的设置
                    settings.setAttribute(QWebEngineSettings.PluginsEnabled, False)
                    settings.setAttribute(QWebEngineSettings.FullScreenSupportEnabled, False)
                    logger.info("WebEngine额外设置已应用。")
            
            # 设置应用样式
            app.setStyle('Fusion')
            
            # 检查是否有VC++ Redistributable
            if not has_vcredist and CURRENT_OS == 'Windows':
                # 显示VC++运行库安装提示
                dialog = VCRedistDialog()
                dialog.exec()
                sys.exit(0)
            
            # 增加异常捕获
            sys.excepthook = lambda *args: logger.critical(f"未捕获的异常: {args[1]}", exc_info=args)
            
            # 将default_profile传递给窗口实例
            window = QQGroupLogin(profile=default_profile)
            window.show()
            
            logger.info("主窗口已显示，启动事件循环...")
            exit_code = app.exec()
            logger.info(f"事件循环已退出，退出码: {exit_code}")
            
            # 强制处理待处理的事件
            QApplication.processEvents()
            
            # 删除窗口对象
            if window:
                logger.info("准备删除主窗口对象...")
                window.deleteLater()
                window = None
                logger.info("主窗口对象已标记为删除。")
                
            # 强制垃圾回收
            import gc
            gc.collect()
            logger.info("垃圾回收已执行。")
            
            sys.exit(exit_code)
                
        except Exception as e:
            logger.critical(f"程序异常: {e}", exc_info=True)
            
            # 尝试使用GUI显示错误
            try:
                # 如果QApplication已经创建，显示错误对话框
                if QApplication.instance():
                    error_msg = f"程序发生严重错误: {e}"
                    additional_info = "请检查系统依赖和环境配置，或联系开发者获取帮助。\n\n"
                    additional_info += "可能的解决方案:\n"
                    additional_info += "1. 更新图形驱动\n"
                    additional_info += "2. 安装最新的Microsoft Visual C++ Redistributable\n"
                    additional_info += "3. 重新安装PySide6\n"
                    additional_info += "4. 以管理员身份运行程序"
                    
                    # 尝试显示错误对话框
                    if 'QDialog' in globals():
                        # 使用自定义错误对话框，如果已加载
                        dlg = ErrorDialog("程序错误", error_msg, additional_info)
                        dlg.exec()
                    else:
                        # 使用标准消息框
                        QMessageBox.critical(None, "程序错误", f"{error_msg}\n\n详细错误信息已记录到日志文件: {log_file}")
                else:
                    # 否则仅记录到日志
                    logger.critical(f"程序启动前出错，无法显示GUI错误信息")
            except Exception as gui_error:
                logger.critical(f"显示错误对话框时出错: {gui_error}")
                
            sys.exit(1)
            
except Exception as e:
    # 捕获最顶层的异常，确保始终记录错误
    try:
        logger.critical(f"严重错误: {e}", exc_info=True)
    except:
        # 如果连logger都不可用，尝试写入到文件
        try:
            with open('error.log', 'a', encoding='utf-8') as f:
                f.write(f"\n\n{'-' * 50}\n{e}\n")
                f.write(traceback.format_exc())
        except:
            pass
    
    # 尝试显示错误对话框
    try:
        from PySide6.QtWidgets import QApplication, QMessageBox
        if not QApplication.instance():
            app = QApplication(sys.argv)
        QMessageBox.critical(None, "致命错误", f"程序启动失败: {e}\n\n请检查是否安装了Visual C++ Redistributable。")
    except:
        # 如果连QApplication都导入失败，只能退出
        pass
        
    sys.exit(1) 
```