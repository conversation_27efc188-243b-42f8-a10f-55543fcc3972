from PySide6.QtWidgets import QFrame, QHBoxLayout, QVBoxLayout, QLabel, QPushButton
from PySide6.QtCore import Signal, Qt
from widgets.styled_widgets import StyledButton
from theme import Theme

class AdvancedBackupSection(QFrame):
    """高级自定义备份/恢复分块组件（风格与其他分块一致）"""
    jump_requested = Signal()  # 跳转信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        # 修改: 通过父组件样式表设置
        self.setStyleSheet(f"""
            QFrame {{ /* 保持框架透明 */
                background-color: transparent;
            }}
            QPushButton#advancedBackupJumpButton {{
                background-color: #CBAF67; /* *** 用户指定颜色 RGB(203, 175, 103) *** */
                color: white; /* *** 保持白色文字 *** */
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-weight: bold;
                /* 移除 font-size 以使用默认值 */
            }}
            QPushButton#advancedBackupJumpButton:hover {{
                background-color: #B89C5A; /* *** 对应的悬停色 *** */
            }}
            QPushButton#advancedBackupJumpButton:pressed {{
                background-color: #A5894D; /* *** 对应的按下色 *** */
            }}
            QPushButton#advancedBackupJumpButton:disabled {{
                background-color: {Theme.DISABLED};
                color: {Theme.TEXT_SECONDARY};
            }}
        """)
        
        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 10, 0, 10) # 修改边距
        layout.setSpacing(10) # 修改间距

        # 左侧文本容器
        text_container = QVBoxLayout()
        text_container.setSpacing(3) # 修改间距

        # 创建标题行布局 (图标 + 标题)
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(0) # 设置为0

        # 创建并添加标题到标题行 (修改字体大小)
        title = QLabel("高级自定义备份/恢复")
        title.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; font-weight: bold; color: {Theme.TEXT_PRIMARY}; background-color: transparent;")
        title_layout.addWidget(title)
        title_layout.addStretch() # 标题行内部拉伸

        # 将标题行添加到垂直文本容器
        text_container.addLayout(title_layout)

        # 添加描述 (保持不变)
        desc = QLabel("可进行更灵活的备份/恢复操作，适合高级用户")
        desc.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.TEXT_SECONDARY}; background-color: transparent;")
        desc.setWordWrap(True)
        text_container.addWidget(desc)

        # 将文本容器添加到主布局 (带拉伸因子)
        layout.addLayout(text_container, 1)

        # 右侧按钮 (保持不变, 但确认添加方式)
        self.jump_btn = QPushButton("进入页面") # 修改: 使用 QPushButton
        self.jump_btn.setObjectName("advancedBackupJumpButton") # 添加: 对象名称
        self.jump_btn.setFixedHeight(36)
        self.jump_btn.setFixedWidth(100) # 修改: 固定宽度为 100
        self.jump_btn.setCursor(Qt.CursorShape.PointingHandCursor) # 添加: 设置手型光标
        self.jump_btn.clicked.connect(self.jump_requested.emit)
        layout.addWidget(self.jump_btn) # 修改: 添加时不带拉伸因子 