#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Cursor授权管理模块
提供Cursor认证信息的获取和更新功能
"""

import os
import sys
import sqlite3


# Cursor认证信息管理器
class CursorAuthManager:
    """Cursor认证信息管理器"""

    def __init__(self):
        # 判断操作系统
        if sys.platform == "win32":  # Windows
            appdata = os.getenv("APPDATA")
            if appdata is None:
                raise EnvironmentError("APPDATA 环境变量未设置")
            self.db_path = os.path.join(
                appdata, "Cursor", "User", "globalStorage", "state.vscdb"
            )
        elif sys.platform == "darwin": # macOS
            self.db_path = os.path.abspath(os.path.expanduser(
                "~/Library/Application Support/Cursor/User/globalStorage/state.vscdb"
            ))
        elif sys.platform == "linux" : # Linux 和其他类Unix系统
            self.db_path = os.path.abspath(os.path.expanduser(
                "~/.config/Cursor/User/globalStorage/state.vscdb"
            ))
        else:
            raise NotImplementedError(f"不支持的操作系统: {sys.platform}")

    def get_current_email(self):
        """
        获取当前登录的邮箱地址
        :return: str 当前登录的邮箱地址，如果未找到则返回None
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查询当前登录的邮箱地址
            cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/cachedEmail'")
            result = cursor.fetchone()
            
            if result is not None:
                return result[0]
            else:
                return None
        except sqlite3.Error as e:
            print(f"数据库错误: {str(e)}")
            return None
        except Exception as e:
            print(f"发生错误: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    def update_auth(self, email=None, access_token=None, refresh_token=None):
        """
        更新Cursor的认证信息
        :param email: 新的邮箱地址
        :param access_token: 新的访问令牌
        :param refresh_token: 新的刷新令牌
        :return: bool 是否成功更新
        """
        updates = []
        # 登录状态
        updates.append(("cursorAuth/cachedSignUpType", "Auth_0"))

        if email is not None:
            updates.append(("cursorAuth/cachedEmail", email))
        if access_token is not None:
            updates.append(("cursorAuth/accessToken", access_token))
        if refresh_token is not None:
            updates.append(("cursorAuth/refreshToken", refresh_token))

        if not updates:
            print("没有提供任何要更新的值")
            return False

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for key, value in updates:
                # 检查键是否存在
                check_query = "SELECT COUNT(*) FROM itemTable WHERE key = ?"
                cursor.execute(check_query, (key,))
                if cursor.fetchone()[0] == 0:
                    insert_query = "INSERT INTO itemTable (key, value) VALUES (?, ?)"
                    cursor.execute(insert_query, (key, value))
                else:
                    update_query = "UPDATE itemTable SET value = ? WHERE key = ?"
                    cursor.execute(update_query, (value, key))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"数据库错误: {str(e)}")
            return False
        except Exception as e:
            print(f"发生错误: {str(e)}")
            return False
        finally:
            if conn:
                conn.close()