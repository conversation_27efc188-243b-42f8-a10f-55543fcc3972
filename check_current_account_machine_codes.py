#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查当前登录账户的机器码匹配状态
仅检查当前登录的Cursor账户，而不是检查所有账户
支持 Windows、Mac 和 Linux 系统
"""

import os
import json
import platform
import subprocess
import sys
from colorama import init, Fore, Style
import sqlite3
import argparse


# 初始化colorama
init()


def print_header(title):
    """打印标题"""
    print(f"{Fore.BLUE}{'=' * 60}")
    print(f" {title}")
    print(f"{'=' * 60}{Style.RESET_ALL}")


def print_success(msg):
    """打印成功消息"""
    print(f"{Fore.GREEN}[✓] {msg}{Style.RESET_ALL}")


def print_error(msg):
    """打印错误消息"""
    print(f"{Fore.RED}[✗] {msg}{Style.RESET_ALL}")


def print_info(msg):
    """打印信息消息"""
    print(f"{Fore.CYAN}[i] {msg}{Style.RESET_ALL}")


def print_warning(msg):
    """打印警告消息"""
    print(f"{Fore.YELLOW}[!] {msg}{Style.RESET_ALL}")


def print_comparison(item_name, current_value, stored_value, indent=0):
    """打印比较结果"""
    spaces = " " * indent
    match = current_value == stored_value
    
    # 处理空值情况
    if not current_value:
        current_display = f"{Fore.YELLOW}(空){Style.RESET_ALL}"
    else:
        current_display = current_value
        
    if not stored_value:
        stored_display = f"{Fore.YELLOW}(空){Style.RESET_ALL}"
    else:
        stored_display = stored_value
    
    # 打印比较结果
    if match:
        status = f"{Fore.GREEN}[匹配]{Style.RESET_ALL}"
    else:
        status = f"{Fore.RED}[不匹配]{Style.RESET_ALL}"
    
    print(f"{spaces}{status} {item_name}:")
    print(f"{spaces}  当前系统: {current_display}")
    print(f"{spaces}  存储记录: {stored_display}")
    print()
    
    return match


def get_system_type():
    """获取当前系统类型"""
    system = platform.system().lower()
    if "windows" in system:
        return "windows"
    elif "darwin" in system:
        return "mac"
    else:
        return "linux"


def get_cursor_accounts():
    """获取Cursor账户数据"""
    
    # 尝试从settings.json中获取自定义账户文件路径
    try:
        # 导入工具函数
        from utils import get_app_data_dir
        
        # 获取settings.json文件路径
        settings_file = os.path.join(get_app_data_dir(), "settings.json")
        
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                
                # 检查是否启用了自定义路径
                if settings.get("use_custom_json_file", False):
                    custom_path = settings.get("custom_json_file_path", "")
                    if custom_path and os.path.exists(custom_path):
                        print_info(f"使用自定义账户文件路径: {custom_path}")
                        try:
                            with open(custom_path, 'r', encoding='utf-8') as f:
                                return json.load(f)
                        except Exception as e:
                            print_error(f"读取自定义账户文件失败: {str(e)}")
                    else:
                        print_warning(f"自定义账户文件路径无效或文件不存在: {custom_path}")
    except Exception as e:
        print_warning(f"读取自定义路径设置失败: {str(e)}")
    
    # 使用默认路径（应用数据目录）
    try:
        from utils import get_app_data_dir
        app_data_dir = get_app_data_dir()
        account_dir = os.path.join(app_data_dir, "account")
        accounts_file = os.path.join(account_dir, "cursor_accounts.json")
        
        if os.path.exists(accounts_file):
            print_info(f"从应用数据目录读取账户文件: {accounts_file}")
            try:
                with open(accounts_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print_error(f"读取应用数据目录账户文件失败: {str(e)}")
                return None
        else:
            print_error(f"未找到账户文件: {accounts_file}")
            return None
    except Exception as e:
        print_warning(f"获取应用数据目录失败: {str(e)}")
        return None


def get_windows_machine_guid():
    """获取Windows系统的MachineGuid"""
    try:
        import winreg
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography", 0, winreg.KEY_READ) as key:
            machine_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
            return machine_guid
    except:
        # 如果使用Python注册表API失败，尝试使用reg命令
        try:
            output = subprocess.check_output(
                ['reg', 'query', 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography', '/v', 'MachineGuid'], 
                universal_newlines=True
            )
            for line in output.splitlines():
                if "MachineGuid" in line:
                    # 提取GUID值
                    return line.split()[-1]
        except:
            pass
    return None


def get_linux_machine_ids():
    """获取Linux系统的machine-id"""
    machine_ids = {}
    
    # 读取/etc/machine-id
    try:
        if os.path.exists("/etc/machine-id"):
            with open("/etc/machine-id", 'r') as f:
                machine_ids["system.machineId"] = f.read().strip()
    except Exception as e:
        print_warning(f"读取/etc/machine-id失败: {str(e)}")
    
    # 读取/var/lib/dbus/machine-id
    try:
        if os.path.exists("/var/lib/dbus/machine-id"):
            with open("/var/lib/dbus/machine-id", 'r') as f:
                machine_ids["system.dbusId"] = f.read().strip()
    except Exception as e:
        print_warning(f"读取/var/lib/dbus/machine-id失败: {str(e)}")
    
    return machine_ids


def get_mac_system_uuid():
    """获取Mac系统的SystemUUID"""
    system_ids = {}
    
    # 尝试获取nvram SystemUUID
    try:
        nvram_output = subprocess.check_output(
            ["nvram", "SystemUUID"],
            universal_newlines=True,
            stderr=subprocess.DEVNULL
        ).strip()
        
        if '\t' in nvram_output:  # 确保输出格式正确
            nvram_uuid = nvram_output.split('\t')[-1]
            system_ids["system.nvramSystemUUID"] = nvram_uuid
    except Exception as e:
        print_warning(f"读取Mac nvram SystemUUID失败: {str(e)}")
    
    # 尝试获取系统UUID
    try:
        system_uuid = subprocess.check_output(
            ["system_profiler", "SPHardwareDataType", "| grep", "'UUID'"],
            shell=True,
            universal_newlines=True
        ).strip()
        if "UUID" in system_uuid:
            uuid_value = system_uuid.split(":")[-1].strip()
            system_ids["system.UUID"] = uuid_value
    except:
        pass
    
    # 尝试获取硬件UUID
    try:
        hardware_uuid = subprocess.check_output(
            ["ioreg", "-rd1", "-c", "IOPlatformExpertDevice", "| grep", "IOPlatformUUID"],
            shell=True,
            universal_newlines=True
        ).strip()
        if "IOPlatformUUID" in hardware_uuid:
            uuid_value = hardware_uuid.split("=")[-1].strip().replace('"', '')
            system_ids["system.hardwareUUID"] = uuid_value
    except:
        pass
    
    # 尝试获取序列号
    try:
        serial = subprocess.check_output(
            ["system_profiler", "SPHardwareDataType", "| grep", "'Serial Number (system)'"],
            shell=True,
            universal_newlines=True
        ).strip()
        if "Serial Number" in serial:
            serial_value = serial.split(":")[-1].strip()
            system_ids["system.serialNumber"] = serial_value
    except:
        pass
    
    return system_ids


def get_telemetry_info():
    """获取Cursor的telemetry机器码信息"""
    system_type = get_system_type()
    telemetry_info = {}
    
    # 获取storage.json路径
    storage_path = ""
    if system_type == "windows":
        storage_path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "globalStorage", "storage.json")
    elif system_type == "mac":
        storage_path = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")
    elif system_type == "linux":
        storage_path = os.path.expanduser("~/.config/Cursor/User/globalStorage/storage.json")
    
    # 读取storage.json文件
    if os.path.exists(storage_path):
        try:
            with open(storage_path, 'r', encoding='utf-8') as f:
                storage_data = json.load(f)
                
                # 提取telemetry相关的机器码
                telemetry_info["telemetry.machineId"] = storage_data.get("telemetry.machineId", "")
                telemetry_info["telemetry.macMachineId"] = storage_data.get("telemetry.macMachineId", "")
                telemetry_info["telemetry.devDeviceId"] = storage_data.get("telemetry.devDeviceId", "")
                telemetry_info["telemetry.sqmId"] = storage_data.get("telemetry.sqmId", "")
        except Exception as e:
            print_error(f"读取storage.json失败: {str(e)}")
    else:
        print_warning(f"未找到storage.json文件: {storage_path}")
    
    return telemetry_info


def get_current_system_identifiers():
    """获取当前系统所有机器码"""
    system_type = get_system_type()
    identifiers = get_telemetry_info()  # 获取telemetry相关的机器码
    
    # 根据系统类型获取额外的系统特定机器码
    if system_type == "windows":
        machine_guid = get_windows_machine_guid()
        if machine_guid:
            identifiers["system.machineGuid"] = machine_guid
    
    elif system_type == "linux":
        linux_ids = get_linux_machine_ids()
        identifiers.update(linux_ids)
    
    elif system_type == "mac":
        mac_ids = get_mac_system_uuid()
        identifiers.update(mac_ids)
    
    return identifiers


def compare_identifiers(current_ids, stored_ids):
    """比较当前机器码和存储的机器码"""
    all_keys = set(current_ids.keys()).union(set(stored_ids.keys()))
    match_count = 0
    total_count = 0
    
    # 根据键名进行排序，保证telemetry相关的在前面
    sorted_keys = sorted(all_keys, key=lambda k: (0 if k.startswith("telemetry") else 1, k))
    
    for key in sorted_keys:
        # 获取当前值和存储值
        current_value = current_ids.get(key, "")
        stored_value = stored_ids.get(key, "")
        
        # 如果两者都有值，则计入总数
        if current_value or stored_value:
            total_count += 1
            
            # 进行比较并打印结果
            if print_comparison(key, current_value, stored_value):
                match_count += 1
    
    return match_count, total_count


def get_current_account():
    """获取当前登录的Cursor账户"""
    system_type = get_system_type()
    
    # 获取state.vscdb数据库路径
    db_path = ""
    if system_type == "windows":
        db_path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "globalStorage", "state.vscdb")
    elif system_type == "mac":
        db_path = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/state.vscdb")
    elif system_type == "linux":
        db_path = os.path.expanduser("~/.config/Cursor/User/globalStorage/state.vscdb")
    
    # 从数据库中获取当前登录邮箱
    current_email = None
    if os.path.exists(db_path):
        conn = None
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询当前登录的邮箱地址
            cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/cachedEmail'")
            result = cursor.fetchone()
            
            if result is not None:
                current_email = result[0]
            else:
                print_warning("在数据库中未找到当前登录账户信息")
        except sqlite3.Error as e:
            print_warning(f"数据库错误: {str(e)}")
        except Exception as e:
            print_warning(f"从数据库读取当前账户信息失败: {str(e)}")
        finally:
            if conn:
                conn.close()
    else:
        print_warning(f"数据库文件不存在: {db_path}")
    
    # 如果在数据库中找不到邮箱，尝试传统的JSON方法作为备选
    if not current_email:
        print_info("尝试从JSON文件获取当前账户信息...")
        storage_path = ""
        # 获取storage.json路径
        if system_type == "windows":
            storage_path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "globalStorage", "storage.json")
        elif system_type == "mac":
            storage_path = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")
        elif system_type == "linux":
            storage_path = os.path.expanduser("~/.config/Cursor/User/globalStorage/storage.json")
        
        # 读取storage.json文件获取当前登录邮箱
        if os.path.exists(storage_path):
            try:
                with open(storage_path, 'r', encoding='utf-8') as f:
                    storage_data = json.load(f)
                    # Cursor的邮箱信息可能存储为cursorAuth/cachedEmail
                    current_email = storage_data.get("cursorAuth/cachedEmail", None)
                    if current_email:
                        print_info(f"从storage.json找到当前账户: {current_email}")
            except json.JSONDecodeError as e:
                print_warning(f"storage.json格式无效: {str(e)}")
            except Exception as e:
                print_warning(f"读取storage.json失败: {str(e)}")
        else:
            print_warning(f"storage.json文件不存在: {storage_path}")
        
        # 如果在storage.json中找不到邮箱，尝试读取auth.json
        if not current_email:
            auth_path = ""
            if system_type == "windows":
                auth_path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "auth.json")
            elif system_type == "mac":
                auth_path = os.path.expanduser("~/Library/Application Support/Cursor/User/auth.json")
            elif system_type == "linux":
                auth_path = os.path.expanduser("~/.config/Cursor/User/auth.json")
                
            if os.path.exists(auth_path):
                try:
                    with open(auth_path, 'r', encoding='utf-8') as f:
                        auth_data = json.load(f)
                        current_email = auth_data.get("email", None)
                        if current_email:
                            print_info(f"从auth.json找到当前账户: {current_email}")
                except json.JSONDecodeError as e:
                    print_warning(f"auth.json格式无效: {str(e)}")
                except Exception as e:
                    print_warning(f"读取auth.json失败: {str(e)}")
            else:
                print_warning(f"auth.json文件不存在: {auth_path}")
    
    return current_email


def check_current_account_identifiers():
    """检查当前登录账户的机器码匹配状态"""
    # 打印系统信息
    system_type = get_system_type()
    system_name = platform.system()
    system_version = platform.version()
    
    print_header("系统信息")
    print_info(f"操作系统: {system_name} ({system_type})")
    print_info(f"系统版本: {system_version}")
    print_info(f"Python版本: {sys.version.split()[0]}")
    print()
    
    # 获取当前系统的机器码
    current_ids = get_current_system_identifiers()
    
    # 获取当前登录的账户邮箱
    print_info("正在获取当前登录的Cursor账户...")
    current_email = get_current_account()
    if not current_email:
        print_error("无法确定当前登录的账户")
        print_warning("请确保Cursor已经登录，并且您有权限访问Cursor的配置文件")
        print_info("您可以尝试重新登录Cursor后再次运行此脚本")
        return False
    
    print_info(f"当前登录的账户: {current_email}")
    print()
    
    # 读取Cursor账户数据
    accounts = get_cursor_accounts()
    if not accounts:
        return False
    
    # 在账户列表中查找当前登录的账户
    current_account = None
    for account in accounts:
        if account.get("email", "") == current_email:
            current_account = account
            break
    
    if not current_account:
        print_error(f"在账户文件中未找到当前登录的账户: {current_email}")
        return False
    
    # 检查当前账户的机器码匹配状态
    machine_info = current_account.get("machine_info", {})
    if not machine_info:
        print_error(f"账户 {current_email} 没有存储机器码信息")
        return False
    
    # 检查系统类型是否匹配
    account_system_type = current_account.get("system_type", "")
    if account_system_type != system_type:
        print_error(f"账户 {current_email} 的系统类型({account_system_type})与当前系统({system_type})不匹配")
        return False
    
    # 比较机器码
    print_header(f"检查当前账户: {current_email} (系统类型: {account_system_type})")
    match_count, total_count = compare_identifiers(current_ids, machine_info)
    
    # 打印摘要
    match_percent = (match_count / total_count * 100) if total_count > 0 else 0
    print(f"\n匹配情况: {match_count}/{total_count} ({match_percent:.1f}%)")
    
    if match_count == total_count:
        print_success("所有机器码完全匹配!")
        return True
    elif match_count > 0:
        print_warning("部分机器码匹配，部分不匹配")
        return False
    else:
        print_error("没有任何机器码匹配!")
        return False


def main():
    """主函数"""
    try:
        # 检查命令行参数
        parser = argparse.ArgumentParser(description='检查当前登录账户的机器码匹配状态')
        parser.add_argument('-e', '--email', help='指定要检查的账户邮箱，跳过自动检测')
        parser.add_argument('-v', '--verbose', action='store_true', help='显示详细的调试信息')
        args = parser.parse_args()
        
        # 根据参数设置调试模式
        if args.verbose:
            print_info("启用详细模式")
        
        # 执行检查
        if args.email:
            print_info(f"使用指定的邮箱: {args.email}")
            from functools import partial
            # 用一个返回固定邮箱的函数替换get_current_account
            original_get_current_account = get_current_account
            get_current_account.__globals__['get_current_account'] = lambda: args.email
            result = check_current_account_identifiers()
            # 恢复原始函数
            get_current_account.__globals__['get_current_account'] = original_get_current_account
            return 0 if result else 1
        else:
            result = check_current_account_identifiers()
            return 0 if result else 1
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return 2
    except Exception as e:
        print_error(f"程序发生错误: {str(e)}")
        if 'args' in locals() and args.verbose:
            import traceback
            traceback.print_exc()
        return 3


if __name__ == "__main__":
    # 设置 Windows 控制台以支持ANSI颜色
    if platform.system() == "Windows":
        try:
            import ctypes
            kernel32 = ctypes.windll.kernel32
            kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)
        except:
            # 如果失败，colorama会尝试使用其他方法
            pass
            
    # 执行主函数并设置退出码
    sys.exit(main())