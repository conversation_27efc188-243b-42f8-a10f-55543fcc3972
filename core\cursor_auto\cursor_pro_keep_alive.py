#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
cursor_pro_keep_alive.py - Cursor Pro 保持活跃工具
"""

# 在任何其他导入前获取启动环境信息
import os
import sys
import inspect
import tempfile
import json

# 尝试获取真实的启动目录信息函数 - 不依赖os.getcwd()
def _get_real_startup_directory():
    """尝试多种方法获取真实的程序启动目录"""
    # 方法1: 获取环境变量中存储的启动目录
    if 'REAL_STARTUP_DIRECTORY' in os.environ:
        startup_dir = os.environ['REAL_STARTUP_DIRECTORY']
        if os.path.exists(startup_dir):
            return startup_dir
    
    # 方法2: 尝试从临时文件读取
    try:
        temp_dir = tempfile.gettempdir()
        info_file = os.path.join(temp_dir, 'cursor_startup_info.json')
        
        if os.path.exists(info_file):
            with open(info_file, 'r') as f:
                info = json.load(f)
                startup_dir = info.get('startup_directory')
                if startup_dir and os.path.exists(startup_dir):
                    return startup_dir
    except Exception:
        pass
            
    # 方法3: 检查当前工作目录是否为临时目录
    current_dir = os.getcwd()
    if not any(temp in current_dir.upper() for temp in ["TEMP", "TMP", "ONEFILE", "~1"]):
        return current_dir
    
    # 方法4: 检查调用栈中的文件路径
    try:
        calling_frame = inspect.stack()[-1]
        calling_file = calling_frame.filename
        if os.path.exists(calling_file) and os.path.isfile(calling_file):
            return os.path.dirname(os.path.abspath(calling_file))
    except Exception:
        pass
    
    # 方法5: 检查启动参数
    try:
        if len(sys.argv) > 0:
            # 获取启动路径目录
            possible_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
            if os.path.exists(possible_dir) and not any(temp in possible_dir.upper() for temp in ["TEMP", "TMP", "ONEFILE", "~1"]):
                return possible_dir
    except Exception:
        pass
    
    # 如果以上方法都失败，返回当前工作目录
    return current_dir

# 获取并保存真正的启动目录
STARTUP_DIRECTORY = _get_real_startup_directory()
# 移除调试日志
# print(f"[初始化] 真正的启动目录: {STARTUP_DIRECTORY}")
# print(f"[初始化] 当前工作目录: {os.getcwd()}")

# 立即切换到正确的工作目录
try:
    # 检查启动目录是否为临时目录
    is_temp_dir = any(temp in STARTUP_DIRECTORY.upper() for temp in ["TEMP", "TMP", "ONEFILE", "~1"])
    
    if not is_temp_dir and os.path.exists(STARTUP_DIRECTORY):
        # 如果不是临时目录，切换到启动目录
        if os.getcwd() != STARTUP_DIRECTORY:
            os.chdir(STARTUP_DIRECTORY)
            # 移除调试日志
            # print(f"[初始化] 已切换到启动目录: {os.getcwd()}")
        # else:
            # print(f"[初始化] 已在正确的目录中: {os.getcwd()}")
except Exception as e:
    print(f"[警告] 切换到启动目录失败: {e}")

# 现在导入其他模块
import time
import random
import string
import uuid
import datetime
import json
import platform
import threading
import re
import base64
import requests
from urllib.parse import urlparse
import logging
import argparse
import subprocess
import zipfile
import hashlib
import ctypes
import socket
from dotenv import load_dotenv

# 定义获取应用数据目录的函数
def get_app_data_dir(app_name="YCursor"):
    """获取应用数据目录，根据操作系统类型返回不同的路径"""
    if sys.platform == "win32":  # Windows
        app_data = os.getenv("APPDATA")
        if app_data:
            app_dir = os.path.join(app_data, app_name)
    elif sys.platform == "darwin":  # macOS
        app_dir = os.path.expanduser(f"~/Library/Application Support/{app_name}")
    else:  # Linux和其他系统
        app_dir = os.path.expanduser(f"~/.config/{app_name}")
    
    # 确保目录存在
    if app_dir and not os.path.exists(app_dir):
        try:
            os.makedirs(app_dir)
        except Exception as e:
            print(f"[警告] 创建应用数据目录失败: {e}")
    
    return app_dir

# 导入工具模块并设置工作目录
try:
    from utils import get_executable_dir, ensure_base_dir
    
    # 在程序启动时设置工作目录为可执行文件所在目录，但优先使用原始目录
    # 修改：传入保存的原始目录
    def custom_ensure_base_dir():
        """确保使用正确的工作目录，优先使用最初的原始目录"""
        # 直接传入真正的原始目录给ensure_base_dir
        ensure_base_dir(true_original_dir=STARTUP_DIRECTORY)
    
    # 使用修改后的函数
    custom_ensure_base_dir()
except ImportError:
    # print("[警告] 无法导入utils模块，使用内置函数")
    
    # 实现简单的内置工具函数，不需要导入utils模块
    def get_executable_dir():
        """获取可执行文件所在目录"""
        if getattr(sys, 'frozen', False):
            return os.path.dirname(os.path.abspath(sys.executable))
        else:
            return os.path.dirname(os.path.abspath(__file__))
    
    # 设置工作目录为当前目录，不再尝试提权或其他高风险操作
    try:
        # 检查原始目录是否为临时目录
        original_is_temp = any(temp in STARTUP_DIRECTORY.upper() for temp in ["TEMP", "TMP", "~1"])
        if not original_is_temp:
            # 如果原始目录不是临时目录，尝试切换回去
            if os.getcwd() != STARTUP_DIRECTORY:
                print(f"[信息] 切换回原始工作目录: {STARTUP_DIRECTORY}")
                os.chdir(STARTUP_DIRECTORY)
        else:
            # 如果原始目录是临时目录，使用当前目录
            print(f"[信息] 使用当前工作目录: {os.getcwd()}")
    except Exception as e:
        print(f"[警告] 切换工作目录失败: {e}")

import os
import sys
import json
import uuid
import platform
import subprocess  # 添加subprocess导入
import tempfile
import datetime
import random
import re
import shutil
import time
import stat
import glob  # 添加glob导入
import traceback
import atexit  # 添加atexit模块
from pathlib import Path
from dotenv import load_dotenv
from colorama import init, Fore, Style

from .exit_cursor import ExitCursor
# 移除旧的机器码修改器
# from reset_machine import MachineIDResetter
# 添加新的机器码修改工具
from .Cursor_Change_ID import main as change_machine_id, get_system_type
from version_checker import VersionChecker

os.environ["PYTHONVERBOSE"] = "0"
os.environ["PYINSTALLER_VERBOSE"] = "0"

# 定义 URL 常量
login_url = "https://authenticator.cursor.sh"
sign_up_url = "https://authenticator.cursor.sh/sign-up"
settings_url = "https://www.cursor.com/settings"
mail_url = "https://tempmail.plus"

import time
import random
from .cursor_auth_manager import CursorAuthManager
from .browser_utils import BrowserManager
from .get_email_code import EmailVerificationHandler
from .logo import print_logo
from .config import Config
from dotenv import load_dotenv

# 设置文件路径 - 修改为使用应用数据目录
try:
    # 使用已经确定的STARTUP_DIRECTORY作为应用程序路径
    application_path = STARTUP_DIRECTORY
    
    # 获取应用数据目录
    app_data_dir = get_app_data_dir("YCursor")
    temp_quota_data_dir = os.path.join(app_data_dir, "temp_quota_data")
    
    # 确保temp_quota_data目录存在
    if not os.path.exists(temp_quota_data_dir):
        os.makedirs(temp_quota_data_dir)
    
    # 优先使用应用数据目录中的.env文件
    dotenv_path = os.path.join(temp_quota_data_dir, ".env")
    
    # 如果应用数据目录中没有.env文件，但在传统位置存在，则复制过去
    if not os.path.exists(dotenv_path):
        legacy_path = os.path.join(application_path, ".env")
        if os.path.exists(legacy_path):
            print(f"[信息] 在旧位置找到.env文件: {legacy_path}")
            print(f"[信息] 将在新位置使用: {dotenv_path}")
            
            try:
                import shutil
                shutil.copy2(legacy_path, dotenv_path)
                print(f"[信息] 已复制.env文件到新位置")
            except Exception as e:
                print(f"[警告] 复制.env文件失败: {e}")
                dotenv_path = legacy_path
except Exception as e:
    print(f"[警告] 设置应用数据目录失败: {e}，使用传统路径")
    # 如果出错，回退到传统路径
    dotenv_path = os.path.join(application_path, ".env")

# 设置默认账号文件路径为None，允许外部代码修改这个变量来自定义保存位置
cursor_accounts_file = None

# 移除调试日志
# print(f"[信息] .env文件路径: {dotenv_path}")

# 加载环境变量
load_dotenv(dotenv_path)

def get_env_choice(env_key, default_value=None, valid_values=None):
    """
    从环境变量获取选项值，并验证其有效性
    :param env_key: 环境变量名
    :param default_value: 默认值
    :param valid_values: 有效值列表
    :return: 有效的选项值
    """
    # 获取环境变量
    value = os.getenv(env_key, '')
    
    # 如果为空并且有默认值，使用默认值
    if not value and default_value is not None:
        return default_value
    
    # 如果有限制的有效值，则进行验证
    if valid_values is not None and value.upper() not in valid_values:
        print("\n环境变量验证")
        print(f"  [错误] 环境变量 {env_key} 的值无效: {value}")
        print(f"  [提示] 有效值为: {', '.join(valid_values)}")
        return default_value if default_value is not None else valid_values[0]
    
    return value.upper() if valid_values else value

# 清屏
os.system('cls' if platform.system().lower() == 'windows' else 'clear')

# 在文件开头添加颜色定义和日志函数
class Colors:
    """统一的颜色定义"""
    RED = ''      # 错误信息
    GREEN = ''    # 成功信息
    YELLOW = ''   # 警告/提示信息
    BLUE = ''     # 框架/标题
    PURPLE = ''   # 重要数据
    CYAN = ''     # 进度信息
    WHITE = ''    # 普通文本
    NC = ''       # 结束颜色

# 添加回调机制 - 全局变量
_log_callback = None
_progress_callback = None

def set_log_callback(callback_func):
    """设置日志回调函数
    
    Args:
        callback_func: 接收 (消息文本, 日志类型) 的回调函数
    """
    global _log_callback
    _log_callback = callback_func

def set_progress_callback(callback_func):
    """设置进度回调函数
    
    Args:
        callback_func: 接收 (当前进度, 总进度) 的回调函数
    """
    global _progress_callback
    _progress_callback = callback_func

def update_progress(current, total=100, print_to_console=False):
    """更新进度
    
    Args:
        current: 当前进度值
        total: 总进度值
        print_to_console: 是否打印进度到控制台，默认为False
    """
    global _progress_callback
    # 仅当 print_to_console 为 True 时才输出进度到控制台
    if print_to_console and current > 0:
        print(f"[进度] {current}/{total} ({int(current/total*100)}%)")
    
    # 如果有回调函数，则调用回调
    if _progress_callback:
        _progress_callback(current, total)

def print_box(title="", content=None, footer=None, color=None):
    """打印消息，不带框"""
    # 在开始前添加一个空行
    print()
    
    # 如果只有标题，直接显示标题
    if title and not content and not footer:
        if color:
            print(f"{color}{title}{Colors.NC}")
        else:
            print(title)
            
        # 如果有日志回调，传递标题内容
        if _log_callback and title.startswith("["):
            log_type = title[1:title.find("]")].lower()
            if log_type in ["信息", "警告", "错误", "进行", "成功", "调试"]:
                msg = title[title.find("]")+2:]  # 获取标题中的消息部分
                _log_callback(msg, log_type)
        return
    
    # 显示标题
    if title:
        if color:
            print(f"{color}{title}{Colors.NC}")
        else:
            print(title)
            
        # 如果有日志回调，传递标题内容
        if _log_callback and title.startswith("["):
            log_type = title[1:title.find("]")].lower()
            if log_type in ["信息", "警告", "错误", "进行", "成功", "调试"]:
                msg = title[title.find("]")+2:]  # 获取标题中的消息部分
                _log_callback(msg, log_type)
    
    # 显示内容
    if content:
        if isinstance(content, str):
            if content.strip():
                if color:
                    print(f"{color}{content}{Colors.NC}")
                else:
                    print(content)
        elif isinstance(content, list):
            for line in content:
                if isinstance(line, tuple):
                    line_color, text = line
                    print(f"{line_color}{text}{Colors.NC}")
                else:
                    if color:
                        print(f"{color}{line}{Colors.NC}")
                    else:
                        print(line)
    
    # 显示页脚
    if footer:
        if color:
            print(f"{color}{footer}{Colors.NC}")
        else:
            print(footer)

def log_info(msg, show_box=False):
    """输出信息日志"""
    # 跳过中间过程消息
    if "使用自定义账号文件路径" in msg or "使用默认账号文件路径" in msg or "邮箱验证码登录模式" in msg or "账号注册模式" in msg:
        return
    if show_box:
        print_box(f"[信息] {msg}")
    else:
        print(f"[信息] {msg}")
        # 如果有日志回调，也发送到回调
        if _log_callback:
            _log_callback(msg, "信息")

def log_warn(msg, show_box=False):
    """输出警告日志"""
    if show_box:
        print_box(f"[警告] {msg}")
    else:
        print(f"[警告] {msg}")
        # 如果有日志回调，也发送到回调
        if _log_callback:
            _log_callback(msg, "警告")

def log_error(msg, show_box=False):
    """输出错误日志"""
    if show_box:
        print_box(f"[错误] {msg}")
    else:
        print(f"[错误] {msg}")
        # 如果有日志回调，也发送到回调
        if _log_callback:
            _log_callback(msg, "错误")

def log_debug(msg, show_box=False):
    """输出调试日志"""
    if show_box:
        print_box(f"[调试] {msg}")
    else:
        print(f"[调试] {msg}")
        # 如果有日志回调，也发送到回调
        if _log_callback:
            _log_callback(msg, "调试")

def log_process(msg, show_box=False):
    """输出进度日志"""
    # 跳过中间过程消息
    if "正在保存账号信息" in msg:
        return
    if show_box:
        print_box(f"[进行] {msg}")
    else:
        print(f"[进行] {msg}")
        # 如果有日志回调，也发送到回调
        if _log_callback:
            _log_callback(msg, "进行")

def log_success(msg, show_box=False):
    """输出成功日志"""
    # 避免重复的成功消息
    if "账号信息已成功保存到" in msg and "账号数据已保存到" in msg:
        return
    if show_box:
        print_box(f"[成功] {msg}")
    else:
        print(f"[成功] {msg}")
        # 如果有日志回调，也发送到回调
        if _log_callback:
            _log_callback(msg, "成功")

def handle_turnstile(tab):
    timeout = int(os.getenv('TURNSTILE_TIMEOUT', '30'))
    manual_cf = os.getenv('MANUAL_CF', 'False').lower() == 'true'
    log_process("正在检测 Turnstile 验证...", show_box=True)
    log_info(f"超时时间: {timeout}秒")
    if manual_cf:
        log_info("手动CF验证模式已启用")
    
    try:
        start_time = time.time()
        
        while True:
            elapsed_time = time.time() - start_time
            remaining_time = max(0, timeout - int(elapsed_time))
            
            if elapsed_time > timeout:
                log_info("Turnstile 验证超时，继续执行下一步(没有进行下一步请按esc关闭然后重新运行自动注册)", show_box=True)
                return True
                
            try:
                error_selectors = [
                    "text:Can't verify the user is human",
                    "text:Please try again",
                    "css:div.text-red-500",
                    "css:[role=alert]"
                ]
                
                for selector in error_selectors:
                    if tab.ele(selector, timeout=1):
                        print_box(
                            f"{Colors.RED}检测到人机验证失败！{Colors.NC}",
                            [
                                (Colors.YELLOW, "99% 网络环境问题"),
                                (Colors.GREEN, "1. 开启或关闭代理后重试"),
                                (Colors.GREEN, "2. 更换网络环境后重试"),
                                (Colors.YELLOW, "3. 如需帮助可以加QQ群：********* 联系群主")
                            ]
                        )
                        browser_manager.quit()
                        input("\n\n请修改后再重试，按回车键退出程序")
                        os._exit(1)

                challengeCheck = (
                    tab.ele("@id=cf-turnstile", timeout=2)
                    .child()
                    .shadow_root.ele("tag:iframe")
                    .ele("tag:body")
                    .sr("tag:input")
                )

                if challengeCheck:
                    if manual_cf:
                        # 手动验证模式
                        log_process("检测到 Turnstile 验证，等待用户手动完成...", show_box=True)

                        # 通知UI显示手动验证按钮
                        try:
                            global worker_instance
                            if worker_instance and hasattr(worker_instance, 'manual_cf_required'):
                                worker_instance.manual_cf_required.emit()
                                log_info("已通知UI显示手动验证按钮")

                                # 等待用户完成验证
                                if hasattr(worker_instance, 'manual_cf_event'):
                                    log_info("等待用户完成人机验证...")
                                    worker_instance.manual_cf_event.wait()
                                    log_info("用户已完成人机验证，继续流程")
                                else:
                                    log_error("工作线程缺少manual_cf_event属性")
                            else:
                                log_error("无法获取工作线程实例或缺少manual_cf_required信号")
                                # 回退到自动模式
                                time.sleep(random.uniform(1, 3))
                                challengeCheck.click()
                                time.sleep(2)

                        except Exception as e:
                            log_error(f"手动验证信号发送失败: {str(e)}", show_box=True)
                            # 如果信号发送失败，回退到自动模式
                            time.sleep(random.uniform(1, 3))
                            challengeCheck.click()
                            time.sleep(2)

                        print_box(f"{Colors.GREEN}[成功]{Colors.NC} Turnstile 验证通过")
                        return True
                    else:
                        # 自动验证模式
                        log_process("检测到 Turnstile 验证，正在处理...", show_box=True)
                        time.sleep(random.uniform(1, 3))
                        challengeCheck.click()
                        time.sleep(2)
                        print_box(f"{Colors.GREEN}[成功]{Colors.NC} Turnstile 验证通过")
                        return True
            except:
                if remaining_time > 0 and remaining_time % 5 == 0:
                    log_info(f"正在检测 Turnstile 验证...（还剩 {remaining_time} 秒）")
                pass

            if tab.ele("@name=password"):
                log_success("验证成功 - 已到达密码输入页面", show_box=True)
                break
            if tab.ele("@data-index=0"):
                log_success("验证成功 - 已到达验证码输入页面", show_box=True)
                break
            if tab.ele("Account Settings"):
                log_success("验证成功 - 已到达账户设置页面", show_box=True)
                break

            time.sleep(random.uniform(1, 2))
    except Exception as e:
        log_error(f"Turnstile 验证失败: {str(e)}", show_box=True)
        return False


def get_cursor_session_token(tab, max_attempts=3, retry_interval=2):
    """
    获取Cursor会话token，带有重试机制
    :param tab: 浏览器标签页
    :param max_attempts: 最大尝试次数
    :param retry_interval: 重试间隔(秒)
    :return: session token 或 None
    """
    log_info("开始获取cookie", show_box=True)
    attempts = 0

    while attempts < max_attempts:
        try:
            cookies = tab.cookies()
            for cookie in cookies:
                if cookie.get("name") == "WorkosCursorSessionToken":
                    return cookie["value"].split("%3A%3A")[1]

            attempts += 1
            if attempts < max_attempts:
                log_warn(f"第 {attempts} 次尝试未获取到CursorSessionToken，{retry_interval}秒后重试...")
                time.sleep(retry_interval)
            else:
                log_error(f"已达到最大尝试次数({max_attempts})，获取CursorSessionToken失败", show_box=True)

        except Exception as e:
            log_error(f"获取cookie失败: {str(e)}", show_box=True)
            attempts += 1
            if attempts < max_attempts:
                log_info(f"将在 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)

    return None


def update_cursor_auth(email=None, access_token=None, refresh_token=None):
    """
    更新Cursor的认证信息的便捷函数
    """
    auth_manager = CursorAuthManager()
    return auth_manager.update_auth(email, access_token, refresh_token)


class AccountInfo:
    """账号信息类"""
    def __init__(self):
        self.email = None
        self.password = None

    def set_info(self, email, password):
        self.email = email
        self.password = password

    def get_info(self):
        return self.email, self.password

# 创建全局账号信息对象
account_info = AccountInfo()

# 全局工作线程实例，用于手动CF验证
worker_instance = None

def handle_login(browser, tab, email, password):
    """处理登录流程
    :param browser: 浏览器实例
    :param tab: 标签页实例
    :param email: 邮箱
    :param password: 密码
    :return: 是否登录成功
    """
    try:
        # 跳转到登录页面
        log_process("正在跳转到登录页面...", show_box=True)
        tab.get(login_url)
        time.sleep(3)  # 增加等待时间

        # 输入邮箱
        log_process("正在输入邮箱...", show_box=True)
        tab.ele('xpath://input[@type="email"]').input(email)
        time.sleep(1.5)  # 增加等待时间

        # 点击Continue按钮
        log_process("正在点击Continue按钮...", show_box=True)
        tab.ele('xpath://button[contains(text(), "Continue")]').click()
        time.sleep(3)  # 增加等待时间

        # 处理人机验证
        handle_turnstile(tab)
        time.sleep(3)  # 增加等待时间

        # 等待密码输入框出现
        max_wait = 10  # 最大等待10秒
        for i in range(max_wait):
            if tab.ele('xpath://input[@type="password"]', timeout=1):
                break
            if i == max_wait - 1:
                log_error("等待密码输入框超时", show_box=True)
                return False
            time.sleep(1)

        # 输入密码
        log_process("正在输入密码...", show_box=True)
        tab.ele('xpath://input[@type="password"]').input(password)
        time.sleep(1.5)  # 增加等待时间

        # 点击Sign in按钮
        log_process("正在点击Sign in按钮...", show_box=True)
        tab.ele('xpath://button[contains(text(), "Sign in")]').click()
        time.sleep(3)  # 增加等待时间

        # 处理人机验证
        handle_turnstile(tab)
        time.sleep(5)  # 增加等待时间

        # 检查是否登录成功（修改判断逻辑）
        max_attempts = 6
        for i in range(max_attempts):
            current_url = tab.url
            if "cursor.com" in current_url:  # 只要URL包含cursor.com就认为成功
                log_success("登录成功！", show_box=True)
                return True
            elif i < max_attempts - 1:
                time.sleep(5)  # 每次检查间隔5秒
                log_info(f"等待页面跳转... ({i + 1}/{max_attempts})")
            else:
                log_error(f"登录失败，当前页面: {current_url}", show_box=True)
                return False

    except Exception as e:
        log_error(f"登录过程出错: {str(e)}", show_box=True)
        return False

def retry_get_token(browser, tab, email, password):
    """通过重新登录获取令牌
    :param browser: 浏览器实例
    :param tab: 标签页实例
    :param email: 邮箱
    :param password: 密码
    :return: 新的令牌
    """
    try:
        # 尝试登录
        if not handle_login(browser, tab, email, password):
            return None

        # 跳转到设置页面
        log_process("正在跳转到设置页面...", show_box=True)
        tab.get(settings_url)
        time.sleep(3)

        # 重新获取令牌
        cookies = tab.cookies()
        for cookie in cookies:
            if cookie.get("name") == "WorkosCursorSessionToken":
                token_value = cookie["value"]
                if "::" in token_value:
                    return token_value.split("::")[-1]
                return token_value
        return None

    except Exception as e:
        log_error(f"重新获取令牌失败: {str(e)}", show_box=True)
        return None

def sign_up_account(browser, tab):
    print_box("开始注册账号流程")
    log_info(f"正在访问注册页面: {sign_up_url}")
    update_progress(45)
    tab.get(sign_up_url)
    update_progress(47)
    log_info(f"\n出现人机验证请手动点击一下，技术有限，这个和注册流程的不一样，没办法自动过这个人机验证")
    handle_turnstile(tab)
    try:
        if tab.ele("@name=first_name"):
            print_box("填写个人信息")
            # 快速连续输入所有信息
            tab.actions.click("@name=first_name").input(first_name)
            log_info(f"已输入名字: {first_name}")
            update_progress(48)
            time.sleep(0.1)  # 极短暂停以确保输入成功

            tab.actions.click("@name=last_name").input(last_name)
            log_info(f"已输入姓氏: {last_name}")
            update_progress(49)
            time.sleep(0.1)  # 极短暂停以确保输入成功

            tab.actions.click("@name=email").input(account)
            log_info(f"已输入邮箱: {account}")
            update_progress(50)
            time.sleep(0.1)  # 极短暂停以确保输入成功

            log_process("正在提交个人信息...", show_box=True)
            tab.actions.click("@type=submit")
            update_progress(52)

    except Exception as e:
        log_error(f"注册页面访问失败: {str(e)}", show_box=True)
        return False

    handle_turnstile(tab)
    update_progress(55)

    try:
        if tab.ele("@name=password"):
            log_process("正在设置密码...", show_box=True)
            tab.ele("@name=password").input(password)
            update_progress(58)
            time.sleep(0.1)  # 极短暂停以确保输入成功

            log_process("正在提交密码...", show_box=True)
            tab.ele("@type=submit").click()
            update_progress(60)
            log_info("密码设置完成，等待系统响应...")

    except Exception as e:
        log_error(f"密码设置失败: {str(e)}", show_box=True)
        return False

    time.sleep(random.uniform(1, 3))
    update_progress(62)
    if tab.ele("This email is not available."):
        log_error("注册失败：邮箱已被使用", show_box=True)
        return False

    handle_turnstile(tab)
    update_progress(65)

    while True:
        try:
            global email_handler
            if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                log_warn("检测到中断信号，注册流程终止", show_box=True)
                return False
            if tab.ele("Account Settings"):
                log_success("注册成功 - 已进入账户设置页面", show_box=True)
                update_progress(70)
                break
            if tab.ele("@data-index=0"):
                log_process("正在获取邮箱验证码...", show_box=True)
                if not hasattr(sys.modules[__name__], 'email_handler') or email_handler is None:
                    log_warn("邮箱验证模块未初始化，正在初始化...", show_box=True)
                    from get_email_code import EmailVerificationHandler
                    email_handler = EmailVerificationHandler()
                if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                    log_warn("检测到中断信号，注册流程终止", show_box=True)
                    return False
                code = email_handler.get_verification_code()  # 使用默认清理设置
                if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                    log_warn("检测到中断信号，注册流程终止", show_box=True)
                    return False
                if not code:
                    log_error("获取验证码失败", show_box=True)
                    return False
                update_progress(66)
                log_process("正在输入验证码...", show_box=True)
                for i, digit in enumerate(code):
                    if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                        log_warn("检测到中断信号，注册流程终止", show_box=True)
                        return False
                    tab.ele(f"@data-index={i}").input(digit)
                    time.sleep(0.05)
                log_success("验证码输入完成", show_box=True)
                update_progress(68)
                handle_turnstile(tab)
                update_progress(69)
                email_handler.clean_after_verification()
                time.sleep(4)
                if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                    log_warn("检测到中断信号，注册流程终止", show_box=True)
                    return False
                invalid_code_element = tab.ele('text:Invalid one-time code', timeout=1)
                if invalid_code_element:
                    # 如果是临时邮箱，先清理所有邮件
                    if email_handler.use_temp_mail:
                        log_warn("检测到验证码输入错误，正在清理临时邮箱中的所有邮件...", show_box=True)
                        email_handler._clean_all_temp_mails()
                        
                    log_error("自动输入验证码有误，程序无法下一步！", show_box=True)
                    log_error("如果很多次都错误，请考虑清空邮箱或更换目标邮箱", show_box=True)
                    return False
                
                break
        except Exception as e:
            log_error(f"验证码处理过程出错: {str(e)}", show_box=True)
            return False

    # handle_turnstile(tab)
    # 在注册成功后保存账号信息
    account_info.set_info(account, password)
    update_progress(70)
    return True


class EmailGenerator:
    def __init__(
        self,
        password="".join(
            random.choices(
                "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********!@#$%^&*",
                k=12,
            )
        ),
    ):
        configInstance = Config()
        configInstance.print_config()
        self.domain = configInstance.get_domain()
        self.default_password = password
        self.default_first_name = self.generate_random_name()
        self.default_last_name = self.generate_random_name()

    def generate_random_name(self, length=6):
        """生成随机用户名"""
        first_letter = random.choice("abcdefghijklmnopqrstuvwxyz")
        rest_letters = "".join(
            random.choices("abcdefghijklmnopqrstuvwxyz", k=length - 1)
        )
        return first_letter + rest_letters

    def generate_email(self, length=8):
        """生成随机邮箱地址"""
        # 生成起始部分(全部小写)
        first_part = random.choice("abcdefghijklmnopqrstuvwxyz") + "".join(random.choices("aeiouy", k=1))
        
        # 生成中间部分(小写字母+数字混合)
        middle_letters = "".join(random.choices("bcdfghklmnpqrstvwxz", k=length-3))
        middle_numbers = "".join(random.choices("**********", k=2))
        
        # 混合中间部分
        middle_parts = list(middle_letters)
        for num in middle_numbers:
            pos = random.randint(0, len(middle_parts))
            middle_parts.insert(pos, num)
        middle = "".join(middle_parts)
        
        # 生成结尾部分(短时间戳)
        timestamp = str(int(time.time()))[-3:]
        
        # 组合成最终的邮箱地址
        return f"{first_part}{middle}{timestamp}@{self.domain}"

    def get_account_info(self):
        """获取完整的账号信息"""
        return {
            "email": self.generate_email(),
            "password": self.default_password,
            "first_name": self.default_first_name,
            "last_name": self.default_last_name,
        }


def get_default_env_template():
    """生成默认的 .env 文件内容"""
    return r'''# 不需要 自动注册 功能不需要配置 
#
# 怎么配置请看注释，已经详细说明了
# 哔哩哔哩 必应 百度等各大搜索网站视频网站都有相关的教程，可以自行搜索
#
# 还是不会配置？配置太繁琐？可以请群主喝杯奶茶让群主给你当牛马帮你配置文件 [滑稽]
# 或者可以直接使用群主提供的独享配置文件，到手就能用 [滑稽]

# 代理（仅在自动注册时有效）
# BROWSER_PROXY='http://127.0.0.1:2080' 

# Cloudflare 域名邮箱地址 (必填)
# 需要把自己的域名托管到 Cloudflare 并且设置邮件路由规则
# 不会配置？配置太繁琐？可以请群主喝杯奶茶让群主给你当牛马帮你配置文件 [滑稽]
# 或者可以直接使用群主提供的独享配置文件，到手就能用 [滑稽]
DOMAIN=xxxxx.com 

# 临时邮箱配置（可选必填，与IMAP二选一）
# 使用 tempmail.plus 临时邮箱服务
# 不会配置？配置太繁琐？可以请群主喝杯奶茶让群主给你当牛马帮你配置文件 [滑稽]
# 或者可以直接使用群主提供的独享配置文件，到手就能用 [滑稽]
TEMP_MAIL=<EMAIL>      # 临时邮箱完整地址
TEMP_MAIL_EPIN=your_epin_code              # 临时邮箱PIN码

# IMAP邮箱配置（可选必填，与临时邮箱二选一）
# 不再支持163邮箱，请使用其他邮箱
# 推荐使用QQ邮箱
# 不会配置？配置太繁琐？可以请群主喝杯奶茶让群主给你当牛马帮你配置文件 [滑稽]
# 或者可以直接使用群主提供的独享配置文件，到手就能用 [滑稽]
IMAP_SERVER=imap.qq.com    # [必填] IMAP服务器地址
IMAP_PORT=993              # [必填] IMAP端口，SSL通常为993
IMAP_USER=<EMAIL>       # [必填] 邮箱地址
IMAP_PASS=xxx              # [必填] 邮箱授权码或密码
# IMAP_DIR=                  # [可选] 默认为收件箱(inbox) 

# 无头模式
# BROWSER_HEADLESS='True'    # True为启用 False为不启用
# 当开启无头模式时，如果发现无法自动获取User-Agent，那么就需要配置浏览器User-Agent，否则无法过人机
# BROWSER_USER_AGENT=your_browser_user_agent         # 浏览器User-Agent

# 验证设置
TURNSTILE_TIMEOUT=20     # Turnstile 验证超时时间（秒）
VERIFICATION_CODE_TIMEOUT=180  # 验证码邮件有效期（秒），默认3分钟

# 是否自动清理所有 Cursor 邮件（避免堆积太多验证码邮件）
# 使用临时邮箱的推荐启用
CLEAN_ALL_CURSOR_MAILS='False'  # True为启用 False为不启用

# 验证码正则表达式配置（可选）
# 注意：在.env文件中配置正则表达式时，所有特殊字符如\b \d等必须使用双反斜杠(\\)表示
# 例如：\b写成\\b, \d写成\\d, 否则将导致正则表达式无法正确解析
# VERIFICATION_CODE_PATTERN="(?<![@.])\\b\\d{6}\\b(?!\\\.[a-z]+)"   # 通用验证码匹配规则，排除邮箱域名中的数字

# 终端询问自动选择配置（启用对应的配置后终端不在询问，而是通过你配置的进行自动选择）
# DEFAULTACTION=3                 # 1.仅修改ID | 2.仅进行注册流程 | 3.执行全流程（修改ID+注册）
# MODIFYUPDATE=N                  # 是否禁用自动更新？(Y/N)
# AUTOREGISTERCURSORACCOUNT=Y     # 是否自动注册Cursor账号？(Y/N) 

# 调试配置
# SHOW_VERIFICATION_EMAIL_CONTENT='True'  # 是否显示验证码邮件的完整内容

# 如需帮助可加QQ群：********* 联系群主
'''

def check_chrome_browser():
    """检查Chrome浏览器是否已安装"""
    print("\n检查 Chrome 浏览器")
    
    try:
        # 检查不同操作系统的默认Chrome安装路径
        if sys.platform == "win32":
            # Windows - 检查标准安装路径
            paths = [
                os.path.join(os.environ.get('PROGRAMFILES', ''), 'Google', 'Chrome', 'Application', 'chrome.exe'),
                os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), 'Google', 'Chrome', 'Application', 'chrome.exe'),
                os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Google', 'Chrome', 'Application', 'chrome.exe')
            ]
            
            for path in paths:
                if os.path.exists(path):
                    print("[成功] 已找到 Chrome 浏览器")
                    print(f"[路径] {path}")
                    return True
                    
        elif sys.platform == "darwin":
            # macOS
            path = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            if os.path.exists(path):
                print("[成功] 已找到 Chrome 浏览器")
                print(f"[路径] {path}")
                return True
                
        elif sys.platform.startswith("linux"):
            # Linux - 检查常见的安装路径
            paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium"
            ]
            
            for path in paths:
                if os.path.exists(path):
                    print("[成功] 已找到 Chrome 浏览器")
                    print(f"[路径] {path}")
                    return True
        
        # 如果没有找到Chrome浏览器
        print("[错误] 未找到 Chrome 浏览器，Google Chrome 浏览器必须安装在默认路径")
        print("")
        print("请安装 Google Chrome 浏览器后再运行程序")
        print("下载地址: https://www.google.com/chrome/")
        print("")
        print("如果已安装但仍显示此错误，请加群联系群主")
        
        return False
    except Exception as e:
        print("[错误] 检查 Chrome 浏览器时出错")
        print("")
        print("请确保已正确安装 Chrome 浏览器，Google Chrome 浏览器必须安装在默认路径")
        
        return False

def get_cursor_version():
    """获取Cursor版本号"""
    system = platform.system().lower()
    home = os.path.expanduser('~')
    
    # 定义可能的路径
    if system == 'windows':
        paths = [
            os.path.join(os.getenv('LOCALAPPDATA'), 'Programs', 'Cursor', 'resources', 'app', 'package.json'),
            os.path.join(os.getenv('PROGRAMFILES'), 'Cursor', 'resources', 'app', 'package.json'),
            os.path.join(os.getenv('PROGRAMFILES(X86)'), 'Cursor', 'resources', 'app', 'package.json')
        ]
    elif system == 'darwin':  # macOS
        paths = [
            '/Applications/Cursor.app/Contents/Resources/app/package.json'
        ]
    else:  # Linux
        paths = [
            '/usr/share/cursor/resources/app/package.json',
            '/opt/Cursor/resources/app/package.json',
            os.path.join(home, '.local', 'share', 'cursor', 'resources', 'app', 'package.json')
        ]
    
    # 检查所有可能的路径
    for path in paths:
        if os.path.exists(path):
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('version')
            except Exception as e:
                log_warn(f'读取版本信息时出错: {str(e)}')
    
    return None

def _get_register_only_setting():
    """
    获取仅注册模式设置
    :return: bool - 是否为仅注册模式
    """
    try:
        app_data_dir = get_app_data_dir()
        function_config_file = os.path.join(app_data_dir, "config", "function.json")
        
        # 如果文件存在，读取配置
        if os.path.exists(function_config_file):
            with open(function_config_file, 'r', encoding='utf-8') as f:
                function_settings = json.load(f)
                return function_settings.get("register_only", False)
        
        return False
    except Exception as e:
        print(f"获取仅注册模式设置时出错: {str(e)}")
        return False

def generate_random_id():
    """生成随机ID"""
    # 生成随机字节并转为十六进制字符串
    random_bytes = os.urandom(32)  # 32字节 = 64位十六进制
    return ''.join('{:02x}'.format(b) for b in random_bytes)

def generate_random_machine_info(system_type):
    """
    根据系统类型生成随机机器码信息
    :param system_type: 系统类型 (windows, mac, linux)
    :return: dict - 包含随机机器码的字典
    """
    machine_info = {}
    
    # 生成通用的telemetry相关的机器码
    # 生成auth0|user_前缀的十六进制
    prefix_hex = ''.join('{:02x}'.format(ord(c)) for c in "auth0|user_")
    random_part = generate_random_id()
    machine_id = prefix_hex + random_part
    
    # 修改为使用标准UUID格式生成macMachineId，与系统实际保存的格式匹配
    mac_machine_id = str(uuid.uuid4()).lower()  # 使用标准UUID格式
    device_id = str(uuid.uuid4()).lower()
    sqm_id = "{" + str(uuid.uuid4()).upper() + "}"
    
    machine_info = {
        "telemetry.machineId": machine_id,
        "telemetry.macMachineId": mac_machine_id,
        "telemetry.devDeviceId": device_id,
        "telemetry.sqmId": sqm_id
    }
    
    # 根据系统类型添加特定的随机值
    if system_type == "windows":
        # Windows 的 MachineGuid - 保持使用UUID格式
        machine_info["system.machineGuid"] = str(uuid.uuid4())
    
    elif system_type == "linux":
        # Linux 的 machine-id - 32字符小写十六进制，无连字符
        machine_info["system.machineId"] = ''.join(random.choices(string.hexdigits.lower(), k=32))
        # Linux 的 dbus machine-id - 32字符小写十六进制，无连字符
        machine_info["system.dbusId"] = ''.join(random.choices(string.hexdigits.lower(), k=32))
    
    elif system_type == "mac":
        # Mac 的 nvram SystemUUID - 大写UUID格式
        machine_info["system.nvramSystemUUID"] = str(uuid.uuid4()).upper()
        # Mac 的 UUID - 大写UUID格式
        machine_info["system.UUID"] = str(uuid.uuid4()).upper()
        # Mac 的 hardwareUUID - 大写UUID格式
        machine_info["system.hardwareUUID"] = str(uuid.uuid4()).upper()
        # 随机序列号 - 12字符大写字母+数字
        machine_info["system.serialNumber"] = ''.join(random.choices(
            string.ascii_uppercase + string.digits, k=12))
    
    return machine_info

def save_account_to_json(email, password, token, register_method=2):
    """
    将账号信息保存到cursor_accounts.json文件
    :param email: 邮箱地址
    :param password: 密码
    :param token: 访问令牌
    :param register_method: 注册方案，1为邮箱验证码登录（无密码），2为账号注册（有密码）
    :return: bool - 操作是否成功
    """
    try:
        update_progress(90)
        log_process("正在保存账号信息...", show_box=True)
        
        # 检查是否有自定义的账号文件路径
        if hasattr(sys.modules[__name__], 'cursor_accounts_file') and cursor_accounts_file:
            file_path = cursor_accounts_file
            log_info(f"使用自定义账号文件路径: {file_path}", show_box=True)
        else:
            # 使用已确定的STARTUP_DIRECTORY作为保存路径的根目录
            # 这确保账号信息保存在用户期望的位置，而不是临时目录
            app_dir = STARTUP_DIRECTORY
            file_path = os.path.join(app_dir, "cursor_accounts.json")
            log_info(f"使用默认账号文件路径: {file_path}", show_box=True)
            
        # 检查文件是否存在，及其权限
        if os.path.exists(file_path):
            # 检查写入权限
            if not os.access(file_path, os.W_OK):
                log_warn(f"文件 {file_path} 没有写入权限，正在尝试设置权限...", show_box=True)
                try:
                    # 设置文件权限为可写
                    os.chmod(file_path, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
                    log_success(f"文件权限设置成功", show_box=True)
                except Exception as e:
                    log_error(f"设置文件权限失败: {str(e)}", show_box=True)
                    log_warn("将尝试继续保存，但可能会失败", show_box=True)
        
        # 获取当前日期时间
        current_time = datetime.datetime.now()
        # 保存当前时间作为注册时间
        register_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 准备auth_info数据
        auth_info = {
            "cursorAuth/cachedSignUpType": "Auth_0",
            "cursorAuth/cachedEmail": email,
            "cursorAuth/accessToken": token,
            "cursorAuth/refreshToken": token
        }
        
        update_progress(93)
        
        # 获取系统类型
        system_type = get_system_type()
        
        # 检查是否为仅注册模式
        register_only_mode = _get_register_only_setting()
        machine_info = {}
        
        if register_only_mode:
            # 仅注册模式：使用随机生成的机器码
            log_info("仅注册模式：将使用随机生成的机器码", show_box=True)
            machine_info = generate_random_machine_info(system_type)
        else:
            # 正常模式：读取当前系统的机器码
            log_info("正常模式：将使用当前系统的机器码", show_box=True)
            storage_path = None
            if system_type == "windows":
                storage_path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "globalStorage", "storage.json")
            elif system_type == "mac":
                storage_path = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")
            elif system_type == "linux":
                storage_path = os.path.expanduser("~/.config/Cursor/User/globalStorage/storage.json")
                
            if storage_path and os.path.exists(storage_path):
                try:
                    with open(storage_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)
                        # 保存所有telemetry相关的机器码
                        machine_info = {
                            "telemetry.machineId": storage_data.get("telemetry.machineId", ""),
                            "telemetry.macMachineId": storage_data.get("telemetry.macMachineId", ""),
                            "telemetry.devDeviceId": storage_data.get("telemetry.devDeviceId", ""),
                            "telemetry.sqmId": storage_data.get("telemetry.sqmId", "")
                        }
                        
                        # 获取系统特定的机器码
                        if system_type == "windows":
                            import winreg
                            try:
                                # 获取MachineGuid
                                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography", 0, winreg.KEY_READ)
                                machine_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
                                machine_info["system.machineGuid"] = machine_guid
                                winreg.CloseKey(key)
                            except:
                                pass
                                
                        elif system_type == "linux":
                            try:
                                # 保存machine-id
                                with open("/etc/machine-id", 'r') as f:
                                    machine_info["system.machineId"] = f.read().strip()
                                
                                # 保存dbus machine-id
                                if os.path.exists("/var/lib/dbus/machine-id"):
                                    with open("/var/lib/dbus/machine-id", 'r') as f:
                                        machine_info["system.dbusId"] = f.read().strip()
                            except Exception as e:
                                log_warn(f"读取Linux机器码失败: {str(e)}")
                                
                        elif system_type == "mac":
                            try:
                                # 优先使用ioreg命令获取所有系统信息，这个命令权限需求较低
                                try:
                                    # 获取硬件UUID - 最可靠的系统标识
                                    hardware_uuid_output = subprocess.check_output(
                                        ["ioreg", "-rd1", "-c", "IOPlatformExpertDevice", "|", "grep", "IOPlatformUUID"],
                                        shell=True,
                                        universal_newlines=True,
                                        stderr=subprocess.DEVNULL
                                    ).strip()
                                    
                                    # 从输出中提取UUID
                                    if "IOPlatformUUID" in hardware_uuid_output and '"' in hardware_uuid_output:
                                        uuid_parts = hardware_uuid_output.split('"')
                                        if len(uuid_parts) >= 3:
                                            hardware_uuid = uuid_parts[3]
                                            machine_info["system.hardwareUUID"] = hardware_uuid
                                            # 如果获取到了硬件UUID，也把它作为主标识符保存
                                            machine_info["system.primaryIdentifier"] = hardware_uuid
                                except Exception as e:
                                    # 静默处理错误，不输出警告
                                    pass
                                
                                # 如果上面的方法失败，尝试获取Model Identifier作为备用标识
                                if "system.primaryIdentifier" not in machine_info:
                                    try:
                                        model_output = subprocess.check_output(
                                            ["ioreg", "-c", "IOPlatformExpertDevice", "-d", "2"],
                                            universal_newlines=True,
                                            stderr=subprocess.DEVNULL
                                        ).strip()
                                        
                                        # 从输出中查找model标识符
                                        model_id = None
                                        for line in model_output.split('\n'):
                                            if "model" in line and "=" in line:
                                                model_parts = line.split('=')
                                                if len(model_parts) >= 2 and '"' in model_parts[1]:
                                                    model_id = model_parts[1].strip().strip('"').strip()
                                                    break
                                        
                                        if model_id:
                                            machine_info["system.modelIdentifier"] = model_id
                                            # 如果没有主标识符，使用模型ID作为备用
                                            machine_info["system.primaryIdentifier"] = model_id
                                    except Exception:
                                        # 静默处理错误
                                        pass
                                
                                # 只有在必要的情况下尝试使用system_profiler，这是权限要求高的命令
                                if "system.primaryIdentifier" not in machine_info:
                                    # 生成一个随机ID作为最后的备用方案
                                    import uuid
                                    backup_id = str(uuid.uuid4())
                                    machine_info["system.generatedUUID"] = backup_id
                                    machine_info["system.primaryIdentifier"] = backup_id
                                    
                                    # 尝试system_profiler，但不输出错误
                                    try:
                                        with open(os.devnull, 'w') as devnull:
                                            system_info = subprocess.check_output(
                                                ["system_profiler", "SPHardwareDataType"],
                                                universal_newlines=True,
                                                stderr=devnull
                                            ).strip()
                                            
                                            # 解析输出查找有用信息
                                            for line in system_info.split('\n'):
                                                if "Hardware UUID" in line and ":" in line:
                                                    uuid_value = line.split(':', 1)[1].strip()
                                                    machine_info["system.hardwareUUID"] = uuid_value
                                                    machine_info["system.primaryIdentifier"] = uuid_value
                                                elif "Serial Number" in line and ":" in line:
                                                    serial = line.split(':', 1)[1].strip()
                                                    machine_info["system.serialNumber"] = serial
                                    except Exception:
                                        # 静默处理错误
                                        pass
                            except Exception:
                                # 捕获所有异常，确保即使获取机器码完全失败也不会中断流程
                                # 生成随机标识符作为最后的备用方案
                                import uuid
                                fallback_id = str(uuid.uuid4())
                                machine_info["system.fallbackIdentifier"] = fallback_id
                                machine_info["system.primaryIdentifier"] = fallback_id
                except Exception as e:
                    log_warn(f"读取机器码信息失败: {str(e)}")
        
        # 创建账号数据结构
        account_data = {
            "email": email,
            "password": password if register_method == 2 and password else "更快方案自动注册没有密码可保存",
            "auth_info": auth_info,
            "register_time": register_time,
            "machine_info": machine_info,  # 添加机器码信息
            "system_type": system_type     # 添加系统类型信息
        }
        
        # 记录密码字段情况
        if register_method == 2 and password:
            log_info("账号注册模式，保存密码字段", show_box=True)
        else:
            log_info("邮箱验证码登录模式，保存密码说明文字", show_box=True)
        
        # 检查文件是否存在
        accounts = []
        if os.path.exists(file_path):
            try:
                with open(file_path, "r", encoding="utf-8") as file:
                    accounts = json.load(file)
            except json.JSONDecodeError:
                log_warn(f"文件 {file_path} 格式不正确，将创建新文件", show_box=True)
        
        # 添加账号到列表
        accounts.append(account_data)
        
        # 保存到文件前检查目录是否存在
        dir_path = os.path.dirname(file_path)
        if dir_path and not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path)
                log_info(f"已创建目录: {dir_path}", show_box=True)
            except Exception as e:
                log_error(f"创建目录失败: {str(e)}", show_box=True)
                print_box(
                    "保存账号信息失败",
                    [
                        (Colors.RED, f"[错误] 无法创建目录: {dir_path}"),
                        (Colors.RED, f"错误信息: {str(e)}")
                    ]
                )
                return False
        
        # 保存到文件
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(accounts, f, ensure_ascii=False, indent=2)
            
            # log_success(f"账号数据已保存到: {file_path}", show_box=True)
            update_progress(100)
            return True
        except Exception as e:
            log_error(f"保存账号信息到文件 {file_path} 失败: {str(e)}", show_box=True)
            print_box(
                "保存账号信息失败",
                [
                    (Colors.RED, f"[错误] 无法保存到文件: {file_path}"),
                    (Colors.RED, f"错误信息: {str(e)}")
                ]
            )
            return False
    except Exception as e:
        log_error(f"保存账号信息时出错: {str(e)}", show_box=True)
        return False
    
    # 日志说明保存成功
    log_info(f"账号信息已成功保存到: {file_path}", show_box=True)
    log_info(f"自动注册成功！", show_box=True)
    return True

def ask_disable_update():
    """询问用户是否禁用自动更新"""
    # 尝试从环境变量中获取设置
    modify_update = os.getenv('MODIFYUPDATE', '').strip().upper()
    
    # 如果设置了有效的环境变量，直接使用
    if modify_update in ['Y', 'N']:
        print("\n自动更新设置")
        print(f"  [配置] 使用环境变量配置: MODIFYUPDATE={modify_update}")
        return modify_update == 'Y'
    
    # 否则询问用户
    print("\n自动更新设置")
    print("  [询问] 是否禁用自动更新？(y/n) [默认: n]: ", end='')
    
    while True:
        choice = input().strip().lower()
        
        if choice == '':
            print("  [选择] 使用默认选项: N")
            return False
            
        if choice in ['y', 'yes']:
            print(f"  [选择] 您选择了: {choice.upper()}")
            return True
            
        if choice in ['n', 'no']:
            print(f"  [选择] 您选择了: {choice.upper()}")
            return False
            
        print("  [错误] 请输入正确的选项 (y 或 n): ", end='')
    
    return False

def check_and_elevate_privileges():
    """检查是否具有管理员权限，如果没有则尝试提升"""
    system = platform.system().lower()
    
    # Mac系统提权处理
    if system == "darwin":
        try:
            # 检查是否有管理员权限
            if os.geteuid() == 0:
                # print("已获得管理员权限")
                return True
            
            print("需要管理员权限，正在提升...")
            
            # 获取当前脚本路径
            script = os.path.abspath(sys.argv[0])
            script_dir = os.path.dirname(script)
            
            # 创建临时AppleScript文件
            temp_script_path = os.path.join(script_dir, "elevate_temp.scpt")
            
            # 创建AppleScript内容 - 使用do shell script with administrator privileges
            applescript_content = """
tell application "Terminal"
    do shell script "cd '" & "%s" & "' && '" & "%s" & "' '" & "%s" & "'" with administrator privileges
end tell
""" % (script_dir.replace("'", "\\'"), sys.executable.replace("'", "\\'"), script.replace("'", "\\'"))
            
            # 写入临时AppleScript文件
            with open(temp_script_path, 'w') as f:
                f.write(applescript_content)
            
            # 使用osascript执行AppleScript
            print(f"执行权限提升脚本: {temp_script_path}")
            os.system(f"osascript {temp_script_path}")
            
            # 删除临时脚本文件
            if os.path.exists(temp_script_path):
                os.remove(temp_script_path)
            
            # 当前进程不再需要继续运行
            sys.exit(0)
        except Exception as e:
            print(f"权限提升失败: {e}")
            
            # 尝试备用方法 - 使用Shell脚本
            try:
                # 创建临时shell脚本
                temp_sh_path = os.path.join(script_dir, "elevate_temp.sh")
                with open(temp_sh_path, 'w') as f:
                    f.write(f"""#!/bin/bash
cd "{script_dir}"
sudo "{sys.executable}" "{script}"
""")
                os.chmod(temp_sh_path, 0o755)  # 添加执行权限
                
                # 在一个新的终端窗口中运行此脚本
                os.system(f"open -a Terminal {temp_sh_path}")
                
                # 删除临时shell脚本
                if os.path.exists(temp_sh_path):
                    time.sleep(1)  # 给一点时间让Terminal打开
                    os.remove(temp_sh_path)
            except Exception as e2:
                print(f"备用提权方法也失败: {e2}")
            
            # 当前进程不再需要继续运行
            sys.exit(0)
    
    # Windows系统提权处理
    elif system == "windows":
        try:
            # 判断是否已有管理员权限
            if ctypes.windll.shell32.IsUserAnAdmin() != 0:
                # print("已获得管理员权限")
                return True
                
            print("需要管理员权限，正在提升...")
            
            # 获取当前脚本路径
            script = os.path.abspath(sys.argv[0])
            
            # 创建命令行参数字符串
            params = ' '.join([f'"{item}"' for item in sys.argv[1:]])
            
            # 使用PowerShell启动提升的进程
            cmd = f'powershell.exe Start-Process -Verb RunAs "{sys.executable}" -ArgumentList "{script} {params}" -Wait'
            
            subprocess.run(cmd, shell=True)
            
            # 原进程不再继续执行
            sys.exit(0)
        except Exception as e:
            print(f"提权失败: {e}")
            return False
    
    # Linux系统提权处理
    elif system == "linux":
        try:
            # 检查是否有root权限
            if os.geteuid() == 0:
                # print("已获得管理员权限")
                return True
                
            print("需要管理员权限，正在提升...")
            
            # 获取当前脚本路径
            script = os.path.abspath(sys.argv[0])
            
            # 使用pkexec或sudo提升权限
            if os.path.exists("/usr/bin/pkexec"):
                cmd = ["pkexec", sys.executable, script] + sys.argv[1:]
                subprocess.Popen(cmd)
            else:
                cmd = ["sudo", sys.executable, script] + sys.argv[1:]
                subprocess.Popen(cmd)
                
            # 原进程不再继续执行
            sys.exit(0)
        except Exception as e:
            print(f"提权失败: {e}")
            return False
    
    # 未知系统
    else:
        print(f"不支持的操作系统: {system}")
        return False
    
    return True

def restore_backup():
    """恢复备份功能"""
    system_type = get_system_type()
    print_box(
        "恢复备份",
        [
            (Colors.GREEN, "欢迎使用备份恢复功能"),
            (Colors.WHITE, f"当前系统类型: {system_type.upper()}")
        ]
    )
    
    # 定义不同系统下的备份路径
    if system_type == "windows":
        appdata = os.getenv("APPDATA")
        localappdata = os.getenv("LOCALAPPDATA")
        config_backup_dir = os.path.join(appdata, "Cursor", "User", "globalStorage", "backups") if appdata else None
        updater_path = os.path.join(localappdata, "cursor-updater") if localappdata else None
    elif system_type == "mac":
        home = os.getenv("HOME", "")
        config_backup_dir = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/backups")
        updater_path = os.path.join(home, "Library/Application Support/Caches/cursor-updater")
    elif system_type == "linux":
        config_backup_dir = os.path.expanduser("~/.config/Cursor/User/globalStorage/backups")
        updater_path = os.path.expanduser("~/.config/cursor-updater")
    else:
        log_error(f"不支持的操作系统: {system_type}", show_box=True)
        return False
    
    # 检查备份目录是否存在
    if not os.path.exists(config_backup_dir) and not os.path.exists(os.path.dirname(updater_path)):
        log_error("未找到任何备份目录", show_box=True)
        return False
    
    # 列出可以恢复的备份类型
    backup_types = []
    
    # 检查配置备份
    storage_backups = []
    if os.path.exists(config_backup_dir):
        storage_backups = sorted(glob.glob(os.path.join(config_backup_dir, "storage.json.backup_*")))
    
    # 检查系统ID备份
    system_id_backups = []
    if os.path.exists(config_backup_dir):
        if system_type == "windows":
            system_id_backups = sorted(glob.glob(os.path.join(config_backup_dir, "MachineGuid_*.reg")))
        else:
            system_id_backups = sorted(glob.glob(os.path.join(config_backup_dir, "system_id.backup_*")))
    
    # 检查更新程序备份
    updater_backups = []
    if os.path.exists(os.path.dirname(updater_path)):
        updater_backups = sorted(glob.glob(f"{updater_path}_*.zip"))
    
    # 检查是否存在更新禁用文件
    updater_file_exists = os.path.exists(updater_path) and os.path.isfile(updater_path)
    
    # 构建可恢复类型列表
    if storage_backups:
        backup_types.append("1")
    if system_id_backups:
        backup_types.append("2")
    if updater_backups:
        backup_types.append("3")
    if updater_file_exists:
        backup_types.append("4")
    
    if not backup_types:
        log_error("未找到任何有效备份", show_box=True)
        return False
    
    # 显示备份类型选择菜单
    backup_menu = [
        (Colors.YELLOW, "请选择要恢复的备份类型:"),
        (Colors.WHITE, "")
    ]
    
    if "1" in backup_types:
        backup_menu.append((Colors.GREEN, f"1. 恢复配置文件备份 ({len(storage_backups)} 个可用备份)"))
        if system_type == "windows":
            backup_menu.append((Colors.WHITE, f"   - 恢复Cursor的storage.json配置文件"))
            backup_menu.append((Colors.WHITE, f"   - 包含应用级别的机器码(telemetry.machineId等)"))
            backup_menu.append((Colors.WHITE, f"   - 位置: %APPDATA%\\Cursor\\User\\globalStorage\\storage.json"))
        elif system_type == "mac":
            backup_menu.append((Colors.WHITE, f"   - 恢复Cursor的storage.json配置文件"))
            backup_menu.append((Colors.WHITE, f"   - 包含应用级别的机器码(telemetry.machineId等)"))
            backup_menu.append((Colors.WHITE, f"   - 位置: ~/Library/Application Support/Cursor/User/globalStorage/storage.json"))
        elif system_type == "linux":
            backup_menu.append((Colors.WHITE, f"   - 恢复Cursor的storage.json配置文件"))
            backup_menu.append((Colors.WHITE, f"   - 包含应用级别的机器码(telemetry.machineId等)"))
            backup_menu.append((Colors.WHITE, f"   - 位置: ~/.config/Cursor/User/globalStorage/storage.json"))
    
    if "2" in backup_types:
        if system_type == "windows":
            backup_menu.append((Colors.WHITE, ""))
            backup_menu.append((Colors.GREEN, f"2. 恢复系统ID备份 ({len(system_id_backups)} 个可用备份)"))
            backup_menu.append((Colors.WHITE, f"   - 恢复Windows系统级别的机器码(MachineGuid)"))
            backup_menu.append((Colors.WHITE, f"   - 这是注册表中的系统唯一标识符"))
            backup_menu.append((Colors.WHITE, f"   - 位置: HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography\\MachineGuid"))
        elif system_type == "linux":
            backup_menu.append((Colors.WHITE, ""))
            backup_menu.append((Colors.GREEN, f"2. 恢复系统ID备份 ({len(system_id_backups)} 个可用备份)"))
            backup_menu.append((Colors.WHITE, f"   - 恢复Linux系统级别的机器码(machine-id)"))
            backup_menu.append((Colors.WHITE, f"   - 这是系统的唯一标识符"))
            backup_menu.append((Colors.WHITE, f"   - 位置: /etc/machine-id"))
        elif system_type == "mac":
            backup_menu.append((Colors.WHITE, ""))
            backup_menu.append((Colors.GREEN, f"2. 恢复系统ID备份 ({len(system_id_backups)} 个可用备份)"))
            backup_menu.append((Colors.WHITE, f"   - 恢复Mac系统级别的机器码"))
            backup_menu.append((Colors.WHITE, f"   - 包含IOPlatformExpertDevice和系统UUID信息"))
    
    if "3" in backup_types:
        backup_menu.append((Colors.WHITE, ""))
        backup_menu.append((Colors.GREEN, f"3. 恢复自动更新 ({len(updater_backups)} 个可用备份)"))
        if system_type == "windows":
            backup_menu.append((Colors.WHITE, f"   - 恢复Cursor的自动更新功能"))
            backup_menu.append((Colors.WHITE, f"   - 删除阻止更新的文件，恢复updater目录"))
            backup_menu.append((Colors.WHITE, f"   - 位置: %LOCALAPPDATA%\\cursor-updater"))
        elif system_type == "mac":
            backup_menu.append((Colors.WHITE, f"   - 恢复Cursor的自动更新功能"))
            backup_menu.append((Colors.WHITE, f"   - 删除阻止更新的文件，恢复updater目录"))
            backup_menu.append((Colors.WHITE, f"   - 位置: ~/Library/Application Support/Caches/cursor-updater"))
        elif system_type == "linux":
            backup_menu.append((Colors.WHITE, f"   - 恢复Cursor的自动更新功能"))
            backup_menu.append((Colors.WHITE, f"   - 删除阻止更新的文件，恢复updater目录"))
            backup_menu.append((Colors.WHITE, f"   - 位置: ~/.config/cursor-updater"))
        backup_menu.append((Colors.YELLOW, f"   - 注意: 恢复后备份文件会被自动删除"))
    
    if "4" in backup_types:
        backup_menu.append((Colors.WHITE, ""))
        backup_menu.append((Colors.GREEN, f"4. 启用自动更新"))
        if system_type == "windows":
            backup_menu.append((Colors.WHITE, f"   - 启用Cursor的自动更新功能"))
            backup_menu.append((Colors.WHITE, f"   - 删除阻止更新的占位符文件"))
            backup_menu.append((Colors.WHITE, f"   - 位置: %LOCALAPPDATA%\\cursor-updater"))
        elif system_type == "mac":
            backup_menu.append((Colors.WHITE, f"   - 启用Cursor的自动更新功能"))
            backup_menu.append((Colors.WHITE, f"   - 删除阻止更新的占位符文件"))
            backup_menu.append((Colors.WHITE, f"   - 位置: ~/Library/Application Support/Caches/cursor-updater"))
        elif system_type == "linux":
            backup_menu.append((Colors.WHITE, f"   - 启用Cursor的自动更新功能"))
            backup_menu.append((Colors.WHITE, f"   - 删除阻止更新的占位符文件"))
            backup_menu.append((Colors.WHITE, f"   - 位置: ~/.config/cursor-updater"))
    
    print_box("备份恢复选项", backup_menu)
    
    # 获取用户选择
    while True:
        # 构建选项提示字符串
        options_str = ",".join(sorted(backup_types))
        choice = input(f"{Colors.CYAN}请输入选项 ({options_str}): {Colors.NC}").strip()
        if choice == "0":
            log_info("已返回主菜单", show_box=True)
            return True
        if choice in backup_types:
            break
        log_error(f"无效的选项，请重新输入", show_box=True)
    
    # 根据用户选择处理不同类型的备份
    if choice == "1":  # 恢复配置文件
        return restore_config_backup(storage_backups)
    elif choice == "2":  # 恢复系统ID
        return restore_system_id_backup(system_id_backups, system_type)
    elif choice == "3":  # 恢复自动更新
        return restore_updater_backup(updater_backups, system_type)
    elif choice == "4":  # 启用自动更新（删除占位符文件）
        return enable_auto_update(system_type)
    
    return False

def restore_config_backup(backups):
    """恢复配置文件备份"""
    if not backups:
        log_error("没有可用的配置文件备份", show_box=True)
        return False
    
    # 显示可用的备份文件
    print_box(
        "可用的配置文件备份",
        [(Colors.YELLOW, "请选择要恢复的备份:")]
    )
    
    for i, backup in enumerate(backups):
        backup_name = os.path.basename(backup)
        # 解析时间戳
        try:
            # 从名称中提取时间戳部分 (storage.json.backup_YYYYMMDD_HHMMSS)
            timestamp_str = backup_name.split("backup_")[1]
            timestamp = datetime.datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
            timestamp_display = timestamp.strftime("%Y-%m-%d %H:%M:%S")
        except:
            timestamp_display = "未知时间"
        
        print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}{i+1}.{Colors.NC} {backup_name} ({timestamp_display})")
    
    print(f"{Colors.BLUE}│{Colors.NC} {Colors.WHITE}0. 返回上一级{Colors.NC}")
    print(f"{Colors.BLUE}└{'─' * 60}┘{Colors.NC}")
    
    # 获取用户选择
    while True:
        choice = input(f"{Colors.CYAN}请输入要恢复的备份编号 (0-{len(backups)}): {Colors.NC}").strip()
        if choice == "0":
            return restore_backup()
        
        try:
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(backups):
                break
        except:
            pass
        
        log_error(f"无效的选项，请重新输入", show_box=True)
    
    # 获取目标备份文件
    backup_file = backups[choice_idx]
    system_type = get_system_type()
    
    # 确定配置文件路径
    if system_type == "windows":
        appdata = os.getenv("APPDATA")
        storage_file = os.path.join(appdata, "Cursor", "User", "globalStorage", "storage.json") if appdata else None
    elif system_type == "mac":
        storage_file = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")
    elif system_type == "linux":
        storage_file = os.path.expanduser("~/.config/Cursor/User/globalStorage/storage.json")
    else:
        log_error(f"不支持的操作系统: {system_type}", show_box=True)
        return False
    
    if not storage_file or not os.path.exists(os.path.dirname(storage_file)):
        log_error("无法确定配置文件路径", show_box=True)
        return False
    
    # 恢复备份前先备份当前配置（如果存在）
    if os.path.exists(storage_file):
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        current_backup = f"{storage_file}.before_restore_{timestamp}"
        try:
            # 获取当前文件权限
            current_mode = os.stat(storage_file).st_mode
            
            # 设置文件为可写
            try:
                os.chmod(storage_file, 0o644)  # 设置为可写
                log_debug("已临时移除文件只读属性")
            except Exception as e:
                log_error(f"无法修改文件权限: {str(e)}")
                return False
            
            # 创建备份
            shutil.copy2(storage_file, current_backup)
            log_info(f"已创建当前配置的备份: {current_backup}", show_box=True)
            
            # 恢复原始权限
            try:
                os.chmod(storage_file, current_mode)
                log_debug("已恢复文件原始权限")
            except Exception as e:
                log_warn(f"恢复文件权限失败: {str(e)}")
                
        except Exception as e:
            log_warn(f"无法备份当前配置: {str(e)}", show_box=True)
    
    # 恢复备份
    try:
        # 如果目标文件存在，获取其当前权限
        if os.path.exists(storage_file):
            current_mode = os.stat(storage_file).st_mode
        else:
            current_mode = 0o644  # 如果文件不存在，使用默认权限
        
        # 设置目标文件为可写（如果存在）
        if os.path.exists(storage_file):
            try:
                os.chmod(storage_file, 0o644)  # 设置为可写
                log_debug("已临时移除目标文件只读属性")
            except Exception as e:
                log_error(f"无法修改目标文件权限: {str(e)}")
                return False
        
        # 复制备份文件到目标位置
        shutil.copy2(backup_file, storage_file)
        
        # 恢复原始权限
        try:
            os.chmod(storage_file, current_mode)
            log_debug("已恢复文件原始权限")
        except Exception as e:
            log_warn(f"恢复文件权限失败: {str(e)}")
        
        log_success(f"配置文件恢复成功", show_box=True)
        return True
    except Exception as e:
        log_error(f"恢复配置文件失败: {str(e)}", show_box=True)
        return False

def restore_system_id_backup(backups, system_type):
    """恢复系统ID备份"""
    if not backups:
        log_error("没有可用的系统ID备份", show_box=True)
        return False
    
    # 显示可用的备份文件
    print_box(
        "可用的系统ID备份",
        [(Colors.YELLOW, "请选择要恢复的备份:")]
    )
    
    for i, backup in enumerate(backups):
        backup_name = os.path.basename(backup)
        # 解析时间戳
        try:
            if system_type == "windows":
                # 从名称中提取时间戳部分 (MachineGuid_YYYYMMDD_HHMMSS.reg)
                timestamp_str = backup_name.split("MachineGuid_")[1].split(".reg")[0]
            else:
                # 从名称中提取时间戳部分 (system_id.backup_YYYYMMDD_HHMMSS)
                timestamp_str = backup_name.split("backup_")[1]
            
            timestamp = datetime.datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
            timestamp_display = timestamp.strftime("%Y-%m-%d %H:%M:%S")
        except:
            timestamp_display = "未知时间"
        
        print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}{i+1}.{Colors.NC} {backup_name} ({timestamp_display})")
    
    print(f"{Colors.BLUE}│{Colors.NC} {Colors.WHITE}0. 返回上一级{Colors.NC}")
    print(f"{Colors.BLUE}└{'─' * 60}┘{Colors.NC}")
    
    # 获取用户选择
    while True:
        choice = input(f"{Colors.CYAN}请输入要恢复的备份编号 (0-{len(backups)}): {Colors.NC}").strip()
        if choice == "0":
            return restore_backup()
        
        try:
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(backups):
                break
        except:
            pass
        
        log_error(f"无效的选项，请重新输入", show_box=True)
    
    # 获取目标备份文件
    backup_file = backups[choice_idx]
    
    # 根据不同的系统类型进行恢复
    if system_type == "windows":
        # Windows 使用注册表文件恢复
        try:
            import subprocess
            import winreg
            
            # 获取当前的 MachineGuid
            try:
                key = winreg.OpenKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    r"SOFTWARE\Microsoft\Cryptography",
                    0,
                    winreg.KEY_READ | winreg.KEY_WOW64_64KEY
                )
                current_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
                winreg.CloseKey(key)
            except Exception:
                current_guid = "无法读取当前值"
            
            # 首先尝试直接使用reg import命令
            try:
                result = subprocess.run(['reg', 'import', backup_file], 
                                    capture_output=True, 
                                    text=True)
                if result.returncode == 0:
                    log_success(f"系统ID恢复成功", show_box=True)
                    log_warn(f"请重启系统以使更改生效", show_box=True)
                    return True
            except:
                pass
                
            # 如果reg import失败，尝试直接修改注册表
            log_info("正在尝试直接修改注册表...", show_box=True)
            
            # 读取.reg文件内容
            with open(backup_file, 'r', encoding='utf-16') as f:
                content = f.read()
            
            # 提取MachineGuid值
            import re
            match = re.search(r'"MachineGuid"="([^"]+)"', content)
            if not match:
                log_error("无法从备份文件中提取MachineGuid值", show_box=True)
                return False
                
            machine_guid = match.group(1)
            
            print_box(
                "MachineGuid 信息",
                [
                    (Colors.WHITE, "当前系统的 MachineGuid:"),
                    (Colors.YELLOW, f"  {current_guid}"),
                    (Colors.WHITE, ""),
                    (Colors.WHITE, "即将恢复为:"),
                    (Colors.GREEN, f"  {machine_guid}")
                ]
            )
            
            # 直接写入注册表
            try:
                key = winreg.OpenKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    r"SOFTWARE\Microsoft\Cryptography",
                    0,
                    winreg.KEY_SET_VALUE | winreg.KEY_WOW64_64KEY
                )
                winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, machine_guid)
                winreg.CloseKey(key)
                log_success(f"系统ID恢复成功", show_box=True)
                log_warn(f"请重启系统以使更改生效", show_box=True)
                return True
            except Exception as e:
                log_error(f"直接修改注册表失败: {str(e)}", show_box=True)
                
            # 如果直接修改失败，尝试使用PowerShell
            log_info("正在尝试使用PowerShell修改注册表...", show_box=True)
            ps_command = f'Set-ItemProperty -Path "HKLM:\\SOFTWARE\\Microsoft\\Cryptography" -Name "MachineGuid" -Value "{machine_guid}"'
            result = subprocess.run(['powershell', '-Command', ps_command], 
                                capture_output=True,
                                text=True)
            
            if result.returncode == 0:
                log_success(f"系统ID恢复成功", show_box=True)
                log_warn(f"请重启系统以使更改生效", show_box=True)
                return True
            
            # 如果所有方法都失败
            print_box(
                "恢复失败",
                [
                    (Colors.RED, "所有恢复方法都失败了，请尝试以下步骤:"),
                    (Colors.WHITE, ""),
                    (Colors.WHITE, "1. 以管理员身份运行命令提示符(CMD)"),
                    (Colors.WHITE, "2. 执行以下命令:"),
                    (Colors.YELLOW, f"   reg import \"{backup_file}\""),
                    (Colors.WHITE, ""),
                    (Colors.WHITE, "如果仍然失败，请尝试:"),
                    (Colors.WHITE, "1. 打开注册表编辑器(regedit)"),
                    (Colors.WHITE, "2. 导航到:"),
                    (Colors.YELLOW, "   HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography"),
                    (Colors.WHITE, "3. 右键点击MachineGuid"),
                    (Colors.WHITE, "4. 选择修改，输入以下值:"),
                    (Colors.YELLOW, f"   {machine_guid}")
                ]
            )
            return False
            
        except Exception as e:
            log_error(f"恢复系统ID失败: {str(e)}", show_box=True)
            return False
    else:
        # Linux/Mac 需要具体解析备份文件内容并恢复
        log_info(f"正在解析系统ID备份文件: {backup_file}", show_box=True)
        
        try:
            with open(backup_file, 'r') as f:
                backup_content = f.read()
            
            if system_type == "linux":
                # 检查是否包含machine-id
                if '/etc/machine-id' in backup_content:
                    # 提取machine-id值
                    machine_id = None
                    for line in backup_content.split('\n'):
                        if line.strip() and not line.startswith('#') and not line.startswith('##'):
                            machine_id = line.strip()
                            break
                    
                    if machine_id:
                        # 需要root权限恢复machine-id
                        log_warn("需要管理员权限恢复系统ID", show_box=True)
                        
                        # 备份当前ID
                        try:
                            if os.path.exists('/etc/machine-id'):
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                subprocess.run(['sudo', 'cp', '/etc/machine-id', f'/etc/machine-id.before_restore_{timestamp}'], 
                                             check=True)
                        except:
                            pass
                        
                        # 恢复ID
                        with open('temp_machine_id', 'w') as f:
                            f.write(machine_id)
                        
                        subprocess.run(['sudo', 'mv', 'temp_machine_id', '/etc/machine-id'], 
                                     check=True)
                        
                        log_success(f"系统ID恢复成功", show_box=True)
                        log_warn(f"请重启系统以使更改生效", show_box=True)
                        return True
                    else:
                        log_error("无法从备份中提取machine-id", show_box=True)
                        return False
                else:
                    log_error("备份文件不包含有效的machine-id信息", show_box=True)
                    return False
            elif system_type == "mac":
                # Mac系统恢复较为复杂，需要更多的系统级操作
                log_warn("Mac系统ID恢复需要系统级操作", show_box=True)
                log_warn("建议使用时间机器或系统恢复工具恢复系统ID", show_box=True)
                return False
        except Exception as e:
            log_error(f"恢复系统ID失败: {str(e)}", show_box=True)
            return False

def force_delete_windows(path):
    """Windows系统下的强制删除函数"""
    import subprocess
    import time
    import ctypes
    from ctypes import windll
    
    def is_admin():
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    try:
        if not is_admin():
            log_error("需要管理员权限才能执行强制删除", show_box=True)
            return False
            
        # 1. 获取文件所有权（修复命令参数）
        try:
            # 使用正确的参数组合
            subprocess.run(
                ['takeown', '/F', path, '/R', '/D', 'Y'],
                capture_output=True,
                check=True,
                shell=True  # 添加 shell=True 来修复权限问题
            )
            log_info("已获取文件所有权", show_box=True)
        except Exception as e:
            # 如果 takeown 失败，尝试使用 cacls
            try:
                subprocess.run(
                    ['cacls', path, '/E', '/G', 'Administrators:F'],
                    capture_output=True,
                    check=True,
                    shell=True
                )
                log_info("已通过 cacls 获取文件权限", show_box=True)
            except Exception as e2:
                log_warn(f"修改文件权限失败，将继续尝试删除", show_box=True)
            
        # 2. 使用更强力的权限修改
        try:
            # 获取当前用户名和系统用户组
            import getpass
            username = getpass.getuser()
            
            # 移除所有现有权限并授予完全控制权限
            cmds = [
                # 禁用继承并移除所有权限
                ['icacls', path, '/inheritance:d'],
                ['icacls', path, '/remove:g', '*'],
                # 授予当前用户完全控制权限
                ['icacls', path, '/grant:r', f'{username}:(F)'],
                # 授予 SYSTEM 完全控制权限
                ['icacls', path, '/grant:r', 'SYSTEM:(F)'],
                # 授予管理员组完全控制权限
                ['icacls', path, '/grant:r', 'Administrators:(F)'],
            ]
            
            for cmd in cmds:
                subprocess.run(cmd, capture_output=True, shell=True)
                
            log_info("已修改文件权限", show_box=True)
        except Exception as e:
            log_warn(f"修改文件权限失败，将继续尝试删除", show_box=True)
            
        # 3. 尝试多种删除方法（重新排序，将方法2放在最前面）
        deletion_methods = [
            # 方法1(原方法2): 使用 cmd del/rmdir 命令强制删除
            lambda: subprocess.run(
                ['cmd', '/c', 'rmdir', '/S', '/Q', path] if os.path.isdir(path) 
                else ['cmd', '/c', 'del', '/F', '/S', '/Q', path],
                capture_output=True,
                check=True,
                shell=True
            ),
            
            # 方法2(原方法1): 使用 Python 内置删除
            lambda: (shutil.rmtree(path) if os.path.isdir(path) else os.remove(path)),
            
            # 方法3: 使用 PowerShell Remove-Item 命令
            lambda: subprocess.run(
                ['powershell', '-Command', 
                 f'Remove-Item -Path "{path}" -Force -Recurse'],
                capture_output=True,
                check=True,
                shell=True
            ),
            
            # 方法4: 使用 Windows API 直接删除
            lambda: windll.kernel32.DeleteFileW(path) if os.path.isfile(path) else None
        ]
        
        for i, delete_method in enumerate(deletion_methods, 1):
            try:
                delete_method()
                if not os.path.exists(path):
                    log_success(f"删除成功（方法{i}）", show_box=True)
                    return True
            except Exception:
                continue
                
        # 4. 如果以上方法都失败，尝试重命名并移动
        if os.path.exists(path):
            try:
                import tempfile
                temp_dir = tempfile.gettempdir()
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                new_path = os.path.join(temp_dir, f'cursor_updater_old_{timestamp}')
                
                # 使用 move 命令
                subprocess.run(
                    ['cmd', '/c', 'move', '/Y', path, new_path],
                    capture_output=True,
                    check=True,
                    shell=True
                )
                log_info(f"已将文件移动到临时目录: {new_path}", show_box=True)
                log_warn("建议在系统重启后手动删除该文件", show_box=True)
                return True
            except Exception as move_error:
                log_error("所有删除方法都失败", show_box=True)
                print_box(
                    "请尝试以下步骤",
                    [
                        (Colors.WHITE, "1. 关闭所有 Cursor 相关进程"),
                        (Colors.WHITE, "2. 使用任务管理器结束以下进程:"),
                        (Colors.YELLOW, "   - Cursor.exe"),
                        (Colors.YELLOW, "   - cursor-updater.exe"),
                        (Colors.WHITE, "3. 重启系统"),
                        (Colors.WHITE, "4. 手动删除或重命名文件夹:"),
                        (Colors.YELLOW, f"   {path}")
                    ]
                )
                return False
                
    except Exception as e:
        log_error(f"强制删除过程出错: {str(e)}", show_box=True)
        return False

def restore_updater_backup(backups, system_type):
    """恢复自动更新备份"""
    if not backups:
        log_error("没有可用的自动更新备份", show_box=True)
        return False
    
    # 显示可用的备份文件
    print_box(
        "可用的自动更新备份",
        [(Colors.YELLOW, "请选择要恢复的备份:")]
    )
    
    for i, backup in enumerate(backups):
        backup_name = os.path.basename(backup)
        # 解析时间戳
        try:
            # 从名称中提取时间戳部分 (cursor-updater_YYYYMMDD_HHMMSS.zip)
            timestamp_str = backup_name.split("_")[-2] + "_" + backup_name.split("_")[-1].split(".")[0]
            timestamp = datetime.datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
            timestamp_display = timestamp.strftime("%Y-%m-%d %H:%M:%S")
        except:
            timestamp_display = "未知时间"
        
        print(f"{Colors.BLUE}│{Colors.NC} {Colors.CYAN}{i+1}.{Colors.NC} {backup_name} ({timestamp_display})")
    
    print(f"{Colors.BLUE}│{Colors.NC} {Colors.WHITE}0. 返回上一级{Colors.NC}")
    print(f"{Colors.BLUE}└{'─' * 60}┘{Colors.NC}")
    
    # 获取用户选择
    while True:
        choice = input(f"{Colors.CYAN}请输入要恢复的备份编号 (0-{len(backups)}): {Colors.NC}").strip()
        if choice == "0":
            return restore_backup()
        
        try:
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(backups):
                break
        except:
            pass
        
        log_error(f"无效的选项，请重新输入", show_box=True)
    
    # 获取目标备份文件
    backup_file = backups[choice_idx]
    
    # 确定updater路径
    if system_type == "windows":
        updater_path = os.path.join(os.getenv('LOCALAPPDATA', ''), 'cursor-updater')
    elif system_type == "mac":
        updater_path = os.path.join(os.getenv('HOME', ''), 'Library/Application Support/Caches/cursor-updater')
    elif system_type == "linux":
        updater_path = os.path.join(os.getenv('HOME', ''), '.config/cursor-updater')
    else:
        log_error(f"不支持的操作系统: {system_type}", show_box=True)
        return False
    
    # 如果当前存在updater文件或目录，尝试删除
    if os.path.exists(updater_path):
        if system_type == "windows":
            # Windows系统使用强制删除函数
            if not force_delete_windows(updater_path):
                return False
        else:
            # 其他系统使用常规删除方法
            try:
                if os.path.isfile(updater_path):
                    os.remove(updater_path)
                else:
                    shutil.rmtree(updater_path)
                log_info(f"已删除现有的updater", show_box=True)
            except Exception as e:
                log_error(f"无法删除现有的updater: {str(e)}", show_box=True)
                return False
    
    # 创建临时解压目录
    import tempfile
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 解压备份文件
        import zipfile
        with zipfile.ZipFile(backup_file, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # 复制到目标位置
        shutil.copytree(temp_dir, updater_path)
        
        # 删除备份文件
        os.remove(backup_file)
        log_info(f"已删除备份文件: {backup_file}", show_box=True)
        
        log_success(f"自动更新功能已成功恢复", show_box=True)
        log_warn(f"请重启Cursor以使更改生效", show_box=True)
        return True
    except Exception as e:
        log_error(f"恢复自动更新失败: {str(e)}", show_box=True)
        return False
    finally:
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir)
        except:
            pass

def enable_auto_update(system_type):
    """启用自动更新功能（删除阻止更新的占位符文件）"""
    # 确定updater路径
    if system_type == "windows":
        updater_path = os.path.join(os.getenv('LOCALAPPDATA', ''), 'cursor-updater')
    elif system_type == "mac":
        updater_path = os.path.join(os.getenv('HOME', ''), 'Library/Application Support/Caches/cursor-updater')
    elif system_type == "linux":
        updater_path = os.path.join(os.getenv('HOME', ''), '.config/cursor-updater')
    else:
        log_error(f"不支持的操作系统: {system_type}", show_box=True)
        return False
    
    # 检查占位符文件是否存在
    if not os.path.exists(updater_path) or not os.path.isfile(updater_path):
        log_error("未找到阻止更新的占位符文件", show_box=True)
        return False
    
    # 确认操作
    print_box(
        "启用自动更新",
        [
            (Colors.YELLOW, "警告: 此操作将删除阻止自动更新的占位符文件"),
            (Colors.YELLOW, "Cursor将在下次启动时可能会自动检查并下载更新"),
            (Colors.WHITE, "")
        ]
    )
    
    confirm = input(f"{Colors.CYAN}确认要启用自动更新吗? (y/n): {Colors.NC}").strip().lower()
    if confirm != 'y':
        log_info("已取消启用自动更新", show_box=True)
        return False
    
    # 删除占位符文件
    try:
        if system_type == "windows":
            # Windows系统使用强制删除函数
            if not force_delete_windows(updater_path):
                return False
        else:
            # 其他系统使用常规删除方法
            os.remove(updater_path)
        
        log_success("已成功删除更新阻止文件，已启用自动更新功能", show_box=True)
        log_warn("请重启Cursor以使更改生效", show_box=True)
        return True
    except Exception as e:
        log_error(f"删除阻止更新文件失败: {str(e)}", show_box=True)
        return False

def handle_auth_flow(browser, tab, email, password):
    """
    处理简化的Cursor授权流程，获取访问令牌
    :param browser: 浏览器实例
    :param tab: 标签页实例
    :param email: 邮箱 (仅用于日志显示)
    :param password: 密码 (不再使用)
    :return: 包含token信息的字典或None
    """
    try:
        # 获取长期JWT的URL
        log_process("正在准备生成授权URL...", show_box=True)
        update_progress(72)
        
        # 确保导入requests库
        import requests
        import secrets
        import hashlib
        import base64
        import uuid
        
        # 生成随机token
        token_bytes = secrets.token_bytes(32)
        
        # URL安全的Base64编码(无填充)
        def base64url_encode(data):
            return base64.urlsafe_b64encode(data).decode('utf-8').rstrip('=')
        
        # 计算验证器和挑战
        verifier = base64url_encode(token_bytes)
        
        # 计算SHA-256摘要
        challenge_bytes = hashlib.sha256(verifier.encode('utf-8')).digest()
        challenge = base64url_encode(challenge_bytes)
        
        # 生成UUID
        request_uuid = str(uuid.uuid4())
        
        # 构建授权URL
        auth_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={request_uuid}&mode=login"
        
        log_info(f"已生成授权URL: {auth_url}", show_box=True)
        update_progress(75)
        
        # 访问授权URL
        log_process("正在访问授权页面...", show_box=True)
        tab.get(auth_url)
        time.sleep(2)  # 增加等待时间，确保页面完全加载
        update_progress(78)
        
        # 检查是否已到授权页面
        if tab.ele('text:Log in to Cursor desktop?', timeout=10):
            log_success("已到达授权页面", show_box=True)
            update_progress(80)
            
            # 尝试多种方式找到并点击按钮
            button_found = False
            
            # 方法1: 通过XPath定位按钮及其内部span
            xpath_selectors = [
                '//button[.//span[contains(text(), "YES, LOG IN")]]',
                '//button[.//span[contains(text(), "Yes, Log In")]]',
                '//button[contains(@class, "relative")]',
                '//button[contains(@class, "flex")]',
                '//button[2]',  # 第二个按钮（通常是确认按钮）
                '//div[contains(@class, "r-10")]//button'
            ]
            
            for xpath in xpath_selectors:
                try:
                    if tab.ele(f'xpath:{xpath}', timeout=2):
                        # log_process(f"通过XPath '{xpath}' 找到确认按钮", show_box=True)
                        tab.ele(f'xpath:{xpath}').click()
                        button_found = True
                        break
                except Exception as e:
                    log_debug(f"XPath '{xpath}' 尝试失败: {str(e)}")
            
            # 如果成功点击了按钮
            if button_found:
                update_progress(82)
                
            # 等待处理结果
            log_process("正在等待授权处理...", show_box=True)
            time.sleep(1)
            update_progress(85)
            
            # 修改API端点：使用api2.cursor.sh/auth/poll而不是api.cursor.sh/auth/codeChallenge
            api_url = f"https://api2.cursor.sh/auth/poll?uuid={request_uuid}&verifier={verifier}"
            
            # 设置最大重试次数
            max_retries = 5
            retry_count = 0
            
            while retry_count < max_retries:
                if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                    log_warn("检测到中断信号，注册流程终止", show_box=True)
                    return None
                try:
                    log_process(f"正在获取授权数据 (尝试 {retry_count + 1}/{max_retries})...", show_box=True)
                    response = requests.get(api_url, headers={
                        "x-ghost-mode": "false",
                        "traceparent": "00-e43365e18cc54992ab8cce503a0f4e50-a3d878dab7eec57b-00"
                    }, timeout=10)
                    if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                        log_warn("检测到中断信号，注册流程终止", show_box=True)
                        return None
                    if response.status_code == 200:
                        token_data = response.json()
                        
                        if token_data and 'refreshToken' in token_data and 'accessToken' in token_data:
                            log_success("成功获取授权令牌!", show_box=True)
                            update_progress(88)
                            return token_data
                        else:
                            log_error("授权API返回的数据缺少令牌信息", show_box=True)
                            # 继续重试，而不是立即退出
                            retry_count += 1
                            if retry_count < max_retries:
                                log_info(f"5秒后重试 ({retry_count}/{max_retries})...")
                                for _ in range(5):
                                    if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                                        log_warn("检测到中断信号，注册流程终止", show_box=True)
                                        return None
                                    time.sleep(1)
                            else:
                                log_error("已达到最大重试次数，获取令牌失败", show_box=True)
                    # 处理204状态码（表示用户还未完成授权）
                    elif response.status_code == 204:
                        log_info("等待用户在浏览器中完成授权...", show_box=True)
                        retry_count += 1
                        if retry_count < max_retries:
                            log_info(f"5秒后重试 ({retry_count}/{max_retries})...")
                            for _ in range(5):
                                if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                                    log_warn("检测到中断信号，注册流程终止", show_box=True)
                                    return None
                                time.sleep(1)
                        else:
                            log_error("已达到最大重试次数，获取令牌失败", show_box=True)
                    else:
                        log_warn(f"API请求失败，状态码: {response.status_code}", show_box=True)
                        retry_count += 1
                        if retry_count < max_retries:
                            log_info(f"5秒后重试 ({retry_count}/{max_retries})...")
                            for _ in range(5):
                                if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                                    log_warn("检测到中断信号，注册流程终止", show_box=True)
                                    return None
                                time.sleep(1)
                        else:
                            log_error("已达到最大重试次数，获取令牌失败", show_box=True)
                
                except Exception as e:
                    log_error(f"获取授权令牌时出错: {str(e)}", show_box=True)
                    retry_count += 1
                    if retry_count < max_retries:
                        log_info(f"5秒后重试 ({retry_count}/{max_retries})...")
                        for _ in range(5):
                            if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                                log_warn("检测到中断信号，注册流程终止", show_box=True)
                                return None
                            time.sleep(1)
                    else:
                        log_error("已达到最大重试次数，获取令牌失败", show_box=True)
        else:
            log_error("未找到授权页面", show_box=True)
    
    except Exception as e:
        log_error(f"授权流程出错: {str(e)}", show_box=True)
    
    return None

def auto_fill_payment_info(tab):
    """
    自动填写支付信息 - 基于真实Stripe页面结构分析的精确方法
    :param tab: 标签页实例
    :return: 是否填写成功
    """
    try:
        import random
        import string

        log_process("开始自动填写支付信息...", show_box=True)

        # 1. 选择支付宝 - 使用基于真实页面分析的精确方法
        log_process("正在选择支付宝...", show_box=True)
        try:
            # 基于真实页面分析：必须点击 data-testid="alipay-accordion-item-button"
            js_select_alipay = """
            // 基于真实Stripe页面结构的支付宝选择方法
            let alipayButton = document.querySelector('[data-testid="alipay-accordion-item-button"]');

            if (alipayButton) {
                alipayButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                alipayButton.click();
                return '成功点击支付宝按钮';
            } else {
                // 备用方法：查找支付宝相关按钮
                let buttons = Array.from(document.querySelectorAll('button'));
                let alipayBtn = buttons.find(btn =>
                    btn.textContent.includes('支付宝') ||
                    btn.getAttribute('aria-label')?.includes('支付宝') ||
                    btn.textContent.includes('用支付宝支付')
                );

                if (alipayBtn) {
                    alipayBtn.click();
                    return '成功点击支付宝按钮(备用方法)';
                } else {
                    // 尝试点击支付宝radio按钮
                    let radios = Array.from(document.querySelectorAll('input[type="radio"]'));
                    for (let radio of radios) {
                        if (radio.value === 'alipay' || radio.id?.includes('alipay')) {
                            radio.click();
                            return '成功选择支付宝radio';
                        }
                    }
                    return '未找到支付宝选项';
                }
            }
            """

            result = tab.run_js(js_select_alipay, timeout=10)
            if "成功" in str(result):
                log_info(f"支付宝选择成功: {result}")
                time.sleep(3)  # 等待表单展开
            else:
                log_error(f"选择支付宝失败: {result}")
                return False

        except Exception as e:
            log_error(f"选择支付宝时出错: {str(e)}")
            return False

        # 2. 一步一步填写表单字段 - 按照用户要求的顺序

        # 生成随机数据
        first_names = ["John", "David", "Michael", "James", "Robert", "William", "Richard", "Thomas", "Christopher", "Daniel"]
        last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez"]
        random_name = f"{random.choice(first_names)} {random.choice(last_names)}"
        postal_code = ''.join(random.choices(string.digits, k=6))
        cities = ["Beijing", "Shanghai", "Guangzhou", "Shenzhen", "Hangzhou", "Nanjing", "Wuhan", "Chengdu"]
        random_city = random.choice(cities)
        districts = ["Chaoyang", "Haidian", "Xicheng", "Dongcheng", "Fengtai", "Shijingshan"]
        random_district = random.choice(districts)
        street_number = random.randint(1, 999)
        street_names = ["Main Street", "Central Avenue", "Park Road", "Beijing Road", "Shanghai Street"]
        address1 = f"{street_number} {random.choice(street_names)}"
        address2 = f"Apt {random.randint(1, 999)}"

        try:
            # 步骤2: 输入姓名 - 使用DrissionPage正确的输入方法
            log_process("正在填写支付信息...", show_box=True)

            # 使用DrissionPage的正确语法
            try:
                # 方法1：通过name属性定位
                name_input = tab.ele('@name=billingName', timeout=5)
                if name_input:
                    name_input.clear()
                    name_input.input(random_name)
                    log_info(f"姓名: {random_name}")
                else:
                    raise Exception("未找到name属性的姓名输入框")
            except Exception as e1:
                try:
                    # 方法2：通过autocomplete属性定位
                    name_input = tab.ele('@autocomplete=name', timeout=5)
                    if name_input:
                        name_input.clear()
                        name_input.input(random_name)
                        log_info(f"姓名: {random_name}")
                    else:
                        raise Exception("未找到autocomplete属性的姓名输入框")
                except Exception as e2:
                    try:
                        # 方法3：通过id属性定位
                        name_input = tab.ele('@id=billingName', timeout=5)
                        if name_input:
                            name_input.clear()
                            name_input.input(random_name)
                            log_info(f"姓名: {random_name}")
                        else:
                            raise Exception("未找到id属性的姓名输入框")
                    except Exception as e3:
                        log_error(f"❌ 姓名填写失败: 方法1={str(e1)}, 方法2={str(e2)}, 方法3={str(e3)}")
                        return False

            time.sleep(1)

            # 步骤3: 选择账单地址国家（中国）- 使用通用的、不依赖语言的选择器
            result = tab.run_js("""
            // 通用选择器：基于name属性和autocomplete属性，不依赖语言
            let countrySelect = document.querySelector('select[name="billingCountry"]') ||
                               document.querySelector('select[autocomplete="billing country"]') ||
                               document.querySelector('select[id="billingCountry"]');
            if (countrySelect) {
                let options = Array.from(countrySelect.querySelectorAll('option'));
                // 智能选择：优先中国，然后美国，最后随机选择
                let targetCountries = ['中国', 'China', '美国', 'United States', 'US'];
                let selectedOption = null;

                for (let country of targetCountries) {
                    selectedOption = options.find(opt =>
                        opt.textContent.includes(country) ||
                        opt.value.toLowerCase().includes(country.toLowerCase())
                    );
                    if (selectedOption) break;
                }

                // 如果没找到目标国家，随机选择一个
                if (!selectedOption && options.length > 1) {
                    let randomIndex = Math.floor(Math.random() * (options.length - 1)) + 1;
                    selectedOption = options[randomIndex];
                }

                if (selectedOption) {
                    countrySelect.value = selectedOption.value;
                    countrySelect.dispatchEvent(new Event('change', { bubbles: true }));
                    countrySelect.dispatchEvent(new Event('blur', { bubbles: true }));
                    return '成功选择国家: ' + selectedOption.textContent;
                } else {
                    return '未找到可选择的国家选项';
                }
            } else {
                return '未找到国家选择框，可能页面结构已变化';
            }
            """, timeout=10)

            if "成功" in str(result):
                log_info(f"国家: 中国")

                # 关键修复：等待Stripe动态加载地址字段
                time.sleep(3)  # 增加等待时间

                # 验证地址字段是否已加载
                for attempt in range(5):  # 最多等待5次
                    check_result = tab.run_js("""
                    // 检查关键地址字段是否已加载并可见
                    let postalInput = document.querySelector('input[name="billingPostalCode"]');
                    let address1Input = document.querySelector('input[name="billingAddressLine1"]');

                    if (postalInput && address1Input) {
                        let postalVisible = postalInput.offsetParent !== null;
                        let address1Visible = address1Input.offsetParent !== null;
                        return '地址字段已加载: 邮编=' + postalVisible + ', 地址1=' + address1Visible;
                    } else {
                        return '地址字段尚未加载';
                    }
                    """, timeout=5)

                    if "已加载" in str(check_result) and "true" in str(check_result):
                        break
                    else:
                        time.sleep(2)
            else:
                log_error(f"❌ 国家选择失败: {result}")
                return False

            # 步骤4: 填写邮编（6位随机数字）- 使用DrissionPage正确的输入方法

            try:
                # 方法1：通过name属性定位
                postal_input = tab.ele('@name=billingPostalCode', timeout=5)
                if postal_input:
                    postal_input.clear()
                    postal_input.input(postal_code)
                    log_info(f"邮编: {postal_code}")
                else:
                    raise Exception("未找到name属性的邮编输入框")
            except Exception as e1:
                try:
                    # 方法2：通过autocomplete属性定位
                    postal_input = tab.ele('@autocomplete=billing postal-code', timeout=5)
                    if postal_input:
                        postal_input.clear()
                        postal_input.input(postal_code)
                        log_info(f"邮编: {postal_code}")
                    else:
                        raise Exception("未找到autocomplete属性的邮编输入框")
                except Exception as e2:
                    log_error(f"❌ 邮编填写失败: 方法1={str(e1)}, 方法2={str(e2)}")
                    return False

            time.sleep(1)

            # 步骤5: 选择省/州（随机选择）

            # 检查是否有省/州字段
            state_result = tab.run_js(f"""
            // 查找省/州选择框
            let stateSelect = document.querySelector('select[name*="state"], select[name*="State"], select[name*="province"]') ||
                             document.querySelector('select[autocomplete*="address-level1"]') ||
                             document.querySelector('combobox[aria-label*="省"], combobox[aria-label*="州"]');

            if (stateSelect && stateSelect.offsetParent !== null) {{
                let options = Array.from(stateSelect.querySelectorAll('option'));
                // 随机选择一个省/州（跳过第一个空选项）
                if (options.length > 1) {{
                    let randomIndex = Math.floor(Math.random() * (options.length - 1)) + 1;
                    let selectedOption = options[randomIndex];
                    stateSelect.value = selectedOption.value;
                    stateSelect.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    stateSelect.dispatchEvent(new Event('blur', {{ bubbles: true }}));
                    return '成功选择省/州: ' + selectedOption.textContent;
                }}
            }}
            return '未找到省/州字段或字段不可见';
            """, timeout=10)

            if "成功" in str(state_result):
                # 提取省/州名称
                province_name = str(state_result).split(': ')[-1] if ': ' in str(state_result) else "随机省/州"
                log_info(f"省/州: {province_name}")
                time.sleep(1)
            else:
                log_info(f"ℹ️ 省/州字段处理: {state_result}")

            # 步骤6: 填写城市（随便填）- 使用DrissionPage正确的输入方法
            try:
                # 尝试多种城市字段选择器
                city_selectors = [
                    'css:input[placeholder*="城市"]',
                    'css:input[aria-label*="城市"]',
                    'css:input[name*="city"]',
                    'css:input[autocomplete*="address-level2"]'
                ]

                city_filled = False
                for selector in city_selectors:
                    try:
                        city_input = tab.ele(selector, timeout=2)
                        if city_input:
                            city_input.clear()
                            city_input.input(random_city)
                            log_info(f"城市: {random_city}")
                            city_filled = True
                            break
                    except:
                        continue

                if not city_filled:
                    log_info("ℹ️ 未找到城市字段或字段不可见")

                time.sleep(1)
            except Exception as e:
                log_info(f"ℹ️ 城市字段处理异常: {str(e)}")

            # 步骤7: 填写地区（随便填）- 使用DrissionPage正确的输入方法
            try:
                district_selectors = [
                    'css:input[placeholder*="地区"]',
                    'css:input[aria-label*="地区"]',
                    'css:input[name*="district"]'
                ]

                district_filled = False
                for selector in district_selectors:
                    try:
                        district_input = tab.ele(selector, timeout=2)
                        if district_input:
                            district_input.clear()
                            district_input.input(random_district)
                            log_info(f"地区: {random_district}")
                            district_filled = True
                            break
                    except:
                        continue

                if not district_filled:
                    log_info("ℹ️ 未找到地区字段或字段不可见")

            except Exception as e:
                log_info(f"ℹ️ 地区字段处理异常: {str(e)}")

            # 步骤8: 填写地址第1行（随便填）- 使用DrissionPage正确的输入方法

            try:
                # 方法1：通过name属性定位
                address1_input = tab.ele('@name=billingAddressLine1', timeout=5)
                if address1_input:
                    address1_input.clear()
                    address1_input.input(address1)
                    log_info(f"地址1: {address1}")
                else:
                    raise Exception("未找到name属性的地址第1行输入框")
            except Exception as e1:
                try:
                    # 方法2：通过autocomplete属性定位
                    address1_input = tab.ele('@autocomplete=billing address-line1', timeout=5)
                    if address1_input:
                        address1_input.clear()
                        address1_input.input(address1)
                        log_info(f"地址1: {address1}")
                    else:
                        raise Exception("未找到autocomplete属性的地址第1行输入框")
                except Exception as e2:
                    log_error(f"❌ 地址第1行填写失败: 方法1={str(e1)}, 方法2={str(e2)}")
                    return False

            time.sleep(1)

            # 步骤9: 填写地址第2行（随便填）- 使用DrissionPage正确的输入方法

            try:
                # 方法1：通过name属性定位
                address2_input = tab.ele('@name=billingAddressLine2', timeout=5)
                if address2_input:
                    address2_input.clear()
                    address2_input.input(address2)
                    log_info(f"地址2: {address2}")
                else:
                    raise Exception("未找到name属性的地址第2行输入框")
            except Exception as e1:
                try:
                    # 方法2：通过autocomplete属性定位
                    address2_input = tab.ele('@autocomplete=billing address-line2', timeout=5)
                    if address2_input:
                        address2_input.clear()
                        address2_input.input(address2)
                        log_info(f"地址2: {address2}")
                    else:
                        raise Exception("未找到autocomplete属性的地址第2行输入框")
                except Exception as e2:
                    log_error(f"❌ 地址第2行填写失败: 方法1={str(e1)}, 方法2={str(e2)}")
                    return False

            time.sleep(1)


        except Exception as e:
            log_error(f"填写表单时出错: {str(e)}")
            return False

        # 步骤10: 等待表单验证完成，然后点击"开始试用"按钮
        log_process("等待表单验证完成...", show_box=True)

        try:
            # 等待按钮状态从 incomplete 变为 complete（最多等待10秒）
            button_ready = False
            max_wait_attempts = 10

            for attempt in range(max_wait_attempts):
                try:
                    # 检查按钮状态
                    button_status = tab.run_js("""
                    const startButton = document.querySelector('button[data-testid="hosted-payment-submit-button"]') ||
                                       document.querySelector('button:has-text("开始试用")');

                    if (startButton) {
                        return {
                            found: true,
                            className: startButton.className,
                            isComplete: startButton.className.includes('SubmitButton--complete'),
                            isIncomplete: startButton.className.includes('SubmitButton--incomplete'),
                            textContent: startButton.textContent
                        };
                    }
                    return { found: false };
                    """, timeout=5)

                    if button_status and button_status.get('found'):
                        if button_status.get('isComplete'):
                            button_ready = True
                            break
                        elif button_status.get('isIncomplete'):
                            time.sleep(1)
                        else:
                            time.sleep(1)
                    else:
                        time.sleep(1)

                except Exception as e:
                    time.sleep(1)

            if not button_ready:
                log_error("等待验证超时，请您手动点击 开始试用 按钮")
                return False

            # 延迟1秒确保表单完全准备就绪
            time.sleep(1)

            # 现在点击按钮
            try:
                # 方法1：通过data-testid定位
                start_button = tab.ele('@data-testid=hosted-payment-submit-button', timeout=5)
                if start_button:
                    start_button.click()
                else:
                    raise Exception("未找到data-testid按钮")
            except Exception as e1:
                try:
                    # 方法2：通过文本内容定位
                    start_button = tab.ele('text:开始试用', timeout=3)
                    if start_button:
                        start_button.click()
                    else:
                        raise Exception("未找到文本匹配的按钮")
                except Exception as e2:
                    log_error(f"❌ 点击开始试用按钮失败: 方法1={str(e1)}, 方法2={str(e2)}")
                    return False

            time.sleep(3)  # 等待页面跳转

            # 填写完成，显示"我已完成绑定，继续"按钮等待用户手动点击
            log_success("支付信息填写完成，请您支付宝扫码完成绑定")

        except Exception as e:
            log_error(f"点击开始试用按钮时出错: {str(e)}")
            return False

        return True

    except Exception as e:
        log_error(f"自动填写支付信息时发生未知错误: {str(e)}")
        return False

def sign_in_by_email_code(browser, tab, email, password):
    """
    通过邮箱验证码登录并获取授权 (方案一)
    :param browser: 浏览器实例
    :param tab: 标签页实例
    :param email: 邮箱
    :param password: 密码 (可能不需要)
    :return: 包含token信息的字典或None
    """
    try:
        # 加载绕过绑卡配置
        import os
        import json
        from utils import get_app_data_dir

        bypass_card_enabled = False  # 默认关闭绕过绑卡
        auto_fill_payment_enabled = True  # 默认开启自动填写支付信息
        try:
            settings_file = os.path.join(get_app_data_dir(), "settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    bypass_card_enabled = settings.get("auto_register_bypass_card", False)
                    auto_fill_payment_enabled = settings.get("auto_register_auto_fill_payment", True)
        except Exception as e:
            log_debug(f"加载配置失败，使用默认值: {str(e)}")

        log_debug(f"绕过绑卡配置: {'开启' if bypass_card_enabled else '关闭'}")
        log_debug(f"自动填写支付信息配置: {'开启' if auto_fill_payment_enabled else '关闭'}")

        # 生成随机token
        import secrets
        import hashlib
        import base64
        import uuid
        import requests
        
        # 生成随机token
        token_bytes = secrets.token_bytes(32)
        
        # URL安全的Base64编码(无填充)
        def base64url_encode(data):
            return base64.urlsafe_b64encode(data).decode('utf-8').rstrip('=')
        
        # 计算验证器和挑战
        verifier = base64url_encode(token_bytes)
        
        # 计算SHA-256摘要
        challenge_bytes = hashlib.sha256(verifier.encode('utf-8')).digest()
        challenge = base64url_encode(challenge_bytes)
        
        # 生成UUID
        request_uuid = str(uuid.uuid4())
        
        # 构建授权URL
        auth_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={request_uuid}&mode=login"
        
        # 不再显示授权URL
        update_progress(45)
        
        # 访问授权URL
        log_process("正在访问登录页面...", show_box=True)
        tab.get(auth_url)
        time.sleep(3)  # 等待页面加载
        update_progress(48)
        
        # 输入邮箱
        log_process("正在输入邮箱...", show_box=True)
        tab.ele('xpath://input[@type="email"]').input(email)
        time.sleep(1)  # 等待输入完成
        update_progress(50)
        
        # 点击Continue按钮
        log_process("正在提交...", show_box=True)
        tab.ele('xpath://button[contains(text(), "Continue")]').click()
        time.sleep(2)  # 等待页面加载
        update_progress(52)
        
        # 点击"Email sign-in code"按钮
        log_process("正在提交请求...", show_box=True)

        # 尝试多种方式定位"Email sign-in code"按钮
        email_signin_button = None
        xpath_selectors = [
            # 方法1: 通过嵌套文本定位（新的页面结构）- 最准确的方法
            '//button[.//text()[contains(., "Email sign-in code")]]',
            # 方法2: 通过子元素文本定位
            '//button[.//*[contains(text(), "Email sign-in code")]]',
            # 方法3: 通过直接文本定位（旧的页面结构，保持兼容性）
            '//button[contains(text(), "Email sign-in code")]',
            # 方法4: 通过部分文本匹配
            '//button[contains(., "Email sign-in code")]',
            # 方法5: 通过按钮文本的normalize-space函数
            '//button[normalize-space(.)="Email sign-in code"]',
            # 方法6: 通过包含特定文本的任何后代元素
            '//button[descendant::*[contains(text(), "Email sign-in code")]]'
        ]

        for i, xpath in enumerate(xpath_selectors, 1):
            try:
                email_signin_button = tab.ele(f'xpath:{xpath}', timeout=3)
                if email_signin_button:
                    break
            except Exception as e:
                log_debug(f"方法{i} XPath '{xpath}' 尝试失败: {str(e)}")

        if email_signin_button:
            email_signin_button.click()
            time.sleep(2)  # 等待页面加载
            update_progress(54)
        else:
            log_error("找不到Email sign-in code按钮", show_box=True)
            return None
        
        # 处理人机验证
        handle_turnstile(tab)
        update_progress(56)
        
        # 添加等待时间，给服务器处理人机验证和发送验证码邮件的时间
        log_process("人机验证完成，等待验证码邮件发送...", show_box=True)
        time.sleep(5)  # 等待5秒，确保邮件有足够时间发送
        
        # 检查页面状态：是否需要输入验证码
        need_verification = False
        max_attempts = 3
        global email_handler
        for attempt in range(max_attempts):
            if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                log_warn("检测到中断信号，注册流程终止", show_box=True)
                return None
            # 检查是否有"Check your email"字样
            if tab.ele('text:Check your email', timeout=5):
                log_info("需要输入邮箱验证码", show_box=True)
                need_verification = True
                log_process("正在获取邮箱验证码...", show_box=True)
                if not hasattr(sys.modules[__name__], 'email_handler') or email_handler is None:
                    log_warn("邮箱验证模块未初始化，正在初始化...", show_box=True)
                    from get_email_code import EmailVerificationHandler
                    email_handler = EmailVerificationHandler()
                if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                    log_warn("检测到中断信号，注册流程终止", show_box=True)
                    return None
                code = email_handler.get_verification_code()  # 使用默认清理设置
                if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                    log_warn("检测到中断信号，注册流程终止", show_box=True)
                    return None
                if not code:
                    log_error("获取验证码失败", show_box=True)
                    return None
                update_progress(60)
                log_process("正在输入验证码...", show_box=True)
                try:
                    if tab.ele("@data-index=0", timeout=3):
                        for i, digit in enumerate(code):
                            if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                                log_warn("检测到中断信号，注册流程终止", show_box=True)
                                return None
                            tab.ele(f"@data-index={i}").input(digit)
                            time.sleep(0.05)
                        log_success("验证码输入完成", show_box=True)
                        update_progress(62)
                    else:
                        log_error("找不到验证码输入框", show_box=True)
                        return None
                    email_handler.clean_after_verification()
                    log_process("验证码已输入，等待页面跳转...", show_box=True)
                    time.sleep(1)
                    update_progress(64)
                    if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                        log_warn("检测到中断信号，注册流程终止", show_box=True)
                        return None
                    
                    # 检查各种可能的验证码错误提示
                    invalid_code = tab.ele('text:invalid verification code', timeout=1)
                    invalid_one_time_code = tab.ele('text:Invalid one-time code', timeout=1)
                    invalid_code_generic = tab.ele('@class=text-red-500', timeout=1)  # 通常错误信息是红色文本
                    
                    if invalid_code or invalid_one_time_code or invalid_code_generic:
                        # 获取具体的错误文本
                        if invalid_one_time_code:
                            error_text = "Invalid one-time code"
                        elif invalid_code:
                            error_text = "invalid verification code"
                        elif invalid_code_generic:
                            try:
                                error_text = invalid_code_generic.get_text()
                            except:
                                error_text = "未知验证码错误"
                        else:
                            error_text = "验证码错误"
                        log_error(f"验证码无效（{error_text}），正在刷新页面...", show_box=True)
                        
                        # 刷新页面
                        try:
                            tab.refresh()
                            log_process("页面已刷新，等待重新加载...", show_box=True)
                            time.sleep(3)  # 等待页面重新加载
                            
                            # 检查页面是否重新加载到验证码输入界面
                            email_check_text = tab.ele('text:Check your email', timeout=5)
                            if not email_check_text:
                                log_warn("刷新后未找到验证码输入界面，尝试重新加载登录页面...", show_box=True)
                                # 可能需要重新加载整个登录流程
                                tab.go_back()  # 尝试返回上一页
                                time.sleep(2)
                            else:
                                log_info("已重新加载到验证码输入界面", show_box=True)
                        except Exception as e:
                            log_error(f"刷新页面时出错: {str(e)}", show_box=True)
                        
                        if attempt < max_attempts - 1:
                            log_process(f"尝试重新获取验证码 (尝试 {attempt+2}/{max_attempts})...", show_box=True)
                            continue
                        else:
                            log_error(f"验证码尝试次数已达上限 ({max_attempts}次)", show_box=True)
                            return None
                except Exception as e:
                    log_error(f"验证码输入流程异常: {e}", show_box=True)
                    return None
            else:
                # 如果没有"Check your email"字样，检查是否已经到了授权页面
                if tab.ele('text:Log in to Cursor desktop?', timeout=5):
                    log_success("已到达授权页面", show_box=True)
                    break
                else:
                    # 检查是否出现了Pro Trial页面（绕过绑卡）
                    pro_trial_detected = False
                    try:
                        # 检测Pro Trial页面标识
                        pro_trial_selectors = [
                            'text:Claim your Pro Trial',
                            'text:Claim your Pro trial',
                            'text:claim your pro trial',
                            'text:Claim Pro Trial',
                            '//h1[contains(text(), "Claim your Pro Trial")]',
                            '//div[contains(text(), "Claim your Pro Trial")]'
                        ]

                        for selector in pro_trial_selectors:
                            try:
                                if selector.startswith('text:'):
                                    element = tab.ele(selector, timeout=1)
                                else:
                                    element = tab.ele(f'xpath:{selector}', timeout=1)

                                if element:
                                    pro_trial_detected = True
                                    break
                            except Exception:
                                continue

                        if pro_trial_detected:
                            if bypass_card_enabled:
                                # 自动绕过绑卡
                                log_info("检测到需要绑卡，开始绕过绑卡", show_box=True)

                                # 重新访问授权URL绕过绑卡
                                bypass_auth_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={request_uuid}&mode=login"
                                tab.get(bypass_auth_url)
                                time.sleep(3)

                                log_process("正在验证绕过结果...", show_box=True)
                                # 检查是否成功到达授权页面
                                if tab.ele('text:Log in to Cursor desktop?', timeout=10):
                                    log_success("成功到达授权页面", show_box=True)
                                    break
                                else:
                                    log_warn("绕过后未找到授权页面，继续尝试", show_box=True)
                                    time.sleep(3)
                                    continue
                            else:
                                # 绕过绑卡关闭时，根据配置决定是否自动填写支付信息
                                if auto_fill_payment_enabled:
                                    log_info("检测到需要绑卡，开始自动填写支付信息", show_box=True)
                                else:
                                    log_info("检测到需要绑卡，自动填写支付信息已关闭，等待用户手动操作", show_box=True)

                                try:
                                    # 点击Continue按钮进入支付页面
                                    log_process("正在点击Continue按钮...", show_box=True)
                                    continue_button = tab.ele('xpath://button[contains(text(), "Continue")]', timeout=10)
                                    if continue_button:
                                        continue_button.click()
                                        time.sleep(5)  # 等待支付页面加载

                                        # 检查是否成功进入支付页面
                                        if tab.ele('text:支付宝', timeout=10) or tab.ele('text:Alipay', timeout=5):
                                            if auto_fill_payment_enabled:
                                                log_process("成功进入支付页面，开始自动填写信息...", show_box=True)
                                                # 自动填写支付信息
                                                auto_fill_success = auto_fill_payment_info(tab)
                                            else:
                                                log_process("成功进入支付页面，等待用户手动填写信息...", show_box=True)
                                                # 直接跳过自动填写，视为需要手动处理
                                                auto_fill_success = False

                                            if auto_fill_success or not auto_fill_payment_enabled:
                                                # 发送手动绑卡信号（如果在工作线程中）
                                                try:
                                                    import threading
                                                    current_thread = threading.current_thread()
                                                    if hasattr(current_thread, 'manual_card_required'):
                                                        current_thread.manual_card_required.emit()
                                                    elif hasattr(current_thread, 'parent') and hasattr(current_thread.parent(), 'manual_card_required'):
                                                        current_thread.parent().manual_card_required.emit()
                                                except Exception as e:
                                                    log_debug(f"发送手动绑卡信号失败: {str(e)}")

                                                # 等待用户手动确认
                                                try:
                                                    if hasattr(current_thread, 'manual_card_event'):
                                                        current_thread.manual_card_event.wait()  # 等待用户点击按钮
                                                    elif hasattr(current_thread, 'parent') and hasattr(current_thread.parent(), 'manual_card_event'):
                                                        current_thread.parent().manual_card_event.wait()
                                                except Exception as e:
                                                    log_debug(f"等待手动绑卡确认失败: {str(e)}")

                                                # 用户确认后，跳转到授权页面
                                                bypass_auth_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={request_uuid}&mode=login"
                                                tab.get(bypass_auth_url)
                                                time.sleep(3)

                                                # 检查是否成功到达授权页面
                                                if tab.ele('text:Log in to Cursor desktop?', timeout=10):
                                                    log_success("成功到达授权页面", show_box=True)
                                                    break
                                                else:
                                                    log_warn("跳转后未找到授权页面，继续尝试", show_box=True)
                                                    time.sleep(3)
                                                    continue
                                            else:
                                                log_error("支付信息填写失败，等待用户手动完成", show_box=True)
                                                # 发送手动绑卡信号，让用户手动处理
                                                try:
                                                    import threading
                                                    current_thread = threading.current_thread()
                                                    if hasattr(current_thread, 'manual_card_required'):
                                                        current_thread.manual_card_required.emit()
                                                    elif hasattr(current_thread, 'parent') and hasattr(current_thread.parent(), 'manual_card_required'):
                                                        current_thread.parent().manual_card_required.emit()
                                                except Exception as e:
                                                    log_debug(f"发送手动绑卡信号失败: {str(e)}")

                                                # 等待用户手动确认
                                                try:
                                                    if hasattr(current_thread, 'manual_card_event'):
                                                        current_thread.manual_card_event.wait()
                                                    elif hasattr(current_thread, 'parent') and hasattr(current_thread.parent(), 'manual_card_event'):
                                                        current_thread.parent().manual_card_event.wait()
                                                except Exception as e:
                                                    log_debug(f"等待手动绑卡确认失败: {str(e)}")

                                                # 用户确认后，跳转到授权页面
                                                bypass_auth_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={request_uuid}&mode=login"
                                                tab.get(bypass_auth_url)
                                                time.sleep(3)

                                                # 检查是否成功到达授权页面
                                                if tab.ele('text:Log in to Cursor desktop?', timeout=10):
                                                    log_success("成功到达授权页面", show_box=True)
                                                    break
                                                else:
                                                    log_warn("跳转后未找到授权页面，继续尝试", show_box=True)
                                                    time.sleep(3)
                                                    continue
                                        else:
                                            log_error("未能进入支付页面，等待用户手动完成", show_box=True)
                                            # 发送手动绑卡信号，让用户手动处理
                                            try:
                                                import threading
                                                current_thread = threading.current_thread()
                                                if hasattr(current_thread, 'manual_card_required'):
                                                    current_thread.manual_card_required.emit()
                                                elif hasattr(current_thread, 'parent') and hasattr(current_thread.parent(), 'manual_card_required'):
                                                    current_thread.parent().manual_card_required.emit()
                                            except Exception as e:
                                                log_debug(f"发送手动绑卡信号失败: {str(e)}")

                                            # 等待用户手动确认
                                            try:
                                                if hasattr(current_thread, 'manual_card_event'):
                                                    current_thread.manual_card_event.wait()
                                                elif hasattr(current_thread, 'parent') and hasattr(current_thread.parent(), 'manual_card_event'):
                                                    current_thread.parent().manual_card_event.wait()
                                            except Exception as e:
                                                log_debug(f"等待手动绑卡确认失败: {str(e)}")

                                            # 用户确认后，跳转到授权页面
                                            bypass_auth_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={request_uuid}&mode=login"
                                            tab.get(bypass_auth_url)
                                            time.sleep(3)

                                            # 检查是否成功到达授权页面
                                            if tab.ele('text:Log in to Cursor desktop?', timeout=10):
                                                log_success("成功到达授权页面", show_box=True)
                                                break
                                            else:
                                                log_warn("跳转后未找到授权页面，继续尝试", show_box=True)
                                                time.sleep(3)
                                                continue
                                    else:
                                        log_error("未找到Continue按钮，等待用户手动完成", show_box=True)
                                        # 发送手动绑卡信号，让用户手动处理
                                        try:
                                            import threading
                                            current_thread = threading.current_thread()
                                            if hasattr(current_thread, 'manual_card_required'):
                                                current_thread.manual_card_required.emit()
                                            elif hasattr(current_thread, 'parent') and hasattr(current_thread.parent(), 'manual_card_required'):
                                                current_thread.parent().manual_card_required.emit()
                                        except Exception as e:
                                            log_debug(f"发送手动绑卡信号失败: {str(e)}")

                                        # 等待用户手动确认
                                        try:
                                            if hasattr(current_thread, 'manual_card_event'):
                                                current_thread.manual_card_event.wait()
                                            elif hasattr(current_thread, 'parent') and hasattr(current_thread.parent(), 'manual_card_event'):
                                                current_thread.parent().manual_card_event.wait()
                                        except Exception as e:
                                            log_debug(f"等待手动绑卡确认失败: {str(e)}")

                                        # 用户确认后，跳转到授权页面
                                        bypass_auth_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={request_uuid}&mode=login"
                                        tab.get(bypass_auth_url)
                                        time.sleep(3)

                                        # 检查是否成功到达授权页面
                                        if tab.ele('text:Log in to Cursor desktop?', timeout=10):
                                            log_success("成功到达授权页面", show_box=True)
                                            break
                                        else:
                                            log_warn("跳转后未找到授权页面，继续尝试", show_box=True)
                                            time.sleep(3)
                                            continue

                                except Exception as e:
                                    log_error(f"自动填写支付信息时出错: {str(e)}", show_box=True)
                                    # 出错时回退到手动模式
                                    log_info("回退到手动绑卡模式", show_box=True)

                                    # 发送手动绑卡信号
                                    try:
                                        import threading
                                        current_thread = threading.current_thread()
                                        if hasattr(current_thread, 'manual_card_required'):
                                            current_thread.manual_card_required.emit()
                                        elif hasattr(current_thread, 'parent') and hasattr(current_thread.parent(), 'manual_card_required'):
                                            current_thread.parent().manual_card_required.emit()
                                    except Exception as e:
                                        log_debug(f"发送手动绑卡信号失败: {str(e)}")

                                    # 等待用户手动确认
                                    try:
                                        if hasattr(current_thread, 'manual_card_event'):
                                            current_thread.manual_card_event.wait()
                                        elif hasattr(current_thread, 'parent') and hasattr(current_thread.parent(), 'manual_card_event'):
                                            current_thread.parent().manual_card_event.wait()
                                    except Exception as e:
                                        log_debug(f"等待手动绑卡确认失败: {str(e)}")

                                    # 用户确认后，跳转到授权页面
                                    bypass_auth_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={request_uuid}&mode=login"
                                    tab.get(bypass_auth_url)
                                    time.sleep(3)

                                    # 检查是否成功到达授权页面
                                    if tab.ele('text:Log in to Cursor desktop?', timeout=10):
                                        log_success("成功到达授权页面", show_box=True)
                                        break
                                    else:
                                        log_warn("跳转后未找到授权页面，继续尝试", show_box=True)
                                        time.sleep(3)
                                        continue
                        else:
                            log_warn("未找到验证码输入页面或授权页面，可能网络环境问题，请关闭重新运行", show_box=True)
                            time.sleep(3)  # 等待页面加载
                            continue
                    except Exception as e:
                        log_warn("未找到验证码输入页面或授权页面，可能网络环境问题，请关闭重新运行", show_box=True)
                        time.sleep(3)
                        continue

        # 判断是否到了授权页面
        if tab.ele('text:Log in to Cursor desktop?', timeout=10):
            log_success("成功到达授权页面", show_box=True)
            update_progress(66)
            
            # 尝试多种方式找到并点击按钮
            button_found = False
            
            # 方法1: 通过XPath定位按钮及其内部span
            xpath_selectors = [
                '//button[.//span[contains(text(), "YES, LOG IN")]]',
                '//button[.//span[contains(text(), "Yes, Log In")]]',
                '//button[contains(@class, "relative")]',
                '//button[contains(@class, "flex")]',
                '//button[2]',  # 第二个按钮（通常是确认按钮）
                '//div[contains(@class, "r-10")]//button'
            ]
            
            for xpath in xpath_selectors:
                try:
                    if tab.ele(f'xpath:{xpath}', timeout=2):
                        tab.ele(f'xpath:{xpath}').click()
                        button_found = True
                        break
                except Exception as e:
                    log_debug(f"XPath '{xpath}' 尝试失败: {str(e)}")
            
            # 如果成功点击了按钮
            if button_found:
                update_progress(70)
                
            # 等待处理结果
            log_process("正在等待授权处理...", show_box=True)
            time.sleep(1)
            update_progress(75)
            
            api_url = f"https://api2.cursor.sh/auth/poll?uuid={request_uuid}&verifier={verifier}"
            
            # 设置最大重试次数
            max_retries = 5
            retry_count = 0
            
            while retry_count < max_retries:
                if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                    log_warn("检测到中断信号，注册流程终止", show_box=True)
                    return None
                try:
                    log_process(f"正在获取授权数据 (尝试 {retry_count + 1}/{max_retries})...", show_box=True)
                    response = requests.get(api_url, headers={
                        "x-ghost-mode": "false",
                        "traceparent": "00-e43365e18cc54992ab8cce503a0f4e50-a3d878dab7eec57b-00"
                    }, timeout=10)
                    if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                        log_warn("检测到中断信号，注册流程终止", show_box=True)
                        return None
                    if response.status_code == 200:
                        token_data = response.json()
                        
                        if token_data and 'refreshToken' in token_data and 'accessToken' in token_data:
                            log_success("成功获取授权令牌!", show_box=True)
                            update_progress(80)
                            return token_data
                        else:
                            log_error("授权API返回的数据缺少令牌信息", show_box=True)
                            # 继续重试，而不是立即退出
                            retry_count += 1
                            if retry_count < max_retries:
                                log_info(f"5秒后重试 ({retry_count}/{max_retries})...")
                                for _ in range(5):
                                    if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                                        log_warn("检测到中断信号，注册流程终止", show_box=True)
                                        return None
                                    time.sleep(1)
                            else:
                                log_error("已达到最大重试次数，获取令牌失败", show_box=True)
                    # 处理204状态码（表示用户还未完成授权）
                    elif response.status_code == 204:
                        log_info("等待用户在浏览器中完成授权...", show_box=True)
                        retry_count += 1
                        if retry_count < max_retries:
                            log_info(f"5秒后重试 ({retry_count}/{max_retries})...")
                            for _ in range(5):
                                if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                                    log_warn("检测到中断信号，注册流程终止", show_box=True)
                                    return None
                                time.sleep(1)
                        else:
                            log_error("已达到最大重试次数，获取令牌失败", show_box=True)
                    else:
                        log_warn(f"API请求失败，状态码: {response.status_code}", show_box=True)
                        retry_count += 1
                        if retry_count < max_retries:
                            log_info(f"5秒后重试 ({retry_count}/{max_retries})...")
                            for _ in range(5):
                                if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                                    log_warn("检测到中断信号，注册流程终止", show_box=True)
                                    return None
                                time.sleep(1)
                        else:
                            log_error("已达到最大重试次数，获取令牌失败", show_box=True)
                
                except Exception as e:
                    log_error(f"获取授权令牌时出错: {str(e)}", show_box=True)
                    retry_count += 1
                    if retry_count < max_retries:
                        log_info(f"5秒后重试 ({retry_count}/{max_retries})...")
                        for _ in range(5):
                            if hasattr(email_handler, 'should_stop') and email_handler.should_stop:
                                log_warn("检测到中断信号，注册流程终止", show_box=True)
                                return None
                            time.sleep(1)
                    else:
                        log_error("已达到最大重试次数，获取令牌失败", show_box=True)
        else:
            log_error("未找到授权页面", show_box=True)
    
    except Exception as e:
        log_error(f"邮箱验证码登录流程出错: {str(e)}", show_box=True)
    
    return None

# 全局变量初始值设置
email_handler = None
account_info = None
account = None
password = None
first_name = None
last_name = None
progress_callback = None
log_callback = None

# 添加模块重置函数，用于彻底清理全局状态
def reset_module_state():
    """
    重置模块的全局状态。
    这个函数应该在每次操作结束时调用，特别是在清理时，
    以确保模块中所有全局变量都被重置到安全状态。
    
    注意：这会重置所有全局变量，包括邮箱验证处理器。
    """
    global email_handler, account_info, account, password, first_name, last_name
    
    # 清理email_handler
    if email_handler is not None:
        try:
            if hasattr(email_handler, 'cleanup'):
                email_handler.cleanup()
            if hasattr(email_handler, 'set_should_stop'):
                email_handler.set_should_stop(True)
        except Exception as e:
            print(f"[警告] 清理email_handler时出错: {str(e)}")
        # 强制将email_handler设为None
        email_handler = None
    
    # 重置账号信息
    account_info = None
    account = None
    password = None
    first_name = None
    last_name = None
    
    # 打印确认信息
    print("[信息] cursor_pro_keep_alive模块状态已重置")
    
    # 手动触发垃圾回收
    import gc
    gc.collect()
    
    return True

# 注册退出时的清理函数
atexit.register(reset_module_state)

if __name__ == "__main__":
    browser_manager = None
    try:
        # 检查并提升权限
        check_and_elevate_privileges()

        print_logo()
        
        print(f"\n{Colors.CYAN}操作选项{Colors.NC}")
        print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        print(f" {Colors.CYAN}请选择要执行的操作:{Colors.NC}")
        print(f" 1. 仅修改ID")
        print(f" 2. 仅进行注册流程")
        print(f" 3. 执行全流程（修改ID+注册）[默认]")
        print(f" 4. 恢复备份")
        print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        print()

        # 从环境变量获取默认选项
        env_action = os.getenv('DEFAULTACTION')
        if env_action and env_action.strip() in ['1', '2', '3', '4']:
            choice = env_action.strip()
            print(f"{Colors.BLUE}┌──────────────────────────────────────────────────────{Colors.NC}")
            print(f"{Colors.BLUE}│{Colors.NC} 操作选择")
            print(f"{Colors.BLUE}│{Colors.NC}   [{Colors.GREEN}配置{Colors.NC}] 使用环境变量配置: DEFAULTACTION={choice}")
            print(f"{Colors.BLUE}└──────────────────────────────────────────────────────{Colors.NC}")
            print()
        else:
            choice = input(f"{Colors.CYAN}请输入选项 (1-4)，直接回车默认选择3: {Colors.NC}").strip()
            # 默认选择全流程
            if not choice:
                choice = "3"
        
        # 添加版本检查
        if not VersionChecker.check_version():
            sys.exit(1)
        
        if choice not in ["1", "2", "3", "4"]:
            print_box(
                "错误",
                [
                    (Colors.RED, "无效的选项"),
                    (Colors.WHITE, "将使用默认选项3：执行全流程")
                ]
            )
            choice = "3"
        
        # 处理恢复备份选项
        if choice == "4":
            restore_backup()
            print_box(f"有问题可以加QQ群联系群主：{Colors.YELLOW}*********{Colors.NC}")
            input("\n备份恢复操作已完成，按回车键退出程序...")
            sys.exit(0)

        try:
            # 获取Cursor版本信息但不使用版本判断
            version = get_cursor_version()
            if version:
                print_box(
                    "版本检测",
                    [
                        (Colors.GREEN, f"检测到 Cursor 版本: v{version}")
                    ]
                )
                
                # 使用已导入的机器码修改工具，不再需要重复导入
                # from Cursor_Change_ID import main as change_id_main
                
                if choice == "1":
                    # 仅修改机器码
                    change_machine_id(silent=False)
                    sys.exit(0)
                elif choice == "2":
                    # 仅注册流程，跳过修改机器码
                    log_info("跳过修改机器码，直接进行注册流程", show_box=True)
                elif choice == "3":
                    # 执行修改机器码
                    change_machine_id(silent=False)
            else:
                print_box(
                    "错误",
                    [
                        (Colors.RED, "无法获取 Cursor 版本信息"),
                        (Colors.WHITE, "请确保 Cursor 安装在默认路径")
                    ]
                )
                sys.exit(1)

            # 如果选择了仅修改机器码，前面已经处理完并退出了
            # 检查.env文件是否存在
            if not os.path.exists(dotenv_path):
                log_error("未检测到.env文件，正在创建...", show_box=True)
                try:
                    env_content = get_default_env_template()
                    with open(dotenv_path, 'w', encoding='utf-8') as env_file:
                        env_file.write(env_content)
                    log_success(f".env文件已创建，路径为: {dotenv_path}", show_box=True)
                    print_box(
                        "配置说明",
                        [
                            (Colors.GREEN, "请按照.env文件里的示例进行配置文件内容"),
                            (Colors.GREEN, "如果不会配置可以加QQ群：********* 联系群主"),
                            (Colors.YELLOW, ""),
                            (Colors.YELLOW, "配置完成后可使用自动注册功能"),
                            (Colors.YELLOW, "不会配置？配置太繁琐？可以请群主喝杯奶茶让群主给你当牛马帮你配置文件 [滑稽]"),
                            (Colors.YELLOW, "或者可以直接使用群主提供的独享配置文件，到手就能用 [滑稽]")
                        ]
                    )
                    input("\n按回车键退出程序...")
                    os._exit(0)
                except Exception as e:
                    log_error(f"创建.env文件失败: {str(e)}", show_box=True)
                    input("\n按回车键退出程序...")
                    os._exit(1)
            else:
                env_path = os.path.abspath(dotenv_path)
                print_box(
                    "环境检查",
                    [
                        (Colors.GREEN, f"已找到 .env 文件:"),
                        (Colors.WHITE, dotenv_path),
                        (Colors.WHITE, ""),
                        (Colors.YELLOW, "自动注册需要配置.env文件"),
                        (Colors.YELLOW, "如果你没有配置.env文件，请先配置.env文件"),
                        (Colors.GREEN, "如果你不会配置.env文件，可以加QQ群：********* 联系群主"),
                        (Colors.YELLOW, "不会配置？配置太繁琐？可以请群主喝杯奶茶让群主给你当牛马帮你配置文件 [滑稽]"),
                        (Colors.YELLOW, "或者可以直接使用群主提供的独享配置文件，到手就能用 [滑稽]")
                    ]
                )

            if choice != "1":  # 如果不是仅修改机器码，则需要确认是否继续
                # 从环境变量获取自动注册配置
                auto_register = os.getenv('AUTOREGISTERCURSORACCOUNT', '').strip()
                if auto_register and auto_register.upper() in ['Y', 'N']:
                    print(f"{Colors.BLUE}┌──────────────────────────────────────────────────────{Colors.NC}")
                    print(f"{Colors.BLUE}│{Colors.NC} 自动注册设置")
                    print(f"{Colors.BLUE}│{Colors.NC}   [{Colors.GREEN}配置{Colors.NC}] 使用环境变量配置: AUTOREGISTERCURSORACCOUNT={auto_register.upper()}")
                    print(f"{Colors.BLUE}└──────────────────────────────────────────────────────{Colors.NC}")
                    print()
                    if auto_register.upper() == 'N':
                        log_info("已取消操作", show_box=True)
                        os._exit(0)
                else:
                    user_input = input(f"\n{Colors.CYAN}请先查看上面的提示，是否继续？(y/n) 默认y: {Colors.NC}").strip().lower()
                    if user_input == 'n':
                        log_info("已取消操作", show_box=True)
                        os._exit(0)

            # 检查Chrome浏览器
            check_chrome_browser()
            
            # 如果是仅注册流程，跳过修改机器码
            if choice == "2":
                log_info("跳过修改机器码，直接进行注册流程", show_box=True)
            # 注释掉这段代码，因为选项3在前面已经执行过change_machine_id，不需要重复执行
            # elif choice == "3":
            #     # 执行修改机器码
            #     if minor <= 44:
            #         change_machine_id()
            #     else:
            #         change_machine_id(True)

            print_box("初始化程序")
            log_process("正在初始化浏览器...", show_box=True)
            browser_manager = BrowserManager()
            browser = browser_manager.init_browser()
            
            # 获取第一个标签页并验证指纹
            tab = browser.get_tab()
            # 验证指纹是否成功应用
            try:
                fingerprint_verified = browser_manager.verify_fingerprint(tab)
                if fingerprint_verified:
                    log_success("浏览器指纹验证通过", show_box=True)
                else:
                    log_warn("部分浏览器指纹特性未能成功应用，但将继续执行", show_box=True)
            except Exception as e:
                log_warn(f"验证指纹时出错: {str(e)}，但将继续执行", show_box=True)

            log_process("正在初始化邮箱验证模块...", show_box=True)
            try:
                # 先检查是否已经存在全局的email_handler实例（可能由auto_register_dialog.py初始化）
                if not hasattr(sys.modules[__name__], 'email_handler') or email_handler is None:
                    email_handler = EmailVerificationHandler()
                else:
                    log_info("使用已初始化的邮箱验证模块", show_box=True)
            except ValueError as e:
                if "不支持163邮箱" in str(e):
                    browser_manager.quit()
                    # 已经在config.py中显示了错误信息，这里不需要重复显示
                    input("\n按回车键退出程序...")
                    os._exit(1)
                else:
                    # 其他ValueError错误，显示完整信息
                    log_error(f"程序执行出现错误: {str(e)}", show_box=True)
                    print_box(f"有问题可以加QQ群联系群主：{Colors.YELLOW}*********{Colors.NC}")
                    import traceback
                    log_error(traceback.format_exc(), show_box=True)
                    input("\n按回车键退出程序...")
                    os._exit(1)

            print_box(
                "配置信息",
                [
                    (Colors.WHITE, f"登录地址: {login_url}"),
                    (Colors.WHITE, f"注册地址: {sign_up_url}"),
                    (Colors.WHITE, f"设置地址: {settings_url}"),
                    (Colors.WHITE, f"邮箱地址: {mail_url}")
                ]
            )

            log_process("正在随机生成账号信息...", show_box=True)
            email_generator = EmailGenerator()
            account = email_generator.generate_email()
            password = email_generator.default_password
            first_name = email_generator.default_first_name
            last_name = email_generator.default_last_name

            log_info(f"生成的邮箱账号: {account}", show_box=True)

            tab = browser.latest_tab
            tab.run_js("try { turnstile.reset() } catch(e) { }")

            if sign_up_account(browser, tab):
                log_process("开始授权流程...", show_box=True)
                
                # 使用新的授权流程获取token
                token_data = handle_auth_flow(browser, tab, account, password)
                
                if token_data and token_data.get('accessToken') and token_data.get('refreshToken'):
                    # 更新认证信息
                    update_cursor_auth(
                        email=account,
                        access_token=token_data['accessToken'],
                        refresh_token=token_data['refreshToken']
                    )
                    
                    # 保存账号信息到JSON文件
                    save_account_to_json(account, password, token_data['accessToken'], 2)  # 使用方案二(账号注册)
                    
                    print_box(
                        "更新认证信息",
                        [
                            (Colors.GREEN, "更新状态:"),
                            (Colors.WHITE, f"• cachedSignUpType: {Colors.GREEN}已更新{Colors.NC}"),
                            (Colors.WHITE, f"• cachedEmail:      {Colors.GREEN}已更新 ({account}){Colors.NC}"),
                            (Colors.WHITE, f"• accessToken:      {Colors.GREEN}已更新 (长度: {len(token_data['accessToken'])}){Colors.NC}"),
                            (Colors.WHITE, f"• refreshToken:     {Colors.GREEN}已更新 (长度: {len(token_data['refreshToken'])}){Colors.NC}")
                        ]
                    )
                    
                    log_success("所有操作已完成", show_box=True)
                else:
                    log_error("获取访问令牌失败，认证流程未完成", show_box=True)

        except Exception as e:
            log_error(f"程序执行出现错误: {str(e)}", show_box=True)
            print_box(f"有问题可以加QQ群联系群主：{Colors.YELLOW}*********{Colors.NC}")
            import traceback
            log_error(traceback.format_exc(), show_box=True)
        finally:
            if browser_manager:
                browser_manager.quit()
            print_box(f"有问题可以加QQ群联系群主：{Colors.YELLOW}*********{Colors.NC}")
            input("\n程序执行完毕\n\n祝您天天开心，发大财，按回车接接接...")
    except KeyboardInterrupt:
        sys.exit(0)  # 直接退出，不显示任何信息
    except Exception:
        sys.exit(1)  # 直接退出，不显示任何信息
    finally:
        try:
            if browser_manager:
                browser_manager.quit()
        except:
            pass  # 忽略关闭浏览器时的任何错误
