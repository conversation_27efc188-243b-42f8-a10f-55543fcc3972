"""
按额度删除账户对话框模块
"""

import os
from PySide6.QtWidgets import (
    QWidget, QFrame, QVBoxLayout, QHBoxLayout, QLabel, 
    QComboBox, QLineEdit, QPushButton, QDialog
)
from PySide6.QtCore import Qt, Signal

from theme import Theme
from utils import Utils
from logger import Logger, info, error
from ui.styled_widgets import StyledFrame
from widgets.dialog import StyledDialog

class DeleteQuotaDialog(QFrame):
    """按额度删除账户对话框类"""
    
    delete_confirmed = Signal(str, str) # operation, value
    
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.parent = parent
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        # 创建对话框
        self.dialog = StyledDialog(self.parent, "按使用次数删除账户")
        
        # 添加说明文本
        description = QLabel("按照高级模型的已使用次数进行筛选删除：")
        description.setStyleSheet(f"""
                color: {Theme.TEXT_PRIMARY};
                font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        self.dialog.addWidget(description)
        
        # 创建条件选择区域的背景框架
        conditions_frame = QFrame()
        conditions_frame.setObjectName("conditionsFrame")
        conditions_frame.setStyleSheet(f"""
            #conditionsFrame {{
                background-color: #121317;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        
        frame_layout = QVBoxLayout(conditions_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        
        # 创建条件选择区域
        condition_layout = QHBoxLayout()
        condition_layout.setSpacing(10)
        
        # 操作符选择
        self.operator_combo = QComboBox()
        self.operator_combo.addItems(["大于等于", "小于等于", "等于"])
        self.operator_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                min-width: 120px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QComboBox:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: center right;
                width: 30px;
                border-left: none;
                background-color: transparent;
            }}
            QComboBox::down-arrow {{
                image: url(:/icons/chevron_down.svg);
                width: 16px;
                height: 16px;
            }}
            QComboBox QAbstractItemView {{
                background-color: #181b21;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                selection-background-color: {Theme.ACCENT};
                selection-color: white;
                outline: 0;
            }}
            QComboBox QAbstractItemView::item {{
                height: 30px;
                padding-left: 15px;
            }}
            QComboBox QAbstractItemView::item:hover {{
                background-color: #1E2128;
            }}
            QComboBox QAbstractItemView::item:selected {{
                background-color: {Theme.ACCENT};
                color: white;
            }}
        """)
        condition_layout.addWidget(self.operator_combo)
        
        # 额度输入
        self.quota_input = QLineEdit()
        self.quota_input.setPlaceholderText("输入额度")
        self.quota_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                min-width: 120px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QLineEdit:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
        """)
        condition_layout.addWidget(self.quota_input)
        
        # 添加条件布局到框架
        frame_layout.addLayout(condition_layout)
        
        # 添加框架到对话框
        self.dialog.addWidget(conditions_frame)
        
        # 添加确认和取消按钮
        confirm_btn = self.dialog.addButtons("确认", "取消")
        # 将确认按钮连接到处理函数
        confirm_btn.clicked.connect(self.delete_accounts_by_quota)
    
    def delete_accounts_by_quota(self):
        """删除指定额度的账户"""
        # 获取输入的额度
        quota_text = self.quota_input.text().strip()
        
        if not quota_text:
            self.main_window.show_toast("请输入要删除的账户额度", error=True)
            return
        
        try:
            quota = int(quota_text)
        except:
            self.main_window.show_toast("请输入有效的额度数值", error=True)
            return
        
        # 获取操作符
        operator = self.operator_combo.currentText()
        
        # 确认是否要删除
        if not Utils.confirm_message(
            self.main_window,
            "删除指定额度账户",
            f"确定要删除所有高级模型使用次数{operator}{quota}的账户吗？",
        ):
            return
        
        # 在执行删除前，检查是否所有账户都已获取额度信息
        accounts_without_quota = [acc for acc in self.main_window.account_data.accounts if 'real_usage' not in acc]
        if accounts_without_quota:
            message = f"有{len(accounts_without_quota)}个账户未获取额度信息，是否先刷新所有账户额度？"
            if Utils.confirm_message(self.main_window, "建议先刷新额度", message):
                # 显示正在获取额度的提示
                self.main_window.show_toast("正在获取账户额度，请稍候...")
                # 获取所有账户额度
                self.main_window.fetch_all_accounts_quota(show_toast=True)
                # 在获取完成后执行删除操作
                self.main_window.quotaFetcher.all_quotas_fetched.connect(lambda: self._execute_delete_by_quota(quota, operator))
                # 关闭对话框
                self.dialog.accept()
                return  # 先返回，等待额度获取完成
            
        # 直接执行删除
        self._execute_delete_by_quota(quota, operator)
        # 关闭对话框
        self.dialog.accept()
    
    def _execute_delete_by_quota(self, quota, operator, dialog=None):
        """实际执行按额度删除的操作"""
        # 导入日志模块
        from logger import info, error
        
        info(f"开始执行按额度删除账户: 条件 = 高级模型使用次数{operator}{quota}")
        
        # 如果是从信号槽调用，断开连接避免重复执行
        try:
            self.main_window.quotaFetcher.all_quotas_fetched.disconnect()
            info("断开额度获取完成信号连接")
        except:
            pass
        
        # 执行删除，获取被删除的账户邮箱列表
        try:
            info(f"开始筛选符合条件的账户: 高级模型使用次数{operator}{quota}")
            deleted_emails = self.main_window.account_data.delete_accounts_by_quota(quota, operator)
            
            if deleted_emails:
                # 记录删除成功的账户
                info(f"成功筛选出{len(deleted_emails)}个符合条件的账户: {', '.join(deleted_emails)}")
                self.main_window.show_toast(f"已删除 {len(deleted_emails)} 个高级模型使用次数{operator}{quota}的账户")
                
                # 1. 重新加载本地存储的账户数据
                self.main_window.account_data.load_accounts()
                info("重新加载账户数据完成")
                
                # 2. 清除已删除账户的行
                for email in deleted_emails:
                    if email in self.main_window.account_rows:
                        row_widget = self.main_window.account_rows[email]
                        self.main_window.accounts_layout.removeWidget(row_widget)
                        row_widget.deleteLater()
                        del self.main_window.account_rows[email]
                info("从UI中移除已删除账户完成")
                
                # 3. 更新账户计数
                self.main_window._update_accounts_count()
                
                # 4. 更新当前账户UI（如果当前账户被删除）
                if self.main_window.current_email in deleted_emails:
                    info(f"当前账户 {self.main_window.current_email} 在删除列表中，需要切换当前账户")
                    # 如果还有账户，切换到第一个
                    if self.main_window.account_data.accounts:
                        new_current = self.main_window.account_data.accounts[0]
                        new_email = new_current.get("email", "")
                        self.main_window.current_email = new_email
                        info(f"切换当前账户至: {new_email}")
                    else:
                        # 没有账户了，清空当前邮箱
                        self.main_window.current_email = ""
                        info("没有可用账户，清空当前账户")
                    self.main_window.load_current_account()
                
                # 5. 仅使用不重建UI的排序方法
                self.main_window._sort_accounts_without_rebuild_ui()
                info("重新排序账户列表完成")
                
                # 6. 只在有账户的情况下刷新额度数据
                if self.main_window.account_data.accounts:
                    info(f"开始刷新剩余账户额度数据（剩余{len(self.main_window.account_data.accounts)}个账户）")
                    # 这里使用手动刷新模式，避免重建UI
                    self.main_window.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=False)
            else:
                info(f"未找到符合条件的账户: 高级模型使用次数{operator}{quota}")
                self.main_window.show_toast(f"没有发现高级模型使用次数{operator}{quota}的账户")
            
            # 记录操作完成
            info(f"按额度删除账户操作完成: 条件=高级模型使用次数{operator}{quota}, 共删除{len(deleted_emails)}个账户")
        except Exception as e:
            error_msg = str(e)
            error(f"按额度删除账户时发生异常: 条件=高级模型使用次数{operator}{quota}, 错误={error_msg}")
            self.main_window.show_toast(f"删除账户时出错: {error_msg}", error=True)
        
        # 如果外部传入了对话框，关闭它
        if dialog:
            dialog.accept()
    
    def exec(self):
        """执行对话框"""
        return self.dialog.exec() 