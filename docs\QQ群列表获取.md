# 源码
```python
import requests
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import json
import time
import qrcode
import os
from PIL import Image
import re

@dataclass
class QQGroup:
    group_name: str
    group_id: str
    member_count: Optional[int] = None
    
class QQGroupAPI:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://qun.qq.com"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Content-Type": "application/json;charset=UTF-8",
            "Referer": "https://qun.qq.com/member.html",
            "Origin": "https://qun.qq.com"
        }
        self.session.headers.update(self.headers)
        self.login_params = {
            "appid": "715030901",
            "daid": "73",
            "pt_3rd_aid": "0",
        }

    def qr_login(self) -> bool:
        """
        使用QQ扫码登录
        :return: 是否登录成功
        """
        try:
            print("正在初始化登录...")
            # 1. 访问群管理页面获取xlogin URL
            response = self.session.get("https://qun.qq.com/member.html")
            xlogin_url = f"https://xui.ptlogin2.qq.com/cgi-bin/xlogin?appid={self.login_params['appid']}&daid={self.login_params['daid']}&s=8&pt_3rd_aid=0"
                
            # 2. 访问xlogin页面获取必要参数
            print("正在获取登录参数...")
            response = self.session.get(xlogin_url)
            
            # 3. 获取二维码
            print("正在获取二维码...")
            qr_code_url = "https://ssl.ptlogin2.qq.com/ptqrshow"
            qr_response = self.session.get(qr_code_url, params={
                "appid": self.login_params["appid"],
                "e": "2",
                "l": "M",
                "s": "3",
                "d": "72",
                "v": "4",
                "t": str(time.time()),
                "daid": self.login_params["daid"],
                "pt_3rd_aid": self.login_params["pt_3rd_aid"],
            })

            if qr_response.status_code != 200:
                print("获取二维码失败")
                return False

            # 保存二维码图片
            qr_file = "qq_login_qr.png"
            with open(qr_file, "wb") as f:
                f.write(qr_response.content)

            # 显示二维码
            print("请使用手机QQ扫描二维码登录：")
            img = Image.open(qr_file)
            img.show()

            # 获取二维码状态
            qr_state_url = "https://ssl.ptlogin2.qq.com/ptqrlogin"
            while True:
                state_response = self.session.get(qr_state_url, params={
                    "u1": "https://qun.qq.com/member.html",
                    "ptqrtoken": self._get_ptqrtoken(qr_response.cookies.get("qrsig")),
                    "ptredirect": "0",
                    "h": "1",
                    "t": "1",
                    "g": "1",
                    "from_ui": "1",
                    "ptlang": "2052",
                    "action": "0-0-" + str(int(time.time())),
                    "js_ver": "23123123",
                    "js_type": "1",
                    "login_sig": "",
                    "pt_uistyle": "40",
                    "aid": self.login_params["appid"],
                    "daid": self.login_params["daid"],
                })

                if "二维码未失效" in state_response.text:
                    print("等待扫码...")
                elif "二维码认证中" in state_response.text:
                    print("扫码成功，请在手机上确认...")
                elif "登录成功" in state_response.text:
                    print("登录成功！")
                    
                    # 解析登录回调URL
                    match = re.search(r"'(https://[^']+)'", state_response.text)
                    if match:
                        redirect_url = match.group(1)
                        print(f"正在跳转到：{redirect_url}")
                        
                        # 访问跳转URL获取必要的Cookie
                        response = self.session.get(redirect_url, allow_redirects=True)
                        
                        # 检查关键Cookie是否存在
                        required_cookies = ['skey', 'p_skey', 'pt4_token']
                        missing_cookies = [cookie for cookie in required_cookies if not self.session.cookies.get(cookie)]
                        
                        if missing_cookies:
                            print(f"缺少必要的Cookie: {', '.join(missing_cookies)}")
                            return False
                        
                        # 再次访问群管理页面
                        self.session.get("https://qun.qq.com/member.html")
                        
                        # 清理二维码文件
                        if os.path.exists(qr_file):
                            os.remove(qr_file)
                        return True
                    else:
                        print("无法获取跳转URL")
                        return False
                        
                elif "二维码已失效" in state_response.text:
                    print("二维码已失效，请重新运行程序")
                    if os.path.exists(qr_file):
                        os.remove(qr_file)
                    return False

                time.sleep(2)

        except Exception as e:
            print(f"扫码登录失败: {str(e)}")
            return False

    def _get_ptqrtoken(self, qrsig: str) -> int:
        """
        计算ptqrtoken
        :param qrsig: qrsig cookie值
        :return: ptqrtoken值
        """
        hash_val = 0
        for char in qrsig:
            hash_val += (hash_val << 5) + ord(char)
        return hash_val & 2147483647

    def get_joined_groups(self) -> List[QQGroup]:
        """
        获取已加入的QQ群列表
        :return: QQ群列表
        """
        try:
            # 获取群列表的API接口
            url = f"{self.base_url}/cgi-bin/qun_mgr/get_group_list"
            bkn = self._get_g_tk()
            
            # 更新请求头
            headers = {
                "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
                "Origin": "https://qun.qq.com",
                "Referer": "https://qun.qq.com/member.html"
            }
            self.session.headers.update(headers)
            
            # 构造POST数据
            data = {
                "bkn": bkn
            }
            
            print(f"请求URL: {url}")
            print(f"bkn: {bkn}")
            print(f"请求头: {self.session.headers}")
            print(f"Cookie: {self.session.cookies.get_dict()}")
            
            # 发送POST请求
            response = self.session.post(url, data=data)
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, str):
                        data = json.loads(data)
                        
                    print(f"解析后的JSON数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    
                    if data.get("ec") == 4:  # 未登录
                        print("登录状态已失效，请重新登录")
                        return []
                        
                    groups = []
                    # 检查create、manage和join三种类型的群
                    for group_type in ['create', 'manage', 'join']:
                        group_list = data.get(group_type, [])
                        for group in group_list:
                            groups.append(QQGroup(
                                group_name=group.get("gn", ""),
                                group_id=str(group.get("gc", "")),
                                member_count=group.get("members", 0)
                            ))
                    return groups
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {str(e)}")
                    return []
            return []
        except Exception as e:
            print(f"获取群列表失败: {str(e)}")
            return []

    def _get_g_tk(self) -> str:
        """
        计算g_tk/bkn值，使用skey而不是p_skey
        :return: g_tk值
        """
        skey = self.session.cookies.get("skey", "")
        print(f"当前skey: {skey}")
        
        hash_val = 5381
        for char in skey:
            hash_val += (hash_val << 5) + ord(char)
        return str(hash_val & 2147483647)

def main():
    # 使用示例
    api = QQGroupAPI()
    
    # 使用扫码登录
    if api.qr_login():
        print("开始获取群列表...")
        groups = api.get_joined_groups()
        print(f"共加入了 {len(groups)} 个群：")
        for group in groups:
            print(f"群名：{group.group_name} | 群号：{group.group_id} | 成员数：{group.member_count}")
    else:
        print("登录失败！")

if __name__ == "__main__":
    main() 
```

# 获取到的
```
正在初始化登录...
正在获取登录参数...
正在获取二维码...
请使用手机QQ扫描二维码登录：
等待扫码...
等待扫码...
等待扫码...
等待扫码...
等待扫码...
等待扫码...
等待扫码...
等待扫码...
等待扫码...
等待扫码...
登录成功！
正在跳转到：https://ptlogin2.qun.qq.com/check_sig?pttype=1&uin=435077052&service=ptqrlogin&nodirect=0&ptsigx=40d9333b7aa120d6ea169f15d9b67541123c070eba5cd278df7b41a202edbdcd75a31fccf8cdf309c000324334a04f0f7de9d85bfee109f20a56f7c86dfa15979984d258307c5ee6&s_url=https%3A%2F%2Fqun.qq.com%2Fmember.html&f_url=&ptlang=2052&ptredirect=100&aid=715030901&daid=73&j_later=0&low_login_hour=0&regmaster=0&pt_login_type=3&pt_aid=0&pt_aaid=16&pt_light=0&pt_3rd_aid=0
开始获取群列表...
当前skey: @DBA3DvonK
请求URL: https://qun.qq.com/cgi-bin/qun_mgr/get_group_list
bkn: 434811009
请求头: {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Accept-Encoding': 'gzip, deflate, zstd', 'Accept': 'application/json, text/plain, */*', 'Connection': 'keep-alive', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8', 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8', 'Referer': 'https://qun.qq.com/member.html', 'Origin': 'https://qun.qq.com'}
Cookie: {'tgw_l7_route': 'f1dd34063017ea8b03d2c90177ee5ce6', 'qrsig': '396120ad5ffc36328787865233e67471ccdba48116e9690ba0af2ba01bdd7dfdc3a0c0b40bdbe13b819a45f72a01b189e0147b5637a73ac0', 'pt2gguin': 'o0435077052', 'ETK': '', 'superuin': 'o0435077052', 'supertoken': '3497884052', 'superkey': 'T5aEo2PMJl3WbswSfWb8KohA9DCZIiSJ6O-udquAqo4_', 'pt_recent_uins': 'fc91ab19b676d1da83dc19bcf91a4025bb5d74ac5f95565ce470363e0e8e2c8baa254144f046f8b78ce23f0a57dbb18c112c54b252d63c66', 'ptnick_435077052': 'e38080', 'uin': 'o0435077052', 'skey': '@DBA3DvonK', 'RK': 'tFfkGEd7aN', 'ptcz': 'f9000c8968d649d760f14c65bc7faa9ff2fb7d18ac221e440d5ebcc7af37453b', 'p_uin': 'o0435077052', 'pt4_token': 'l2RHSwyB07EAkDX4-eIYWqTz0hmxZSre5at26jMDDAg_', 'p_skey': '82AyLiyM3dmUg5j*RXgspnOz73qIphzQ53xtItwVTIk_'}
响应状态码: 200
响应内容: {"ec":0,"errcode":0,"em":"","join":[{"gc":154095590,"gn":"\u6d4b\u8bd5\u7fa4","owner":43891963},{"gc":2157041667,"gn":"\u8001\u5e74\u4e0d\u4ea4\u6d41&nbsp;Four","owner":43891963}]}
解析后的JSON数据: {
  "ec": 0,
  "errcode": 0,
  "em": "",
  "join": [
    {
      "gc": 154095590,
      "gn": "测试群",
      "owner": 43891963
    },
    {
      "gc": 2157041667,
      "gn": "老年不交流&nbsp;Four",
      "owner": 43891963
    }
  ]
}
共加入了 2 个群：
群名：测试群 | 群号：154095590 | 成员数：0
群名：老年不交流&nbsp;Four | 群号：2157041667 | 成员数：0
```