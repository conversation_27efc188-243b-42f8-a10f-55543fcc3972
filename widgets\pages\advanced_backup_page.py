from PySide6.QtWidgets import Q<PERSON><PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QListWidget, QFrame, QSizePolicy, QListWidgetItem, QScrollArea, QDialog, QTextEdit
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QShowEvent, QColor, QTextCursor, QFont
from theme import Theme
from core.cursor_auto.advanced_backup_manager import AdvancedBackupManager, BackupScheme, BackupItem
from widgets.dialog import StyledDialog
from widgets.styled_widgets import StyledFrame
import os
import re
from utils import get_app_data_dir
import sqlite3 # 导入 sqlite3
from PySide6.QtWidgets import QListWidget, QListWidgetItem # 确保导入
from PySide6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView, QTreeWidget, QTreeWidgetItem, QAbstractItemView, QMenu # Added QMenu
from PySide6.QtGui import QCursor # Added QCursor
import json
from PySide6.QtGui import QColor # --- 添加：导入 QColor --- 
import shutil
import os # 导入 os
import subprocess # 导入 subprocess
import platform # 导入 platform
# --- 添加: 导入全局日志函数 ---
from logger import info as log_info, warning as log_warning, error as log_error, debug as log_debug
# --- 添加结束 ---

class BackupSchemeCard(QFrame):
    def __init__(self, scheme, base_dir, on_detail, on_delete, on_backup, on_restore, parent=None):
        super().__init__(parent)
        self.scheme = scheme
        self.base_dir = base_dir # 保存 base_dir
        self.setObjectName("BackupSchemeCard")
        self.setFixedHeight(76)
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        self.setStyleSheet(f'''
            QFrame#BackupSchemeCard {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS};
                margin: 0px;
                padding: 0px;
            }}
            QFrame#BackupSchemeCard:hover {{
                background-color: #23262e;
            }}
        ''')
        layout = QHBoxLayout(self)
        layout.setContentsMargins(24, 0, 24, 0)
        layout.setSpacing(18)
        icon = QLabel("📦")
        icon.setStyleSheet(f"font-size: 28px;")
        layout.addWidget(icon, 0, Qt.AlignmentFlag.AlignVCenter)
        name = QLabel(scheme.name)
        name.setStyleSheet(f"font-size: {Theme.FONT_SIZE_TITLE}; font-weight: bold; color: {Theme.TEXT_PRIMARY};")
        layout.addWidget(name, 0, Qt.AlignmentFlag.AlignVCenter)
        layout.addStretch(1)

        delete_color = Theme.ERROR
        delete_hover = "#e74c3c"
        delete_pressed = "#c0392b"

        backup_color = Theme.SUCCESS
        backup_hover = "#34B892"
        backup_pressed = "#24856A"

        restore_color = "#3498db"
        restore_hover = "#2980b9"
        restore_pressed = "#2471a3"

        self.btn_restore = QPushButton("🚀 恢复")
        self.btn_backup = QPushButton("💾 备份")
        self.btn_delete = QPushButton("🗑️ 删除")

        common_style = f"""
            QPushButton {{ border: none; border-radius: {Theme.BORDER_RADIUS_SMALL}; font-weight: bold; color: white; padding: 4px 10px; }}
            QPushButton:disabled {{ background-color: {Theme.DISABLED}; color: {Theme.TEXT_SECONDARY}; }}
        """
        restore_style = common_style + f"""
            QPushButton {{ background-color: {restore_color}; }}
            QPushButton:hover {{ background-color: {restore_hover}; }}
            QPushButton:pressed {{ background-color: {restore_pressed}; }}
        """
        backup_style = common_style + f"""
            QPushButton {{ background-color: {backup_color}; }}
            QPushButton:hover {{ background-color: {backup_hover}; }}
            QPushButton:pressed {{ background-color: {backup_pressed}; }}
        """
        delete_style = common_style + f"""
            QPushButton {{ background-color: {delete_color}; }}
            QPushButton:hover {{ background-color: {delete_hover}; }}
            QPushButton:pressed {{ background-color: {delete_pressed}; }}
        """

        self.btn_restore.setStyleSheet(restore_style)
        self.btn_backup.setStyleSheet(backup_style)
        self.btn_delete.setStyleSheet(delete_style)

        for btn in [self.btn_backup, self.btn_restore, self.btn_delete]:
            btn.setFixedHeight(36)
            btn.setMinimumWidth(80)
            btn.setCursor(Qt.CursorShape.PointingHandCursor)

        self.btn_restore.clicked.connect(lambda: on_restore(scheme, direct=True))
        self.btn_backup.clicked.connect(lambda: on_backup(scheme, direct=True))
        self.btn_delete.clicked.connect(lambda: on_delete(scheme))

        layout.addWidget(self.btn_backup, 0, Qt.AlignmentFlag.AlignVCenter)
        layout.addWidget(self.btn_restore, 0, Qt.AlignmentFlag.AlignVCenter)
        layout.addWidget(self.btn_delete, 0, Qt.AlignmentFlag.AlignVCenter)

        self.mouseDoubleClickEvent = lambda e: on_detail(scheme)
        
        # 修改：调用合并后的状态更新方法
        self.update_button_states()

    # 修改：合并为 update_button_states
    def update_button_states(self):
        # 检查备份按钮状态
        backup_enabled = bool(self.scheme.items)
        self.btn_backup.setEnabled(backup_enabled)
        
        # 修改：恢复按钮状态检查逻辑
        can_restore = False
        if self.scheme.items: # 条件1：方案必须有备份项
            scheme_dir = os.path.join(self.base_dir, self.scheme.name)
            backup_files_dir = os.path.join(scheme_dir, 'backups')
            if os.path.isdir(backup_files_dir): # 条件2: backups 目录必须存在
                # 条件3 (修改)：必须至少有一个当前项存在备份文件
                for item in self.scheme.items:
                    base_filename = item.get_backup_filename()
                    if not base_filename:
                        continue # 跳过无法生成文件名的项
                    prefix_to_check = base_filename + "__"
                    try:
                        for filename in os.listdir(backup_files_dir):
                            if filename.startswith(prefix_to_check):
                                can_restore = True # 找到了！
                                break # 无需再检查此项的其他备份或检查其他项
                    except OSError:
                        # 访问目录出错，保守起见认为无法恢复
                        print(f"[WARN] Error accessing backup dir '{backup_files_dir}' for scheme '{self.scheme.name}'")
                        can_restore = False # 出错则禁用
                        break # 停止检查
                    if can_restore:
                        break # 已找到一个有效备份，无需检查其他项
                # 循环结束，如果 can_restore 仍为 False，则表示未找到任何有效备份
                    
        self.btn_restore.setEnabled(can_restore) # 设置恢复按钮状态

# --- BEGIN DataViewerDialog CLASS ---
class DataViewerDialog(StyledDialog):
    """对话框，用于显示数据库表数据并支持选择列或行/列组合"""
    class Mode:
        SELECT_COLUMN = 1
        SELECT_ROW_COLUMNS = 2

    def __init__(self, db_path, table_name, mode, parent=None):
        title = "选择 Key 列" if mode == self.Mode.SELECT_COLUMN else "选择主键列、JSON列和数据行"
        # --- 修改：设置固定高度为 700 --- 
        super().__init__(parent, title, width=1100, height=700) # Use fixed width and height
        # --- 修改结束 ---
        self.db_path = db_path
        self.table_name = table_name
        self.mode = mode
        self.columns = []
        self.selected_column = None
        self.selected_pk_col = None
        self.selected_json_col = None
        self.selected_row_pk_value = None

        layout = QVBoxLayout()

        self.info_label = QLabel("请稍候，正在加载数据...")
        self.info_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY};")
        layout.addWidget(self.info_label)

        self.table_widget = QTableWidget()
        self.table_widget.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.table_widget.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table_widget.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table_widget.setStyleSheet(f"""
            QTableWidget {{
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                gridline-color: {Theme.BORDER};
                outline: none;
                alternate-background-color: {Theme.CARD_LEVEL_2}; /* 交替行背景色 */
            }}
            QHeaderView::section {{
                background-color: {Theme.CARD_LEVEL_3}; /* 稍深的标题背景 */
                color: {Theme.TEXT_SECONDARY}; /* 标题文字颜色稍暗 */
                padding: 6px 5px; /* 调整内边距 */
                border: none;
                border-bottom: 1px solid {Theme.BORDER};
                font-weight: bold; /* 标题加粗 */
                text-align: left; /* 标题左对齐 */
            }}
            QHeaderView::section:hover {{
                background-color: {Theme.HOVER};
                color: {Theme.TEXT_PRIMARY};
            }}
            QTableWidget::item {{
                padding: 8px 10px; /* 增加单元格垂直内边距 */
                border-bottom: 1px solid {Theme.BORDER}; /* 保留细分隔线 */
                border-right: 1px solid {Theme.BORDER}; /* 添加右侧细线 */
            }}
            QTableWidget::item:selected {{
                background-color: {Theme.ACCENT_HOVER}; /* 保持选中颜色 */
                color: white;
            }}
            /* --- 新增：滚动条样式 --- */
            QScrollBar:vertical {{
                border: none;
                background: {Theme.CARD_LEVEL_1}; /* 轨道背景色 */
                width: 8px; /* 滚动条宽度 */
                margin: 0px 0px 0px 0px;
            }}
            QScrollBar::handle:vertical {{
                background: {Theme.CARD_LEVEL_3}; /* 滑块颜色 */
                min-height: 20px;
                border-radius: 4px; /* 圆角 */
            }}
            QScrollBar::handle:vertical:hover {{
                background: {Theme.HOVER}; /* 悬停颜色 */
            }}
            QScrollBar::handle:vertical:pressed {{
                background: {Theme.SECONDARY}; /* 按下颜色 */
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                border: none;
                background: none;
                height: 0px;
                width: 0px;
            }}
            QScrollBar:horizontal {{
                border: none;
                background: {Theme.CARD_LEVEL_1};
                height: 8px;
                margin: 0px 0px 0px 0px;
            }}
            QScrollBar::handle:horizontal {{
                background: {Theme.CARD_LEVEL_3};
                min-width: 20px;
                border-radius: 4px;
            }}
            QScrollBar::handle:horizontal:hover {{
                background: {Theme.HOVER};
            }}
            QScrollBar::handle:horizontal:pressed {{
                background: {Theme.SECONDARY};
            }}
             QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                border: none;
                background: none;
                height: 0px;
                width: 0px;
            }}
            /* --- 滚动条样式结束 --- */
        """)
        # --- 启用交替行颜色 --- 
        self.table_widget.setAlternatingRowColors(True)
        # --- 启用结束 --- 
        # --- 新增：强制刷新样式 --- 
        self.table_widget.style().unpolish(self.table_widget)
        self.table_widget.style().polish(self.table_widget)
        # --- 新增结束 --- 
        layout.addWidget(self.table_widget)

        self.addLayout(layout)
        self.ok_btn = self.addButtons("确定", "取消")
        self.ok_btn.setEnabled(False) # Initially disabled

        # --- 修复：连接确认按钮信号 ---
        self.ok_btn.clicked.connect(self.accept)
        # --- 修复结束 ---

        # --- 修复：添加禁用状态样式 ---
        current_style = self.ok_btn.styleSheet()
        # 从 StyledDialog.addButtons 复制基础样式结构以确保一致性
        confirm_color = Theme.ACCENT
        hover_color = Theme.ACCENT_HOVER
        disabled_bg_color = Theme.DISABLED # 使用主题中的禁用颜色
        disabled_text_color = Theme.TEXT_SECONDARY # 使用主题中的次要文本颜色
        
        new_style = f"""
            QPushButton {{
                background-color: {confirm_color};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 10px 20px;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {confirm_color};
                opacity: 0.8;
            }}
            QPushButton:disabled {{
                background-color: {disabled_bg_color};
                color: {disabled_text_color};
                opacity: 0.6; /* 可选：进一步降低不透明度 */
            }}
        """
        self.ok_btn.setStyleSheet(new_style)
        # --- 修复结束 ---

        self.load_data()

    def load_data(self):
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            # Get column names
            cursor.execute(f"PRAGMA table_info('{self.table_name}')")
            self.columns = [col[1] for col in cursor.fetchall()]
            if not self.columns:
                raise sqlite3.Error(f"无法获取表 '{self.table_name}' 的列信息")

            # Get data (limit rows)
            cursor.execute(f'SELECT * FROM "{self.table_name}" LIMIT 50')
            data = cursor.fetchall()

            self.table_widget.setColumnCount(len(self.columns))
            self.table_widget.setHorizontalHeaderLabels(self.columns)
            self.table_widget.setRowCount(len(data))

            for row_idx, row_data in enumerate(data):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    self.table_widget.setItem(row_idx, col_idx, item)

            # --- 修改：调整列宽策略 --- 
            self.table_widget.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch) # Stretch columns
            # --- 修改结束 ---
            self.setup_mode()
            self.info_label.setText(self.get_info_text())

        except sqlite3.Error as e:
            self.info_label.setText(f"数据库错误：{e}")
            self.info_label.setStyleSheet(f"color: {Theme.ERROR};")
            self.ok_btn.setEnabled(False)
        finally:
            if conn:
                conn.close()

    def get_info_text(self):
        if self.mode == self.Mode.SELECT_COLUMN:
            if self.selected_column:
                return f"已选择 Key 列: {self.selected_column}. 点击确定完成选择"
            else:
                return "请点击<font color='#3498db'><b>单元格</b></font>选择 Key 值"
        elif self.mode == self.Mode.SELECT_ROW_COLUMNS:
            pk_text = f"主键列: <font color='#3498db'><b>{self.selected_pk_col}</b></font>" if self.selected_pk_col else "主键列: <font color='#e74c3c'><b>未选择</b></font>"
            json_text = f"JSON 列: <font color='#3498db'><b>{self.selected_json_col}</b></font>" if self.selected_json_col else "JSON 列: <font color='#e74c3c'><b>未选择</b></font>"
            row_text = f"数据行: <font color='#3498db'><b>已选择</b></font>" if self.selected_row_pk_value is not None else "数据行: <font color='#e74c3c'><b>未选择</b></font>"
            if self.selected_pk_col and self.selected_json_col and self.selected_row_pk_value is not None:
                 return f"{pk_text}, {json_text}, {row_text}. 点击确定"
            else:
                 # --- 修改：优化提示为双击 --- 
                 return f"<b>双击</b>进入主键数据行里进行选择"
                 # --- 修改结束 --- 
        return ""

    def setup_mode(self):
        if self.mode == self.Mode.SELECT_COLUMN:
            # --- 修改：移除无效的 disconnect 调用 --- 
            # try:
            #     self.table_widget.horizontalHeader().sectionClicked.disconnect(self.on_header_clicked_for_key)
            # except (TypeError, RuntimeError):
            #      pass # Signal might not be connected or function doesn't exist anymore
            self.table_widget.itemClicked.connect(self.on_item_clicked_for_key)
            # --- 修改结束 ---
        elif self.mode == self.Mode.SELECT_ROW_COLUMNS:
            # --- 修改：使用双击交互 --- 
            # Remove previous setup for right-click and single-click
            # self.table_widget.horizontalHeader().setContextMenuPolicy(Qt.CustomContextMenu)
            # self.table_widget.horizontalHeader().customContextMenuRequested.disconnect(self.show_header_context_menu) # Disconnect if connected
            # try:
            #     self.table_widget.itemSelectionChanged.disconnect(self.on_row_selected_for_nested)
            # except (TypeError, RuntimeError):
            #     pass
            
            # Connect double click signal
            self.table_widget.itemDoubleClicked.connect(self.on_item_double_clicked_for_nested)
            
            # --- 修改：移除此处无效的 disconnect 调用 ---
            # Ensure header click is also disconnected if previously connected
            # try:
            #      self.table_widget.horizontalHeader().sectionClicked.disconnect(self.on_header_clicked_for_key)
            # except (TypeError, RuntimeError):
            #      pass 
            # --- 修改结束 ---
            # --- 修改结束 --- 

    def on_item_clicked_for_key(self, item):
        if item:
            # --- 修改：获取点击行第一列的值作为 Key --- 
            row = item.row()
            if row >= 0:
                key_item = self.table_widget.item(row, 0)
                if key_item:
                    self.selected_column = key_item.text() # Store the key value from the first column
                    self.ok_btn.setEnabled(True)
                    self.info_label.setText(self.get_info_text()) # Update info label
                else:
                    # Fallback or error handling if the first column item doesn't exist (shouldn't happen)
                    print(f"[WARN] Key item not found in row {row}, column 0")
                    self.selected_column = None # Ensure selection is cleared
                    self.ok_btn.setEnabled(False)
                    self.info_label.setText(self.get_info_text()) # Update info label
            # --- 修改结束 --- 

    # --- 新增：处理双击事件 (用于 SELECT_ROW_COLUMNS 模式) ---
    def on_item_double_clicked_for_nested(self, item):
        if item and self.columns: # Ensure item and columns exist
            row = item.row()
            col = item.column()
            
            if row >= 0 and col >= 0:
                conn = None # Initialize conn outside try
                try:
                    pk_col = self.columns[0] # Assume first column is PK
                    json_col = self.columns[col] # Column double-clicked is JSON column
                    pk_item = self.table_widget.item(row, 0) 
                    if not pk_item:
                        print("[WARN] Could not get PK item at row 0")
                        StyledDialog.showInfoDialog(self, "错误", "无法获取主键单元格信息")
                        return # Stop processing
                        
                    pk_value = pk_item.text()
                    
                    # --- 新增：验证 JSON 有效性 --- 
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    # Use parameters for values, but column/table names are assumed safe from UI
                    sql = f'SELECT "{json_col}" FROM "{self.table_name}" WHERE "{pk_col}" = ?'
                    cursor.execute(sql, (pk_value,))
                    db_row = cursor.fetchone()
                    
                    json_str = None
                    if db_row and db_row[0] is not None:
                        json_str = db_row[0]
                    else:
                        # Handle case where row doesn't exist or JSON cell is NULL
                        StyledDialog.showInfoDialog(self, "无效选择", f"行 '{pk_value}' 的 '{json_col}' 列为空或未找到")
                        return # Stop processing, do not accept

                    # Try parsing
                    json.loads(json_str) # This will raise JSONDecodeError if invalid
                    
                    # --- 如果解析成功，设置值并接受 --- 
                    self.selected_pk_col = pk_col
                    self.selected_json_col = json_col
                    self.selected_row_pk_value = pk_value
                    self.accept() # Accept the dialog *only* if JSON is valid
                    # --- 验证结束 --- 

                except json.JSONDecodeError as e:
                     StyledDialog.showInfoDialog(self, "JSON 解析错误", f"无法将 '{json_col}' 列的内容解析为 JSON\n请选择包含有效 JSON 的单元格\n错误: {e}")
                     # Do not accept
                except sqlite3.Error as e:
                     StyledDialog.showInfoDialog(self, "数据库错误", f"查询 JSON 数据时出错：\n{e}")
                     # Do not accept
                except IndexError:
                     # Handle case where columns list is empty or index out of bounds (e.g., col=0)
                     print("[WARN] Column index out of bounds during double click")
                     StyledDialog.showInfoDialog(self, "错误", "选择的列索引无效")
                     # Do not accept
                except Exception as e: # Catch any other unexpected errors
                    StyledDialog.showInfoDialog(self, "未知错误", f"处理双击选择时发生错误：\n{e}")
                    # Do not accept
                finally:
                    if conn:
                        conn.close()
            # --- 移除：旧的 Accept 调用（已移入 try 块成功路径）--- 
            # try:
            #     # ... (old logic determining pk_col, json_col, pk_value) ...
            #     # All necessary info gathered, accept the dialog (OLD LOGIC, REMOVED)
            #     # self.accept() 
            # except IndexError:
            #      # Handle case where columns list is empty or index out of bounds
            #      print("[WARN] Column index out of bounds") # Or show message
            #      pass

    def getSelectedColumn(self):
        return self.selected_column

    def getSelectedRowColumnsInfo(self):
        # --- 修改：检查选择值是否有效，而不是按钮状态 --- 
        if self.mode == self.Mode.SELECT_ROW_COLUMNS and \
           self.selected_pk_col is not None and \
           self.selected_json_col is not None and \
           self.selected_row_pk_value is not None:
        # --- 修改结束 --- 
            return { # 确保字典格式正确
                "pk_col": self.selected_pk_col,
                "json_col": self.selected_json_col,
                "pk_value": self.selected_row_pk_value
            }
        return None
# --- END DataViewerDialog CLASS ---

# --- BEGIN JsonTreeViewDialog CLASS ---
# --- REVISED JsonTreeViewDialog CLASS for List Navigation ---
class JsonTreeViewDialog(StyledDialog):
    """对话框，使用列表视图显示 JSON 数据并允许选择路径"""
    # --- 修改：接受初始路径前缀 ---
    def __init__(self, json_data, initial_path_prefix=None, parent=None):
        # --- 修改结束 ---
        # --- 修改：设置固定高度为 700 --- 
        super().__init__(parent, "选择嵌套路径", width=1100, height=700)
        # --- 修改结束 ---
        self.root_data = json_data
        self.current_data = json_data
        # --- 修改：初始化路径和前缀 --- 
        self.initial_path_prefix = initial_path_prefix if initial_path_prefix else []
        self.current_path_parts = list(self.initial_path_prefix) # Start with the prefix
        # --- 修改结束 ---
        self.selected_path = "" # Final confirmed path

        main_layout = QVBoxLayout()
        
        # --- 修改：添加返回按钮和路径标签的水平布局 ---
        top_bar_layout = QHBoxLayout()
        self.back_btn = QPushButton("⬅️ 返回")
        self.back_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.back_btn.setStyleSheet(f"""
            QPushButton {{ 
                background-color: {Theme.CARD_LEVEL_1}; color: {Theme.TEXT_PRIMARY}; 
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL}; padding: 5px 10px; 
                font-weight: bold; min-width: 60px;
            }}
            QPushButton:hover {{ border: 1px solid {Theme.ACCENT}; color: {Theme.ACCENT}; }}
            QPushButton:disabled {{ background-color: {Theme.DISABLED}; color: {Theme.TEXT_SECONDARY}; border-color: {Theme.DISABLED}; }}
        """)
        self.back_btn.clicked.connect(self._handle_back)
        self.back_btn.setEnabled(False) # Initially at root
        
        # --- 修改：创建路径和提示的垂直布局 --- 
        path_hint_layout = QVBoxLayout()
        path_hint_layout.setSpacing(2) # 设置小间距
        path_hint_layout.setContentsMargins(0,0,0,0) # 无外边距
        
        self.path_label = QLabel("当前路径：/")
        self.path_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; padding-left: 10px;")
        self.path_label.setWordWrap(True)
        path_hint_layout.addWidget(self.path_label) # 添加到垂直布局

        # --- 修改：移动提示标签的创建和添加 --- 
        self.selection_hint_label = QLabel("双击导航到目标值或层级，然后点击 '确定' 按钮确认选择")
        # 修改样式，移除padding，靠左对齐可能更好
        self.selection_hint_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL}; padding-left: 10px;") 
        self.selection_hint_label.setAlignment(Qt.AlignLeft)
        path_hint_layout.addWidget(self.selection_hint_label) # 添加到垂直布局
        # --- 修改结束 --- 
        
        top_bar_layout.addWidget(self.back_btn)
        # top_bar_layout.addWidget(self.path_label, 1) # 移除旧的 path_label 添加
        top_bar_layout.addLayout(path_hint_layout, 1) # 添加包含路径和提示的垂直布局，并允许拉伸
        main_layout.addLayout(top_bar_layout)
        # --- 修改结束 ---

        # --- 修改：使用 QTableWidget 代替 QListWidget ---
        # self.list_widget = QListWidget()
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(2)
        self.table_widget.setHorizontalHeaderLabels(["Key/Index", "Value Preview"]) 
        self.table_widget.verticalHeader().setVisible(False) # Hide row numbers
        self.table_widget.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table_widget.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table_widget.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.table_widget.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Interactive) # Key column interactive
        self.table_widget.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch) # Value stretches
        self.table_widget.setStyleSheet(f"""
            QTableWidget {{
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                gridline-color: {Theme.BORDER};
                outline: none;
                alternate-background-color: {Theme.CARD_LEVEL_2};
            }}
            QHeaderView::section {{
                background-color: {Theme.CARD_LEVEL_3};
                color: {Theme.TEXT_SECONDARY};
                padding: 6px 5px;
                border: none;
                border-bottom: 1px solid {Theme.BORDER};
                font-weight: bold;
                text-align: left;
            }}
            QHeaderView::section:hover {{
                background-color: {Theme.HOVER};
                color: {Theme.TEXT_PRIMARY};
            }}
            QTableWidget::item {{
                padding: 8px 10px;
                border-bottom: 1px solid {Theme.BORDER};
                border-right: 1px solid {Theme.BORDER};
            }}
            QTableWidget::item:selected {{
                background-color: {Theme.ACCENT_HOVER};
                color: white;
            }}
            QTableWidget::item:hover {{
                background-color: {Theme.HOVER};
            }}
        """)
        # --- 启用交替行颜色 --- 
        self.table_widget.setAlternatingRowColors(True)
        # --- 启用结束 --- 
        # --- 新增：强制刷新样式 --- 
        self.table_widget.style().unpolish(self.table_widget)
        self.table_widget.style().polish(self.table_widget)
        # --- 新增结束 --- 
        main_layout.addWidget(self.table_widget)
        # --- 修改结束 ---

        # --- 新增：添加选择提示 --- 
        # self.selection_hint_label = QLabel("双击导航到目标值或层级，然后点击 '确定' 按钮确认选择")
        # self.selection_hint_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL}; padding: 5px 0px;")
        # self.selection_hint_label.setAlignment(Qt.AlignCenter)
        # main_layout.addWidget(self.selection_hint_label)
        # --- 新增结束 --- 

        # --- 修改：连接表格双击 --- 
        self.table_widget.itemDoubleClicked.connect(self._handle_double_click)
        # --- 修改结束 ---

        self.addLayout(main_layout)
        
        # --- 修改：确保按钮样式和连接正确 ---
        self.ok_btn = self.addButtons("确定", "取消")
        self.ok_btn.clicked.connect(self.accept)
        # Add disabled style to OK button as well
        confirm_color = Theme.ACCENT
        hover_color = Theme.ACCENT_HOVER
        disabled_bg_color = Theme.DISABLED
        disabled_text_color = Theme.TEXT_SECONDARY
        ok_style = f"""
            QPushButton {{ background-color: {confirm_color}; color: white; border: none; border-radius: {Theme.BORDER_RADIUS_SMALL}; padding: 10px 20px; font-weight: bold; min-width: 80px; }}
            QPushButton:hover {{ background-color: {hover_color}; }}
            QPushButton:pressed {{ background-color: {confirm_color}; opacity: 0.8; }}
            QPushButton:disabled {{ background-color: {disabled_bg_color}; color: {disabled_text_color}; opacity: 0.6; }}
        """
        self.ok_btn.setStyleSheet(ok_style)
        self.ok_btn.setEnabled(False) # Initially disabled at root
        # --- 修改结束 ---
        
        self._update_view() # Initial population

    def _update_view(self):
        """更新列表视图和路径标签"""
        # --- 修改：更新表格视图 --- 
        self.table_widget.setRowCount(0) # Clear table rows
        
        # Update path label (No change needed here)
        current_path_str = "/" + "/".join(map(str, self.current_path_parts))
        self.path_label.setText(f"当前路径: <font color='{Theme.ACCENT}'><b>{current_path_str}</b></font>")

        row_count = 0
        # Populate list based on current data type
        if isinstance(self.current_data, dict):
            keys = sorted(self.current_data.keys(), key=str) # Sort keys for consistent order
            self.table_widget.setRowCount(len(keys))
            for row, key in enumerate(keys):
                key_item = QTableWidgetItem(str(key))
                key_item.setData(Qt.UserRole, key) # Store actual key
                self.table_widget.setItem(row, 0, key_item)
                
                value = self.current_data.get(key)
                # --- 修改：增强值预览 --- 
                if isinstance(value, dict):
                    # value_preview = "{...}" # Dictionary indicator
                    keys_preview = list(value.keys())[:2] # Get first 2 keys
                    value_preview = f"{{ { ', '.join(map(str, keys_preview)) }{ '...' if len(value.keys()) > 2 else '' } }}"
                elif isinstance(value, list):
                    # value_preview = "[...]" # List indicator
                    value_preview = f"[len={len(value)}]"
                # --- 修改结束 ---
                else:
                    value_preview = str(value)[:100] + ('...' if len(str(value)) > 100 else '') # Primitive preview
                    key_item.setForeground(QColor(Theme.TEXT_SECONDARY)) # Dim primitive keys
                    
                value_item = QTableWidgetItem(value_preview)
                if not isinstance(value, (dict, list)):
                     value_item.setForeground(QColor(Theme.TEXT_SECONDARY))
                self.table_widget.setItem(row, 1, value_item)
                row_count += 1
                
        elif isinstance(self.current_data, list):
            self.table_widget.setRowCount(len(self.current_data))
            for index in range(len(self.current_data)):
                index_str = f"[{index}]"
                index_item = QTableWidgetItem(index_str)
                index_item.setData(Qt.UserRole, index) # Store actual index
                self.table_widget.setItem(index, 0, index_item)
                
                value = self.current_data[index]
                # --- 修改：增强值预览 --- 
                if isinstance(value, dict):
                    # value_preview = "{...}"
                    keys_preview = list(value.keys())[:2] # Get first 2 keys
                    value_preview = f"{{ { ', '.join(map(str, keys_preview)) }{ '...' if len(value.keys()) > 2 else '' } }}"
                elif isinstance(value, list):
                    # value_preview = "[...]"
                    value_preview = f"[len={len(value)}]"
                # --- 修改结束 ---
                else:
                    value_preview = str(value)[:100] + ('...' if len(str(value)) > 100 else '')
                    index_item.setForeground(QColor(Theme.TEXT_SECONDARY))
                    
                value_item = QTableWidgetItem(value_preview)
                if not isinstance(value, (dict, list)):
                     value_item.setForeground(QColor(Theme.TEXT_SECONDARY))
                self.table_widget.setItem(index, 1, value_item)
                row_count += 1
                
        else:
            # Current data is a primitive, show it
            self.table_widget.setRowCount(1)
            key_item = QTableWidgetItem("(Value)")
            key_item.setData(Qt.UserRole, None)
            key_item.setForeground(QColor(Theme.TEXT_DISABLED)) # 使用更暗的颜色
            self.table_widget.setItem(0, 0, key_item)
            value_item = QTableWidgetItem(str(self.current_data))
            value_item.setForeground(QColor(Theme.TEXT_DISABLED)) # 使用更暗的颜色
            self.table_widget.setItem(0, 1, value_item)
            row_count = 1

        self.table_widget.resizeColumnToContents(0) # Adjust key column width
            
        # --- 移除：调试语句 --- 
        # print(f"[DEBUG] Path Update: Current Path = {self.current_path_parts} (len={len(self.current_path_parts)}), Initial Prefix = {self.initial_path_prefix} (len={len(self.initial_path_prefix)})")
        # --- 移除结束 --- 
            
        # Update button states (based on prefix)
        # --- 修改：返回按钮在有前缀时始终可用 ---
        self.back_btn.setEnabled(bool(self.initial_path_prefix)) 
        # --- 修改结束 ---
        # --- 修改：允许在初始前缀层级确认 ---
        self.ok_btn.setEnabled(len(self.current_path_parts) >= len(self.initial_path_prefix)) # Allow confirming path at any level including prefix start
        # --- 修改结束 ---
        # --- 强制刷新按钮样式以确保视觉更新 ---
        self.back_btn.style().unpolish(self.back_btn)
        self.back_btn.style().polish(self.back_btn)
        self.ok_btn.style().unpolish(self.ok_btn)
        self.ok_btn.style().polish(self.ok_btn)
        # --- 强制刷新结束 ---
        # --- 修改结束 ---

    def _handle_double_click(self, item):
        """处理列表项双击事件，用于向下导航"""
        # --- 修改：适配表格点击 --- 
        row = item.row()
        if row < 0: return
        key_item = self.table_widget.item(row, 0) # Key/Index is in the first column
        if not key_item: return
        
        key_or_index = key_item.data(Qt.UserRole)
        # --- 修改结束 ---
        if key_or_index is None: # Clicked on a primitive value display or invalid item
            return
            
        next_data = None
        try:
            if isinstance(self.current_data, dict):
                next_data = self.current_data[key_or_index]
            elif isinstance(self.current_data, list):
                next_data = self.current_data[key_or_index]
        except (KeyError, IndexError):
             print(f"[Error] Failed to access data at {key_or_index}")
             return # Error accessing data
             
        # Navigate deeper only if the next level is a dict or list
        if isinstance(next_data, (dict, list)):
            self.current_path_parts.append(key_or_index)
            self.current_data = next_data
            self._update_view()
        else:
            # --- 修改：允许导航到原始值 --- 
            self.current_path_parts.append(key_or_index)
            self.current_data = next_data
            self._update_view() # Update view to show primitive
            # --- 修改结束 ---
             # Double-clicked on a primitive value's parent key/index
             # --- 移除：调试信息 ---

    def _handle_back(self):
        """处理返回按钮点击事件，用于向上导航或取消"""
        # --- 修改：如果在初始前缀层级，则取消对话框 --- 
        if len(self.current_path_parts) <= len(self.initial_path_prefix):
            self.reject() # Act like Cancel when at the initial level
            return
        # --- 修改结束 ---
            
        self.current_path_parts.pop() # Remove last part of the path
        
        # Reset current_data to root and navigate back down
        self.current_data = self.root_data
        try:
            # --- 修改：只使用前缀之后的路径部分来导航 root_data ---
            relative_path_parts = self.current_path_parts[len(self.initial_path_prefix):]
            for part in relative_path_parts:
            # --- 修改结束 ---
                if isinstance(self.current_data, dict):
                    self.current_data = self.current_data[part]
                elif isinstance(self.current_data, list):
                    # --- 修复：将路径部分转换为整数用于列表索引 ---
                    try:
                        index = int(part)
                        self.current_data = self.current_data[index]
                    except ValueError:
                        raise ValueError(f"Invalid list index format in path: '{part}'")
                    # --- 修复结束 ---
                else:
                    raise ValueError("Invalid path segment encountered during back navigation")
        except (KeyError, IndexError, ValueError) as e:
             print(f"[Error] Error navigating back: {e}")
             # --- 修复：重置到初始前缀，而非空列表 ---
             # Reset to root if back navigation fails
             self.current_path_parts = list(self.initial_path_prefix)
             # --- 修复：修正缩进 --- 
             # self.current_path_parts = [] 
             # --- 修复结束 ---
             self.current_data = self.root_data
             
        self._update_view()

    # --- 修改：获取当前确认的路径 ---
    def getSelectedPath(self):
        # Return the path built when OK is clicked, relative to the prefix
        # --- 修改：使用 :: 作为分隔符 --- 
        return "::".join(map(str, self.current_path_parts[len(self.initial_path_prefix):]))
        # --- 修改结束 --- 
    # --- 修改结束 ---

# --- END JsonTreeViewDialog CLASS ---
# --- END REVISED CLASS ---

class AdvancedBackupPage(QWidget):
    """
    高级自定义备份/恢复页面（重构版）
    只保留左侧方案列表（大卡片盒子包裹），所有方案详情、备份项管理、执行、日志等全部在弹窗内完成
    """
    back_requested = Signal()  # 返回信号
    # 类型英文到中文映射
    item_type_map = {
        'folder': '文件夹',
        'file': '文件',
        'db_table': '数据库表',
        'db_key': '表字段',
        'db_nested_key': '嵌套字段',
    }

    def __init__(self, parent=None):
        super().__init__(parent)
        app_data_dir = get_app_data_dir("YCursor")
        base_backup_dir = os.path.join(app_data_dir, "backups", "Advanced_BackupRestore_Hub")
        self.manager = AdvancedBackupManager(base_backup_dir)
        self.placeholder_widget = None # Add placeholder widget attribute
        self.current_worker = None # Add worker attribute
        self.init_ui()

    def init_ui(self):
        self.setStyleSheet("background: transparent;")
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        card = StyledFrame(has_glass_effect=True)
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(30, 20, 30, 40) # Changed left/right margins to 30
        card_layout.setSpacing(30)

        top_bar = QHBoxLayout()
        self.back_btn = QPushButton("⬅️ 返回")
        self.back_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        # self.back_btn.setFixedHeight(36) # Removed fixed height
        self.back_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent; 
                color: {Theme.TEXT_SECONDARY};
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px; /* Unified padding */
                font-size: {Theme.FONT_SIZE_NORMAL};
                font-weight: bold;
                text-align: left;
            }}
            QPushButton:hover {{
                background-color: {Theme.HOVER};
                color: {Theme.ACCENT};
            }}
            QPushButton:pressed {{
                background-color: {Theme.SECONDARY};
            }}
        """)
        self.back_btn.clicked.connect(self.back_requested.emit)
        self.add_btn = QPushButton("新建方案") # 修改: 移除图标
        # self.add_btn.setFixedHeight(36) # 删除: 移除固定高度
        # self.add_btn.setMinimumWidth(110) # 删除: 移除最小宽度
        self.add_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.add_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent; /* Changed to transparent */
                color: white; /* Changed back to white */
                border: none; /* 添加 border: none */
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                font-weight: bold;
                padding: 8px 15px; /* 修改: 统一 padding */
                font-size: {Theme.FONT_SIZE_NORMAL}; /* 添加: 统一字体大小 */
            }}
            QPushButton:hover {{
                background-color: {Theme.HOVER}; /* Matched back_btn hover */
                color: white; /* Ensure hover text color is white */
            }}
            QPushButton:pressed {{
                background-color: {Theme.SECONDARY}; /* Matched back_btn pressed */
            }}
            QPushButton:disabled {{
                background-color: {Theme.DISABLED};
                color: {Theme.TEXT_SECONDARY};
            }}
        """)
        self.add_btn.clicked.connect(self.on_add_scheme)
        
        top_bar.addWidget(self.back_btn, 0, Qt.AlignmentFlag.AlignLeft)
        top_bar.addStretch(1)
        top_bar.addWidget(self.add_btn)

        card_layout.addLayout(top_bar)
        
        self.scroll = QScrollArea()
        self.scroll.setWidgetResizable(True)
        self.scroll.setStyleSheet("""
            QScrollArea {
                background: transparent; 
                border: none;
            }
            QScrollBar:vertical {
                border: none;
                background: transparent;
                width: 0px;
                margin: 0px 0 0px 0;
            }
            QScrollBar::handle:vertical {
                background: transparent;
                min-height: 0px;
            }
            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {
                border: none;
                background: none;
                height: 0px;
                width: 0px;
            }
            QScrollBar::up-arrow:vertical,
            QScrollBar::down-arrow:vertical {
                background: none;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """)
        
        self.list_container = QWidget()
        self.list_layout = QVBoxLayout(self.list_container)
        self.list_layout.setContentsMargins(0, 0, 0, 0)
        self.list_layout.setSpacing(8)
        self.list_layout.setAlignment(Qt.AlignTop)
        self.list_container.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Maximum)
        self.scroll.setWidget(self.list_container)
        card_layout.addWidget(self.scroll, 1)

        self.placeholder_widget = QWidget()
        self.placeholder_widget.setStyleSheet("background: transparent;")
        placeholder_layout = QVBoxLayout(self.placeholder_widget)
        placeholder_layout.setAlignment(Qt.AlignCenter)
        placeholder_layout.setSpacing(20)

        icon_label = QLabel("📂")
        icon_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: 56px;")
        icon_label.setAlignment(Qt.AlignCenter)

        text_label = QLabel("✨ 还没有备份方案，点击右上角 '新建方案' 创建一个吧！ ✨")
        text_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_TITLE};")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        placeholder_layout.addWidget(icon_label)
        placeholder_layout.addWidget(text_label)

        self.placeholder_widget.hide()
        card_layout.addWidget(self.placeholder_widget, 1, Qt.AlignCenter)

        main_layout.addWidget(card, 1)
        self.refresh_scheme_list()

    def refresh_scheme_list(self):
        # 清空旧卡片 (Always clear the layout first)
        for i in reversed(range(self.list_layout.count())):
            widget = self.list_layout.itemAt(i).widget()
            if widget:
                widget.deleteLater()
                
        # 获取并排序方案
        schemes = sorted(self.manager.list_schemes(), key=lambda s: s.name)

        if not schemes:
            # 列表为空：显示占位符，隐藏滚动区
            self.scroll.hide()
            self.placeholder_widget.show()
        else:
            # 列表不为空：隐藏占位符，显示滚动区，添加卡片
            self.placeholder_widget.hide()
            self.scroll.show()
            for scheme in schemes:
                card = BackupSchemeCard(
                    scheme,
                    base_dir=self.manager.base_dir,
                    on_detail=self._show_scheme_dialog_and_refresh, # 修改: 连接到包装方法
                    on_delete=self.on_del_scheme_card,
                    on_backup=self.on_backup_scheme_card,
                    on_restore=self.on_exec_scheme_card
                )
                self.list_layout.addWidget(card)
                
    # 新增：showEvent 方法，在页面显示时刷新列表
    def showEvent(self, event: QShowEvent): # 添加类型提示
        """页面显示时强制刷新列表"""
        super().showEvent(event) # 调用父类的 showEvent
        print("[DEBUG] AdvancedBackupPage showEvent triggered. Refreshing list...") # 添加调试打印
        self.refresh_scheme_list() # 调用列表刷新方法

    # 新增：包装方法，用于显示对话框并在关闭后刷新列表
    def _show_scheme_dialog_and_refresh(self, scheme):
        """显示方案详情对话框，并在其关闭后刷新主列表"""
        self.show_scheme_dialog(scheme) # 显示对话框并等待其关闭
        # 对话框关闭后执行刷新
        print("[DEBUG] Scheme dialog closed. Refreshing main list...")
        self.refresh_scheme_list()

    def on_add_scheme(self):
        name, ok = self.simple_input_dialog("新建方案", "请输入方案名称：")
        if ok and name:
            scheme = BackupScheme(name)
            self.manager.save_scheme(scheme)
            log_info(f"成功创建备份方案: {scheme.name}") # 添加日志
            self.refresh_scheme_list() # 添加后刷新列表 (保持不变)

    def on_del_scheme_card(self, scheme):
        # 手动创建确认对话框，统一调用方式
        dialog = StyledDialog(self.window(), "确认删除")
        
        message_frame = QFrame()
        message_frame.setObjectName("messageFrame")
        message_frame.setStyleSheet(f"""
            #messageFrame {{
                background-color: #121317;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        message_frame_layout = QVBoxLayout(message_frame)
        message_frame_layout.setContentsMargins(20, 20, 20, 20)
        message_label = QLabel(f"确定要删除方案：{scheme.name}？")
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: {Theme.FONT_SIZE_NORMAL}; background-color: transparent; padding: 0px;")
        message_frame_layout.addWidget(message_label)
        dialog.addWidget(message_frame)
        confirm_btn = dialog.addButtons("确认", "取消")
        confirm_btn.clicked.connect(dialog.accept)

        # ok = StyledDialog.showConfirmDialog(self.window(), "确认删除", f"确定要删除方案：{scheme.name}？")
        if dialog.exec() == QDialog.Accepted:
            log_info(f"请求删除备份方案: {scheme.name}") # 添加删除请求日志
            success, message = self.manager.delete_scheme(scheme.name) # 获取结果
            if success:
                log_info(f"成功删除备份方案: {scheme.name}") # 记录成功
                self.window().show_toast(f"方案 '{scheme.name}' 删除成功", error=False) # 添加 Toast
            else:
                log_error(f"删除备份方案 '{scheme.name}' 失败: {message}") # 记录失败
                self.window().show_toast(f"方案 '{scheme.name}' 删除失败: {message}", error=True) # 添加 Toast
            self.refresh_scheme_list() # 删除后刷新列表 (保持不变)

    def on_backup_scheme_card(self, scheme, direct=False):
        if direct:
            self._do_backup_or_restore(scheme, mode='backup')
        else:
            # 修改：调用包装方法
            self._show_scheme_dialog_and_refresh(scheme)

    def on_exec_scheme_card(self, scheme, direct=False):
        if direct:
            self._do_backup_or_restore(scheme, mode='restore')
        else:
            # 修改：调用包装方法
            self._show_scheme_dialog_and_refresh(scheme)

    def _do_backup_or_restore(self, scheme, mode):
        from core.cursor_auto.advanced_backup_worker import AdvancedBackupWorker
        from PySide6.QtCore import QTimer
        # Remove unused imports for dialog UI
        # from PySide6.QtWidgets import QTextEdit, QVBoxLayout, QLabel
        # from PySide6.QtGui import QColor, QTextCursor

        # Prevent starting a new operation if one is already running
        if self.current_worker and self.current_worker.isRunning():
            log_warning(f"无法启动新的 {'备份' if mode=='backup' else '恢复'} 操作，因为上一个操作仍在进行中")
            self.window().show_toast(f"无法启动操作：上一个任务仍在运行", error=True)
            return

        steps = self.manager.generate_steps(scheme, mode=mode)
        if not steps:
            StyledDialog.warning(self.window(), "操作无效", f"方案 '{scheme.name}' 中没有有效的{'备份' if mode == 'backup' else '恢复'}步骤")
            return
            
        scheme_dir = self.manager._get_scheme_dir(scheme.name)
        worker = AdvancedBackupWorker(steps, mode=mode, scheme_dir=scheme_dir)
        
        # Find the card to update its state later
        card_to_update = None
        for i in range(self.list_layout.count()):
            widget = self.list_layout.itemAt(i).widget()
            if isinstance(widget, BackupSchemeCard) and widget.scheme == scheme:
                card_to_update = widget
                break
                
        # --- Dialog creation and related UI code removed --- 
        # Progress dialog, status label, log display, on_progress, on_status, on_log_received are removed.

        # Define only the finished handler
        def on_finished(success):
            # --- Removed status_label updates ---

            # Call Toast notification
            if success:
                self.window().show_toast(f"方案 '{scheme.name}' {'备份' if mode=='backup' else '恢复'} 成功！", error=False)
            else:
                 self.window().show_toast(f"方案 '{scheme.name}' {'备份' if mode=='backup' else '恢复'} 失败，请检查日志", error=True)

            # Update button states on card if backup was successful
            if success and mode == 'backup' and card_to_update:
                card_to_update.update_button_states()
            
            # Clear the worker reference
            self.current_worker = None 
            
        # Connect only the finished signal
        worker.finished_signal.connect(on_finished)

        # --- Removed connections for progress_signal, status_signal, log_signal --- 
        
        # --- Removed initial and final log messages for dialog ---
        
        # Store worker reference and start it
        self.current_worker = worker
        log_info(f"后台开始执行 {scheme.name} - {'备份' if mode=='backup' else '恢复'}") # Log start to central log
        self.current_worker.start()
        
        # Show an initial toast indicating the start
        self.window().show_toast(f"开始执行方案 '{scheme.name}' {'备份' if mode=='backup' else '恢复'}...", error=False)

        # --- progress_dialog.exec() removed --- 

    def simple_input_dialog(self, title, prompt):
        from PySide6.QtWidgets import QVBoxLayout, QLabel, QLineEdit
        import re
        parent = self.window() # Ensure parent is main window for global modal
        dialog = StyledDialog(parent, title, width=400)
        layout = QVBoxLayout()
        layout.setSpacing(15)

        # 添加图标
        icon_label = QLabel("📝") # Edit/Note icon
        icon_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: 42px;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # 提示标签
        label = QLabel(prompt)
        label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: {Theme.FONT_SIZE_NORMAL};")
        layout.addWidget(label)

        # 输入框
        input_edit = QLineEdit()
        input_edit.setPlaceholderText("输入方案名称 (最多13字符)") # Add placeholder
        input_edit.setStyleSheet(f"background-color: {Theme.CARD_LEVEL_2}; color: {Theme.TEXT_PRIMARY}; border-radius: {Theme.BORDER_RADIUS_SMALL}; padding: 8px 12px;")
        layout.addWidget(input_edit)

        # 错误标签 (添加到布局，初始隐藏)
        error_label = QLabel("")
        error_label.setStyleSheet("color: #ff4d4f; font-size: 13px; margin-top: 2px; margin-bottom: 5px;")
        error_label.hide()
        layout.addWidget(error_label) # <--- Add here permanently

        dialog.addLayout(layout)
        # 使用StyledDialog的addButtons方法（放在内容之后）
        ok_btn = dialog.addButtons("确认", "取消")
        
        def validate():
            name = input_edit.text().strip()
            exists = [s.name.strip().lower() for s in self.manager.list_schemes()]
            error_label.setText("")
            error_label.hide()
            show_error = False
            if not name:
                error_label.setText("方案名不能为空")
                show_error = True
            elif name.lower() in exists:
                error_label.setText("方案名已存在，请更换名称")
                show_error = True
            elif len(name) > 13:
                error_label.setText("方案名过长，最多13个字符")
                show_error = True
            elif name in ['.', '..'] or name.startswith('.') or name.endswith('.'):
                error_label.setText("方案名不能为点、点点或以点开头/结尾")
                show_error = True
            elif re.search(r'[\\/:*?"<>|]', name):
                error_label.setText("方案名不能包含以下字符：\\ / : * ? \" < > |")
                show_error = True
            elif name != input_edit.text():
                error_label.setText("方案名不能有首尾空格")
                show_error = True
            if show_error:
                error_label.show()
                return False
            return True
        def on_ok():
            if validate():
                dialog.accept()
        ok_btn.clicked.connect(on_ok)
        if dialog.exec() == QDialog.Accepted:
            return input_edit.text().strip(), True
        return "", False

    def show_scheme_dialog(self, scheme, exec_mode=False, backup_mode=False, log_mode=False, parent_dialog=None):
        from PySide6.QtWidgets import QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView, QDialog
        parent = parent_dialog or self.window()
        dialog = StyledDialog(parent, f"方案详情 - {scheme.name}", width=700)
        layout = QVBoxLayout()
        # 标题区：重命名按钮 + 标题 + 拉伸 + 打开目录按钮 + 新增备份项按钮
        title_bar = QHBoxLayout()
        title_bar.setSpacing(10) # 增加按钮间距
        title = QLabel(f"方案：{scheme.name}") 
        title.setStyleSheet(f"font-size: {Theme.FONT_SIZE_TITLE}; font-weight: bold; color: {Theme.ACCENT}; background: transparent;")
        rename_btn = QPushButton("✏️")
        rename_btn.setFixedSize(32, 32)
        rename_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        rename_btn.setToolTip("重命名方案")
        rename_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                border: none;
                color: {Theme.TEXT_SECONDARY};
                font-size: 18px;
                padding: 0px;
            }}
            QPushButton:hover {{ 
                color: {Theme.ACCENT};
                background-color: {Theme.HOVER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        rename_btn.clicked.connect(lambda checked=False, d=dialog: self.on_rename_scheme_card(scheme, d))
        
        # 创建 打开目录 按钮
        open_dir_btn = QPushButton("📂 打开目录")
        open_dir_btn.setFixedHeight(32)
        open_dir_btn.setMinimumWidth(110)
        open_dir_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        open_dir_btn.setToolTip("打开此方案的备份文件目录")
        open_dir_btn.setStyleSheet(f"background-color: {Theme.CARD_LEVEL_1}; color: {Theme.TEXT_PRIMARY}; border-radius: {Theme.BORDER_RADIUS_SMALL}; font-weight: bold; padding: 4px 10px;")
        
        # 创建 快捷配置 按钮
        quick_config_btn = QPushButton("⚡ 快捷配置")
        quick_config_btn.setFixedHeight(32)
        quick_config_btn.setMinimumWidth(110)
        quick_config_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        quick_config_btn.setToolTip("从其他方案快速复制备份配置项")
        quick_config_btn.setStyleSheet(f"background-color: {Theme.CARD_LEVEL_1}; color: {Theme.TEXT_PRIMARY}; border-radius: {Theme.BORDER_RADIUS_SMALL}; font-weight: bold; padding: 4px 10px;")
        quick_config_btn.clicked.connect(lambda: self.show_quick_config_dialog(scheme, table, dialog))
        
        # 创建 新增备份项 按钮
        add_item_btn = QPushButton("➕ 新增备份项")
        add_item_btn.setFixedHeight(32)
        add_item_btn.setMinimumWidth(110)
        add_item_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        add_item_btn.setStyleSheet(f"background-color: {Theme.CARD_LEVEL_1}; color: {Theme.TEXT_PRIMARY}; border-radius: {Theme.BORDER_RADIUS_SMALL}; font-weight: bold; padding: 4px 10px;")
        
        # 实现打开目录功能
        def open_backup_dir():
            scheme_dir = self.manager._get_scheme_dir(scheme.name)
            if os.path.isdir(scheme_dir):
                system = platform.system().lower()
                try:
                    if system == "windows":
                        os.startfile(scheme_dir)
                    elif system == "darwin": # macOS
                        subprocess.run(['open', scheme_dir], check=True)
                    else: # Linux and other Unix-like
                        subprocess.run(['xdg-open', scheme_dir], check=True)
                except (FileNotFoundError, PermissionError, subprocess.CalledProcessError, Exception) as e:
                    StyledDialog.showInfoDialog(dialog, "打开失败", f"无法打开目录 '{scheme_dir}':\n{e}")
            else:
                 StyledDialog.showInfoDialog(dialog, "提示", "备份目录尚不存在，请先执行一次备份")
                 
        open_dir_btn.clicked.connect(open_backup_dir)
        
        title_bar.addWidget(rename_btn)
        title_bar.addWidget(title)
        title_bar.addStretch(1)
        title_bar.addWidget(open_dir_btn) # 添加打开目录按钮
        title_bar.addWidget(quick_config_btn) # 添加快捷配置按钮
        title_bar.addWidget(add_item_btn) # 添加新增按钮
        layout.addLayout(title_bar)
        
        # 表格设置
        table = QTableWidget(len(scheme.items), 4)
        table.setHorizontalHeaderLabels(["名称", "类型", "恢复时需关闭重启", "已有备份"])
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.verticalHeader().setVisible(False)
        self._refresh_scheme_items_table(table, scheme)
        layout.addWidget(table)
        
        dialog.addLayout(layout)
        
        # 新增/编辑逻辑 (add_item 函数需要连接 add_item_btn)
        def add_item():
            data, ok, _ = self.item_edit_dialog(parent_dialog=dialog)
            if ok:
                new_item = BackupItem(**data) # 创建实例以获取信息
                scheme.items.append(new_item)
                self.manager.save_scheme(scheme)
                item_desc = new_item.name or self._gen_item_full_desc(new_item) # 获取描述
                log_info(f"向方案 '{scheme.name}' 添加备份项: {item_desc}") # 添加日志
                self._refresh_scheme_items_table(table, scheme)
                
        add_item_btn.clicked.connect(add_item)
        
        # 双击行编辑逻辑
        def on_table_double_click(row, col):
            if row < 0 or row >= len(scheme.items):
                return
            old_item = scheme.items[row]
            data, ok, deleted = self.item_edit_dialog(old_item, parent_dialog=dialog, allow_delete=True)
            if deleted:
                deleted_item_desc = old_item.name or self._gen_item_full_desc(old_item) # 获取旧项描述
                scheme.items.pop(row)
                self.manager.save_scheme(scheme) # 先保存配置更改
                # 删除关联备份文件 (这里可以考虑是否添加更详细日志)
                self._delete_item_backups(scheme.name, old_item.get_backup_filename())
                log_info(f"成功从方案 '{scheme.name}' 删除备份项: {deleted_item_desc}") # 添加日志
                self._refresh_scheme_items_table(table, scheme)
            elif ok: # 编辑路径
                edited_item = BackupItem(**data) # 创建实例
                scheme.items[row] = edited_item
                self.manager.save_scheme(scheme)
                item_desc = edited_item.name or self._gen_item_full_desc(edited_item) # 获取描述
                log_info(f"成功编辑方案 '{scheme.name}' 中的备份项: {item_desc}") # 添加日志
                self._refresh_scheme_items_table(table, scheme)
                
        table.cellDoubleClicked.connect(on_table_double_click)
        
        dialog.exec() # exec() 仍然在这里，包装函数会在它返回后刷新主列表

    def _show_scheme_log(self, scheme, parent_dialog):
        # 实际的日志显示逻辑
        from core.cursor_auto.advanced_backup_worker import AdvancedBackupWorker
        scheme_dir = self.manager._get_scheme_dir(scheme.name)
        # 查找最新日志（这里只做演示，实际可扩展为读取日志文件）
        logs = []
        # 这里可扩展为读取worker日志
        text = "\n".join([f"[{l['level']}] {l['message']}" for l in logs]) or "暂无日志"
        StyledDialog.showInfoDialog(parent_dialog, "日志", text)

    def show_quick_config_dialog(self, current_scheme, current_table, parent_dialog):
        """
        显示快捷配置对话框，用于从其他方案复制备份项配置
        
        Args:
            current_scheme: 当前方案对象
            current_table: 当前方案的备份项表格控件
            parent_dialog: 父对话框
        """
        from PySide6.QtWidgets import (QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                                      QTreeWidget, QTreeWidgetItem, QCheckBox, QFrame)
        
        # 使用全局日志函数记录
        log_info(f"打开快捷配置对话框，当前方案: {current_scheme.name}, 有 {len(current_scheme.items)} 个备份项")
        for idx, item in enumerate(current_scheme.items):
            log_info(f"当前方案项目 #{idx+1}: 类型={item.item_type}, 路径={item.path}")
        
        # 获取所有方案（排除当前方案）
        all_schemes = [s for s in self.manager.list_schemes() if s.name != current_scheme.name]
        if not all_schemes:
            StyledDialog.showInfoDialog(parent_dialog, "提示", "没有其他可用方案可供复制")
            return
        
        # 创建一个辅助函数，用于检查备份项是否已存在于当前方案中
        def is_duplicate_item(item):
            """检查备份项是否已存在于当前方案中（路径、类型一样）"""
            # 直接使用日志函数确保输出
            log_info(f"检查是否重复: 类型={item.item_type}, 路径={item.path}")
            
            # 超级简单的比较逻辑：只比较路径
            for idx, existing_item in enumerate(current_scheme.items):
                log_info(f"  与现有项 #{idx+1} 比较: 类型={existing_item.item_type}, 路径={existing_item.path}")
                
                # 最简单的路径比较
                if existing_item.path == item.path:
                    log_info(f"  >>> 检测到重复项! 路径匹配: {item.path}")
                    return True
            
            log_info(f"  没有重复项，路径={item.path}")
            return False
            
        # 创建一个辅助函数，生成唯一的名称（处理重复名称）
        def generate_unique_name(base_name):
            """生成唯一名称，如果有重复则添加数字后缀"""
            # 获取当前方案中所有项目的名称
            existing_names = [item.name for item in current_scheme.items if item.name]
            
            # 如果基础名称不存在冲突，直接返回
            if base_name not in existing_names:
                return base_name
                
            # 添加数字后缀直到找到唯一名称
            counter = 1
            while True:
                new_name = f"{base_name}{counter}"
                if new_name not in existing_names:
                    return new_name
                counter += 1
            
        # 创建对话框
        dialog = StyledDialog(parent_dialog, "快捷配置 - 从其他方案复制备份项", width=700, height=500)
        layout = QVBoxLayout()
        
        # 提示说明
        info_label = QLabel("请选择要复制的备份项，可多选：")
        info_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; margin-bottom: 10px; font-size: {Theme.FONT_SIZE_NORMAL};")
        layout.addWidget(info_label)
        
        # 创建树形控件用于显示方案和备份项
        tree = QTreeWidget()
        tree.setHeaderLabels(["名称", "类型", "详情"])
        tree.setColumnWidth(0, 240)  # 增加第一列宽度
        tree.setColumnWidth(1, 120)  # 设置第二列宽度
        
        # 修改：手动设置第一列标题居中对齐
        header = tree.header()
        header.setDefaultAlignment(Qt.AlignLeft | Qt.AlignVCenter)  # 默认左对齐
        # 设置第一列居中
        tree.headerItem().setTextAlignment(0, Qt.AlignCenter)
        
        # 完全重新设计的树形控件样式
        tree.setStyleSheet(f"""
            QTreeWidget {{
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                outline: none;
                padding: 5px;
                selection-background-color: transparent; /* 关键：选中背景透明 */
                selection-color: inherit; /* 关键：继承原有颜色 */
            }}
            QTreeWidget::item {{
                height: 36px; /* 增加高度，确保能完整显示复选框 */
                padding: 4px;
                border: none;
                margin-top: 2px;
                margin-bottom: 2px;
                background-color: {Theme.CARD_LEVEL_1}; /* 统一背景色 */
                show-decoration-selected: 0; /* 不显示选中装饰 */
                /* 移除不支持的overflow属性 */
            }}
            /* 完全移除选中和悬浮效果 */
            QTreeWidget::item:selected {{
                background-color: transparent;
                color: inherit; /* 继承原有颜色，不要变白 */
                border: none;
            }}
            QTreeWidget::item:hover {{
                background-color: transparent;
            }}
            /* 简化展开/收起样式，不使用content属性 */
            QTreeWidget::branch {{
                background-color: transparent;
                color: {Theme.TEXT_PRIMARY};
                border: none;
            }}
            /* 设置表头样式 - 修改背景色与列表一致 */
            QHeaderView::section {{
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_PRIMARY};
                border: none;
                padding: 6px 6px 6px 4px; /* 调整内边距以便对齐 */
                font-weight: bold;
                border-bottom: 1px solid {Theme.BORDER}; /* 添加底部边框区分标题 */
                text-align: left; /* 恢复默认左对齐 */
            }}
            
            /* 美化垂直滚动条 */
            QScrollBar:vertical {{
                background: {Theme.CARD_LEVEL_1}; /* 滚动条背景色 */
                width: 10px; /* 滚动条宽度 */
                margin: 0px;
                border-radius: 5px; /* 滚动条整体圆角 */
            }}
            
            /* 滚动条滑块 */
            QScrollBar::handle:vertical {{
                background: {Theme.CARD_LEVEL_3}; /* 滑块颜色 */
                min-height: 30px; /* 最小高度 */
                border-radius: 5px; /* 滑块圆角 */
            }}
            
            /* 滑块悬停和按下状态 */
            QScrollBar::handle:vertical:hover {{
                background: {Theme.HOVER}; /* 悬停颜色 */
            }}
            
            QScrollBar::handle:vertical:pressed {{
                background: {Theme.ACCENT}; /* 按下时的颜色 */
            }}
            
            /* 去除滚动条上下按钮 */
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
                background: none;
                border: none;
            }}
            
            /* 去除滚动条槽的背景 */
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
            }}
            
            /* 美化水平滚动条 */
            QScrollBar:horizontal {{
                background: {Theme.CARD_LEVEL_1}; /* 滚动条背景色 */
                height: 10px; /* 滚动条高度 */
                margin: 0px;
                border-radius: 5px; /* 滚动条整体圆角 */
            }}
            
            /* 水平滚动条滑块 */
            QScrollBar::handle:horizontal {{
                background: {Theme.CARD_LEVEL_3}; /* 滑块颜色 */
                min-width: 30px; /* 最小宽度 */
                border-radius: 5px; /* 滑块圆角 */
            }}
            
            /* 水平滑块悬停和按下状态 */
            QScrollBar::handle:horizontal:hover {{
                background: {Theme.HOVER}; /* 悬停颜色 */
            }}
            
            QScrollBar::handle:horizontal:pressed {{
                background: {Theme.ACCENT}; /* 按下时的颜色 */
            }}
            
            /* 去除水平滚动条左右按钮 */
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                width: 0px;
                background: none;
                border: none;
            }}
            
            /* 去除水平滚动条槽的背景 */
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                background: none;
            }}
        """)
        # 调整标题行高度以便与内容对齐
        tree.header().setDefaultSectionSize(32) # 设置标准高度
        tree.header().setStretchLastSection(True) # 最后一列拉伸填充
        # 调整标题垂直对齐
        tree.header().setDefaultAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        # 完全禁用选择功能
        tree.setSelectionMode(QTreeWidget.NoSelection)
        layout.addWidget(tree)
        
        # 创建全选按钮和全选逻辑
        select_frame = QFrame()
        select_frame.setFrameShape(QFrame.NoFrame)
        # 增加最小高度确保内容完全显示
        select_frame.setMinimumHeight(40) # 增加最小高度
        select_layout = QHBoxLayout(select_frame)
        # 增加上下边距，确保复选框有足够空间
        select_layout.setContentsMargins(4, 10, 4, 10) # 增加上下边距
        
        # 改进复选框样式
        select_all_box = QCheckBox("全选")
        select_all_box.setStyleSheet(f"""
            QCheckBox {{
                color: {Theme.TEXT_PRIMARY};
                padding: 0px 0px 0px 4px;
                font-weight: bold;
                margin: 0px;
                min-height: 20px; /* 确保最小高度 */
            }}
            QCheckBox::indicator {{
                width: 14px; /* 修改: 缩小尺寸 */
                height: 14px; /* 修改: 缩小尺寸 */
                border: 1px solid {Theme.BORDER};
                border-radius: 7px; /* 修改: 设置为宽度的一半确保是圆形 */
                background-color: {Theme.CARD_LEVEL_2};
                margin-top: 0px; /* 重置顶部边距 */
                position: relative; /* 保留相对定位 */
                top: 2px; /* 微调：小幅度下移 */
                vertical-align: middle; /* 保留垂直居中 */
            }}
            QCheckBox::indicator:checked {{
                background-color: {Theme.ACCENT};
                border: 1px solid {Theme.ACCENT};
                image: url(icons/check-white.png);
            }}
            QCheckBox::indicator:hover {{
                border: 1px solid {Theme.ACCENT};
            }}
        """)
        
        # 创建特殊容器来托管复选框，确保完整显示
        checkbox_wrapper = QWidget()
        checkbox_wrapper.setStyleSheet(f"background-color: transparent; min-height: 26px;")
        wrapper_layout = QHBoxLayout(checkbox_wrapper)
        wrapper_layout.setContentsMargins(0, 0, 0, 0)
        wrapper_layout.addWidget(select_all_box)
        
        # 添加选中项统计标签
        stats_label = QLabel("已选择: 0 / 0")
        stats_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; margin: 0px; padding: 0px;")
        
        select_layout.addWidget(checkbox_wrapper) # 使用包装容器
        select_layout.addStretch(1)
        select_layout.addWidget(stats_label)
        layout.addWidget(select_frame)
        
        # 存储所有复选框的引用，用于全选功能
        checkboxes = []
        item_to_checkbox = {}  # 用于映射树项到复选框
        checkbox_to_item = {}  # 用于映射复选框到树项和数据
        
        # 处理展开/收起状态变化
        def update_scheme_icon(item, expanded):
            # 只处理方案项（没有父项的项）
            if item.parent() is None:
                icon = "📂" if expanded else "📁"
                # 如果文本中已有emoji，则替换它
                text = item.text(0)
                if text.startswith("📂 ") or text.startswith("📁 "):
                    text = text[2:].strip()  # 移除现有emoji
                
                # 去除末尾的" 方案"后缀，防止重复添加
                if text.endswith(" 方案"):
                    text = text[:-3].strip()
                    
                item.setText(0, f"{icon} {text} 方案")  # 添加" 方案"后缀
        
        # 监听展开/收起事件
        tree.itemExpanded.connect(lambda item: update_scheme_icon(item, True))
        tree.itemCollapsed.connect(lambda item: update_scheme_icon(item, False))
        
        # 定义更新统计信息的函数
        def update_stats():
            selected_count = sum(1 for cb in checkboxes if cb.isChecked())
            total_count = len(checkboxes)
            stats_label.setText(f"已选择: {selected_count} / {total_count}")
            
        # 填充树形控件
        for scheme in all_schemes:
            if not scheme.items:  # 跳过没有备份项的方案
                continue
                
            # 创建方案节点
            scheme_item = QTreeWidgetItem(tree)
            scheme_item.setText(0, f"📂 {scheme.name} 方案")  # 修改: 添加" 方案"后缀
            scheme_item.setExpanded(False)  # 默认不展开
            
            # 设置方案标题样式，加粗显示并设置字体颜色
            for col in range(3):  # 设置所有列的样式
                scheme_item.setForeground(col, QColor(Theme.ACCENT))  # 使用主题强调色
                # 使用默认字体，只设置加粗属性
                font = QFont()
                font.setBold(True)
                scheme_item.setData(col, Qt.FontRole, font)  # 只应用加粗
                
            # 确保方案项不会被选中状态影响颜色
            scheme_item.setFlags(scheme_item.flags() & ~Qt.ItemIsSelectable)
            
            # 添加备份项作为子节点
            for item_idx, item in enumerate(scheme.items):
                # 记录当前遍历到的项目
                log_info(f"处理方案 '{scheme.name}' 的项目 #{item_idx+1}: 类型={item.item_type}, 路径={item.path}")
                
                # 检查是否为重复项（提前检查）
                is_duplicate = is_duplicate_item(item)
                log_info(f"项目重复检查结果: {is_duplicate}")
                
                # 创建备份项节点
                item_node = QTreeWidgetItem(scheme_item)
                # 确保不可被选中
                item_node.setFlags(item_node.flags() & ~Qt.ItemIsSelectable)
                
                # 显示备份项信息
                item_name = item.name or "未命名"
                item_node.setText(1, self.item_type_map.get(item.item_type, item.item_type))
                item_node.setText(2, self._gen_item_full_desc(item))
                
                # 设置备份项的样式，使其与方案区分
                for col in range(3):
                    if col > 0:  # 第一列有自定义组件，只需要设置其他列
                        item_node.setForeground(col, QColor(Theme.TEXT_SECONDARY))  # 使用次要文本颜色
                
                # 创建自定义容器来保存复选框/禁用图标和名称
                checkbox_container = QWidget()
                checkbox_container.setStyleSheet(f"background-color: transparent; min-height: 36px;")  # 确保容器高度足够
                container_layout = QHBoxLayout(checkbox_container)
                container_layout.setContentsMargins(4, 0, 0, 0)  # 左侧留点边距，其他为0
                container_layout.setSpacing(8)  # 减小间距
                
                if is_duplicate:
                    # 对于重复项，直接创建禁用图标替代复选框
                    log_info(f"为重复项创建禁用图标: {item_name}, 路径={item.path}")
                    disabled_icon = QLabel("🚫")
                    disabled_icon.setStyleSheet(f"""
                        color: {Theme.ERROR};
                        font-size: 16px; /* 放大图标 */
                        background-color: transparent;
                        min-width: 18px; /* 匹配更大的字体 */
                        min-height: 18px; /* 匹配更大的字体 */
                        margin: 0px 0px 0px -4px; /* 添加负的左边距使图标向左移动 */
                        padding: 0px;
                        position: relative; /* 添加相对定位 */
                        top: 2px; /* 添加top属性使其与复选框对齐 */
                        qproperty-alignment: AlignVCenter; /* 添加垂直居中属性 */
                    """)
                    disabled_icon.setToolTip("此备份项已存在于当前方案中，不可选择")
                    # 添加禁用图标作为第一个元素
                    container_layout.addWidget(disabled_icon)
                else:
                    # 对于非重复项，创建正常复选框
                    log_info(f"为非重复项创建复选框: {item_name}, 路径={item.path}")
                    checkbox = QCheckBox()
                    checkbox.setStyleSheet(f"""
                        QCheckBox {{
                            background-color: transparent;
                            margin: 0px;
                            padding: 0px;
                        }}
                        QCheckBox::indicator {{
                            width: 14px; /* 修改: 缩小尺寸 */
                            height: 14px; /* 修改: 缩小尺寸 */
                            border: 1px solid {Theme.BORDER};
                            border-radius: 7px; /* 修改: 设置为宽度的一半确保是圆形 */
                            background-color: {Theme.CARD_LEVEL_2};
                            margin-top: 0px; /* 重置顶部边距 */
                            position: relative; /* 保留相对定位 */
                            top: 2px; /* 微调：小幅度下移 */
                            vertical-align: middle; /* 保留垂直居中 */
                        }}
                        QCheckBox::indicator:checked {{
                            background-color: {Theme.ACCENT};
                            border: 1px solid {Theme.ACCENT};
                            image: url(icons/check-white.png);
                        }}
                        QCheckBox::indicator:hover {{
                            border: 1px solid {Theme.ACCENT};
                        }}
                    """)
                    # 保存复选框和数据的对应关系
                    checkboxes.append(checkbox)
                    item_to_checkbox[item_node] = checkbox
                    checkbox_to_item[checkbox] = (item_node, item, scheme)
                    
                    # 连接复选框状态变化信号
                    checkbox.stateChanged.connect(update_stats)
                    
                    # 添加复选框作为第一个元素
                    container_layout.addWidget(checkbox)
                
                # 为备份项添加对应的图标和名称，使用统一的容器
                name_container = QWidget()
                name_container.setStyleSheet("background-color: transparent;")
                name_layout = QHBoxLayout(name_container)
                name_layout.setContentsMargins(0, 0, 0, 0) # 无边距
                name_layout.setSpacing(4) # 小间距
                
                # 创建图标标签
                icon_text = ""
                if item.item_type == "folder":
                    icon_text = "📁"
                elif item.item_type == "file":
                    icon_text = "📄"
                elif item.item_type.startswith("db"):
                    icon_text = "🗃️"
                    
                if icon_text:
                    icon_label = QLabel(icon_text)
                    icon_label.setStyleSheet(f"""
                        color: {Theme.TEXT_PRIMARY}; 
                        background-color: transparent;
                        qproperty-alignment: AlignVCenter; /* 添加垂直居中属性 */
                    """)
                    name_layout.addWidget(icon_label)
                
                # 创建名称标签
                text_label = QLabel(item_name)
                text_label.setStyleSheet(f"""
                    color: {Theme.TEXT_PRIMARY}; 
                    background-color: transparent;
                    qproperty-alignment: AlignVCenter; /* 添加垂直居中属性 */
                """)
                name_layout.addWidget(text_label)
                name_layout.addStretch()
                
                # 添加名称容器到主容器
                container_layout.addWidget(name_container)
                container_layout.addStretch()
                
                # 将自定义容器设置到第一列
                tree.setItemWidget(item_node, 0, checkbox_container)
        
        # 初始更新统计信息
        update_stats()
        
        # 设置全选逻辑
        def toggle_all_checkboxes():
            checked = select_all_box.isChecked()
            for checkbox in checkboxes:
                checkbox.setChecked(checked)
            update_stats()  # 更新统计信息
        
        select_all_box.clicked.connect(toggle_all_checkboxes)
        
        # 处理点击事件：点击行时切换复选框状态
        def handle_item_clicked(item, column):
            # 防止默认的选择行为
            tree.clearSelection()
            
            # 如果是点击方案项，展开或收起
            if item.parent() is None:
                # 切换展开状态
                item.setExpanded(not item.isExpanded())
                return
                
            # 如果是备份项，切换复选框状态
            if item in item_to_checkbox:
                checkbox = item_to_checkbox[item]
                checkbox.setChecked(not checkbox.isChecked())
        
        tree.itemClicked.connect(handle_item_clicked)
        
        # 改进按钮样式
        dialog.addLayout(layout)
        ok_btn = dialog.addButtons("复制选中项", "取消")
        
        # 获取取消按钮的引用
        cancel_btn = dialog.cancel_btn
        
        # 统一设置按钮样式
        button_style = {
            "confirm": f"""
                QPushButton {{
                    background-color: {Theme.ACCENT};
                    color: white;
                    border: none;
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    padding: 10px 20px;
                    font-weight: bold;
                    font-size: {Theme.FONT_SIZE_NORMAL};
                    min-width: 120px;
                }}
                QPushButton:hover {{
                    background-color: {Theme.ACCENT_HOVER};
                }}
                QPushButton:pressed {{
                    background-color: {Theme.ACCENT};
                    opacity: 0.8;
                }}
            """,
            "cancel": f"""
                QPushButton {{
                    background-color: #2A2E36;
                    color: {Theme.TEXT_PRIMARY};
                    border: 1px solid {Theme.BORDER};
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    padding: 10px 20px;
                    font-weight: bold;
                    font-size: {Theme.FONT_SIZE_NORMAL};
                    min-width: 120px;
                }}
                QPushButton:hover {{
                    border: 1px solid {Theme.ACCENT};
                    color: {Theme.ACCENT};
                }}
                QPushButton:pressed {{
                    background-color: #252830;
                }}
            """
        }
        
        # 应用按钮样式
        ok_btn.setStyleSheet(button_style["confirm"])
        cancel_btn.setStyleSheet(button_style["cancel"])
        
        # 确认按钮逻辑
        def on_ok():
            # 获取所有选中的备份项
            selected_items = []
            for checkbox, (item_node, item, scheme) in checkbox_to_item.items():
                if checkbox.isChecked():
                    selected_items.append((item, scheme))
            
            if not selected_items:
                StyledDialog.showInfoDialog(dialog, "提示", "请至少选择一个备份项")
                return
                
            # 复制所选备份项到当前方案
            for item, source_scheme in selected_items:
                # 创建备份项的副本
                item_dict = item.to_dict()
                new_item = BackupItem(**item_dict)
                
                # 如果项目有名称，检查并处理名称冲突
                if new_item.name:
                    new_item.name = generate_unique_name(new_item.name)
                    
                # 添加到当前方案
                current_scheme.items.append(new_item)
                # 记录日志
                log_info(f"从方案 '{source_scheme.name}' 复制备份项 '{item.name or self._gen_item_full_desc(item)}' 到方案 '{current_scheme.name}'")
            
            # 保存当前方案
            self.manager.save_scheme(current_scheme)
            # 刷新表格
            self._refresh_scheme_items_table(current_table, current_scheme)
            dialog.accept()
        
        ok_btn.clicked.connect(on_ok)
        cancel_btn.clicked.connect(dialog.reject)
        dialog.exec()

    def item_edit_dialog(self, item=None, parent_dialog=None, allow_delete=False):
        from PySide6.QtWidgets import QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QComboBox, QCheckBox, QPushButton, QFileDialog, QListWidget, QListWidgetItem
        parent = parent_dialog or self.window()
        dialog = StyledDialog(parent, "编辑备份项" if item else "新增备份项", width=400)
        layout = QVBoxLayout()
        type_box = QComboBox()
        # 优化后的中文选项
        type_options = [
            "文件夹",
            "文件",
            "数据库表",
            "数据库表字段（key）",
            "数据库嵌套字段（多级key）"
        ]
        type_box.addItems(type_options)
        # 设置深色主题样式
        type_box.setStyleSheet(f'''
            QComboBox {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px 8px 15px;
                min-width: 200px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QComboBox:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: right center;
                width: 20px;
                border-left: none;
                padding-right: 5px;
            }}
            QComboBox::down-arrow {{
                width: 10px;
                height: 10px;
                image: none;
                border-top: 5px solid {Theme.TEXT_PRIMARY};
                border-right: 5px solid transparent;
                border-left: 5px solid transparent;
            }}
            QComboBox QAbstractItemView {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                selection-background-color: {Theme.ACCENT};
                outline: none;
                padding: 5px;
            }}
            QComboBox QAbstractItemView::item {{
                padding: 8px 10px;
                min-height: 25px;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            QComboBox QAbstractItemView::item:hover {{
                background-color: #1E2128;
            }}
            QComboBox QAbstractItemView::item:selected {{
                background-color: {Theme.ACCENT};
                color: white;
            }}
        ''')
        # 表单项
        path_label = QLabel("路径/数据库文件")
        path_layout = QHBoxLayout()
        path_edit = QLineEdit()
        browse_path_btn = QPushButton("浏览...")
        browse_path_btn.setFixedHeight(path_edit.sizeHint().height()) # 尝试匹配输入框高度
        browse_path_btn.setFixedWidth(80)
        browse_path_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        path_layout.addWidget(path_edit, 1) # 输入框占据更多空间
        path_layout.addWidget(browse_path_btn)
        
        table_label = QLabel("表名(数据库类型)")
        # --- 修改：同样为 table_edit 和 key_path_edit 预留按钮位置（后续实现） ---
        table_container = QWidget() # 创建容器
        table_layout = QHBoxLayout()
        table_layout.setContentsMargins(0,0,0,0) # 容器内的布局通常不需要外边距
        table_edit = QLineEdit()
        select_table_btn = QPushButton("选择...")
        select_table_btn.setFixedHeight(table_edit.sizeHint().height())
        select_table_btn.setFixedWidth(80)
        select_table_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        select_table_btn.setVisible(False) # 初始隐藏
        table_layout.addWidget(table_edit, 1)
        table_layout.addWidget(select_table_btn)
        table_container.setLayout(table_layout) # 设置布局给容器

        key_label = QLabel("键路径(用/分隔,数据库嵌套key类型)")
        key_container = QWidget() # 创建容器
        key_layout = QHBoxLayout()
        key_layout.setContentsMargins(0,0,0,0)
        key_path_edit = QLineEdit()
        select_key_btn = QPushButton("选择/预览...")
        select_key_btn.setFixedHeight(key_path_edit.sizeHint().height())
        select_key_btn.setFixedWidth(100) # 按钮文本可能较长
        select_key_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        select_key_btn.setVisible(False) # 初始隐藏
        key_layout.addWidget(key_path_edit, 1)
        key_layout.addWidget(select_key_btn)
        key_container.setLayout(key_layout) # 设置布局给容器
        # --- 修改结束 ---

        key_hint = QLabel("")
        key_hint.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL}; margin-bottom: 2px;")

        kill_box = QCheckBox("恢复前需关闭Cursor")
        restart_box = QCheckBox("恢复后自动重启Cursor")
        name_label = QLabel("自定义名称(可选)")
        name_edit = QLineEdit()

        # 定义新的复选框样式
        checkbox_style = f"""
            QCheckBox {{
                spacing: 5px; /* 指示器和文本之间的间距 */
                color: {Theme.TEXT_PRIMARY}; /* 文本颜色 */
            }}
            QCheckBox::indicator {{
                width: 16px;  /* 保持原始宽度 */
                height: 16px; /* 保持原始高度 */
                border-radius: 4px; /* 轻微圆角 */
                background-color: transparent; /* 基础背景透明 */
            }}
            QCheckBox::indicator:unchecked {{
                border: 1px solid {Theme.SUCCESS}; /* 未选中时绿色边框 */
            }}
            QCheckBox::indicator:unchecked:hover {{
                border: 1px solid {Theme.ACCENT_HOVER}; /* 修改: 使用 Theme.ACCENT_HOVER */
            }}
            QCheckBox::indicator:checked {{
                background-color: {Theme.SUCCESS}; /* 选中时绿色背景 */
                border: 1px solid {Theme.SUCCESS}; /* 选中时绿色边框 */
                /* Qt 会自动绘制选中标记 */
            }}
            QCheckBox::indicator:checked:hover {{
                background-color: {Theme.ACCENT_HOVER}; /* 修改: 使用 Theme.ACCENT_HOVER */
                border: 1px solid {Theme.ACCENT_HOVER};    /* 修改: 使用 Theme.ACCENT_HOVER */
            }}
            QCheckBox::indicator:disabled {{
                 border: 1px solid {Theme.DISABLED}; /* 禁用时边框 */
                 background-color: transparent;
            }}
             QCheckBox::indicator:checked:disabled {{
                 background-color: {Theme.DISABLED}; /* 禁用且选中时背景 */
                 border: 1px solid {Theme.DISABLED};
             }}
        """

        # 应用样式
        kill_box.setStyleSheet(checkbox_style)
        restart_box.setStyleSheet(checkbox_style)

        # 预填充
        if item:
            # 英文转中文
            type_map = {
                "folder": "文件夹",
                "file": "文件",
                "db_table": "数据库表",
                "db_key": "数据库表字段（key）",
                "db_nested_key": "数据库嵌套字段（多级key）"
            }
            type_box.setCurrentText(type_map.get(item.item_type, "文件夹"))
            path_edit.setText(item.path)
            table_edit.setText(item.db_table or "")
            key_path_edit.setText("/".join(item.db_key_path) if item.db_key_path else "")
            kill_box.setChecked(item.need_kill_cursor)
            restart_box.setChecked(item.need_restart_cursor)
            name_edit.setText(item.name or "")
            # --- 修改：根据类型正确处理加载 db_key_path --- 
            if item.item_type == "db_key" and item.db_key_path:
                key_path_edit.setText(item.db_key_path[0])
            elif item.item_type == "db_nested_key" and item.db_key_path:
                # --- 修改：加载时使用 :: 连接 --- 
                key_path_edit.setText("::".join(item.db_key_path))
                # --- 修改结束 --- 
            else:
                 key_path_edit.setText("") # Clear for other types or empty path
            # --- 修改结束 --- 
        # 动态显示/隐藏逻辑
        def update_fields():
            t = type_box.currentText()
            # --- 控制浏览按钮显隐 ---
            browse_path_btn.setVisible(True) # 路径选择总是可见
            select_table_btn.setVisible(False) # 默认隐藏
            select_key_btn.setVisible(False) # 默认隐藏
            select_key_btn.setEnabled(False) # 默认禁用
            select_table_btn.setEnabled(False) # 默认禁用
            # --- 控制结束 ---

            # --- 新增：动态设置标签文本 ---
            if t == "文件夹":
                path_label.setText("文件夹路径")
                table_label.setText("") # 清空或设为默认，因为会隐藏
                key_label.setText("")   # 清空或设为默认，因为会隐藏
            elif t == "文件":
                path_label.setText("文件路径")
                table_label.setText("")
                key_label.setText("")
            else: # 数据库类型
                path_label.setText("数据库文件路径")
                table_label.setText("表名") # 统一设置表名标签
                # key_label 的文本在下面根据具体数据库子类型设置
                # 数据库类型时，启用表选择按钮（如果路径有效）
                select_table_btn.setVisible(True)
                select_table_btn.setEnabled(bool(path_edit.text() and os.path.exists(path_edit.text())))

            # --- 保留：现有显隐逻辑和部分 key_label 设置 ---
            # 先全部隐藏输入框和相关标签/按钮（除了路径）
            table_label.setVisible(False)
            table_container.setVisible(False) # 修改：控制容器可见性
            key_label.setVisible(False)
            key_container.setVisible(False) # 修改：控制容器可见性
            key_hint.setVisible(False)

            # 动态调整 key_label 标签内容 (保留原有逻辑, 但在数据库分支内设置)
            select_key_btn.setText("选择/预览...") # 重置按钮文本
            select_key_btn.setToolTip("") # 重置 ToolTip

            if t == "数据库表字段（key）":
                key_label.setText("键名（单层key）") # 修改标签文本
                select_key_btn.setText("选择 Key...") # 更新按钮文本
                select_key_btn.setToolTip("从数据表中选择作为 Key 的列") # 添加 ToolTip
            elif t == "数据库嵌套字段（多级key）":
                key_label.setText("键路径（多级key）") # 修改标签文本，不再显示/分隔符
                select_key_btn.setText("选择路径...") # 更新按钮文本
                select_key_btn.setToolTip("通过数据表和 JSON 树选择嵌套路径") # 添加 ToolTip
            # else: # 非 key 类型数据库或文件/文件夹，key_label 已在上面清空

            # 动态显示控件 (保留原有逻辑)
            if t in ["数据库表", "数据库表字段（key）", "数据库嵌套字段（多级key）"]:
                table_label.setVisible(True) # 显示 "表名" 标签
                table_container.setVisible(True) # 修改：控制容器可见性
                select_table_btn.setVisible(True) # 表选择按钮可见
                select_table_btn.setEnabled(bool(path_edit.text() and os.path.exists(path_edit.text()))) # 状态依赖于路径

            if t == "数据库表字段（key）":
                key_label.setVisible(True) # 显示 "键名（单层key）" 标签
                key_container.setVisible(True) # 修改：控制容器可见性
                key_path_edit.setPlaceholderText("点击右侧按钮选择...") # 更新提示
                key_hint.setText("单层 Key，如 user_id")
                key_hint.setVisible(True)
                # 启用键选择按钮（如果表名有效）
                select_key_btn.setVisible(True) # Key 选择按钮可见
                select_key_btn.setEnabled(bool(table_edit.text()))
            elif t == "数据库嵌套字段（多级key）":
                key_label.setVisible(True) # 显示 "键路径（多级key...）" 标签
                key_container.setVisible(True) # 修改：控制容器可见性
                key_path_edit.setPlaceholderText("点击右侧按钮选择...") # 更新提示
                # --- 修改：更新提示分隔符为 :: --- 
                key_hint.setText("格式: 主键值::JSON列名::嵌套路径") # 更新提示
                # --- 修改结束 --- 
                key_hint.setVisible(True)
                # 启用路径选择按钮（如果表名有效）
                select_key_btn.setVisible(True) # 路径选择按钮可见
                select_key_btn.setEnabled(bool(table_edit.text()))

            # --- 保留：自适应高度调用 ---
            from PySide6.QtCore import QTimer
            QTimer.singleShot(0, dialog.adjustSize)

        # --- 新增：路径浏览方法 ---
        def _browse_path():
            from PySide6.QtWidgets import QFileDialog
            import os
            current_path = path_edit.text()
            dialog_title = "选择"
            selected_path = None
            mode = QFileDialog.FileMode.AnyFile # 默认模式
            file_filter = "所有文件 (*)" # 初始化默认文件过滤器

            t = type_box.currentText()
            if t == "文件夹":
                dialog_title += "文件夹"
                mode = QFileDialog.FileMode.Directory
            elif t == "文件":
                dialog_title += "文件"
                # file_filter 保持默认值 "所有文件 (*)"
                mode = QFileDialog.FileMode.ExistingFile
            else: # 数据库
                dialog_title += "数据库文件"
                # 覆盖默认过滤器
                file_filter = "数据库文件 (*.db *.sqlite *.sqlite3 *.vscdb);;所有文件 (*)" 
                mode = QFileDialog.FileMode.ExistingFile

            if mode == QFileDialog.FileMode.Directory:
                 selected_path = QFileDialog.getExistingDirectory(
                     dialog, # 父窗口设为当前弹窗
                     dialog_title,
                     current_path if current_path and os.path.isdir(current_path) else os.path.expanduser("~")
                 )
            else:
                 initial_dir = os.path.dirname(current_path) if current_path and os.path.exists(os.path.dirname(current_path)) else os.path.expanduser("~")
                 selected_path, _ = QFileDialog.getOpenFileName(
                     dialog, # 父窗口设为当前弹窗
                     dialog_title,
                     initial_dir,
                     file_filter
                 )

            if selected_path:
                path_edit.setText(selected_path)
                # 路径变化后，可能需要更新依赖该路径的按钮状态（如下面的表选择）
                update_fields() # 重新调用 update_fields 来更新按钮状态

        # --- 新增：数据库表选择方法 ---
        def _select_table():
            db_path = path_edit.text()
            if not db_path or not os.path.exists(db_path):
                StyledDialog.showInfoDialog(dialog, "错误", "请先选择一个有效的数据库文件")
                return

            conn = None
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                # 查询所有用户表名
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';")
                tables = [row[0] for row in cursor.fetchall()]
                conn.close()

                if not tables:
                    StyledDialog.showInfoDialog(dialog, "提示", "数据库中未找到用户表")
                    return

                # 创建选择对话框
                table_dialog = StyledDialog(dialog, "选择数据库表", width=300)
                list_widget = QListWidget()
                list_widget.setStyleSheet(f"""
                    QListWidget {{ 
                        background-color: {Theme.CARD_LEVEL_1};
                        color: {Theme.TEXT_PRIMARY};
                        border: 1px solid {Theme.BORDER};
                        border-radius: {Theme.BORDER_RADIUS_SMALL};
                        outline: none; /* 移除焦点轮廓 */
                        padding: 5px;
                    }}
                    QListWidget::item {{
                        padding: 8px 10px;
                        border-radius: {Theme.BORDER_RADIUS_SMALL};
                    }}
                    QListWidget::item:hover {{
                        background-color: {Theme.HOVER};
                    }}
                    QListWidget::item:selected {{
                        background-color: {Theme.ACCENT};
                        color: white;
                    }}
                """)
                for table_name in tables:
                    list_widget.addItem(QListWidgetItem(table_name))
                
                # 双击确认选择
                list_widget.itemDoubleClicked.connect(lambda item: (table_edit.setText(item.text()), table_dialog.accept()))

                table_dialog.addWidget(list_widget)
                ok_btn = table_dialog.addButtons("确定", "取消")
                
                def on_table_ok():
                    selected_item = list_widget.currentItem()
                    if selected_item:
                        table_edit.setText(selected_item.text())
                        table_dialog.accept()
                    else:
                        StyledDialog.showInfoDialog(table_dialog, "提示", "请先选择一个表")

                ok_btn.clicked.connect(on_table_ok)
                table_dialog.exec() # 显示模态对话框
                # 表名变化后，更新依赖它的按钮状态
                update_fields()

            except sqlite3.Error as e:
                StyledDialog.showInfoDialog(dialog, "数据库错误", f"无法连接或查询数据库：\n{e}")
            finally:
                if conn:
                    conn.close()
        # --- 表选择方法结束 ---

        # --- 修改：数据库键选择/值预览方法 ---
        def _select_key_or_preview():
            t = type_box.currentText()
            db_path = path_edit.text()
            table_name = table_edit.text()

            if not db_path or not os.path.exists(db_path):
                StyledDialog.showInfoDialog(dialog, "错误", "请先选择一个有效的数据库文件")
                return
            if not table_name:
                StyledDialog.showInfoDialog(dialog, "错误", "请先选择或输入数据库表名")
                return

            # --- 新逻辑：使用新对话框 ---
            if t == "数据库表字段（key）":
                data_viewer = DataViewerDialog(db_path, table_name, DataViewerDialog.Mode.SELECT_COLUMN, parent=dialog)
                if data_viewer.exec() == QDialog.Accepted:
                    selected_col = data_viewer.getSelectedColumn()
                    if selected_col:
                        key_path_edit.setText(selected_col)
                        # Key 变化后更新依赖它的按钮状态 (虽然当前没有，但保持一致)
                        update_fields()

            elif t == "数据库嵌套字段（多级key）":
                # 1. 打开 DataViewerDialog 选择主键列、JSON列、数据行
                data_viewer = DataViewerDialog(db_path, table_name, DataViewerDialog.Mode.SELECT_ROW_COLUMNS, parent=dialog)
                if data_viewer.exec() == QDialog.Accepted:
                    selected_info = data_viewer.getSelectedRowColumnsInfo()
                    if not selected_info:
                        StyledDialog.showInfoDialog(dialog, "错误", "未能获取选择信息")
                        return

                    pk_col = selected_info["pk_col"]
                    json_col = selected_info["json_col"]
                    pk_value = selected_info["pk_value"]

                    # 2. 查询选定单元格的 JSON 数据
                    conn = None
                    json_data = None
                    try:
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        # 确保列名和表名安全（这里我们信任它们来自UI选择）
                        # 使用参数化查询值
                        sql = f'SELECT "{json_col}" FROM "{table_name}" WHERE "{pk_col}" = ?'
                        cursor.execute(sql, (pk_value,))
                        row = cursor.fetchone()

                        if not row or row[0] is None:
                            StyledDialog.showInfoDialog(dialog, "未找到或为空", f"未找到主键 '{pk_value}' 或其 '{json_col}' 列为空")
                            return

                        json_str = row[0]
                        try:
                            json_data = json.loads(json_str)
                        except json.JSONDecodeError as e:
                            StyledDialog.showInfoDialog(dialog, "JSON 解析错误", f"无法解析 '{json_col}' 列的内容：\n{e}")
                            return

                    except sqlite3.Error as e:
                        StyledDialog.showInfoDialog(dialog, "数据库错误", f"查询 JSON 数据时出错：\n{e}")
                        return
                    finally:
                        if conn:
                            conn.close()

                    if json_data is None:
                        return # 错误已在上面处理

                    # 3. 打开 JsonTreeViewDialog 选择嵌套路径
                    # --- 修改：传递初始路径前缀 ---
                    initial_prefix = [pk_value, json_col]
                    json_tree_viewer = JsonTreeViewDialog(json_data, initial_path_prefix=initial_prefix, parent=dialog)
                    # --- 修改结束 ---
                    if json_tree_viewer.exec() == QDialog.Accepted:
                        nested_path = json_tree_viewer.getSelectedPath()
                        if nested_path is not None: # 现在允许空路径（表示选择了 JSON 列本身）
                            # --- 修改：最终路径不包含 JSON 列名 ---
                            # --- 修改：使用 :: 连接路径 --- 
                            full_path = f"{pk_value}" # Start with only PK value
                            # json_col is used to fetch data, but not part of the final logical path
                            if nested_path: # 只有在选择了更深的路径时才添加
                                full_path += f"::{nested_path}"
                            # --- 修改结束 --- 
                            key_path_edit.setText(full_path)
                            update_fields() # 更新依赖状态

            else:
                # 其他类型不应该触发此按钮
                StyledDialog.showInfoDialog(dialog, "提示", "此功能仅适用于数据库 Key 和嵌套 Key 类型")
        # --- 键选择/预览方法修改结束 ---

        # --- 连接信号 ---
        browse_path_btn.clicked.connect(_browse_path)
        select_table_btn.clicked.connect(_select_table) 
        select_key_btn.clicked.connect(_select_key_or_preview) # 连接键选择/预览按钮
        # --- 连接结束 ---

        type_box.currentIndexChanged.connect(update_fields)
        # 输入框内容变化也可能影响按钮状态
        path_edit.textChanged.connect(update_fields)
        table_edit.textChanged.connect(update_fields)
        key_path_edit.textChanged.connect(update_fields)

        update_fields() # 初始调用以设置状态

        # --- 修改：添加到布局的部分 ---
        layout.addWidget(QLabel("类型"))
        layout.addWidget(type_box)
        layout.addWidget(path_label)
        layout.addLayout(path_layout)
        layout.addWidget(table_label)
        layout.addWidget(table_container) # 修改：添加容器 widget
        layout.addWidget(key_label)
        layout.addWidget(key_container)   # 修改：添加容器 widget
        layout.addWidget(key_hint)
        layout.addWidget(kill_box)
        layout.addWidget(restart_box)
        layout.addWidget(name_label)
        layout.addWidget(name_edit)
        # --- 修改结束 ---
        btn_bar = QHBoxLayout()
        ok_btn = QPushButton("确定")
        cancel_btn = QPushButton("取消")
        del_btn = None
        if allow_delete:
            del_btn = QPushButton("删除")
            del_btn.setStyleSheet(f"background-color: {Theme.ERROR}; color: white; border-radius: {Theme.BORDER_RADIUS_SMALL}; font-weight: bold; padding: 4px 10px;")
            del_btn.setFixedHeight(36)
            del_btn.setMinimumWidth(80)
            del_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        for btn in [ok_btn, cancel_btn]:
            btn.setFixedHeight(36)
            btn.setMinimumWidth(80)
            btn.setCursor(Qt.CursorShape.PointingHandCursor)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {Theme.CARD_LEVEL_1}; 
                    color: {Theme.TEXT_PRIMARY};
                    border: none;
                    border-radius: {Theme.BORDER_RADIUS_SMALL}; 
                    font-weight: bold;
                    padding: 4px 10px;
                }}
                QPushButton:hover {{
                    background-color: {Theme.HOVER};
                }}
                QPushButton:pressed {{
                    background-color: {Theme.SECONDARY};
            }}
            """)
        btn_bar.addStretch(1)
        if del_btn:
            btn_bar.addWidget(del_btn)
        btn_bar.addWidget(ok_btn)
        btn_bar.addWidget(cancel_btn)
        layout.addLayout(btn_bar)
        dialog.addLayout(layout)
        deleted = False
        def on_ok():
            dialog.accept()
        def on_cancel():
            dialog.reject()
        def on_delete():
            nonlocal deleted
            deleted = True
            dialog.accept()
        ok_btn.clicked.connect(on_ok)
        cancel_btn.clicked.connect(on_cancel)
        if del_btn:
            del_btn.clicked.connect(on_delete)
        result = dialog.exec()
        if deleted:
            return None, False, True
        if result == QDialog.Accepted:
            # 中文转英文
            type_map = {
                "文件夹": "folder",
                "文件": "file",
                "数据库表": "db_table",
                "数据库表字段（key）": "db_key",
                "数据库嵌套字段（多级key）": "db_nested_key"
            }
            data = {
                'item_type': type_map.get(type_box.currentText(), "folder"),
                'path': path_edit.text(),
                'db_table': table_edit.text() or None,
                'db_key_path': key_path_edit.text().split("/") if key_path_edit.text() else [],
                'need_kill_cursor': kill_box.isChecked(),
                'need_restart_cursor': restart_box.isChecked(),
                'name': name_edit.text() or None
            }
            # --- 修改：根据类型正确处理保存 db_key_path --- 
            key_text = key_path_edit.text()
            if data['item_type'] == 'db_key':
                data['db_key_path'] = [key_text] if key_text else []
            elif data['item_type'] == 'db_nested_key':
                # --- 修改：保存时使用 :: 分割 --- 
                data['db_key_path'] = key_text.split("::") if key_text else []
                # --- 修改结束 --- 
            else:
                data['db_key_path'] = [] # Ensure it's an empty list for non-db-key types
            # --- 修改结束 --- 
            return data, True, False
        return None, False, False 

    def on_rename_scheme_card(self, scheme, parent_dialog):
        """处理方案卡片上的重命名按钮点击"""
        from PySide6.QtCore import QTimer
        old_name = scheme.name
        new_name, ok = self.rename_input_dialog(old_name, parent_dialog=parent_dialog)
        if ok and new_name:
            log_info(f"请求将方案 '{old_name}' 重命名为 '{new_name}'") # 添加重命名请求日志
            success, message = self.manager.rename_scheme(old_name, new_name)
            if success:
                log_info(f"成功将方案 '{old_name}' 重命名为 '{new_name}'") # 记录成功
                # 关闭旧对话框可能导致问题，改为直接刷新主列表并尝试获取新scheme对象
                # self._show_scheme_dialog_and_refresh(scheme) # 这会用旧scheme对象尝试打开
                self.refresh_scheme_list() # 刷新主列表以显示新名称
                if parent_dialog and parent_dialog.isVisible():
                    parent_dialog.accept() # 关闭当前（旧名称的）详情对话框
                # 尝试获取新方案对象以备后续操作（例如立即打开新方案详情）
                # new_scheme = self.manager.get_scheme(new_name)
                # if new_scheme:
                #     QTimer.singleShot(0, lambda: self.show_scheme_dialog(new_scheme))
            else:
                log_error(f"重命名方案 '{old_name}' 为 '{new_name}' 失败: {message}") # 记录失败
                StyledDialog.showInfoDialog(parent_dialog or self.window(), "重命名失败", message)
                
    def rename_input_dialog(self, old_name, parent_dialog=None):
        """显示重命名输入对话框并进行校验"""
        from PySide6.QtWidgets import QVBoxLayout, QLabel, QLineEdit, QHBoxLayout, QPushButton
        import re
        # --- Use provided parent_dialog first --- 
        parent = parent_dialog or self.window()
        # --------------------------------------
        dialog = StyledDialog(parent, "重命名方案", width=400)
        layout = QVBoxLayout()
        layout.setSpacing(15) 

        # 添加图标
        icon_label = QLabel("✏️") # Rename/Edit icon
        icon_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: 42px;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # 原名称标签
        old_name_label = QLabel(f"原名称： {old_name}")
        old_name_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_NORMAL}; margin-bottom: 10px;") # Add margin
        old_name_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(old_name_label)
        
        # 新名称提示标签
        prompt_label = QLabel("请输入新方案名称：")
        prompt_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: {Theme.FONT_SIZE_NORMAL};")
        layout.addWidget(prompt_label)

        # 输入框
        input_edit = QLineEdit()
        input_edit.setText(old_name) 
        input_edit.setPlaceholderText("新方案名称 (最多13字符)") # New placeholder
        input_edit.setStyleSheet(f"background-color: {Theme.CARD_LEVEL_2}; color: {Theme.TEXT_PRIMARY}; border-radius: {Theme.BORDER_RADIUS_SMALL}; padding: 8px 12px;")
        layout.addWidget(input_edit)

        # 错误标签
        error_label = QLabel("")
        error_label.setStyleSheet("color: #ff4d4f; font-size: 13px; margin-top: 2px; margin-bottom: 5px;")
        error_label.hide()
        layout.addWidget(error_label)

        # --- Manually create Button Bar --- 
        btn_bar = QHBoxLayout()
        ok_btn = QPushButton("确认")
        cancel_btn = QPushButton("取消")
        
        # --- Apply EXACT style copied from simple_input_dialog --- 
        copied_style = f"""    # <--- Re-use the style string defined above
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_1}; 
                color: {Theme.TEXT_PRIMARY};
                border: none; 
                border-radius: {Theme.BORDER_RADIUS_SMALL}; 
                font-weight: bold;
                padding: 4px 10px;
            }}
            QPushButton:hover {{
                background-color: {Theme.HOVER};
            }}
            QPushButton:pressed {{
                background-color: {Theme.SECONDARY};
            }}
        """
        for btn in [ok_btn, cancel_btn]:
            btn.setFixedHeight(36)
            btn.setMinimumWidth(80)
            btn.setCursor(Qt.CursorShape.PointingHandCursor)
            btn.setStyleSheet(copied_style)
            
        # -------------------------------------------------------
        btn_bar.addWidget(ok_btn)
        btn_bar.addWidget(cancel_btn)
        layout.addLayout(btn_bar)
        
        # Validation logic remains the same
        def validate():
            new_name = input_edit.text().strip()
            # Exclude the *current* old name from the duplicate check
            exists = [s.name.strip().lower() for s in self.manager.list_schemes() if s.name.strip().lower() != old_name.lower()]
            error_label.setText("")
            error_label.hide()

            if not new_name:
                error_label.setText("新名称不能为空")
                error_label.show()
                return False
            if new_name.lower() == old_name.lower():
                 # Allow confirming with no change, or handle differently?
                 # For now, treat as success but do nothing in the handler
                 # Alternatively, show message and return False? Let's return False.
                 error_label.setText("新旧名称相同")
                 error_label.show()
                 return False
            if new_name.lower() in exists:
                error_label.setText("该名称已被其他方案使用")
                error_label.show()
                return False
            if len(new_name) > 13:
                error_label.setText("名称过长，最多13个字符")
                error_label.show()
                return False
            if new_name in ['.', '..'] or new_name.startswith('.') or new_name.endswith('.'):
                error_label.setText("名称格式不合法")
                error_label.show()
                return False
            if re.search(r'[\\/:*?"<>|]', new_name):
                error_label.setText("名称包含非法字符")
                error_label.show()
                return False
            if new_name != input_edit.text(): # Check for leading/trailing spaces in original input
                 error_label.setText("名称不能有首尾空格")
                 error_label.show()
                 return False
            return True

        # Connect manually created buttons
        def on_ok():
            if validate():
                dialog.accept()
        ok_btn.clicked.connect(on_ok) # Connect ok_btn
        cancel_btn.clicked.connect(dialog.reject) # Connect cancel_btn

        dialog.addLayout(layout)
        if dialog.exec() == QDialog.Accepted:
            return input_edit.text().strip(), True
        return "", False 

    # --- 新增：删除备份项关联文件的辅助方法 ---
    def _delete_item_backups(self, scheme_name, item_base_filename):
        if not scheme_name or not item_base_filename:
            log_warning("请求清理备份项文件，但方案名或基础文件名无效")
            return
            
        log_info(f"开始清理方案 '{scheme_name}' 中与备份项 '{item_base_filename}' 关联的备份文件...")
        try:
            scheme_dir = self.manager._get_scheme_dir(scheme_name)
            backup_dir = os.path.join(scheme_dir, 'backups')
            
            if not os.path.isdir(backup_dir):
                log_info(f"清理方案 '{scheme_name}': 备份目录 '{backup_dir}' 不存在，无需清理")
                return # No backup directory, nothing to delete
                
            prefix = item_base_filename + "__"
            deleted_count = 0
            found_files = False
            for filename in os.listdir(backup_dir):
                if filename.startswith(prefix):
                    found_files = True
                    path_to_delete = os.path.join(backup_dir, filename)
                    try:
                        if os.path.isfile(path_to_delete) or os.path.islink(path_to_delete):
                            os.remove(path_to_delete)
                            log_info(f"清理方案 '{scheme_name}': 已删除旧备份文件 '{filename}'") # 记录文件删除
                            deleted_count += 1
                        elif os.path.isdir(path_to_delete):
                            shutil.rmtree(path_to_delete)
                            log_info(f"清理方案 '{scheme_name}': 已删除旧备份文件夹 '{filename}'") # 记录文件夹删除
                            deleted_count += 1
                    except (OSError, shutil.Error) as e:
                        log_error(f"清理方案 '{scheme_name}': 删除旧备份 '{filename}' 失败: {e}") # 记录错误
            
            if not found_files:
                log_info(f"清理方案 '{scheme_name}': 未找到与备份项 '{item_base_filename}' 关联的备份文件")
            elif deleted_count > 0:
                 log_info(f"清理方案 '{scheme_name}': 成功删除 {deleted_count} 个与备份项 '{item_base_filename}' 关联的备份")
            else:
                 log_warning(f"清理方案 '{scheme_name}': 找到了与 '{item_base_filename}' 关联的文件但未能删除任何文件（可能发生错误）")
                 
        except Exception as e:
            log_error(f"清理方案 '{scheme_name}' 的备份文件时发生意外错误: {e}")
    # --- 新增结束 ---

    def _refresh_scheme_items_table(self, table, scheme):
        import os
        from PySide6.QtCore import Qt # 导入 Qt
        table.setRowCount(len(scheme.items))
        for row, item in enumerate(scheme.items):
            # 名称列 (修改为居中对齐, 无自定义名时显示'未命名')
            display_name = item.name or "未命名" # 修改 fallback 值为 '未命名'
            name_item = QTableWidgetItem(display_name)
            name_item.setToolTip(self._gen_item_full_desc(item)) # Tooltip 保持不变
            name_item.setTextAlignment(Qt.AlignCenter) # 保持居中对齐
            table.setItem(row, 0, name_item)
            
            # 类型列 (居中对齐)
            type_cn = self.item_type_map.get(item.item_type, item.item_type)
            type_item = QTableWidgetItem(type_cn)
            type_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(row, 1, type_item)
            
            # 需关闭重启列 (合并 Kill/Restart, ✅/❌ Emoji, 居中对齐)
            kill_emoji = "✅" if item.need_kill_cursor else "❌"
            restart_emoji = "✅" if item.need_restart_cursor else "❌"
            special_ops_text = f"{kill_emoji} {restart_emoji}"
            special_ops_item = QTableWidgetItem(special_ops_text)
            special_ops_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(row, 2, special_ops_item)
            
            # 已有备份列 (emoji, 居中对齐)
            backup_files_dir = os.path.join(self.manager._get_scheme_dir(scheme.name), 'backups')
            base_filename = item.get_backup_filename()
            exists = False
            backup_tooltip = "未找到备份目录"
            if os.path.isdir(backup_files_dir):
                backup_tooltip = f"在目录 '{backup_files_dir}' 中未找到以 '{base_filename}__' 开头的备份"
                prefix_to_check = base_filename + "__"
                try:
                    for filename in os.listdir(backup_files_dir):
                        if filename.startswith(prefix_to_check):
                            exists = True
                            backup_tooltip = f"找到备份文件，例如: {os.path.join(backup_files_dir, filename)}"
                            break
                except OSError as e:
                    backup_tooltip = f"检查备份目录时出错: {e}"
            
            backup_emoji = "🟢" if exists else "⚪️"
            backup_item = QTableWidgetItem(backup_emoji)
            backup_item.setTextAlignment(Qt.AlignCenter)
            backup_item.setToolTip(backup_tooltip)
            table.setItem(row, 3, backup_item)

    def _gen_item_full_desc(self, item):
        # 生成完整描述用于tooltip
        if item.item_type in ("folder", "file"):
            return item.path
        elif item.item_type.startswith("db"):
            base = item.db_table or "db"
            if item.db_key_path:
                key_str = "::".join(item.db_key_path)
                return f"表: {base}\n键路径: {key_str}"
            else:
                return f"表: {base}"
        else:
            return ""