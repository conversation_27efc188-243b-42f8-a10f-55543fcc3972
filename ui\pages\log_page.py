from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QPushButton, 
    QTextBrowser, QApplication, QTableWidget, QTableWidgetItem, 
    QHeaderView, QDateEdit, QTextEdit
)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QColor

from theme import Theme
from widgets.styled_widgets import StyledFrame
from logger import Logger, read_today_log, open_log_dir

class LogPage(QWidget):
    """日志页面类，封装了CursorAccountManager.create_log_page的逻辑"""
    
    def __init__(self, main_window, parent=None):
        """初始化日志页面
        
        Args:
            main_window: 主窗口实例
            parent: 父组件
        """
        super().__init__(parent)
        self.main_window = main_window
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        # 设置整个页面背景为透明
        self.setStyleSheet("background: transparent;")
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)  # 修改为0边距与功能页一致
        main_layout.setSpacing(25)  # 修改为25与功能页一致
        
        # 日志容器 - 使用StyledFrame代替QWidget，应用玻璃效果
        log_container = StyledFrame(has_glass_effect=True)
        log_container.setObjectName("log_container")
        # 使用主题定义的玻璃效果样式
        log_container.setStyleSheet(f"""
            StyledFrame {{
                background-color: {Theme.GLASS_BG};
                border-radius: {Theme.BORDER_RADIUS};
                border: 1px solid {Theme.GLASS_BORDER};
            }}
        """)
        log_layout = QVBoxLayout(log_container)
        log_layout.setContentsMargins(25, 25, 25, 25)  # 修改内边距为25px与功能页一致
        log_layout.setSpacing(15)
        
        # 顶部区域：日志级别筛选按钮和操作按钮
        top_area = QWidget()
        top_area.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_2};  # 使用主题定义的卡片层级2颜色
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            margin: 4px 2px;
        """)
        top_layout = QHBoxLayout(top_area)
        top_layout.setContentsMargins(15, 12, 15, 12)
        top_layout.setSpacing(15)
        
        # 创建日志级别筛选按钮
        # 存储日志级别按钮的字典，以便稍后可以访问它们
        self.main_window.log_filter_buttons = {}
        self.main_window.active_log_filters = set()  # 当前激活的过滤器集合（空表示显示所有日志）
        
        # 日志级别及其颜色
        log_levels = {
            "信息": "#4fc3f7",  # 蓝色
            "警告": "#ffb74d",  # 橙色
            "错误": "#ef5350",  # 红色
            "调试": "#9ccc65",  # 绿色
            "严重": "#d32f2f"   # 深红色
        }
        
        # 创建筛选按钮
        for level_name, color in log_levels.items():
            filter_btn = QPushButton(level_name)
            filter_btn.setCursor(Qt.CursorShape.PointingHandCursor)
            filter_btn.setCheckable(True)  # 设置按钮为可选中状态
            
            # 设置按钮样式
            filter_btn.setStyleSheet(f"""
                QPushButton {{
                    color: {color};
                    background-color: transparent;
                    border: 1px solid {color};
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    padding: 4px 8px;
                    font-size: {Theme.FONT_SIZE_SMALL};
                    font-weight: bold;
                    min-width: 45px;
                }}
                QPushButton:hover {{
                    background-color: rgba({', '.join(str(int(color.lstrip('#')[i:i+2], 16)) for i in (0, 2, 4))}, 0.2);
                }}
                QPushButton:checked {{
                    background-color: {color};
                    color: white;
                }}
            """)
            
            # 连接按钮点击事件 - 使用Lambda函数捕获当前的level_name和checked状态
            filter_btn.clicked.connect(lambda checked, level=level_name: self.main_window._toggle_log_filter(level, checked))
            
            # 添加到布局和按钮字典
            top_layout.addWidget(filter_btn)
            self.main_window.log_filter_buttons[level_name] = filter_btn
        
        # 添加弹性空间，让操作按钮靠右显示
        top_layout.addStretch()
        
        # 创建操作按钮
        # 打开文件夹按钮
        open_logs_btn = QPushButton("日志目录")
        open_logs_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        open_logs_btn.setStyleSheet(f"""
            QPushButton {{
                color: {Theme.TEXT_PRIMARY};
                background-color: transparent;
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_SMALL};
                min-width: 80px;
                max-height: 28px;
            }}
            QPushButton:hover {{
                background-color: #d6b16a;
                border-color: #d6b16a;
                color: white;
            }}
            QPushButton:pressed {{
                background-color: #B89C5D;
                border-color: #B89C5D;
                color: white;
            }}
        """)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新日志")
        refresh_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        refresh_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: {Theme.TEXT_PRIMARY};
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_SMALL};
                min-width: 60px;
                max-height: 28px;
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        
        # 将按钮添加到顶部区域布局中
        top_layout.addWidget(open_logs_btn)
        top_layout.addWidget(refresh_btn)
        
        # 添加顶部区域到日志容器
        log_layout.addWidget(top_area)
        
        # 日志内容区域 - 使用QTextBrowser代替QLabel，直接使用其内置滚动条
        self.main_window.log_text = QTextBrowser()
        self.main_window.log_text.setHtml("<div style='color: #9e9e9e; text-align: center; margin-top: 20px;'>正在加载日志...</div>")
        
        # 强制显示垂直滚动条
        self.main_window.log_text.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)
        self.main_window.log_text.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 设置QTextBrowser的样式，使用子控件选择器语法
        self.main_window.log_text.setStyleSheet("""
            QTextBrowser {
                font-family: 'Consolas', 'Menlo', 'Courier New', monospace;
                font-size: 14px; 
                padding: 10px 15px;
                color: white;
                background-color: transparent;
                border: none;
            }
            
            QTextBrowser QScrollBar:vertical {
                border: none;
                background: #2A2E36;  /* 滚动条凹槽背景色 */
                width: 10px;          /* 减小宽度到10px */
                margin: 0px;
                border-radius: 5px;   /* 调整凹槽圆角 */
            }
            
            QTextBrowser QScrollBar::handle:vertical {
                background-color: #4A4D56;
                min-height: 30px;
                border-radius: 5px;   /* 与凹槽一致的圆角 */
                margin: 0px;          /* 移除边距，确保圆角正确显示 */
            }
            
            QTextBrowser QScrollBar::handle:vertical:hover {
                background-color: #5A5D66;
            }
            
            QTextBrowser QScrollBar::add-line:vertical, 
            QTextBrowser QScrollBar::sub-line:vertical {
                height: 0px;
            }
            
            QTextBrowser QScrollBar::add-page:vertical, 
            QTextBrowser QScrollBar::sub-page:vertical {
                background: #2A2E36;  /* 滚动条页面区域背景色，与凹槽背景一致 */
                border-radius: 5px;   /* 与凹槽一致的圆角 */
            }
        """)
        
        # 设置文本浏览器属性
        self.main_window.log_text.setOpenExternalLinks(False)  # 不打开外部链接
        self.main_window.log_text.setReadOnly(True)  # 只读模式
        self.main_window.log_text.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        self.main_window.log_text.document().setDocumentMargin(0)  # 减少文档边距
        
        # 额外设置，确保没有边框
        self.main_window.log_text.setFrameShape(QFrame.Shape.NoFrame)
        self.main_window.log_text.setLineWidth(0)
        
        # 为文本区域设置自定义上下文菜单
        self.main_window.log_text.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.main_window.log_text.customContextMenuRequested.connect(self.main_window._show_log_context_menu)
        
        # 直接将QTextBrowser添加到布局中
        log_layout.addWidget(self.main_window.log_text, 1)  # 使用拉伸因子1
        
        # 将滚动条引用保存为log_text的滚动条，供LogPageManager使用
        self.main_window.log_scroll_area = None  # 不再使用QScrollArea
        
        # 添加日志容器到主布局
        main_layout.addWidget(log_container)
        
        # 连接按钮事件
        refresh_btn.clicked.connect(lambda: self.main_window._refresh_log_display())
        open_logs_btn.clicked.connect(open_log_dir)
        
        # 初始空日志 - 仅在首次切换到日志页面时加载日志
        # 避免初始化时就读取大量日志导致程序启动缓慢
        # self.main_window._refresh_log_display()  # 注释掉，通过页面切换时再加载 