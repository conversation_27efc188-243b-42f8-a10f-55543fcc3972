from PySide6.QtWidgets import QWidget, QLabel, QGraphicsDropShadowEffect
from PySide6.QtCore import Qt, QPropertyAnimation, QPoint, QEasingCurve, QTimer, QRect
from PySide6.QtGui import QColor
from theme import Theme

class BubbleTip(QWidget):
    def __init__(self, parent, text="", target_widget=None):
        super().__init__(parent)
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents)
        self.label = QLabel(text, self)
        self.label.setStyleSheet(f'''
            background-color: rgba(188, 74, 89, 0.85); /* 红色半透明 */
            color: white;
            border: 1.5px solid {Theme.ERROR};
            border-radius: 8px;
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            padding: 7px 18px;
        ''')
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label.adjustSize()
        self.setFixedSize(self.label.size())
        # 阴影
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(18)
        shadow.setColor(QColor(0, 0, 0, 120))
        shadow.setOffset(0, 6)
        self.label.setGraphicsEffect(shadow)
        self.hide()
        self._target_widget = target_widget
        self._animation = QPropertyAnimation(self, b"windowOpacity")
        self._animation.setDuration(Theme.ANIMATION_NORMAL)
        self._animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.setWindowOpacity(0)
        self._is_showing = False
        self._follow_timer = QTimer(self)
        self._follow_timer.timeout.connect(self._follow_target)

    def setText(self, text):
        self.label.setText(text)
        self.label.adjustSize()
        # 动态宽度：label内容宽度与输入框宽度取最大
        min_width = 0
        if self._target_widget:
            min_width = self._target_widget.width()
        w = max(self.label.width(), min_width)
        h = self.label.height()
        self.setFixedSize(w, h)
        self.label.setFixedSize(w, h)

    def showTip(self, text=None):
        if text:
            self.setText(text)
        self._is_showing = True
        self._follow_target()
        self.show()
        self.raise_()
        self._animation.stop()
        self.setWindowOpacity(0)
        self._animation.setStartValue(0)
        self._animation.setEndValue(1)
        self._animation.start()
        self._follow_timer.start(30)

    def hideTip(self):
        if not self._is_showing:
            return
        self._is_showing = False
        self._animation.stop()
        self._animation.setStartValue(self.windowOpacity())
        self._animation.setEndValue(0)
        self._animation.finished.connect(self._on_hide)
        self._animation.start()
        self._follow_timer.stop()

    def _on_hide(self):
        self.hide()
        self._animation.finished.disconnect(self._on_hide)

    def _follow_target(self):
        if not self._target_widget:
            return
        parent = self.parentWidget()
        if not parent:
            return
        # 获取输入框内容区rect
        target_rect = self._target_widget.rect()
        # 输入框右下角全局坐标
        global_bottom_right = self._target_widget.mapToGlobal(target_rect.bottomRight())
        # 计算气泡左上角，使气泡右边与输入框右边对齐
        x = global_bottom_right.x() - self.width()
        y = global_bottom_right.y() + 6
        # 以parent为基准
        local_pos = parent.mapFromGlobal(QPoint(x, y))
        # 边界检测与自适应调整
        parent_rect = parent.rect()
        tip_rect = QRect(local_pos, self.size())
        # 左侧溢出
        if tip_rect.left() < parent_rect.left():
            local_pos.setX(parent_rect.left() + 10)
        # 下方溢出，尝试上弹
        if tip_rect.bottom() > parent_rect.bottom():
            # 输入框右上角
            global_top_right = self._target_widget.mapToGlobal(target_rect.topRight())
            x_up = global_top_right.x() - self.width()
            y_up = global_top_right.y() - self.height() - 6
            local_pos_up = parent.mapFromGlobal(QPoint(x_up, y_up))
            tip_rect_up = QRect(local_pos_up, self.size())
            if tip_rect_up.top() >= parent_rect.top():
                local_pos = local_pos_up
            else:
                local_pos.setY(parent_rect.bottom() - self.height() - 10)
        # 上方溢出
        if local_pos.y() < parent_rect.top():
            local_pos.setY(parent_rect.top() + 10)
        self.setParent(parent)
        self.move(local_pos)

    def setTargetWidget(self, widget):
        self._target_widget = widget
        self._follow_target() 