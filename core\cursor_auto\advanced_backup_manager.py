import os
import json
import threading
import hashlib
import re
from typing import List, Dict, Any, Optional
import shutil

class BackupItem:
    """
    单个备份项对象，支持类型（文件夹/文件/数据库表/表key/嵌套key）、路径/表名/键路径、两类开关等
    """
    def __init__(self, item_type: str, path: str, db_table: str = None, db_key_path: List[str] = None,
                 need_kill_cursor: bool = False, need_restart_cursor: bool = False, name: str = None):
        self.item_type = item_type  # folder/file/db_table/db_key/db_nested_key
        self.path = path  # 路径或数据库文件路径
        self.db_table = db_table  # 数据库表名
        self.db_key_path = db_key_path or []  # 键路径（支持嵌套）
        self.need_kill_cursor = need_kill_cursor
        self.need_restart_cursor = need_restart_cursor
        self.name = name  # 用户自定义命名

    def to_dict(self):
        return {
            'item_type': self.item_type,
            'path': self.path,
            'db_table': self.db_table,
            'db_key_path': self.db_key_path,
            'need_kill_cursor': self.need_kill_cursor,
            'need_restart_cursor': self.need_restart_cursor,
            'name': self.name
        }

    @staticmethod
    def from_dict(data: Dict[str, Any]):
        return BackupItem(
            item_type=data.get('item_type'),
            path=data.get('path'),
            db_table=data.get('db_table'),
            db_key_path=data.get('db_key_path', []),
            need_kill_cursor=data.get('need_kill_cursor', False),
            need_restart_cursor=data.get('need_restart_cursor', False),
            name=data.get('name')
        )

    def get_backup_filename(self):
        """智能生成备份文件名"""
        if self.item_type in ('folder', 'file'):
            return os.path.basename(self.path)
        elif self.item_type.startswith('db'):
            # 例如: table__key1__key2.json，超长时加hash
            base = self.db_table or 'db'
            if self.db_key_path:
                key_str = '__'.join(self.db_key_path)
                sanitized_key_str = re.sub(r'[<>:"/\\|?*]', '_', key_str)
                full_name = f"{base}__{sanitized_key_str}.json"
                if len(full_name) > 100:
                    hash_part = hashlib.md5(full_name.encode()).hexdigest()[:8]
                    truncated_sanitized_key = sanitized_key_str[:40]
                    full_name = f"{base}__{truncated_sanitized_key}__{hash_part}.json"
                return full_name
            else:
                return f"{base}.json"
        else:
            return self.name or 'backup_item'

class BackupScheme:
    """
    备份方案对象，包含多个备份项及元数据
    """
    def __init__(self, name: str, items: List[BackupItem] = None, description: str = None):
        self.name = name
        self.items = items or []
        self.description = description

    def to_dict(self):
        return {
            'name': self.name,
            'items': [item.to_dict() for item in self.items],
            'description': self.description
        }

    @staticmethod
    def from_dict(data: Dict[str, Any]):
        return BackupScheme(
            name=data.get('name'),
            items=[BackupItem.from_dict(i) for i in data.get('items', [])],
            description=data.get('description')
        )

class BackupStep:
    """
    执行步骤对象，描述单步操作（如kill、备份、恢复、重启、延迟等）
    """
    def __init__(self, step_type: str, item: Optional[BackupItem] = None, extra: Dict[str, Any] = None):
        self.step_type = step_type  # backup/restore/kill/restart/delay
        self.item = item
        self.extra = extra or {}

class AdvancedBackupManager:
    """
    高级自定义备份/恢复业务逻辑管理器
    """
    def __init__(self, base_dir: str):
        self.base_dir = base_dir  # YCursor/backups/Advanced_BackupRestore_Hub
        os.makedirs(self.base_dir, exist_ok=True)
        self.schemes: Dict[str, BackupScheme] = {}
        self._load_all_schemes()

    def _get_scheme_dir(self, scheme_name: str):
        return os.path.join(self.base_dir, scheme_name)

    def _get_scheme_config_path(self, scheme_name: str):
        return os.path.join(self._get_scheme_dir(scheme_name), 'config.json')

    def _load_all_schemes(self):
        """加载所有方案配置"""
        self.schemes.clear()
        if not os.path.exists(self.base_dir):
            return
        for name in os.listdir(self.base_dir):
            scheme_dir = self._get_scheme_dir(name)
            config_path = self._get_scheme_config_path(name)
            if os.path.isdir(scheme_dir) and os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        scheme = BackupScheme.from_dict(data)
                        self.schemes[name] = scheme
                except Exception as e:
                    print(f"加载方案失败: {name}, {e}")

    def save_scheme(self, scheme: BackupScheme):
        """保存方案配置"""
        scheme_dir = self._get_scheme_dir(scheme.name)
        os.makedirs(scheme_dir, exist_ok=True)
        config_path = self._get_scheme_config_path(scheme.name)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(scheme.to_dict(), f, ensure_ascii=False, indent=2)
        self.schemes[scheme.name] = scheme

    def delete_scheme(self, scheme_name: str) -> tuple[bool, str]:
        """删除方案及其关联的整个文件夹"""
        scheme_file = os.path.join(self.base_dir, f"{scheme_name}.json")
        scheme_dir_path = self._get_scheme_dir(scheme_name)
        
        deleted_file = False
        deleted_dir = False
        error_msg = ""
        
        # 1. 删除 .json 文件
        if os.path.exists(scheme_file):
            try:
                os.remove(scheme_file)
                deleted_file = True
                print(f"[INFO] Deleted scheme file: {scheme_file}")
            except OSError as e:
                error_msg += f"删除方案文件失败: {e}. "
                print(f"[ERROR] Failed to delete scheme file '{scheme_file}': {e}")
        else:
            print(f"[WARN] Scheme file not found, continuing to delete directory: {scheme_file}")
            # Even if file doesn't exist, try deleting directory
            deleted_file = True # Consider it 'handled'

        # 2. 删除整个方案目录 (包含 backups 子目录)
        if os.path.isdir(scheme_dir_path):
            try:
                shutil.rmtree(scheme_dir_path)
                deleted_dir = True
                print(f"[INFO] Deleted scheme directory: {scheme_dir_path}")
            except (OSError, shutil.Error) as e:
                 error_msg += f"删除方案目录失败: {e}. "
                 print(f"[ERROR] Failed to delete scheme directory '{scheme_dir_path}': {e}")
        else:
            print(f"[INFO] Scheme directory not found, nothing to delete: {scheme_dir_path}")
            deleted_dir = True # Consider it 'handled' if not found

        # 3. 从内存中移除
        self.schemes.pop(scheme_name, None)

        if deleted_file and deleted_dir:
             return True, "方案及相关文件已删除"
        elif error_msg:
             return False, error_msg.strip()
        else:
             # Should not happen if logic is correct
             return False, "未知错误，方案可能未完全删除"

    def rename_scheme(self, old_name: str, new_name: str) -> tuple[bool, str]:
        """
        重命名备份方案，包括目录和配置文件

        Args:
            old_name: 旧方案名
            new_name: 新方案名

        Returns:
            tuple: (是否成功, 消息)
        """
        import re
        old_name = old_name.strip()
        new_name = new_name.strip()

        # 1. 基础校验
        if not old_name or not new_name:
            return (False, "方案名不能为空")
        if old_name.lower() == new_name.lower():
            return (False, "新旧名称相同，无需重命名")

        # 2. 存在性与冲突校验
        if old_name not in self.schemes:
            return (False, f"原方案 '{old_name}' 不存在")
        # 检查新名称是否与除了旧名称之外的其他方案冲突
        if any(s.lower() == new_name.lower() for s in self.schemes if s.lower() != old_name.lower()):
             return (False, f"目标方案名 '{new_name}' 已被其他方案使用")

        # 3. 新名称合法性校验 (安全冗余)
        if re.search(r'[\\/:*?"<>|]', new_name):
             return (False, f"新方案名 '{new_name}' 包含非法字符")
        if len(new_name) > 13: # Ensure length check consistency
            return (False, "新方案名过长，最多13个字符")
        if new_name in ['.', '..'] or new_name.startswith('.') or new_name.endswith('.'):
             return (False, "新方案名格式不合法")

        # 4. 路径校验
        old_dir = self._get_scheme_dir(old_name)
        new_dir = self._get_scheme_dir(new_name)
        old_config_path = self._get_scheme_config_path(old_name)
        new_config_path = self._get_scheme_config_path(new_name)

        if not os.path.isdir(old_dir):
             return (False, f"原方案目录 '{old_dir}' 不存在或不是一个目录")
        if not os.path.exists(old_config_path):
             return (False, f"原方案配置文件 '{old_config_path}' 不存在")
        if os.path.exists(new_dir):
             return (False, f"目标方案目录 '{new_dir}' 已存在，无法重命名")

        # 5. 读取原配置文件 (提前读取以防万一)
        try:
            with open(old_config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            if config_data.get('name') != old_name:
                 # 文件内容与预期不符，增加一道保险
                 print(f"[Warning] Config name mismatch in {old_config_path}. Expected '{old_name}', found '{config_data.get('name')}'. Proceeding cautiously.")
                 # return (False, f"原配置文件名与内容不符: {old_config_path}")
        except Exception as e:
            return (False, f"读取原配置文件失败: {e}")

        # 6. 重命名目录 (关键步骤)
        try:
            os.rename(old_dir, new_dir)
            print(f"Renamed directory from {old_dir} to {new_dir}")
        except OSError as e:
            return (False, f"重命名方案目录失败: {e}")

        # 7. 更新并写入新配置文件
        config_data['name'] = new_name # 更新内存中的配置数据
        try:
            with open(new_config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            print(f"Updated and wrote new config to {new_config_path}")
        except Exception as e:
            # 尝试回滚目录重命名
            print(f"[Error] Failed to write new config: {e}. Attempting to roll back directory rename...")
            try:
                os.rename(new_dir, old_dir)
                print(f"Successfully rolled back directory rename from {new_dir} to {old_dir}")
                return (False, f"写入新配置失败，目录已回滚: {e}")
            except OSError as re:
                print(f"[Critical Error] Failed to write new config AND failed to roll back directory rename: {re}")
                return (False, f"写入新配置失败({e})，且目录回滚失败({re})！请手动检查目录 '{new_dir}' 和原配置")

        # 8. 更新内存中的管理器状态
        try:
            scheme_object = self.schemes.pop(old_name)
            scheme_object.name = new_name # 更新方案对象的名称
            self.schemes[new_name] = scheme_object
            print(f"Updated internal manager state for scheme '{new_name}'")
        except KeyError:
             # 理论上不应该发生，因为前面检查过old_name存在
             print(f"[Error] Failed to update internal manager state: old_name '{old_name}' not found in schemes dictionary.")
             # 此时数据已部分更改，需要用户注意
             return (False, f"重命名文件成功，但更新内部状态失败请重启应用")


        return (True, f"方案 '{old_name}' 已成功重命名为 '{new_name}'")

    def get_scheme(self, scheme_name: str) -> Optional[BackupScheme]:
        return self.schemes.get(scheme_name)

    def list_schemes(self) -> List[BackupScheme]:
        return list(self.schemes.values())

    def generate_steps(self, scheme: BackupScheme, mode: str = 'restore') -> List[BackupStep]:
        """根据方案生成执行步骤（支持kill/restart/delay等）"""
        steps = []
        for item in scheme.items:
            if mode == 'restore' and item.need_kill_cursor:
                steps.append(BackupStep('kill', item))
                steps.append(BackupStep('delay', item, {'seconds': 2}))
            steps.append(BackupStep(mode, item))
            if mode == 'restore' and item.need_restart_cursor:
                steps.append(BackupStep('restart', item))
                steps.append(BackupStep('delay', item, {'seconds': 2}))
        return steps

class BackupLogger:
    """
    详细日志接口（可扩展为写入系统日志/文件/推送UI等）
    """
    def __init__(self):
        self.logs = []
        self.lock = threading.Lock()

    def log(self, message: str, level: str = 'INFO', extra: Dict[str, Any] = None):
        entry = {
            'time': threading.current_thread().name,
            'level': level,
            'message': message,
            'extra': extra or {}
        }
        with self.lock:
            self.logs.append(entry)
        # 可扩展为写入logger.py或推送UI
        print(f"[{level}] {message}")

    def get_logs(self):
        with self.lock:
            return list(self.logs) 