from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTableWidget,
    QTableWidgetItem, QHeaderView, QComboBox, QDateEdit, QPushButton,
    QSplitter, QTextEdit, QLineEdit, QScrollArea, QProgressDialog,
    QSpacerItem, QSizePolicy, QMenu
)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QIcon, QColor

from ..styled_widgets import StyledFrame, StyledButton
from theme import Theme
from log_page_manager import LogPageManager

class LogFilterBlock(QFrame):
    """日志筛选模块"""
    
    # 信号定义
    filter_changed = Signal(dict)  # 参数为筛选条件
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("日志筛选")
        title_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        
        # 筛选控件布局
        filter_layout = QHBoxLayout()
        filter_layout.setSpacing(15)
        
        # 日志类型筛选
        type_layout = QVBoxLayout()
        type_label = QLabel("日志类型:")
        type_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        
        self.type_combo = QComboBox()
        self.type_combo.addItem("全部类型")
        self.type_combo.addItem("操作日志")
        self.type_combo.addItem("错误日志")
        self.type_combo.addItem("系统日志")
        self.type_combo.setStyleSheet(f"""
            QComboBox {{
                border: 1px solid {Theme.BORDER_COLOR};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px;
                background-color: {Theme.INPUT_BG};
                color: {Theme.TEXT_PRIMARY};
                min-height: 28px;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
        """)
        self.type_combo.currentIndexChanged.connect(self._on_filter_changed)
        
        type_layout.addWidget(type_label)
        type_layout.addWidget(self.type_combo)
        
        # 日期筛选
        date_layout = QVBoxLayout()
        date_label = QLabel("开始日期:")
        date_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-7))  # 默认一周前
        self.start_date.setCalendarPopup(True)
        self.start_date.setStyleSheet(f"""
            QDateEdit {{
                border: 1px solid {Theme.BORDER_COLOR};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px;
                background-color: {Theme.INPUT_BG};
                color: {Theme.TEXT_PRIMARY};
                min-height: 28px;
            }}
        """)
        self.start_date.dateChanged.connect(self._on_filter_changed)
        
        date_layout.addWidget(date_label)
        date_layout.addWidget(self.start_date)
        
        # 结束日期
        end_date_layout = QVBoxLayout()
        end_date_label = QLabel("结束日期:")
        end_date_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())  # 默认今天
        self.end_date.setCalendarPopup(True)
        self.end_date.setStyleSheet(f"""
            QDateEdit {{
                border: 1px solid {Theme.BORDER_COLOR};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px;
                background-color: {Theme.INPUT_BG};
                color: {Theme.TEXT_PRIMARY};
                min-height: 28px;
            }}
        """)
        self.end_date.dateChanged.connect(self._on_filter_changed)
        
        end_date_layout.addWidget(end_date_label)
        end_date_layout.addWidget(self.end_date)
        
        # 清除筛选按钮
        clear_layout = QVBoxLayout()
        clear_label = QLabel("")  # 占位
        clear_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        
        self.clear_btn = StyledButton("清除筛选")
        self.clear_btn.setFixedHeight(28)
        self.clear_btn.clicked.connect(self._clear_filters)
        
        clear_layout.addWidget(clear_label)
        clear_layout.addWidget(self.clear_btn)
        
        # 添加所有筛选控件到布局
        filter_layout.addLayout(type_layout)
        filter_layout.addLayout(date_layout)
        filter_layout.addLayout(end_date_layout)
        filter_layout.addLayout(clear_layout)
        filter_layout.addStretch()
        
        layout.addWidget(title_label)
        layout.addLayout(filter_layout)
        
    def _on_filter_changed(self):
        """筛选条件变化处理函数"""
        filter_data = {
            "type": self.type_combo.currentText(),
            "start_date": self.start_date.date().toString("yyyy-MM-dd"),
            "end_date": self.end_date.date().toString("yyyy-MM-dd")
        }
        self.filter_changed.emit(filter_data)
        
    def _clear_filters(self):
        """清除筛选条件"""
        self.type_combo.setCurrentIndex(0)  # 重置为全部类型
        self.start_date.setDate(QDate.currentDate().addDays(-7))  # 一周前
        self.end_date.setDate(QDate.currentDate())  # 今天
        
        # 触发筛选变化信号
        self._on_filter_changed()


class LogTableBlock(QFrame):
    """日志表格模块"""
    
    # 信号定义
    log_selected = Signal(dict)  # 参数为所选日志的详细信息
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["时间", "类型", "操作", "状态"])
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.verticalHeader().setVisible(False)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.table.setStyleSheet(f"""
            QTableWidget {{
                border: none;
                background-color: {Theme.CARD_LEVEL_2};
                gridline-color: {Theme.BORDER_COLOR};
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {Theme.BORDER_COLOR};
            }}
            QTableWidget::item:selected {{
                background-color: {Theme.SELECTION_BG};
                color: {Theme.TEXT_PRIMARY};
            }}
            QHeaderView::section {{
                background-color: {Theme.CARD_LEVEL_1};
                padding: 8px;
                border: none;
                border-bottom: 1px solid {Theme.BORDER_COLOR};
                color: {Theme.TEXT_PRIMARY};
                font-weight: bold;
            }}
        """)
        
        # 连接选择信号
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        
        layout.addWidget(self.table)
        
        # 添加示例数据
        self._add_sample_data()
        
    def _add_sample_data(self):
        """添加示例数据"""
        sample_data = [
            {"time": "2025-03-27 10:30:25", "type": "操作日志", "action": "添加账户", "status": "成功",
             "details": "添加了账户****************"},
            {"time": "2025-03-27 09:15:40", "type": "操作日志", "action": "删除账户", "status": "成功",
             "details": "删除了账户************"},
            {"time": "2025-03-26 16:52:10", "type": "错误日志", "action": "切换账户", "status": "失败",
             "details": "切换账户时发生错误：无法找到对应的机器码文件"},
            {"time": "2025-03-25 14:20:33", "type": "系统日志", "action": "程序启动", "status": "成功",
             "details": "应用程序启动，加载了32个账户"},
            {"time": "2025-03-24 11:05:18", "type": "系统日志", "action": "检查更新", "status": "成功",
             "details": "检查到新版本v2.0.1可用"},
        ]
        
        self.table.setRowCount(len(sample_data))
        
        for row, data in enumerate(sample_data):
            time_item = QTableWidgetItem(data["time"])
            type_item = QTableWidgetItem(data["type"])
            action_item = QTableWidgetItem(data["action"])
            status_item = QTableWidgetItem(data["status"])
            
            # 设置状态颜色
            if data["status"] == "成功":
                status_item.setForeground(QColor(Theme.SUCCESS))
            else:
                status_item.setForeground(QColor(Theme.ERROR))
            
            # 存储详细信息
            time_item.setData(Qt.ItemDataRole.UserRole, data)
            
            self.table.setItem(row, 0, time_item)
            self.table.setItem(row, 1, type_item)
            self.table.setItem(row, 2, action_item)
            self.table.setItem(row, 3, status_item)
    
    def update_filter(self, filter_data):
        """根据筛选条件更新表格"""
        # 实际应用中，这里应该查询数据库或日志文件
        # 此处仅模拟筛选效果
        self.table.clearContents()
        
        # 获取筛选条件
        log_type = filter_data["type"]
        
        # 示例：根据类型筛选示例数据
        filtered_data = []
        sample_data = [
            {"time": "2025-03-27 10:30:25", "type": "操作日志", "action": "添加账户", "status": "成功",
             "details": "添加了账户****************"},
            {"time": "2025-03-27 09:15:40", "type": "操作日志", "action": "删除账户", "status": "成功",
             "details": "删除了账户************"},
            {"time": "2025-03-26 16:52:10", "type": "错误日志", "action": "切换账户", "status": "失败",
             "details": "切换账户时发生错误：无法找到对应的机器码文件"},
            {"time": "2025-03-25 14:20:33", "type": "系统日志", "action": "程序启动", "status": "成功",
             "details": "应用程序启动，加载了32个账户"},
            {"time": "2025-03-24 11:05:18", "type": "系统日志", "action": "检查更新", "status": "成功",
             "details": "检查到新版本v2.0.1可用"},
        ]
        
        for data in sample_data:
            if log_type == "全部类型" or data["type"] == log_type:
                filtered_data.append(data)
        
        # 更新表格
        self.table.setRowCount(len(filtered_data))
        for row, data in enumerate(filtered_data):
            time_item = QTableWidgetItem(data["time"])
            type_item = QTableWidgetItem(data["type"])
            action_item = QTableWidgetItem(data["action"])
            status_item = QTableWidgetItem(data["status"])
            
            # 设置状态颜色
            if data["status"] == "成功":
                status_item.setForeground(QColor(Theme.SUCCESS))
            else:
                status_item.setForeground(QColor(Theme.ERROR))
            
            # 存储详细信息
            time_item.setData(Qt.ItemDataRole.UserRole, data)
            
            self.table.setItem(row, 0, time_item)
            self.table.setItem(row, 1, type_item)
            self.table.setItem(row, 2, action_item)
            self.table.setItem(row, 3, status_item)
    
    def _on_selection_changed(self):
        """选择变化处理函数"""
        selected_items = self.table.selectedItems()
        if selected_items:
            # 获取选中行的第一个单元格（时间列）
            first_item = selected_items[0]
            row = first_item.row()
            time_item = self.table.item(row, 0)
            
            # 获取存储的详细信息
            log_data = time_item.data(Qt.ItemDataRole.UserRole)
            if log_data:
                self.log_selected.emit(log_data)


class LogDetailBlock(QFrame):
    """日志详情模块"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("日志详情")
        title_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        
        # 详情文本框
        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        self.detail_text.setStyleSheet(f"""
            QTextEdit {{
                border: 1px solid {Theme.BORDER_COLOR};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                background-color: {Theme.INPUT_BG};
                color: {Theme.TEXT_PRIMARY};
                padding: 10px;
            }}
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(self.detail_text)
        
    def update_detail(self, log_data):
        """更新日志详情"""
        # 格式化详情显示
        detail_html = f"""
        <div style='font-family: Arial; color: {Theme.TEXT_PRIMARY};'>
            <p><b>时间:</b> {log_data['time']}</p>
            <p><b>类型:</b> {log_data['type']}</p>
            <p><b>操作:</b> {log_data['action']}</p>
            <p><b>状态:</b> <span style='color: {"green" if log_data["status"] == "成功" else "red"};'>{log_data['status']}</span></p>
            <p><b>详细信息:</b></p>
            <p style='margin-left: 20px;'>{log_data['details']}</p>
        </div>
        """
        
        self.detail_text.setHtml(detail_html)


class LogPage(QWidget):
    """日志页面类"""
    
    toast_request = Signal(str, str)

    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setStyleSheet("background: transparent;")
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(25)
        
        # 标题区域
        title_frame = StyledFrame(has_glass_effect=True)
        title_layout = QVBoxLayout(title_frame)
        title_layout.setContentsMargins(25, 25, 25, 25)
        
        title_label = QLabel("系统日志")
        title_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_TITLE}; 
            font-weight: bold; 
            color: {Theme.ACCENT}; 
            background-color: transparent;
        """)
        
        subtitle_label = QLabel("查看系统操作记录和错误信息")
        subtitle_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            color: {Theme.TEXT_SECONDARY}; 
            background-color: transparent;
            margin-top: 5px;
        """)
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        
        main_layout.addWidget(title_frame)
        
        # 筛选区域
        self.filter_block = LogFilterBlock()
        main_layout.addWidget(self.filter_block)
        
        # 创建分割器，用于日志列表和详情
        log_splitter = QSplitter(Qt.Orientation.Vertical)
        log_splitter.setChildrenCollapsible(False)
        
        # 日志表格
        self.log_table = LogTableBlock()
        log_splitter.addWidget(self.log_table)
        
        # 日志详情
        self.log_detail = LogDetailBlock()
        log_splitter.addWidget(self.log_detail)
        
        # 设置初始大小（表格占60%，详情占40%）
        log_splitter.setSizes([600, 400])
        
        # 添加分割器到主布局
        main_layout.addWidget(log_splitter)
        
        # 连接信号
        self.filter_block.filter_changed.connect(self.log_table.update_filter)
        self.log_table.log_selected.connect(self.log_detail.update_detail)