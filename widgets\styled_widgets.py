"""
样式化的自定义部件模块
提供应用程序中使用的各种视觉上增强的界面组件
"""

from PySide6.QtWidgets import (
    QPushButton, QLabel, QFrame, QHBoxLayout, QVBoxLayout, QWidget, QProgressBar,
    QSizePolicy, QSpacerItem, QGraphicsOpacityEffect, QGraphicsDropShadowEffect,
    QLineEdit, QComboBox, QSlider, QRadioButton, QCheckBox, QSpinBox, QDoubleSpinBox,
    QPlainTextEdit, QToolTip, QMenu, QApplication
)
from PySide6.QtCore import (
    Qt, QPropertyAnimation, QEasingCurve, QPoint, QSize, QTimer, 
    Signal, QObject, QAbstractAnimation, Property, QRect, QEvent, QUrl
)
from PySide6.QtGui import (
    QColor, QPalette, QFont, QPainter, QLinearGradient, QPen, QBrush, QIcon, QPixmap, QAction,
    QTextCursor, QDesktopServices, QGuiApplication
)
import sys

from theme import Theme


class NoUnderlineMenuButton(QPushButton):
    """没有下划线的菜单按钮"""
    
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QPushButton {
                padding: 12px 20px;
                text-align: left;
                font-size: 16px;
                border: none;
                border-bottom: none;
                background-color: transparent;
                color: #9CA2AE;
                font-weight: normal;
                border-radius: 10px;
                text-decoration: none;
            }
            QPushButton:hover {
                background-color: #1A1D23;
                color: #FFFFFF;
            }
            QPushButton:checked {
                background-color: #2B9D7C;
                color: #FFFFFF;
                font-weight: bold;
                border-bottom: none;
            }
        """)
        
    def paintEvent(self, event):
        # 自定义绘制，确保不会出现下划线
        super().paintEvent(event)
        
    def focusInEvent(self, event):
        # 阻止显示焦点框
        super().focusInEvent(event)
        self.clearFocus()


class WindowControlButton(QPushButton):
    """自定义窗口控制按钮"""
    
    def __init__(self, button_type, parent=None):
        super().__init__(parent)
        self.button_type = button_type
        self.setFixedSize(16, 16)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 设置按钮样式
        if button_type == "close":
            bg_color = Theme.WINDOW_CLOSE
            hover_color = Theme.WINDOW_CLOSE_HOVER
        elif button_type == "minimize":
            bg_color = Theme.WINDOW_MINIMIZE
            hover_color = Theme.WINDOW_MINIMIZE_HOVER
        elif button_type == "maximize":
            bg_color = Theme.WINDOW_MAXIMIZE
            hover_color = Theme.WINDOW_MAXIMIZE_HOVER
        else:
            bg_color = Theme.CARD_LEVEL_3
            hover_color = Theme.HOVER
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border-radius: 8px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
        """)
        
    def enterEvent(self, event):
        """鼠标进入事件 - 实现简单放大效果"""
        self.setFixedSize(18, 18)
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件 - 恢复原始大小"""
        self.setFixedSize(16, 16)
        super().leaveEvent(event)


class CustomTitleBar(QWidget):
    """自定义标题栏"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setFixedHeight(50)
        self.setObjectName("customTitleBar")
        
        # 鼠标拖动相关变量
        self.pressing = False
        self.start_point = QPoint(0, 0)
        
        # 初始化UI
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 5, 15, 5)
        
        # 窗口标题 - 不显示任何文字
        self.title_label = QLabel("")
        self.title_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_TITLE};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
        """)
        
        # 窗口控制按钮
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        self.minimize_btn = WindowControlButton("minimize")
        self.maximize_btn = WindowControlButton("maximize")
        self.close_btn = WindowControlButton("close")
        
        # 根据平台连接不同的最小化处理函数
        if sys.platform == "darwin":  # macOS系统
            self.minimize_btn.clicked.connect(self.mac_minimize)
        else:
            self.minimize_btn.clicked.connect(self.parent.showMinimized)
            
        self.maximize_btn.clicked.connect(self.toggle_maximize)
        self.close_btn.clicked.connect(self.parent.close)
        
        buttons_layout.addWidget(self.minimize_btn)
        buttons_layout.addWidget(self.maximize_btn)
        buttons_layout.addWidget(self.close_btn)
        
        # 添加到主布局
        layout.addWidget(self.title_label)
        layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # 设置样式
        self.setStyleSheet(f"""
            CustomTitleBar {{
                background-color: transparent;
            }}
        """)
    
    def mac_minimize(self):
        """macOS系统特定的最小化处理函数"""
        try:
            # 方法1：尝试直接调用Qt窗口API
            try:
                if hasattr(self.parent, "windowHandle") and self.parent.windowHandle():
                    self.parent.windowHandle().setWindowState(Qt.WindowState.WindowMinimized)
                    return
            except Exception as e1:
                print(f"方法1失败: {str(e1)}")
            
            # 方法2：使用AppleScript最小化当前应用程序的前台窗口
            try:
                import subprocess
                applescript = """
                tell application "System Events"
                    set frontApp to first application process whose frontmost is true
                    set visible of frontApp to false
                end tell
                """
                subprocess.run(["osascript", "-e", applescript], check=True)
                return
            except Exception as e2:
                print(f"方法2失败: {str(e2)}")
            
            # 方法3：最后尝试标准的最小化方法
            self.parent.showMinimized()
            
        except Exception as e:
            print(f"macOS最小化窗口出错: {str(e)}")
            # 如果所有方法都失败，尝试默认方法
            self.parent.showMinimized()
    
    def toggle_maximize(self):
        """切换窗口最大化状态"""
        if self.parent.isMaximized():
            self.parent.showNormal()
        else:
            self.parent.showMaximized()
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.pressing = True
            self.start_point = event.pos()
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.pressing and not self.parent.isMaximized():
            self.parent.move(self.parent.pos() + event.pos() - self.start_point)
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        self.pressing = False
        super().mouseReleaseEvent(event)
    
    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.toggle_maximize()
        super().mouseDoubleClickEvent(event)


class StyledFrame(QFrame):
    """自定义样式的Frame"""
    def __init__(self, parent=None, has_glass_effect=False):
        super().__init__(parent)
        self.has_glass_effect = has_glass_effect
        
        if has_glass_effect:
            # 毛玻璃效果样式
            self.setStyleSheet(f"""
                StyledFrame {{
                    background-color: {Theme.GLASS_BG};
                    border-radius: {Theme.BORDER_RADIUS};
                    border: 1px solid {Theme.GLASS_BORDER};
                }}
            """)
        else:
            # 普通样式
            self.setStyleSheet(f"""
                StyledFrame {{
                    background-color: {Theme.CARD_BG};
                    border-radius: {Theme.BORDER_RADIUS};
                    border: 1px solid {Theme.BORDER};
                }}
            """)
        
        self.setObjectName("styledFrame")
        
        # 添加阴影效果
        if has_glass_effect:
            shadow = QGraphicsDropShadowEffect(self)
            shadow.setBlurRadius(15)
            shadow.setColor(QColor(0, 0, 0, 50))
            shadow.setOffset(0, 2)
            self.setGraphicsEffect(shadow)


class StyledProgressBar(QProgressBar):
    """自定义样式的进度条，重写绘制方法实现圆角效果"""
    def __init__(self, parent=None):
        super().__init__(parent)
        # 基本设置
        self.setTextVisible(True)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet(f"""
            QProgressBar {{
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_PRIMARY};
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                border: none;
            }}
        """)
        
        # 默认颜色设置
        self._chunk_color = QColor(Theme.ACCENT)
        
        # 动画属性
        self._animation_value = 0
        self._animation = QPropertyAnimation(self, b"animationValue")
        self._animation.setEasingCurve(QEasingCurve.Type.OutQuint)  # 更平滑的动画曲线
        self._animation.setDuration(1500)  # 使用适中的动画时间
        self._animation.valueChanged.connect(self.update)
        self._animation.finished.connect(self._animation_finished)
        self._target_format = ""
        self._current_format = ""
        
        # 存储原始值，用于在UI刷新后重新应用动画
        self._original_value = 0
        
        # 添加持久值存储，不会被reset_without_animation重置
        self._persistent_value = 0
        self._persistent_max = 0
        self._persistent_format = ""
        
        # 流光动画属性
        self._glow_position = 0.0  # 流光位置 (0.0 到 1.0)
        self._glow_width = 0.2     # 流光宽度 (占总宽度的比例)
        self._glow_opacity = 0.4   # 流光不透明度
        
        # 创建流光动画
        self._glow_animation = QPropertyAnimation(self, b"glowPosition")
        self._glow_animation.setDuration(2200)  # 更长的动画时间，更流畅的感觉
        self._glow_animation.setStartValue(-self._glow_width)
        self._glow_animation.setEndValue(1.0 + self._glow_width)
        self._glow_animation.setEasingCurve(QEasingCurve.Type.InOutSine)  # 使用平滑的曲线
        self._glow_animation.finished.connect(self._restart_glow_animation)
        self._glow_animation.start()
        
        # 确保初始值设置
        QTimer.singleShot(0, lambda: self._init_animation_value())
    
    def reset_without_animation(self, store_original=True):
        """在不触发动画的情况下重置进度条
        
        Args:
            store_original: 是否存储原始值以便后续恢复，默认为True
        """
        try:
            # 检查当前格式是否为"正在加载中"，如果是则不重置
            if self.format() == "正在加载中":
                return
                
            # 如果需要，保存当前值
            if store_original:
                self._original_value = self.value()
                
                # 注意：不修改_persistent_value，它是持久存储，不受重置影响
            
            # 停止任何正在运行的动画
            if self._animation.state() == QAbstractAnimation.State.Running:
                self._animation.stop()
            
            # 直接修改内部状态，跳过动画
            self.blockSignals(True)
            self._animation_value = 0
            # 使用父类的setValue方法，避免触发我们自定义的动画逻辑
            QProgressBar.setValue(self, 0)
            self.blockSignals(False)
            self.update()  # 强制更新UI
        except Exception as e:
            print(f"重置进度条失败: {str(e)}")
    
    def restore_original_value(self):
        """恢复原始值并启动动画"""
        try:
            # 检查是否已经在动画中，如果是则不要重复启动
            if self._animation.state() == QAbstractAnimation.State.Running:
                # print(f"进度条已在动画中，跳过重复恢复")
                return
                
            # 初始化值变量
            value_to_use = 0
            max_to_use = 0
            format_to_use = ""
            
            # 特殊处理：检查当前格式是否为"无限制"
            current_format = self.format()
            if current_format == "无限制":
                value_to_use = 100
                max_to_use = 100
                format_to_use = "无限制"
            else:
                # 优先级1：使用持久值存储（如果有效）
                if hasattr(self, '_persistent_value') and self._persistent_value > 0:
                    value_to_use = self._persistent_value
                    max_to_use = self._persistent_max if self._persistent_max > 0 else self.maximum()
                    
                    # 如果持久格式是特殊格式，转换为实际值的字符串
                    if self._persistent_format == "%p%":
                        if max_to_use > 0:
                            percent = int((value_to_use / max_to_use) * 100)
                            format_to_use = f"{percent}%"
                        else:
                            format_to_use = "0%"
                    elif self._persistent_format == "%v/%m":
                        format_to_use = f"{value_to_use}/{max_to_use}"
                    else:
                        format_to_use = self._persistent_format
                    
                    # 显示详细日志的级别降低，避免日志过多
                    # print(f"使用持久值: {value_to_use}/{max_to_use}, 格式={format_to_use}")
                
                # 优先级2：使用原始值存储（如果有效且持久值无效）
                elif hasattr(self, '_original_value') and self._original_value > 0:
                    value_to_use = self._original_value
                    max_to_use = self.maximum()
                    format_to_use = ""
                    # print(f"使用原始值: {value_to_use}/{max_to_use}")
                
                # 优先级3：尝试从当前格式字符串中提取值（如果前两者都无效）
                else:
                    try:
                        import re
                        # 尝试匹配"数字/数字"格式（如"646/500"）
                        match = re.match(r"^(\d+)/(\d+)$", current_format)
                        if match:
                            value_to_use = int(match.group(1))
                            max_to_use = int(match.group(2))
                            format_to_use = current_format
                            # print(f"从格式提取值: {value_to_use}/{max_to_use}")
                    except Exception as e:
                        # print(f"从格式提取值失败: {str(e)}")
                        pass
            
            # 检查是否有有效值可用于恢复
            if value_to_use > 0:
                # 设置最大值（如果需要）
                if max_to_use > 0 and max_to_use != self.maximum():
                    self.setRange(0, max_to_use)
                
                # 设置值并启动动画
                self.setValue(value_to_use)
                
                # 如果有格式字符串，也恢复它
                if format_to_use:
                    # 直接设置格式，不经过setFormat处理特殊格式
                    self._current_format = format_to_use
                    super().setFormat(format_to_use)
            # else:
                # print(f"未找到可恢复的值")
        except Exception as e:
            print(f"恢复进度条原始值时出错: {str(e)}")
    
    def _init_animation_value(self):
        """初始化动画值为当前值"""
        self._animation_value = self.value()
        self.update()
    
    def _animation_finished(self):
        """动画完成后的回调"""
        try:
            # 获取整数目标值，避免浮点数和整数转换的舍入差异
            target_int_value = int(self._animation.endValue())
            
            # 在设置值之前停止可能的任何事件处理
            self.blockSignals(True)
            
            # 将动画值和实际值完全同步为相同的整数值
            self._animation_value = float(target_int_value)  # 使用精确的float值
            super().setValue(target_int_value)
            
            # 恢复信号处理
            self.blockSignals(False)
            
            # 强制更新绘制，确保显示正确
            self.update()
            
            if self._target_format:
                self._current_format = self._target_format
                super().setFormat(self._target_format)
                self._target_format = ""
        except Exception as e:
            print(f"动画完成回调出错: {str(e)}")
            # 发生错误时，尝试简单同步值
            self._animation_value = self.value()
            self.update()
    
    def getAnimationValue(self):
        """获取动画值"""
        return self._animation_value
    
    def setAnimationValue(self, value):
        """设置动画值"""
        # 确保动画值在合理范围内
        min_val = float(self.minimum())
        max_val = float(self.maximum())
        value = max(min_val, min(max_val, value))
        
        self._animation_value = value
        self.update()  # 更新绘制
    
    # 定义动画属性
    animationValue = Property(float, getAnimationValue, setAnimationValue)
    
    def setFormat(self, format_str):
        """重写设置格式方法，添加动画支持"""
        # 处理特殊格式字符串，如"%p%"（百分比）
        if format_str == "%p%" or format_str == "%v/%m":
            # 手动构建实际值，而不是使用原始格式字符串
            actual_value = self.value()
            maximum = self.maximum()
            if maximum > 0:
                # 如果是百分比格式
                if format_str == "%p%":
                    percent = int((actual_value / maximum) * 100)
                    self._current_format = f"{percent}%"
                # 如果是分数格式
                else:
                    self._current_format = f"{actual_value}/{maximum}"
            else:
                self._current_format = "0%"
        else:
            # 对于其他格式，直接使用
            self._current_format = format_str
            
        # 存储非加载状态的格式
        if format_str and not format_str.startswith("加载中"):
            self._persistent_format = format_str
        
        if self._animation.state() == QAbstractAnimation.State.Running:
            # 如果动画正在运行，保存目标格式以便在动画完成后设置
            self._target_format = format_str
        else:
            # 否则直接设置
            super().setFormat(format_str)
    
    def setValue(self, value):
        """重写setValue方法，添加动画效果"""
        try:
            if self._animation.state() == QAbstractAnimation.State.Running:
                self._animation.stop()
            
            # 保存当前动画值为起点
            current_value = self.value()
            
            # 将值转换为整数，避免精度问题
            value = int(value)
            
            # 更新持久值存储 - 始终保存最新的非零值
            if value > 0:
                self._persistent_value = value
                self._persistent_max = self.maximum()
                # 如果有格式字符串，也保存它
                current_format = self.format()
                if current_format and not current_format.startswith("加载中"):
                    self._persistent_format = current_format
            
            # 设置实际值（用于保存状态）
            super().setValue(value)
            
            # 如果值相同，不启动动画
            if current_value == value:
                self._animation_value = float(value)  # 保持精确的float值
                self.update()
                return
            
            # 确保最小动画差异
            start_value = current_value
            end_value = value
            
            # 当值变化很小时，仍然保持一个最小的动画效果
            if abs(end_value - start_value) < 1 and end_value != start_value:
                if end_value > start_value:
                    start_value = max(0, end_value - 1)
                else:
                    start_value = min(self.maximum(), end_value + 1)
            
            # 设置当前动画值
            self._animation_value = float(start_value)
            
            # 设置动画
            self._animation.setStartValue(float(start_value))
            self._animation.setEndValue(float(end_value))  # 确保使用浮点数避免精度问题
            self._animation.start()
        except Exception as e:
            print(f"设置进度条值出错: {str(e)}")
            # 如果出错，尝试直接设置值而不使用动画
            try:
                self._animation_value = float(value)
                super().setValue(value)
                self.update()
            except:
                pass
    
    def paintEvent(self, event):
        # 创建绘制器
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)  # 抗锯齿
        
        # 获取进度条尺寸
        width = self.width()
        height = self.height()
        radius = height / 2  # 圆角半径为高度的一半，创建胶囊形状
        
        # 绘制背景 - 胶囊形状
        bg_color = QColor(Theme.CARD_LEVEL_1)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(bg_color)
        painter.drawRoundedRect(0, 0, width, height, radius, radius)
        
        try:
            # 计算填充宽度 - 使用动画值而不是实际值
            # 确保进度在有效范围内
            min_val = float(self.minimum())
            max_val = float(self.maximum())
            range_val = max_val - min_val
            
            if range_val <= 0:
                progress = 0
            else:
                progress = (self._animation_value - min_val) / range_val
                progress = max(0.0, min(1.0, progress))
            
            # 使用round代替int，更准确的舍入
            chunk_width = round(width * progress)
            
            # 绘制填充部分 - 也是胶囊形状
            if chunk_width > 0:
                painter.setBrush(self._chunk_color)
                # 极小值特殊处理 - 绘制一个小圆形
                if chunk_width <= height:
                    # 绘制一个小于或等于高度的圆形
                    painter.drawRoundedRect(0, 0, height, height, radius, radius)
                    # 如果宽度小于高度，裁剪掉多余部分
                    if chunk_width < height:
                        # 创建一个矩形覆盖超出部分
                        painter.setBrush(bg_color)
                        painter.drawRect(chunk_width, 0, height - chunk_width, height)
                else:
                    # 正常绘制胶囊形状
                    painter.drawRoundedRect(0, 0, chunk_width, height, radius, radius)
                
                # 绘制流光效果（仅在填充部分上）
                if progress > 0:
                    # 创建裁剪区域，确保流光仅在进度条填充区域内显示
                    painter.setClipRect(0, 0, chunk_width, height)
                    
                    # 计算流光位置和宽度
                    glow_start = width * (self._glow_position - self._glow_width)
                    glow_end = width * (self._glow_position + self._glow_width)
                    
                    # 创建线性渐变作为流光效果
                    gradient = QLinearGradient(glow_start, 0, glow_end, 0)
                    base_color = QColor(self._chunk_color)
                    highlight_color = QColor(255, 255, 255, int(255 * self._glow_opacity))
                    
                    # 创建半透明的中间颜色，用于羽化效果
                    mid_opacity_1 = self._glow_opacity * 0.3
                    mid_opacity_2 = self._glow_opacity * 0.7
                    mid_color_1 = QColor(255, 255, 255, int(255 * mid_opacity_1))
                    mid_color_2 = QColor(255, 255, 255, int(255 * mid_opacity_2))
                    
                    # 设置渐变颜色，增加更多点实现平滑羽化
                    gradient.setColorAt(0.0, base_color)
                    gradient.setColorAt(0.2, mid_color_1)  # 添加过渡色
                    gradient.setColorAt(0.35, mid_color_2)  # 添加过渡色
                    gradient.setColorAt(0.5, highlight_color)  # 中心点
                    gradient.setColorAt(0.65, mid_color_2)  # 添加过渡色
                    gradient.setColorAt(0.8, mid_color_1)  # 添加过渡色
                    gradient.setColorAt(1.0, base_color)
                    
                    # 应用渐变并绘制
                    painter.setBrush(QBrush(gradient))
                    
                    # 重新绘制填充区域，但使用渐变
                    if chunk_width <= height:
                        painter.drawRoundedRect(0, 0, height, height, radius, radius)
                        if chunk_width < height:
                            painter.setBrush(bg_color)
                            painter.drawRect(chunk_width, 0, height - chunk_width, height)
                    else:
                        painter.drawRoundedRect(0, 0, chunk_width, height, radius, radius)
                    
                    # 清除裁剪区域
                    painter.setClipping(False)
        except Exception as e:
            print(f"进度条绘制错误: {str(e)}")
        
        # 绘制文本 - 使用当前格式而不是内部格式
        painter.setPen(QColor(Theme.TEXT_PRIMARY))
        painter.setFont(self.font())
        
        # 从_current_format获取文本，如果它包含格式字符串，手动替换
        display_text = self._current_format
        
        # 处理特殊格式字符串
        if display_text == "%p%":
            # 替换为实际百分比
            if self.maximum() > 0:
                percent = int((self._animation_value / self.maximum()) * 100)
                display_text = f"{percent}%"
            else:
                display_text = "0%"
        elif display_text == "%v/%m":
            # 替换为实际分数
            display_text = f"{int(self._animation_value)}/{self.maximum()}"
        
        # 如果没有设置_current_format，使用父类的text()
        if not display_text:
            display_text = self.text()
            
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, display_text)
        
        # 结束绘制
        painter.end()
    
    def setChunkColor(self, color):
        """设置填充颜色"""
        if isinstance(color, str):
            self._chunk_color = QColor(color)
        else:
            self._chunk_color = color
        self.update()  # 更新绘制

    def getGlowPosition(self):
        """获取流光位置属性"""
        return self._glow_position
    
    def setGlowPosition(self, position):
        """设置流光位置属性"""
        self._glow_position = position
        self.update()  # 触发重绘
    
    # 定义流光位置属性
    glowPosition = Property(float, getGlowPosition, setGlowPosition)
    
    def _restart_glow_animation(self):
        """重新启动流光动画"""
        self._glow_animation.start()
    
    def hideEvent(self, event):
        """隐藏事件"""
        # 停止流光动画以节省资源
        self._glow_animation.stop()
        super().hideEvent(event)
    
    def showEvent(self, event):
        """显示事件"""
        # 恢复流光动画
        self._glow_animation.start()
        super().showEvent(event)
    
    def closeEvent(self, event):
        """关闭事件"""
        # 停止所有动画
        self._glow_animation.stop()
        self._animation.stop()
        super().closeEvent(event)


class StyledButton(QPushButton):
    """自定义样式的按钮"""
    
    def __init__(self, text, parent=None, icon=None):
        """初始化StyledButton
        
        Args:
            text: 按钮文本
            parent: 父组件
            icon: 按钮图标（可选）
        """
        super().__init__(text, parent)
        
        if icon:
            self.setIcon(icon)
        
        # 透明背景+主色边框+主色文字，悬浮动画
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 1px 2px;
                font-size: {Theme.FONT_SIZE_SMALL};
            }}
            QPushButton:hover {{
                background-color: rgba(43, 157, 124, 0.10); /* ACCENT 10%透明 */
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
            }}
            QPushButton:pressed {{
                background-color: rgba(43, 157, 124, 0.18); /* ACCENT 18%透明 */
                border: 1.5px solid {Theme.ACCENT_PRESSED};
                color: {Theme.ACCENT};
            }}
            QPushButton:disabled {{
                background-color: transparent;
                border: 1px solid {Theme.DISABLED};
                color: {Theme.TEXT_SECONDARY};
            }}
        """)
        self.setCursor(Qt.CursorShape.PointingHandCursor)


class StyledSwitch(QCheckBox):
    """自定义风格的开关控件，带有平滑动画效果，胶囊形外观"""
    
    # 状态改变信号
    stateChanged = Signal(bool)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._checked = False
        
        # 创建动画
        self._animation = QPropertyAnimation(self, b"position")
        self._animation.setDuration(250)  # 动画持续时间增加到250ms，使动画更平滑
        self._animation.setEasingCurve(QEasingCurve.Type.OutCubic)  # 使用更平缓的缓动曲线
        
        # 初始位置 - 确保在初始化时正确设置
        self._position = 0.0  # 0.0表示关闭状态，1.0表示开启状态
        
        # 设置固定大小，适合胶囊形状
        self.setFixedSize(42, 21)  # 缩小尺寸 (原 56, 28)
        self.setCursor(Qt.CursorShape.PointingHandCursor)  # 鼠标悬停时显示为手型
    
    def isChecked(self):
        """获取开关状态"""
        return self._checked
    
    def setChecked(self, checked):
        """设置开关状态"""
        if self._checked != checked:
            self._checked = checked
            
            # 动画从当前位置到目标位置
            try:
                self._animation.setStartValue(self._position)
                self._animation.setEndValue(1.0 if checked else 0.0)
                self._animation.start()
            except AttributeError:
                # 如果_position不存在，则初始化它
                self._position = 1.0 if checked else 0.0
                self.update()
            
            # 发送状态改变信号
            self.stateChanged.emit(checked)
            
            # 触发重绘
            self.update()
    
    def getPosition(self):
        """获取开关动画位置属性"""
        # 添加异常处理，确保即使属性不存在也能返回合适的值
        try:
            return self._position
        except AttributeError:
            # 如果_position不存在，根据checked状态返回适当的值
            self._position = 1.0 if self._checked else 0.0
            return self._position
    
    def setPosition(self, position):
        """设置开关动画位置属性"""
        self._position = position
        self.update()  # 触发重绘
    
    # 定义Qt属性用于动画
    position = Property(float, getPosition, setPosition)
    
    def paintEvent(self, event):
        """绘制胶囊形开关"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)  # 抗锯齿
        
        # 控件尺寸
        width = self.width()
        height = self.height()
        
        # 确保_position属性存在
        try:
            position = self._position
        except AttributeError:
            # 如果_position不存在，根据checked状态设置适当的值
            self._position = 1.0 if self._checked else 0.0
            position = self._position
        
        # 计算胶囊形状和指示器的尺寸
        capsule_radius = height / 2
        indicator_size = height - 6  # 调整指示器大小，留出边距
        indicator_radius = indicator_size / 2
        
        # 计算颜色过渡 - 从未选中状态到选中状态的平滑过渡
        off_color = QColor(Theme.CARD_LEVEL_3)
        on_color = QColor(Theme.ACCENT)
        
        r = int(off_color.red() + (on_color.red() - off_color.red()) * position)
        g = int(off_color.green() + (on_color.green() - off_color.green()) * position)
        b = int(off_color.blue() + (on_color.blue() - off_color.blue()) * position)
        
        current_color = QColor(r, g, b)
        
        # 绘制胶囊形背景（带有微弱边框）
        painter.setPen(QPen(QColor(0, 0, 0, 30), 0.5))  # 添加微弱的边框
        painter.setBrush(current_color)
        painter.drawRoundedRect(0, 0, width, height, capsule_radius, capsule_radius)
        
        # 计算指示器在轨道上的移动范围
        track_width = width - 6  # 轨道宽度（留出边距）
        travel_distance = track_width - indicator_size
        
        # 计算指示器位置（考虑位置和边距）
        indicator_x = 3 + travel_distance * position
        indicator_y = 3  # 垂直居中
        
        # 为指示器添加阴影效果
        if self.isEnabled():
            shadow = QColor(0, 0, 0, 40)
            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(shadow)
            painter.drawEllipse(
                int(indicator_x + 1),  # 轻微偏移
                int(indicator_y + 1),  # 轻微偏移
                int(indicator_size),
                int(indicator_size)
            )
        
        # 绘制指示器（白色圆形）
        painter.setPen(QPen(QColor(0, 0, 0, 20), 0.5))  # 添加微弱的边框
        painter.setBrush(QColor(255, 255, 255))
        painter.drawEllipse(
            int(indicator_x),
            int(indicator_y),
            int(indicator_size),
            int(indicator_size)
        )
    
    def mousePressEvent(self, event):
        """处理鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 切换状态
            self.setChecked(not self._checked)
        super().mousePressEvent(event)


class StyledLineEdit(QLineEdit):
    """
    自定义样式的QLineEdit
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 导入Theme
        from theme import Theme
        
        self.setStyleSheet(f"""
            QLineEdit {{
                background-color: {Theme.PRIMARY};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 1px 2px; /* 减小内边距到最小 */
                height: 24px;
                selection-background-color: {Theme.SELECTION};
                text-align: left; /* 强制左对齐 */
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
            }}
        """)
        
        # 设置size policy，使得控件可以水平拉伸但垂直固定
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.setFixedHeight(26)  # 固定高度
        
        # 调整文本边距，确保文本显示区域最大化
        self.setTextMargins(8, 0, 8, 0)  # 左边距8px，右边距8px
        
        # 确保文本左对齐
        self.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)


class StyledPasswordLineEdit(StyledLineEdit):
    """带有密码可见性切换按钮的输入框"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 导入Theme
        from theme import Theme
        
        # 默认设置为密码模式
        self.setEchoMode(QLineEdit.EchoMode.Password)
        
        # 创建并设置切换按钮
        self.toggle_button = QPushButton(self)
        self.toggle_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.toggle_button.setFlat(True)
        
        # 按钮尺寸和位置
        self.toggle_button.setFixedSize(16, 16)
        
        # 设置样式 - 使用简单的文本作为图标
        self.toggle_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                border: none;
                color: {Theme.TEXT_SECONDARY};
                font-size: 12px;
                padding: 0px;
                margin: 0px;
            }}
            QPushButton:hover {{
                color: {Theme.ACCENT};
            }}
        """)
        
        # 默认设置为显示图标，表示点击可见
        self.toggle_button.setText("⊙")
        
        # 连接信号
        self.toggle_button.clicked.connect(self.toggle_password_visibility)
        
        # 设置右侧margin为按钮的宽度+边距
        self.setTextMargins(8, 0, 22, 0)  # 左8px, 上0, 右22px(留出按钮空间), 下0
        
        # 更新按钮位置
        self._update_button_position()
        
        # 当窗口大小变化时，更新按钮位置
        self.textChanged.connect(self._update_button_position)
        
    def resizeEvent(self, event):
        """处理大小变化事件"""
        super().resizeEvent(event)
        self._update_button_position()
        
    def _update_button_position(self):
        """更新按钮位置"""
        # 将按钮放在输入框的右侧，与文本垂直居中对齐
        button_x = self.width() - self.toggle_button.width() - 4  # 右边距4px
        button_y = (self.height() - self.toggle_button.height()) // 2
        self.toggle_button.move(button_x, button_y)
        
    def toggle_password_visibility(self):
        """切换密码可见性"""
        if self.echoMode() == QLineEdit.EchoMode.Password:
            # 切换为普通模式
            self.setEchoMode(QLineEdit.EchoMode.Normal)
            # 更改按钮文本为"隐藏"图标
            self.toggle_button.setText("−")
        else:
            # 切换回密码模式
            self.setEchoMode(QLineEdit.EchoMode.Password)
            # 更改按钮文本为"显示"图标
            self.toggle_button.setText("⊙")


class StyledComboBox(QComboBox):
    """
    自定义样式的QComboBox
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 导入Theme
        from theme import Theme
        
        self.setStyleSheet(f"""
            QComboBox {{
                background-color: {Theme.PRIMARY};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 1px 2px; /* 减小内边距到最小 */
                height: 24px;
                text-align: left;
                selection-background-color: {Theme.SELECTION};
            }}
            QComboBox:hover {{
                border: 1px solid {Theme.HOVER};
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 16px; /* 减小下拉按钮宽度 */
                border-left: 1px solid {Theme.BORDER};
                background-color: {Theme.PRIMARY};
            }}
            QComboBox::down-arrow {{
                image: url(:/icons/dropdown);
                width: 10px; /* 减小下拉箭头尺寸 */
                height: 10px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {Theme.PRIMARY};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                selection-background-color: {Theme.SELECTION};
                padding: 0px; /* 移除下拉列表内边距 */
            }}
        """)
        
        # 设置size policy，使得控件可以水平拉伸但垂直固定
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.setFixedHeight(26)  # 固定高度
        
        # 调整内容边距
        self.setContentsMargins(0, 0, 0, 0)
        
        # 设置项目内容显示位置，确保文本左对齐
        self.setEditable(False)  # 确保不可编辑状态
        self.setLayoutDirection(Qt.LayoutDirection.LeftToRight)  # 设置布局方向从左到右
        
        # 确保文本显示区域最大化
        self.setIconSize(QSize(0, 0))  # 不显示图标，最大化文本显示区域
        
    def wheelEvent(self, event):
        """
        重写滚轮事件，禁用通过滚轮切换选项的功能
        这样用户就不会在滚动页面时意外改变下拉框选项
        """
        event.ignore()  # 忽略滚轮事件


class SelectableLabel(QLabel):
    """一个支持文本选择和自定义右键菜单的QLabel子类, 左键点击打开URL"""
    def __init__(self, text="", parent=None, url=None):
        super().__init__(text, parent)
        self.url = url # Store the URL
        # 仅允许鼠标选择文本
        self.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)

    def mouseReleaseEvent(self, event):
        # Left click opens URL if one is set
        if event.button() == Qt.MouseButton.LeftButton and self.url:
            try:
                QDesktopServices.openUrl(QUrl(self.url))
                print(f"Mouse release opening URL: {self.url}") # Debug print
                event.accept() # Consume the event, prevent text selection start
            except Exception as e:
                print(f"Error opening URL on mouse release {self.url}: {e}")
                super().mouseReleaseEvent(event) # Allow base class handling on error
        else:
            # Let the base class handle other mouse buttons (e.g., right-click for context menu)
            # or if no URL is set.
            super().mouseReleaseEvent(event)

    def contextMenuEvent(self, event):
        """重写上下文菜单事件处理函数 - 只处理复制"""
        if not self.text():
             return

        context_menu = QMenu(self)
        context_menu.setStyleSheet(f"""
            QMenu {{
                background-color: #1A1D24; 
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px;
                font-weight: bold;
            }}
            QMenu::item {{
                padding: 5px 15px;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            QMenu::item:selected {{
                background-color: {Theme.ACCENT};
                color: white;
            }}
            QMenu::separator {{
                height: 1px;
                background-color: {Theme.BORDER};
                margin: 5px;
            }}
        """)

        # Always add "Copy Selected"
        copy_action = QAction("复制选中", self)
        copy_action.setEnabled(self.hasSelectedText())
        context_menu.addAction(copy_action)

        # Conditionally add "Copy Link" action
        copy_all_action = None # Initialize to None
        if self.url:
            copy_all_action = QAction("复制链接", self)
            context_menu.addAction(copy_all_action)
            # copy_all_action is implicitly enabled because self.url exists

        action = context_menu.exec(event.globalPos())

        clipboard = QGuiApplication.clipboard()
        if action == copy_action and self.hasSelectedText():
            clipboard.setText(self.selectedText())
        # Check if the action exists (i.e., self.url was present) and was triggered
        elif copy_all_action is not None and action == copy_all_action:
            clipboard.setText(self.url)