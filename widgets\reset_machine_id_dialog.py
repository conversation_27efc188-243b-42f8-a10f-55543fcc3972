#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重置机器码对话框模块
提供重置Cursor机器码的对话框界面，显示进度和日志
"""

import sys
import time
import requests
import threading

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton, QHBoxLayout, QMessageBox, 
    QTextEdit, QScrollArea, QWidget, QFrame, QPlainTextEdit, QSizePolicy, QApplication, QMenu
)
from PySide6.QtCore import Qt, Signal, QThread, QTimer, QTime
from PySide6.QtGui import QColor

from widgets.dialog import StyledDialog
from widgets.styled_widgets import StyledButton, StyledProgressBar
from widgets.toast import ToastMessage
from theme import Theme
from logger import info, warning, error, debug
from utils import get_app_data_dir
import os
import json


class ResetMachineIDWorker(QThread):
    """重置机器码的工作线程"""
    
    # 定义信号
    progress_updated = Signal(int, int)  # 参数为当前进度和总进度
    log_produced = Signal(str, str)  # 参数为日志文本和日志类型 (info, warning, error)
    operation_finished = Signal(bool, str)  # 参数为是否成功和结果信息
    
    def __init__(self):
        super().__init__()
        self.system_type = self._get_system_type()
        
    def _get_system_type(self):
        """获取系统类型"""
        import platform
        system = platform.system().lower()
        if "windows" in system:
            return "windows"
        elif "darwin" in system:
            return "mac"
        else:
            return "linux"
    
    def run(self):
        """线程主函数"""
        try:
            self.log_produced.emit("开始重置机器码...", "info")
            self.progress_updated.emit(0, 100)
            
            if self.system_type == "windows":
                success = self._reset_windows_machine_id()
            elif self.system_type == "mac":
                success = self._reset_mac_machine_id()
            else:  # linux
                success = self._reset_linux_machine_id()
                
            if success:
                self.log_produced.emit("机器码重置成功！", "info")
                self.operation_finished.emit(True, "机器码重置成功！")
            else:
                self.log_produced.emit("机器码重置失败！", "error")
                self.operation_finished.emit(False, "机器码重置失败！")
                
        except Exception as e:
            import traceback
            error_info = f"机器码重置过程中出现异常: {str(e)}\n{traceback.format_exc()}"
            self.log_produced.emit(error_info, "error")
            self.operation_finished.emit(False, f"重置失败: {str(e)}")
    
    def _reset_windows_machine_id(self):
        """重置Windows系统的机器码"""
        import os
        import subprocess
        import uuid
        import json
        import shutil
        import time
        import datetime
        import traceback
        
        try:
            # 进度更新
            self.progress_updated.emit(5, 100)
            self.log_produced.emit("正在检查系统...", "info")
            
            # 关闭Cursor进程
            if not self._kill_cursor_process():
                self.log_produced.emit("无法关闭Cursor进程，请手动关闭后重试", "error")
                return False
            
            # 进度更新
            self.progress_updated.emit(15, 100)
            self.log_produced.emit("正在备份配置文件...", "info")
            
            # 获取配置文件路径
            appdata = os.getenv("APPDATA")
            if appdata is None:
                self.log_produced.emit("APPDATA 环境变量未设置", "error")
                return False
            
            storage_file = os.path.join(appdata, "Cursor", "User", "globalStorage", "storage.json")
            backup_dir = os.path.join(appdata, "Cursor", "User", "globalStorage", "backups")
            
            # 创建备份目录
            os.makedirs(backup_dir, exist_ok=True)
            
            # 备份配置文件
            if os.path.exists(storage_file):
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = os.path.join(backup_dir, f"storage.json.backup_{timestamp}")
                
                try:
                    shutil.copy2(storage_file, backup_file)
                    self.log_produced.emit(f"配置文件已备份到: {backup_file}", "info")
                except Exception as e:
                    self.log_produced.emit(f"备份配置文件失败: {str(e)}", "error")
                    return False
            else:
                self.log_produced.emit("配置文件不存在，跳过备份", "warning")
            
            # 进度更新
            self.progress_updated.emit(25, 100)
            self.log_produced.emit("正在备份系统ID...", "info")
            
            # 备份系统ID
            try:
                import winreg
                
                # 打开注册表键
                registry_path = r"SOFTWARE\Microsoft\Cryptography"
                reg_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, registry_path, 0, winreg.KEY_READ)
                
                try:
                    # 读取MachineGuid值
                    machine_guid, _ = winreg.QueryValueEx(reg_key, "MachineGuid")
                    
                    # 创建备份文件
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_file = os.path.join(backup_dir, f"MachineGuid_{timestamp}.reg")
                    
                    # 使用reg.exe导出注册表项
                    subprocess.run([
                        "reg.exe", "export", 
                        r"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography", 
                        backup_file
                    ], check=False, capture_output=True)
                    
                    # 日志
                    self.log_produced.emit(f"系统ID已备份到: {backup_file}", "info")
                    self.log_produced.emit(f"当前MachineGuid: {machine_guid}", "info")
                    
                finally:
                    winreg.CloseKey(reg_key)
            except Exception as e:
                self.log_produced.emit(f"备份系统ID失败: {str(e)}", "error")
                # 继续执行，不中断流程
            
            # 进度更新
            self.progress_updated.emit(35, 100)
            self.log_produced.emit("正在修改系统ID...", "info")
            
            # 修改系统ID
            try:
                # 生成新的GUID
                new_guid = str(uuid.uuid4())
                
                # 使用reg.exe修改注册表
                subprocess.run(
                    ['reg', 'add', 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography', 
                     '/v', 'MachineGuid', '/t', 'REG_SZ', '/d', new_guid, '/f'],
                    check=True,
                    capture_output=True
                )
                
                self.log_produced.emit(f"系统ID已更新为: {new_guid}", "info")
            except Exception as e:
                self.log_produced.emit(f"修改系统ID失败: {str(e)}", "error")
                return False
            
            # 进度更新
            self.progress_updated.emit(50, 100)
            self.log_produced.emit("正在更新配置文件...", "info")
            
            # 更新配置文件
            try:
                if not os.path.exists(storage_file):
                    self.log_produced.emit(f"配置文件不存在: {storage_file}", "error")
                    return False
                
                # 读取当前配置
                with open(storage_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 生成新的ID
                # 将 auth0|user_ 转换为字节数组的十六进制
                prefix = "auth0|user_"
                prefix_hex = ''.join('{:02x}'.format(ord(c)) for c in prefix)
                random_part = self._generate_random_id()
                new_machine_id = prefix_hex + random_part
                new_mac_id = str(uuid.uuid4()).lower()
                new_device_id = str(uuid.uuid4()).lower()
                new_sqm_id = "{" + str(uuid.uuid4()).upper() + "}"
                
                # 更新配置
                config['telemetry.machineId'] = new_machine_id
                config['telemetry.macMachineId'] = new_mac_id
                config['telemetry.devDeviceId'] = new_device_id
                config['telemetry.sqmId'] = new_sqm_id
                
                # 获取文件当前权限
                current_mode = os.stat(storage_file).st_mode
                
                # 设置文件为可写
                os.chmod(storage_file, 0o644)
                
                # 写入新配置
                with open(storage_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2)
                
                # 恢复原始权限
                os.chmod(storage_file, current_mode)
                
                self.log_produced.emit("配置文件更新成功", "info")
                self.log_produced.emit(f"新的machineId: {new_machine_id}", "info")
                self.log_produced.emit(f"新的macMachineId: {new_mac_id}", "info")
                self.log_produced.emit(f"新的devDeviceId: {new_device_id}", "info")
                self.log_produced.emit(f"新的sqmId: {new_sqm_id}", "info")
                
            except Exception as e:
                self.log_produced.emit(f"更新配置文件失败: {str(e)}", "error")
                return False
            
            # 进度更新
            self.progress_updated.emit(75, 100)
            self.log_produced.emit("正在清除DNS缓存...", "info")
            
            # 清除DNS缓存
            try:
                subprocess.run(["ipconfig", "/flushdns"], check=True, capture_output=True)
                self.log_produced.emit("DNS缓存已清除", "info")
            except Exception as e:
                self.log_produced.emit(f"清除DNS缓存失败: {str(e)}", "warning")
                # 继续执行，不中断流程
            
            # 进度更新
            self.progress_updated.emit(100, 100)
            self.log_produced.emit("重置机器码完成!", "info")
            
            return True
            
        except Exception as e:
            self.log_produced.emit(f"重置机器码过程中出现错误: {str(e)}", "error")
            self.log_produced.emit(traceback.format_exc(), "error")
            return False
    
    def _kill_cursor_process(self):
        """关闭Cursor进程"""
        import psutil
        import time
        import os
        
        self.log_produced.emit("检查 Cursor 进程...", "info")
        
        try:
            current_pid = os.getpid()  # 获取当前脚本的进程ID
            
            # 定义最大重试次数和等待时间
            max_retries = 5
            wait_time = 1
            
            def is_cursor_editor_process(proc):
                """判断是否为Cursor编辑器进程"""
                try:
                    # 检查进程名称
                    if not proc.name().lower().startswith('cursor'):
                        return False
                    
                    # 检查可执行文件路径
                    exe_path = proc.exe().lower()
                    if not ('\\cursor.exe' in exe_path or '\\cursor\\cursor.exe' in exe_path):
                        return False
                    
                    # 检查命令行参数
                    cmdline = ' '.join(proc.cmdline()).lower()
                    if 'cursor_change_id' in cmdline or 'cursor-auto' in cmdline:
                        return False
                    
                    # 检查是否为当前进程或其子进程
                    if proc.pid == current_pid or proc.ppid() == current_pid:
                        return False
                    
                    return True
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    return False
            
            for attempt in range(max_retries):
                # 查找Cursor编辑器进程
                cursor_processes = [p for p in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']) 
                                 if is_cursor_editor_process(p)]
                
                if not cursor_processes:
                    if attempt == 0:
                        self.log_produced.emit("未发现运行中的 Cursor 编辑器进程", "info")
                    break
                
                # 如果找到了进程
                if attempt == 0:
                    self.log_produced.emit(f"发现 {len(cursor_processes)} 个 Cursor 编辑器进程正在运行", "warning")
                    self.log_produced.emit("尝试关闭 Cursor 编辑器进程...", "warning")
                
                # 尝试终止所有匹配的进程
                for proc in cursor_processes:
                    try:
                        if attempt == max_retries - 1:
                            proc.kill()  # 最后一次尝试时使用强制终止
                        else:
                            proc.terminate()  # 正常终止
                    except:
                        pass
                
                # 等待一段时间
                time.sleep(wait_time)
                
                # 检查进程是否仍然存在
                cursor_processes = [p for p in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']) 
                                 if is_cursor_editor_process(p)]
                
                if not cursor_processes:
                    self.log_produced.emit("Cursor 编辑器进程已成功关闭", "info")
                    break
                
                if attempt < max_retries - 1:
                    self.log_produced.emit(f"等待进程关闭，尝试 {attempt+1}/{max_retries}...", "warning")
                else:
                    self.log_produced.emit("无法关闭 Cursor 编辑器进程，请手动关闭后重试", "error")
                    return False
            
            return True
            
        except Exception as e:
            self.log_produced.emit(f"处理进程时出错: {str(e)}", "error")
            return False
    
    def _generate_random_id(self):
        """生成随机ID"""
        import os
        import random
        import string
        
        # 生成随机字节并转为十六进制字符串
        random_bytes = os.urandom(32)  # 32字节 = 64位十六进制
        return ''.join('{:02x}'.format(b) for b in random_bytes)
    
    def _reset_mac_machine_id(self):
        """重置Mac系统的机器码，使用id_mac.sh的逻辑实现"""
        import os
        import subprocess
        import uuid
        import json
        import shutil
        import time
        import datetime
        import tempfile
        import traceback
        
        try:
            # 获取当前用户
            current_user = os.environ.get('USER')
            
            # 定义配置文件路径
            storage_file = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")
            backup_dir = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/backups")
            cursor_app_path = "/Applications/Cursor.app"
            
            # 进度更新
            self.progress_updated.emit(5, 100)
            self.log_produced.emit("开始重置机器码，请稍候...", "info")
            
            # 检查并关闭Cursor进程
            self.log_produced.emit("检查 Cursor 进程...", "info")
            if not self._kill_cursor_process_mac():
                self.log_produced.emit("无法关闭Cursor进程，请手动关闭后重试", "error")
                return False
            
            # 进度更新
            self.progress_updated.emit(20, 100)
            
            # 备份配置文件
            self.log_produced.emit("正在备份配置文件...", "info")
            os.makedirs(backup_dir, exist_ok=True)
            
            if os.path.exists(storage_file):
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = os.path.join(backup_dir, f"storage.json.backup_{timestamp}")
                
                try:
                    shutil.copy2(storage_file, backup_file)
                    # 设置权限
                    os.chmod(backup_file, 0o644)
                    self.log_produced.emit(f"配置已备份到: {backup_file}", "info")
                except Exception as e:
                    self.log_produced.emit(f"备份失败: {str(e)}", "error")
                    return False
            else:
                self.log_produced.emit("配置文件不存在，跳过备份", "warning")
            
            # 进度更新
            self.progress_updated.emit(30, 100)
            
            # 备份系统ID
            self.log_produced.emit("正在备份系统ID...", "info")
            system_id_file = os.path.join(backup_dir, f"system_id.backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            try:
                # 创建备份文件并写入信息
                with open(system_id_file, 'w', encoding='utf-8') as f:
                    f.write(f"# Original System ID Backup - {datetime.datetime.now()}\n")
                    f.write("## IOPlatformExpertDevice Info:\n")
                    
                    try:
                        ioreg_output = subprocess.check_output(
                            ["ioreg", "-rd1", "-c", "IOPlatformExpertDevice"],
                            universal_newlines=True
                        )
                        f.write(ioreg_output)
                    except Exception as ioreg_error:
                        f.write(f"Error getting IOPlatformExpertDevice info: {str(ioreg_error)}\n")
                
                # 设置文件权限
                os.chmod(system_id_file, 0o444)  # 设为只读
                self.log_produced.emit(f"系统ID已备份到: {system_id_file}", "info")
                
            except Exception as e:
                self.log_produced.emit(f"备份系统ID失败: {str(e)}", "error")
                # 继续执行，不中断流程
            
            # 进度更新
            self.progress_updated.emit(40, 100)
            
            # 修改系统ID
            self.log_produced.emit("正在修改系统ID...", "info")
            
            # 生成新的系统UUID
            new_system_uuid = str(uuid.uuid4())
            
            try:
                # 使用nvram修改系统UUID
                subprocess.run(["sudo", "nvram", f"SystemUUID={new_system_uuid}"], check=True)
                self.log_produced.emit(f"系统UUID已更新为: {new_system_uuid}", "info")
                self.log_produced.emit("请重启系统以使更改生效", "warning")
            except Exception as e:
                self.log_produced.emit(f"修改系统UUID失败: {str(e)}", "error")
                # 继续执行
            
            # 进度更新
            self.progress_updated.emit(60, 100)
            self.log_produced.emit("正在修改配置文件...", "info")
            
            # 准备生成新ID
            # 生成auth0|user_前缀的十六进制
            prefix_hex = ''.join('{:02x}'.format(ord(c)) for c in "auth0|user_")
            random_part = self._generate_random_id()
            machine_id = prefix_hex + random_part
            
            mac_machine_id = self._generate_random_id()
            device_id = str(uuid.uuid4()).lower()
            sqm_id = "{" + str(uuid.uuid4()).upper() + "}"
            
            # 检查并修改配置文件
            if os.path.exists(storage_file):
                # 确保文件可写
                try:
                    os.chmod(storage_file, 0o644)
                except Exception as e:
                    self.log_produced.emit(f"无法修改文件权限: {str(e)}", "error")
                    return False
                
                # 读取原始配置
                try:
                    with open(storage_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                except Exception as e:
                    self.log_produced.emit(f"读取配置文件失败: {str(e)}", "error")
                    return False
                
                # 更新配置
                config['telemetry.machineId'] = machine_id
                config['telemetry.macMachineId'] = mac_machine_id
                config['telemetry.devDeviceId'] = device_id
                config['telemetry.sqmId'] = sqm_id
                
                # 创建临时文件
                with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp:
                    # 写入更新后的JSON
                    json.dump(config, temp, indent=4)
                    temp_name = temp.name
                
                # 复制临时文件到原始位置
                shutil.copy2(temp_name, storage_file)
                
                # 删除临时文件
                os.unlink(temp_name)
                
                # 设置文件权限
                os.chmod(storage_file, 0o444)  # 设为只读
                
                self.log_produced.emit("配置文件更新成功", "info")
                self.log_produced.emit(f"新的machineId: {machine_id}", "info")
                self.log_produced.emit(f"新的macMachineId: {mac_machine_id}", "info")
                self.log_produced.emit(f"新的devDeviceId: {device_id}", "info")
                self.log_produced.emit(f"新的sqmId: {sqm_id}", "info")
            else:
                self.log_produced.emit(f"配置文件不存在: {storage_file}", "error")
                return False
            
            # 进度更新
            self.progress_updated.emit(80, 100)
            
            # 修改应用程序文件 - 调整Cursor主程序文件中UUID生成逻辑
            self.log_produced.emit("正在修改Cursor主程序文件...", "info")
            
            if not os.path.exists(cursor_app_path):
                self.log_produced.emit(f"未找到Cursor应用: {cursor_app_path}", "error")
                return False
            
            # 创建临时工作目录
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_dir = f"/tmp/cursor_reset_{timestamp}"
            temp_app = f"{temp_dir}/Cursor.app"
            backup_app = f"/tmp/Cursor.app.backup_{timestamp}"
            
            # 创建临时目录
            os.makedirs(temp_dir, exist_ok=True)
            
            # 备份原应用
            self.log_produced.emit("备份原应用...", "info")
            try:
                subprocess.run(["cp", "-R", cursor_app_path, backup_app], check=True)
            except Exception as e:
                self.log_produced.emit(f"无法创建应用备份: {str(e)}", "error")
                shutil.rmtree(temp_dir, ignore_errors=True)
                return False
            
            # 复制应用到临时目录
            self.log_produced.emit("创建临时工作副本...", "info")
            try:
                subprocess.run(["cp", "-R", cursor_app_path, temp_dir], check=True)
            except Exception as e:
                self.log_produced.emit(f"无法复制应用到临时目录: {str(e)}", "error")
                shutil.rmtree(temp_dir, ignore_errors=True)
                shutil.rmtree(backup_app, ignore_errors=True)
                return False
            
            # 移除签名
            self.log_produced.emit("移除应用签名...", "info")
            try:
                subprocess.run(["codesign", "--remove-signature", temp_app], check=False)
                
                # 移除所有相关组件签名
                components = [
                    f"{temp_app}/Contents/Frameworks/Cursor Helper.app",
                    f"{temp_app}/Contents/Frameworks/Cursor Helper (GPU).app",
                    f"{temp_app}/Contents/Frameworks/Cursor Helper (Plugin).app",
                    f"{temp_app}/Contents/Frameworks/Cursor Helper (Renderer).app"
                ]
                
                for component in components:
                    if os.path.exists(component):
                        subprocess.run(["codesign", "--remove-signature", component], check=False)
            except Exception as e:
                self.log_produced.emit(f"移除签名失败: {str(e)}", "warning")
                # 继续执行
            
            # 修改目标文件
            modified_count = 0
            target_files = [
                f"{temp_app}/Contents/Resources/app/out/main.js",
                f"{temp_app}/Contents/Resources/app/out/vs/code/node/cliProcessMain.js"
            ]
            
            for file_path in target_files:
                if not os.path.exists(file_path):
                    self.log_produced.emit(f"文件不存在: {os.path.basename(file_path)}", "warning")
                    continue
                
                self.log_produced.emit(f"处理文件: {os.path.basename(file_path)}", "info")
                
                # 创建文件备份
                shutil.copy2(file_path, f"{file_path}.bak")
                
                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 查找关键位置并修改
                uuid_pos = content.find("IOPlatformUUID")
                if uuid_pos == -1:
                    self.log_produced.emit(f"在 {os.path.basename(file_path)} 中未找到 IOPlatformUUID", "warning")
                    continue
                
                # 从UUID位置向前查找switch
                before_uuid = content[:uuid_pos]
                switch_pos = before_uuid.rfind("switch")
                if switch_pos == -1:
                    self.log_produced.emit(f"在 {os.path.basename(file_path)} 中未找到 switch 关键字", "warning")
                    continue
                
                # 插入代码以生成随机UUID
                new_content = content[:switch_pos] + "return crypto.randomUUID();\n" + content[switch_pos:]
                
                # 写入修改后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                modified_count += 1
                self.log_produced.emit(f"成功修改文件: {os.path.basename(file_path)}", "info")
            
            if modified_count == 0:
                self.log_produced.emit("未能成功修改任何文件", "error")
                shutil.rmtree(temp_dir, ignore_errors=True)
                return False
            
            # 重新签名应用
            self.log_produced.emit("正在签名应用...", "info")
            
            max_retry = 3
            sign_success = False
            
            for retry in range(max_retry):
                self.log_produced.emit(f"尝试签名 (第 {retry+1} 次)...", "info")
                
                try:
                    subprocess.run(
                        ["codesign", "--sign", "-", "--force", "--deep", "--preserve-metadata=entitlements,identifier,flags", temp_app],
                        check=True, capture_output=True
                    )
                    
                    # 验证签名
                    verification = subprocess.run(
                        ["codesign", "--verify", "-vvvv", temp_app],
                        check=False, capture_output=True
                    )
                    
                    if verification.returncode == 0:
                        sign_success = True
                        self.log_produced.emit("应用签名验证通过", "info")
                        break
                    else:
                        self.log_produced.emit("签名验证失败，将重试", "warning")
                
                except Exception as e:
                    self.log_produced.emit(f"签名失败: {str(e)}", "warning")
                
                time.sleep(1)
            
            if not sign_success:
                self.log_produced.emit(f"经过 {max_retry} 次尝试仍无法完成签名", "error")
                self.log_produced.emit("无法完成修改", "error")
                shutil.rmtree(temp_dir, ignore_errors=True)
                return False
            
            # 替换原应用
            self.log_produced.emit("安装修改版应用...", "info")
            
            try:
                # 删除原应用
                subprocess.run(["sudo", "rm", "-rf", cursor_app_path], check=True)
                # 复制修改后的应用
                subprocess.run(["sudo", "cp", "-R", temp_app, "/Applications/"], check=True)
                # 设置权限
                subprocess.run(["sudo", "chown", "-R", f"{current_user}:staff", cursor_app_path], check=True)
                subprocess.run(["sudo", "chmod", "-R", "755", cursor_app_path], check=True)
                
                self.log_produced.emit("Cursor主程序文件修改完成！", "info")
            except Exception as e:
                self.log_produced.emit(f"应用替换失败: {str(e)}", "error")
                self.log_produced.emit("正在恢复原应用...", "warning")
                
                try:
                    subprocess.run(["sudo", "rm", "-rf", cursor_app_path], check=False)
                    subprocess.run(["sudo", "cp", "-R", backup_app, "/Applications/"], check=False)
                except:
                    pass
                
                shutil.rmtree(temp_dir, ignore_errors=True)
                return False
            
            # 清理临时文件
            shutil.rmtree(temp_dir, ignore_errors=True)
            shutil.rmtree(backup_app, ignore_errors=True)
            
            # 进度更新
            self.progress_updated.emit(90, 100)
            
            # 清除DNS缓存
            self.log_produced.emit("正在清除DNS缓存...", "info")
            
            try:
                # 首先执行dscacheutil命令
                subprocess.run(["dscacheutil", "-flushcache"], check=False)
                
                # 然后重启mDNSResponder服务
                subprocess.run(["sudo", "killall", "-HUP", "mDNSResponder"], check=False)
                
                self.log_produced.emit("DNS缓存已清除", "info")
            except Exception as e:
                self.log_produced.emit(f"清除DNS缓存失败: {str(e)}", "warning")
                # 继续执行，不中断流程
            
            # 完成
            self.progress_updated.emit(100, 100)
            self.log_produced.emit("重置机器码完成!", "info")
            
            return True
            
        except Exception as e:
            self.log_produced.emit(f"重置机器码过程中出现错误: {str(e)}", "error")
            self.log_produced.emit(traceback.format_exc(), "error")
            return False
    
    def _kill_cursor_process_mac(self):
        """关闭Mac系统下的Cursor进程"""
        import subprocess
        import time
        
        self.log_produced.emit("检查 Cursor 进程...", "info")
        
        try:
            # 定义最大重试次数和等待时间
            max_attempts = 5
            wait_time = 1
            
            for attempt in range(max_attempts):
                # 使用ps查找Cursor进程
                cursor_pids = []
                try:
                    ps_output = subprocess.check_output(
                        ["ps", "aux"], universal_newlines=True
                    )
                    for line in ps_output.split('\n'):
                        if "/Applications/Cursor.app" in line and 'grep' not in line:
                            parts = line.split()
                            if len(parts) > 1:
                                cursor_pids.append(parts[1])  # 获取PID
                except:
                    pass
                
                if not cursor_pids:
                    self.log_produced.emit("未发现运行中的 Cursor 进程", "info")
                    return True
                
                if attempt == 0:
                    self.log_produced.emit("发现 Cursor 进程正在运行", "warning")
                    self.log_produced.emit("尝试关闭 Cursor 进程...", "warning")
                
                # 尝试终止进程
                if attempt == max_attempts - 1:
                    # 最后一次尝试使用SIGKILL
                    self.log_produced.emit("尝试强制终止进程...", "warning")
                    for pid in cursor_pids:
                        if pid.strip():
                            try:
                                subprocess.run(["kill", "-9", pid], stderr=subprocess.DEVNULL)
                            except:
                                pass
                else:
                    # 使用正常的SIGTERM
                    for pid in cursor_pids:
                        try:
                            subprocess.run(["kill", pid], stderr=subprocess.DEVNULL)
                        except:
                            pass
                
                # 等待进程退出
                time.sleep(wait_time)
                
                # 检查进程是否还在运行
                still_running = False
                try:
                    ps_output = subprocess.check_output(
                        ["ps", "aux"], universal_newlines=True
                    )
                    for line in ps_output.split('\n'):
                        if "/Applications/Cursor.app" in line and 'grep' not in line:
                            still_running = True
                            break
                except:
                    pass
                
                if not still_running:
                    self.log_produced.emit("Cursor 进程已成功关闭", "info")
                    return True
                
                self.log_produced.emit(f"等待进程关闭，尝试 {attempt+1}/{max_attempts}...", "warning")
            
            # 最后检查
            try:
                ps_output = subprocess.check_output(
                    ["ps", "aux"], universal_newlines=True
                )
                for line in ps_output.split('\n'):
                    if "/Applications/Cursor.app" in line and 'grep' not in line:
                        self.log_produced.emit(f"在 {max_attempts} 次尝试后仍无法关闭 Cursor 进程", "error")
                        self.log_produced.emit("请手动关闭进程后重试", "error")
                        return False
            except:
                pass
            
            return True
                
        except Exception as e:
            self.log_produced.emit(f"处理进程时出错: {str(e)}", "error")
            return False
    
    def _reset_linux_machine_id(self):
        """重置Linux系统的机器码，使用id_linux.sh的逻辑实现"""
        import os
        import subprocess
        import uuid
        import json
        import shutil
        import time
        import datetime
        import tempfile
        import traceback
        
        try:
            # 进度更新
            self.progress_updated.emit(5, 100)
            self.log_produced.emit("正在检查系统...", "info")
            
            # 检查是否为root权限
            if os.geteuid() != 0:
                self.log_produced.emit("需要root权限才能修改系统ID", "error")
                self.log_produced.emit("请使用sudo运行此程序", "error")
                return False
            
            # 查找Cursor安装目录
            def find_cursor_dir():
                possible_dirs = [
                    "/opt/Cursor",
                    "/usr/lib/cursor",
                    os.path.expanduser("~/.cursor")
                ]
                
                for dir_path in possible_dirs:
                    if os.path.isdir(dir_path):
                        return dir_path
                
                self.log_produced.emit("无法找到Cursor安装目录", "error")
                return None
            
            cursor_dir = find_cursor_dir()
            if not cursor_dir:
                return False
            
            self.log_produced.emit(f"找到Cursor安装目录: {cursor_dir}", "info")
            
            # 进度更新
            self.progress_updated.emit(15, 100)
            
            # 关闭Cursor进程
            if not self._kill_cursor_process_linux():
                self.log_produced.emit("无法关闭Cursor进程，请手动关闭后重试", "error")
                return False
            
            # 进度更新
            self.progress_updated.emit(30, 100)
            self.log_produced.emit("正在备份配置文件...", "info")
            
            # 获取当前用户和用户主目录
            current_user = os.environ.get('USER') or os.environ.get('SUDO_USER') or subprocess.getoutput("logname")
            user_home = os.path.expanduser(f"~{current_user}")
            
            # 配置文件路径
            cursor_dir_name = os.path.basename(cursor_dir)
            config_dir = os.path.join(user_home, ".config", cursor_dir_name, "User", "globalStorage")
            storage_file = os.path.join(config_dir, "storage.json")
            backup_dir = os.path.join(config_dir, "backups")
            
            # 创建备份目录
            os.makedirs(backup_dir, exist_ok=True)
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 设置目录权限
            try:
                # 确保目录权限正确
                subprocess.run(["chown", "-R", f"{current_user}:{current_user}", config_dir], check=False)
                subprocess.run(["chmod", "-R", "755", config_dir], check=False)
            except Exception as e:
                self.log_produced.emit(f"设置目录权限失败: {str(e)}", "warning")
            
            # 备份配置文件
            if os.path.exists(storage_file):
                backup_storage = os.path.join(backup_dir, f"storage.json.bak_{timestamp}")
                try:
                    shutil.copy2(storage_file, backup_storage)
                    subprocess.run(["chmod", "644", backup_storage], check=False)
                    self.log_produced.emit(f"配置已备份: {backup_storage}", "info")
                except Exception as e:
                    self.log_produced.emit(f"备份配置文件失败: {str(e)}", "error")
                    return False
            
            # 备份machine-id
            machine_id_path = "/etc/machine-id"
            if os.path.exists(machine_id_path):
                backup_machine_id = os.path.join(backup_dir, f"machine-id.bak_{timestamp}")
                try:
                    shutil.copy2(machine_id_path, backup_machine_id)
                    subprocess.run(["chmod", "644", backup_machine_id], check=False)
                    self.log_produced.emit("创建了备份机器 ID", "info")
                except Exception as e:
                    self.log_produced.emit(f"备份machine-id失败: {str(e)}", "warning")
            
            # 进度更新
            self.progress_updated.emit(50, 100)
            self.log_produced.emit("正在生成新的ID...", "info")
            
            # 生成新的ID
            new_machine_id = str(uuid.uuid4()).replace("-", "")
            new_device_id = str(uuid.uuid4())
            new_sqm_id = "{" + str(uuid.uuid4()).upper() + "}"
            
            self.log_produced.emit("生成新的 ID:", "info")
            self.log_produced.emit(f"Machine ID: {new_machine_id}", "info")
            self.log_produced.emit(f"Device ID: {new_device_id}", "info")
            self.log_produced.emit(f"SQM ID: {new_sqm_id}", "info")
            
            # 确保配置目录存在
            if not os.path.isdir(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            # 移除任何现有的不可修改属性
            if os.path.exists(storage_file):
                try:
                    subprocess.run(["chattr", "-i", storage_file], check=False, stderr=subprocess.DEVNULL)
                except Exception:
                    pass
            
            # 设置正确的所有权和权限
            try:
                subprocess.run(["chown", "-R", f"{current_user}:{current_user}", config_dir], check=False)
                subprocess.run(["chmod", "-R", "755", config_dir], check=False)
            except Exception as e:
                self.log_produced.emit(f"设置目录权限失败: {str(e)}", "warning")
            
            # 如果配置文件不存在，创建一个空的JSON结构
            if not os.path.exists(storage_file):
                with open(storage_file, "w") as f:
                    f.write("{}")
            
            # 修改/etc/machine-id
            try:
                with open("/etc/machine-id", "w") as f:
                    f.write(new_machine_id[:32])
                self.log_produced.emit(f"已更新系统 machine-id: {new_machine_id[:32]}", "info")
            except Exception as e:
                self.log_produced.emit(f"修改machine-id失败: {str(e)}", "error")
                return False
            
            # 修改dbus machine-id (如果存在)
            dbus_machine_id = "/var/lib/dbus/machine-id"
            if os.path.exists(dbus_machine_id):
                try:
                    with open(dbus_machine_id, "w") as f:
                        f.write(new_machine_id[:32])
                    self.log_produced.emit(f"已更新 dbus machine-id: {new_machine_id[:32]}", "info")
                except Exception as e:
                    self.log_produced.emit(f"修改dbus machine-id失败: {str(e)}", "warning")
            
            # 进度更新
            self.progress_updated.emit(75, 100)
            self.log_produced.emit("正在更新配置文件...", "info")
            
            # 更新配置文件
            # 首先检查jq是否可用
            try:
                subprocess.run(["jq", "--version"], check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                use_jq = True
            except (subprocess.SubprocessError, FileNotFoundError):
                use_jq = False
            
            if use_jq:
                # 使用jq更新配置文件
                temp_file = tempfile.mktemp()
                try:
                    jq_cmd = [
                        "jq",
                        f'--arg machine "{new_machine_id}"',
                        f'--arg device "{new_device_id}"',
                        f'--arg sqm "{new_sqm_id}"',
                        '.telemetry.machineId = $machine | .telemetry.devDeviceId = $device | .telemetry.sqmId = $sqm',
                        storage_file
                    ]
                    
                    with open(temp_file, 'w') as outfile:
                        subprocess.run(" ".join(jq_cmd), shell=True, stdout=outfile, check=True)
                    
                    # 检查临时文件是否为空
                    if os.path.getsize(temp_file) == 0:
                        raise Exception("jq生成的文件为空")
                    
                    # 移动临时文件到目标位置
                    shutil.move(temp_file, storage_file)
                except Exception as e:
                    self.log_produced.emit(f"使用jq更新配置文件失败: {str(e)}", "warning")
                    if os.path.exists(temp_file):
                        os.unlink(temp_file)
                    use_jq = False
            
            if not use_jq:
                # 如果jq不可用，使用Python的json模块
                try:
                    with open(storage_file, 'r') as f:
                        config = json.load(f)
                    
                    # 更新配置
                    if 'telemetry' not in config:
                        config['telemetry'] = {}
                    
                    config['telemetry']['machineId'] = new_machine_id
                    config['telemetry']['devDeviceId'] = new_device_id
                    config['telemetry']['sqmId'] = new_sqm_id
                    
                    # 写回文件
                    with open(storage_file, 'w') as f:
                        json.dump(config, f, indent=2)
                    
                    self.log_produced.emit("使用Python更新了配置文件", "info")
                except Exception as e:
                    self.log_produced.emit(f"更新配置文件失败: {str(e)}", "error")
                    return False
            
            # 设置文件权限
            try:
                subprocess.run(["chmod", "644", storage_file], check=True)
                subprocess.run(["chown", f"{current_user}:{current_user}", storage_file], check=True)
                
                # 确保文件可写
                subprocess.run(["chattr", "-i", storage_file], check=False, stderr=subprocess.DEVNULL)
            except Exception as e:
                self.log_produced.emit(f"设置文件权限失败: {str(e)}", "warning")
            
            # 进度更新
            self.progress_updated.emit(90, 100)
            self.log_produced.emit("正在清除DNS缓存...", "info")
            
            # 清除DNS缓存 - 尝试多种方法
            dns_methods = [
                ["systemd-resolve", "--flush-caches"],
                ["service", "nscd", "restart"],
                ["service", "dnsmasq", "restart"],
                ["service", "NetworkManager", "restart"]
            ]
            
            success = False
            for cmd in dns_methods:
                try:
                    result = subprocess.run(cmd, stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
                    if result.returncode == 0:
                        self.log_produced.emit(f"成功执行: {' '.join(cmd)}", "info")
                        success = True
                        break
                except:
                    continue
            
            if success:
                self.log_produced.emit("DNS缓存已清除", "info")
            else:
                self.log_produced.emit("未能找到适用的DNS缓存清除方法", "warning")
            
            # 进度更新
            self.progress_updated.emit(100, 100)
            self.log_produced.emit("操作完成! 请重新启动Cursor", "info")
            
            return True
            
        except Exception as e:
            self.log_produced.emit(f"重置机器码过程中出现错误: {str(e)}", "error")
            self.log_produced.emit(traceback.format_exc(), "error")
            return False
    
    def _kill_cursor_process_linux(self):
        """关闭Linux系统下的Cursor进程"""
        import subprocess
        import time
        
        self.log_produced.emit("检查 Cursor 进程...", "info")
        
        try:
            # 定义最大重试次数和等待时间
            max_attempts = 5
            wait_time = 1
            
            for attempt in range(max_attempts):
                # 使用pgrep查找Cursor进程
                try:
                    cursor_pids_output = subprocess.check_output(
                        ["pgrep", "-i", "cursor"], universal_newlines=True, stderr=subprocess.DEVNULL
                    ).strip()
                    cursor_pids = cursor_pids_output.split('\n') if cursor_pids_output else []
                except subprocess.CalledProcessError:
                    # 如果没有找到进程，pgrep会返回非零退出码
                    cursor_pids = []
                
                if not cursor_pids:
                    self.log_produced.emit("未发现运行中的 Cursor 进程", "info")
                    return True
                
                if attempt == 0:
                    self.log_produced.emit("发现 Cursor 进程正在运行", "warning")
                    self.log_produced.emit("尝试关闭 Cursor 进程...", "warning")
                
                # 尝试终止进程
                if attempt == max_attempts - 1:
                    # 最后一次尝试使用SIGKILL
                    self.log_produced.emit("尝试强制终止进程...", "warning")
                    for pid in cursor_pids:
                        if pid.strip():
                            try:
                                subprocess.run(["kill", "-9", pid.strip()], stderr=subprocess.DEVNULL)
                            except:
                                pass
                else:
                    # 使用正常的SIGTERM
                    for pid in cursor_pids:
                        if pid.strip():
                            try:
                                subprocess.run(["kill", pid.strip()], stderr=subprocess.DEVNULL)
                            except:
                                pass
                
                # 等待进程退出
                time.sleep(wait_time)
                
                # 检查进程是否还在运行
                try:
                    subprocess.check_output(["pgrep", "-i", "cursor"], stderr=subprocess.DEVNULL)
                    # 如果没有抛出异常，表示进程仍在运行
                    self.log_produced.emit(f"等待进程关闭，尝试 {attempt+1}/{max_attempts}...", "warning")
                except subprocess.CalledProcessError:
                    # 进程已经退出
                    self.log_produced.emit("Cursor 进程已成功关闭", "info")
                    return True
            
            # 最后检查
            try:
                subprocess.check_output(["pgrep", "-i", "cursor"], stderr=subprocess.DEVNULL)
                self.log_produced.emit(f"在 {max_attempts} 次尝试后仍无法关闭 Cursor 进程", "error")
                self.log_produced.emit("请手动关闭进程后重试", "error")
                return False
            except subprocess.CalledProcessError:
                # 进程已经退出
                self.log_produced.emit("Cursor 进程已成功关闭", "info")
                return True
                
        except Exception as e:
            self.log_produced.emit(f"处理进程时出错: {str(e)}", "error")
            return False


class ResetMachineIDDialog(StyledDialog):
    """重置机器码对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent, "重置机器码", 500)
        self.worker = None
        self.log_text_edit = None
        self.progress_bar = None
        self.toast = None
        
        # 初始化UI
        self.init_ui()
        
        # 添加浮动提示
        self.add_floating_tip("按 Esc键 关闭对话框")
        
        # 自动开始重置流程（如果不需要自动开始，请注释掉此行）
        QTimer.singleShot(100, self.start_reset)
        
    def init_ui(self):
        """初始化UI"""
        # 创建日志文本框 - 直接添加到对话框，减少嵌套
        self.log_text_edit = QPlainTextEdit()
        self.log_text_edit.setReadOnly(True)
        self.log_text_edit.setLineWrapMode(QPlainTextEdit.LineWrapMode.WidgetWidth)
        self.log_text_edit.setMinimumHeight(200)
        self.log_text_edit.setStyleSheet(f"""
            QPlainTextEdit {{
                background-color: #0e1015;
                color: {Theme.TEXT_PRIMARY};
                border-radius: 10px;
                padding: 10px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 13px;
                border: none;
                selection-background-color: {Theme.ACCENT};
                selection-color: white;
            }}
            QScrollBar:vertical {{
                background-color: transparent;
                width: 8px;
                margin: 2px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #3a3f4c;
                min-height: 20px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {Theme.ACCENT};
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical,
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
                border: none;
                height: 0px;
            }}
        """)
        
        # 启用右键菜单
        self.log_text_edit.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.log_text_edit.customContextMenuRequested.connect(self._show_log_context_menu)
        
        # 直接添加到对话框
        self.addWidget(self.log_text_edit)
        
        # 添加进度条 - 更纤细现代的样式
        self.progress_bar = StyledProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setFixedHeight(20)  # 增加进度条高度到25px
        self.progress_bar.setFormat("")  # 移除百分比文本
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                background-color: #1a1e24;
                border-radius: 12px;
                text-align: center;
                color: transparent;
            }}
            
            QProgressBar::chunk {{
                background-color: {Theme.ACCENT};
                border-radius: 12px;
            }}
        """)
        
        # 直接添加到对话框
        self.addWidget(self.progress_bar)
    
    def _show_log_context_menu(self, position):
        """显示日志文本区域的自定义上下文菜单"""
        try:
            # 创建自定义上下文菜单
            context_menu = QMenu(self)
            
            # 设置菜单样式与应用的暗色主题匹配
            context_menu.setStyleSheet(f"""
                QMenu {{
                    background-color: #1A1D24;
                    color: {Theme.TEXT_PRIMARY};
                    border: 1px solid {Theme.BORDER};
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    padding: 5px;
                    font-weight: bold;
                }}
                QMenu::item {{
                    padding: 5px 15px;
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                }}
                QMenu::item:selected {{
                    background-color: {Theme.ACCENT};
                    color: white;
                }}
                QMenu::separator {{
                    height: 1px;
                    background-color: {Theme.BORDER};
                    margin: 5px;
                }}
            """)
            
            # 添加菜单项
            copy_action = context_menu.addAction("复制")
            select_all_action = context_menu.addAction("全选")
            
            context_menu.addSeparator()
            
            copy_all_action = context_menu.addAction("复制全部日志")
            
            # 获取选中的文本
            has_selection = self.log_text_edit.textCursor().hasSelection()
            
            # 根据是否有选中文本启用/禁用复制操作
            copy_action.setEnabled(has_selection)
            
            # 显示菜单并处理选择
            action = context_menu.exec(self.log_text_edit.mapToGlobal(position))
            
            # 处理动作
            if action == copy_action and has_selection:
                # 复制选中文本
                clipboard = QApplication.clipboard()
                clipboard.setText(self.log_text_edit.textCursor().selectedText())
            elif action == select_all_action:
                # 全选
                self.log_text_edit.selectAll()
            elif action == copy_all_action:
                # 复制全部文本
                clipboard = QApplication.clipboard()
                clipboard.setText(self.log_text_edit.toPlainText())
                self.show_toast("已复制全部日志内容")
                
        except Exception as e:
            print(f"显示上下文菜单时出错: {str(e)}")
    
    def add_log(self, text, log_type="info"):
        """添加日志"""
        # 获取时间戳
        timestamp = QTime.currentTime().toString("hh:mm:ss")
        timestamp_text = f"[{timestamp}] "
        
        # 根据日志类型设置前缀和颜色
        if log_type == "error":
            color = QColor(Theme.ERROR)
            prefix = "❌ "
            # 为错误消息添加更明显的样式
            formatted_text = f"{timestamp_text}{prefix}{text}"
            
            # 使用HTML格式使整行显示为红色并加粗
            html_text = f"<span style='color: {Theme.ERROR}; font-weight: bold;'>{formatted_text}</span>"
            
            # 在纯文本模式下添加文本
            self.log_text_edit.appendHtml(html_text)
        elif log_type == "hint":
            color = QColor("#9370DB")
            prefix = "💡 "
            formatted_text = f"{timestamp_text}{prefix}{text}"
            html_text = f"<span style='color: {color.name()};'>{formatted_text}</span>"
            self.log_text_edit.appendHtml(html_text)
        else:
            # 其他类型日志保持原来的样式
            if log_type == "warning":
                color = QColor(Theme.WARNING)
                prefix = "⚠️ "
            elif log_type == "info":
                color = QColor(Theme.TEXT_PRIMARY)
                prefix = "ℹ️ "
            else:
                color = QColor(Theme.TEXT_PRIMARY)
                prefix = ""
            
            formatted_text = f"{timestamp_text}{prefix}{text}"
            
            # 设置文本颜色并添加到日志
            current_cursor = self.log_text_edit.textCursor()
            current_format = current_cursor.charFormat()
            current_format.setForeground(color)
            current_cursor.setCharFormat(current_format)
            
            self.log_text_edit.appendPlainText(formatted_text)
        
        # 滚动到底部
        self.log_text_edit.verticalScrollBar().setValue(
            self.log_text_edit.verticalScrollBar().maximum()
        )
        
        # 记录到系统日志
        if log_type == "info":
            info(f"[重置机器码] {text}")
        elif log_type == "warning":
            warning(f"[重置机器码] {text}")
        elif log_type == "error":
            error(f"[重置机器码] {text}")
        elif log_type == "hint":
            info(f"[重置机器码][提示] {text}")
        else:
            debug(f"[重置机器码] {text}")
    
    def show_toast(self, message, is_error=False, duration=2000):
        """显示Toast提示消息"""
        # 如果有正在显示的Toast，先移除
        if self.toast:
            self.toast.hide()
            self.toast.deleteLater()
            self.toast = None
            
        # 创建并显示新的Toast
        self.toast = ToastMessage(
            self,
            message,
            duration,
            Theme.ERROR if is_error else None
        )
        self.toast.showToast()
    
    def update_progress(self, current, total):
        """更新进度条"""
        if total > 0:
            percent = int(current * 100 / total)
            self.progress_bar.setValue(percent)
    
    def on_operation_finished(self, success, message):
        """操作完成回调"""
        # 更新进度条为完成状态
        self.progress_bar.setValue(100)
        
        if success:
            # 显示成功消息
            self.add_log(f"操作完成: {message}", "info")
            
            # 显示toast提示
            if self.parent():
                # 如果有父窗口，使用父窗口的toast
                parent_window = self.parent()
                if hasattr(parent_window, 'show_toast'):
                    parent_window.show_toast(message)
                else:
                    self.show_toast(message)
            else:
                # 否则使用对话框自己的toast
                self.show_toast(message)
                
            # 检查是否应该自动关闭对话框
            should_auto_close = False # 默认不自动关闭
            try:
                # 读取设置文件
                settings_file = os.path.join(get_app_data_dir(), "settings.json")
                if os.path.exists(settings_file):
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        # 检查自动关闭设置
                        if settings.get("auto_close_dialog_on_success", True):
                            should_auto_close = True
            except Exception as e:
                print(f"读取设置时出错: {str(e)}")

            if should_auto_close:
                self.close() # 自动关闭
            else:
                # 如果不自动关闭，添加提示 (使用 hint 类型)
                # self.add_log("按 ESC 键 关闭对话框", "hint")
                pass
        else:
            # 显示错误消息
            # self.add_log(f"操作失败: {message}", "error")
            # 添加提示 (使用 hint 类型)
            # self.add_log("按 ESC 键 关闭对话框", "hint")
            pass
    
    def start_reset(self):
        """开始重置机器码"""
        # 清空日志
        self.log_text_edit.clear()
        
        # 重置进度条
        self.progress_bar.setValue(0)
        
        # 创建并启动工作线程
        self.worker = ResetMachineIDWorker()
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.log_produced.connect(self.add_log)
        self.worker.operation_finished.connect(self.on_operation_finished)
        self.worker.start()
        
        # 添加初始日志
        self.add_log("开始重置机器码，请稍候...", "info") 