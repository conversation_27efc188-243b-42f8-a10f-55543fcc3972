#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户操作功能模块
包含账户切换、删除等核心操作逻辑
"""

import os
import json
import subprocess
import platform
import tempfile
import shutil
from PySide6.QtCore import QTimer
from PySide6.QtWidgets import QDialog
import asyncio

# 导入自定义模块
from logger import info, error, warning
from widgets.dialog import StyledDialog
from theme import Theme
from utils import get_app_data_dir, Utils
from account.logout_batch import LogoutBatcher, LogoutResult

def switch_account_action(main_window, account):
    """切换到指定账户
    
    Args:
        main_window: 主窗口实例 (CursorAccountManager)
        account: 要切换到的账户数据字典
    """
    email = account.get("email")
    auth_info = account.get("auth_info", {})
    
    # 记录操作开始
    info(f"开始切换账户操作: 目标账户={email}")
    
    # 确认是否要切换，并获取是否恢复机器码的选项
    confirmed, restore_machine_code = StyledDialog.showConfirmDialogWithCheckbox(
        main_window,
        "切换账户",
        f"确定要切换到账户 {email} 吗？\n\n此操作可能将会重启 Cursor",
        checkbox_text="同时恢复此账户关联的机器码 (若存在且匹配当前系统)",
        checkbox_default_checked=True, # 默认勾选
        confirm_text="切换",
        confirm_color=Theme.ACCENT
    )
    
    if not confirmed:
        info(f"用户取消了切换到账户 {email} 的操作")
        return
    
    # 获取Token信息
    access_token = auth_info.get("cursorAuth/accessToken")
    refresh_token = auth_info.get("cursorAuth/refreshToken", access_token)
    
    if not email or not access_token:
        error(f"账户信息不完整，无法切换邮箱={email}, 是否有token={bool(access_token)}")
        main_window.show_toast("账户信息不完整，无法切换", error=True)
        return

    if restore_machine_code: # <--- 根据复选框状态判断是否执行
        # ============== 添加恢复机器码功能开始 ==============
        # 检查账户数据是否包含机器码信息
        machine_info = account.get("machine_info", {})
        account_system_type = account.get("system_type", "")
        
        # 获取当前系统类型
        current_system_type = ""
        system = platform.system().lower()
        if "windows" in system:
            current_system_type = "windows"
        elif "darwin" in system:
            current_system_type = "mac"
        else:
            current_system_type = "linux"
        
        # 只有当账户有机器码信息且系统类型匹配时才恢复
        if machine_info and account_system_type and account_system_type == current_system_type:
            info(f"检测到匹配的机器码信息，准备恢复机器码账户={email}, 系统类型={current_system_type}")
            main_window.show_toast(f"检测到匹配的机器码信息，正在恢复...", error=False)
            
            try:
                import uuid
                
                # 获取storage.json路径
                storage_path = ""
                if current_system_type == "windows":
                    storage_path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "globalStorage", "storage.json")
                elif current_system_type == "mac":
                    storage_path = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")
                elif current_system_type == "linux":
                    storage_path = os.path.expanduser("~/.config/Cursor/User/globalStorage/storage.json")
                
                # 检查storage.json是否存在
                if os.path.exists(storage_path):
                    # 读取当前配置
                    with open(storage_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 更新telemetry相关的机器码
                    if "telemetry.machineId" in machine_info and machine_info["telemetry.machineId"]:
                        config["telemetry.machineId"] = machine_info["telemetry.machineId"]
                        print(f"已恢复 telemetry.machineId: {machine_info['telemetry.machineId']}")
                    
                    if "telemetry.macMachineId" in machine_info and machine_info["telemetry.macMachineId"]:
                        config["telemetry.macMachineId"] = machine_info["telemetry.macMachineId"]
                        print(f"已恢复 telemetry.macMachineId: {machine_info['telemetry.macMachineId']}")
                    
                    if "telemetry.devDeviceId" in machine_info and machine_info["telemetry.devDeviceId"]:
                        config["telemetry.devDeviceId"] = machine_info["telemetry.devDeviceId"]
                        print(f"已恢复 telemetry.devDeviceId: {machine_info['telemetry.devDeviceId']}")
                    
                    if "telemetry.sqmId" in machine_info and machine_info["telemetry.sqmId"]:
                        config["telemetry.sqmId"] = machine_info["telemetry.sqmId"]
                        print(f"已恢复 telemetry.sqmId: {machine_info['telemetry.sqmId']}")
                    
                    # 创建临时文件
                    with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp:
                        # 写入更新后的JSON
                        json.dump(config, temp, indent=4)
                        temp_name = temp.name
                    
                    # 获取文件当前权限
                    current_mode = os.stat(storage_path).st_mode
                    
                    # 设置文件为可写
                    os.chmod(storage_path, 0o644)
                    
                    # 复制临时文件到原始位置
                    shutil.copy2(temp_name, storage_path)
                    
                    # 删除临时文件
                    os.unlink(temp_name)
                    
                    # 恢复原始权限
                    os.chmod(storage_path, current_mode)
                    
                    print(f"已成功更新配置文件: {storage_path}")
                    
                    # 根据系统类型恢复系统特有的机器码
                    machine_code_restored = False  # 跟踪机器码恢复状态
                    
                    if current_system_type == "windows":
                        # 恢复Windows的MachineGuid
                        if "system.machineGuid" in machine_info and machine_info["system.machineGuid"]:
                            try:
                                import winreg
                                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography", 0, winreg.KEY_SET_VALUE)
                                winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, machine_info["system.machineGuid"])
                                winreg.CloseKey(key)
                                print(f"已恢复 Windows MachineGuid: {machine_info['system.machineGuid']}")
                                machine_code_restored = True
                            except Exception as e:
                                error_msg = str(e)
                                winreg_error = f"恢复 Windows MachineGuid 失败: {error_msg}"
                                print(winreg_error)
                                # 尝试使用reg.exe命令
                                try:
                                    result = subprocess.run(
                                        ['reg', 'add', 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography', '/v', 'MachineGuid', '/t', 'REG_SZ', '/d', machine_info["system.machineGuid"], '/f'],
                                        check=True,
                                        capture_output=True
                                    )
                                    if result.returncode == 0:
                                        print(f"已使用reg.exe恢复 Windows MachineGuid: {machine_info['system.machineGuid']}")
                                        machine_code_restored = True
                                    else:
                                        error_msg = result.stderr.decode() if result.stderr else "未知错误"
                                        reg_error = f"使用reg.exe恢复 Windows MachineGuid 失败: {error_msg}"
                                        print(reg_error)
                                        # 保存详细错误信息
                                        main_window._last_error_detail = f"{winreg_error}\n{reg_error}"
                                        main_window.show_toast("恢复机器码失败，请以管理员身份运行程序", error=True, copy_error=True)
                                except Exception as e:
                                    error_msg = str(e)
                                    reg_error = f"使用reg.exe恢复 Windows MachineGuid 失败: {error_msg}"
                                    print(reg_error)
                                    # 保存详细错误信息
                                    main_window._last_error_detail = f"{winreg_error}\n{reg_error}"
                                    main_window.show_toast("恢复机器码失败，请以管理员身份运行程序", error=True, copy_error=True)
                    
                    elif current_system_type == "linux":
                        # 恢复Linux的machine-id
                        if "system.machineId" in machine_info and machine_info["system.machineId"]:
                            try:
                                with open("/etc/machine-id", 'w') as f:
                                    f.write(machine_info["system.machineId"])
                                print(f"已恢复 Linux machine-id: {machine_info['system.machineId']}")
                                machine_code_restored = True
                            except Exception as e:
                                error_msg = str(e)
                                error_detail = f"恢复 Linux machine-id 失败: {error_msg}"
                                print(error_detail)
                                main_window._last_error_detail = error_detail
                                main_window.show_toast("恢复机器码失败，请以root身份运行程序", error=True, copy_error=True)
                        
                        # 恢复Linux的dbus machine-id
                        if "system.dbusId" in machine_info and machine_info["system.dbusId"]:
                            try:
                                if os.path.exists("/var/lib/dbus/machine-id"):
                                    with open("/var/lib/dbus/machine-id", 'w') as f:
                                        f.write(machine_info["system.dbusId"])
                                    print(f"已恢复 Linux dbus machine-id: {machine_info['system.dbusId']}")
                                    machine_code_restored = True
                            except Exception as e:
                                error_msg = str(e)
                                error_detail = f"恢复 Linux dbus machine-id 失败: {error_msg}"
                                print(error_detail)
                                main_window._last_error_detail = error_detail
                                main_window.show_toast("恢复机器码失败，请以root身份运行程序", error=True, copy_error=True)
                    
                    elif current_system_type == "mac":
                        # 恢复Mac的nvram SystemUUID
                        if "system.nvramSystemUUID" in machine_info and machine_info["system.nvramSystemUUID"]:
                            try:
                                result = subprocess.run(["nvram", f"SystemUUID={machine_info['system.nvramSystemUUID']}"], check=True, capture_output=True)
                                if result.returncode == 0:
                                    print(f"已恢复 Mac nvram SystemUUID: {machine_info['system.nvramSystemUUID']}")
                                    machine_code_restored = True
                                else:
                                    error_msg = result.stderr.decode() if result.stderr else "未知错误"
                                    print(f"恢复 Mac nvram SystemUUID 失败: {error_msg}")
                                    main_window.show_toast("恢复机器码失败，请以root身份运行程序", error=True, copy_error=True)
                            except Exception as e:
                                error_msg = str(e)
                                error_detail = f"恢复 Mac nvram SystemUUID 失败: {error_msg}"
                                print(error_detail)
                                main_window._last_error_detail = error_detail
                                main_window.show_toast("恢复机器码失败，请以root身份运行程序", error=True, copy_error=True)
                    
                    # 只在机器码恢复成功时显示成功提示
                    if machine_code_restored:
                        main_window.show_toast(f"机器码已成功恢复", error=False)
                    else:
                        error_msg = f"未执行任何有效的机器码恢复操作"
                        # 不显示失败提示，因为可能是用户未存储或系统不匹配，或者权限问题已在具体操作中提示
                        info(error_msg)
                else:
                    error_msg = f"配置文件不存在: {storage_path}"
                    print(error_msg)
                    main_window.show_toast("配置文件不存在", error=True, copy_error=True)
            
            except Exception as e:
                error_msg = str(e)
                error_info = f"恢复机器码时出错: {error_msg}"
                print(error_info)
                main_window.show_toast("恢复机器码时出错", error=True, copy_error=True)
        else:
            info(f"账户 {email} 无匹配当前系统 ({current_system_type}) 的机器码信息，跳过恢复")
        # ============== 添加恢复机器码功能结束 ==============
    else:
        info("用户选择不恢复机器码，跳过该步骤")

    # 尝试结束Cursor进程
    info("开始关闭Cursor进程，准备切换账户")
    main_window.show_toast("正在切换账户，请稍候...")
    cursor_killed = main_window.kill_cursor_process()
    
    if cursor_killed:
        info("成功关闭Cursor进程")
    else:
        warning("未能成功关闭Cursor进程，可能会影响切换效果")
    
    # 更新认证信息
    info(f"开始更新认证信息: 邮箱={email}")
    success = main_window.auth_manager.update_auth(
        email=email,
        access_token=access_token,
        refresh_token=refresh_token
    )
    
    if success:
        info(f"成功更新认证信息: 邮箱={email}")
        # 如果成功更新认证信息
        if cursor_killed:
            # 检查是否开启了"关闭Cursor后自动启动"选项
            auto_restart_setting = True  #
            try:
                settings_file = os.path.join(get_app_data_dir(), "settings.json")
                if os.path.exists(settings_file):
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        auto_restart_setting = settings.get("auto_restart_cursor", True)
            except Exception as e:
                info(f"读取设置时出错: {str(e)}")
            
            if auto_restart_setting:
                info(f"准备重启Cursor应用，切换到账户: {email}")
                main_window.show_toast(f"已切换到账户 {email}，正在重启Cursor...")
                # 给数据库写入操作留出一点时间
                QTimer.singleShot(1500, lambda: main_window.start_cursor_app())
            else:
                info(f"根据设置不自动启动Cursor，切换到账户: {email}")
                main_window.show_toast(f"已切换到账户 {email}，请手动启动Cursor")
        else:
            main_window.show_toast(f"已切换到账户 {email}")
        
        # 更新当前账户信息
        main_window.current_email = email
        
        # 加载当前账户基本UI
        main_window.load_current_account()
        
        # 立即获取新切换账户的额度并更新首页UI
        main_window._fetch_current_account_quota(silent=False)

        # 更新账户列表中所有行的状态
        info(f"开始更新账户列表UI状态以反映新的当前账户: {main_window.current_email}")
        for row_email, row_widget in main_window.account_rows.items():
            if row_widget: # 确保行组件仍然存在
                try:
                    is_now_current = (row_email == main_window.current_email)
                    row_widget.set_current(is_now_current)
                except Exception as e:
                    error(f"更新账户行 {row_email} 状态时出错: {e}")
        info("账户列表UI状态更新完成")

        # 清除旧账户的额度数据，确保显示当前账户数据 - 这段逻辑似乎不再需要，因为上面会主动获取新额度
        # if email in main_window.account_quotas:
        #     # 如果存在此邮箱的配额数据，直接添加到UI更新队列触发更新
        #     main_window.ui_update_thread.add_update_request(email, main_window.account_quotas[email])
        
        # 不再直接调用load_accounts()，避免重复创建账户行
        # main_window.load_accounts()
        
        # 移除延迟刷新所有账户的逻辑
        # QTimer.singleShot(2000, lambda: main_window.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=True))
    else:
        main_window.show_toast("切换账户失败", error=True)

def delete_account_action(main_window, email):
    """删除指定邮箱的账户，支持可选注销
    Args:
        main_window: 主窗口实例 (CursorAccountManager)
        email: 要删除的账户邮箱
    """
    from logger import info, error
    info(f"开始删除账户: {email}")

    # 弹窗带复选框，默认勾选
    confirmed, do_logout = StyledDialog.showConfirmDialogWithCheckbox(
        main_window,
        "删除账户",
        f"确定要删除账户 {email} 吗？",
        checkbox_text="同时注销账户（推荐）",
        checkbox_default_checked=True,
        confirm_text="删除",
        confirm_color=Theme.ERROR
    )
    if not confirmed:
        info(f"取消删除账户: {email}")
        return

    def _do_delete():
        try:
            success = main_window.account_data.delete_account(email)
            if success:
                info(f"成功从账户列表中删除账户: {email}")
                if email in main_window.account_rows:
                    row_widget = main_window.account_rows[email]
                    main_window.accounts_layout.removeWidget(row_widget)
                    row_widget.deleteLater()
                    del main_window.account_rows[email]
                    info(f"成功从UI中移除账户: {email}")
                main_window._update_accounts_count()
                if main_window.current_email == email:
                    info(f"当前账户 {email} 被删除，需要切换当前账户")
                    if main_window.account_data.accounts:
                        new_current = main_window.account_data.accounts[0]
                        new_email = new_current.get("email", "")
                        main_window.current_email = new_email
                        info(f"切换当前账户至: {new_email}")
                    else:
                        main_window.current_email = ""
                        info("没有可用账户，清空当前账户")
                    from account.account_data import save_current_email
                    save_current_email(main_window.current_email)
                    info(f"已更新保存的当前邮箱: {main_window.current_email}")
                    main_window.load_current_account()
                main_window.show_toast(f"已删除账户 {email}")
            else:
                error(f"删除账户失败: {email}")
                main_window.show_toast("删除账户失败", error=True)
        except Exception as e:
            error_msg = str(e)
            error(f"删除账户出错: {email}, 错误: {error_msg}")
            main_window.show_toast(f"删除账户出错: {error_msg}", error=True)

    # 注销流程
    if do_logout:
        account = next((acc for acc in main_window.account_data.accounts if acc.get('email') == email), None)
        if account:
            batcher = LogoutBatcher([account])
            def on_logout_finished(results):
                res = results.get(email, LogoutResult.FAILED)
                stat = {LogoutResult.SUCCESS:0, LogoutResult.DISABLED:0, LogoutResult.REPEATED:0, LogoutResult.FAILED:0}
                stat[res] += 1
                msg = f"注销账户共 1 个，成功 {stat[LogoutResult.SUCCESS]} 个，被禁用 {stat[LogoutResult.DISABLED]} 个，重复注销 {stat[LogoutResult.REPEATED]} 个，失败 {stat[LogoutResult.FAILED]} 个"
                main_window.show_toast(msg)
                _do_delete()
            batcher.all_logout_finished.connect(on_logout_finished)
            batcher.start_logout()
        else:
            _do_delete()
    else:
        _do_delete()


