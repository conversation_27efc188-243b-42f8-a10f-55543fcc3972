import os
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QScrollArea, QPushButton, QFrame, QSpacerItem,
    QSizePolicy, QGridLayout, QGraphicsDropShadowEffect,
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QColor

from theme import Theme
from widgets.styled_widgets import SelectableLabel

class AboutPage(QWidget):
    """关于页面类 - 显示YCursor的版本和作者信息"""
    
    def __init__(self, app_version="N/A", about_info=None):
        super().__init__()
        self.app_version = app_version
        # Provide default values in case about_info is None or missing keys
        self.about_info = about_info if about_info else {}
        # 初始化UI
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置整个页面的背景色
        self.setStyleSheet("background-color: " + Theme.GLASS_BG + ";")
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20) # Add margins for overall padding
        main_layout.setSpacing(20) # Spacing between cards
        
        # --- 关于信息容器 ---
        about_container = QWidget()
        about_container.setObjectName("aboutContainer")
        about_container.setStyleSheet(f"""
            #aboutContainer {{
                background-color: {Theme.SECONDARY};
                border-radius: {Theme.BORDER_RADIUS};
                /* Removed fixed background role - handled by card */
            }}
        """)
        
        # 关于容器的布局 - 简化，直接用卡片的布局
        container_layout = QVBoxLayout(about_container)
        container_layout.setContentsMargins(0, 0, 0, 0) # No margins for container
        container_layout.setSpacing(0)
        
        # 创建关于卡片
        about_card = self.create_card_widget() # Use the helper
        about_layout = QVBoxLayout(about_card)
        about_layout.setContentsMargins(25, 25, 25, 25)
        about_layout.setSpacing(20)
        
        # 卡片标题
        title_layout = QHBoxLayout()
        # title_layout.setContentsMargins(0, 0, 0, 20) # Margin handled by spacing
        title_layout.setSpacing(10)
        
        title_icon = QLabel("✨")
        title_icon.setStyleSheet(f"color: {Theme.ACCENT}; font-size: 24px;")
        title_layout.addWidget(title_icon)
        
        title_label = QLabel("关于")
        title_label.setFont(QFont(Theme.FONT_FAMILY, 22, QFont.Weight.Bold))
        title_label.setStyleSheet("color: " + Theme.TEXT_PRIMARY + ";")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        about_layout.addLayout(title_layout)
        
        # 关于内容区域 (QWidget wrapper might be redundant if only one layout)
        about_content_layout = QVBoxLayout() # Directly use layout
        about_content_layout.setContentsMargins(0, 0, 0, 0) # No inner margins needed
        about_content_layout.setSpacing(10) # Reduced spacing from 20
        
        # 版本信息区域
        version_widget = QWidget()
        version_widget.setStyleSheet("""
            background-color: transparent;
            border-bottom: 1px solid rgba(60, 63, 70, 0.5);
            padding-bottom: 10px;
        """)
        version_layout = QHBoxLayout(version_widget)
        version_layout.setContentsMargins(0, 0, 0, 10)
        version_layout.setSpacing(0)
        
        # 版本号 - Use passed app_version
        version_label = SelectableLabel(f"YCursor v{self.app_version}")
        version_label.setObjectName("aboutVersionLabel")
        version_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: 18px;
            font-weight: bold;
            padding: 0px;
            border-bottom: none;
        """)
        version_layout.addWidget(version_label)
        version_layout.addStretch()
        
        # 添加打造者信息
        made_by_label = SelectableLabel("Developed by Yan")
        made_by_label.setObjectName("aboutMadeByLabel")
        made_by_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: {Theme.FONT_SIZE_SMALL};
            padding-top: 5px;
            border-bottom: none;
        """)
        version_layout.addWidget(made_by_label)
        
        about_content_layout.addWidget(version_widget)

        # 联系信息区域
        contact_layout = QVBoxLayout()
        contact_layout.setSpacing(15)
        contact_layout.setContentsMargins(0, 5, 0, 0) # Reduced top margin from 15
        
        # 版权信息
        copyright_layout = QHBoxLayout()
        copyright_layout.setSpacing(10)
        
        copyright_icon = QLabel("©")
        copyright_icon.setStyleSheet("color: " + Theme.ACCENT + "; font-size: 14px; font-weight: bold;")
        copyright_layout.addWidget(copyright_icon)
        
        copyright_text = SelectableLabel(self.about_info.get('copyright', '© 2025 Yan - All rights reserved'))
        copyright_text.setStyleSheet("color: " + Theme.TEXT_SECONDARY + "; font-size: " + Theme.FONT_SIZE_SMALL + ";")
        copyright_layout.addWidget(copyright_text)
        copyright_layout.addStretch()
        
        contact_layout.addLayout(copyright_layout)
        
        # 联系方式
        email_layout = QHBoxLayout()
        email_layout.setSpacing(10)
        
        email_icon = QLabel("📧")
        email_icon.setStyleSheet("font-size: 14px;")
        email_layout.addWidget(email_icon)
        
        email_text = SelectableLabel(self.about_info.get('email', '<EMAIL>'))
        email_text.setStyleSheet("color: " + Theme.TEXT_SECONDARY + "; font-size: " + Theme.FONT_SIZE_SMALL + ";")
        email_layout.addWidget(email_text)
        email_layout.addStretch()
        
        contact_layout.addLayout(email_layout)
        
        # QQ交流群
        qq_layout = QHBoxLayout()
        qq_layout.setSpacing(10)
        
        qq_icon = QLabel("👥")
        qq_icon.setStyleSheet("font-size: 14px;")
        qq_layout.addWidget(qq_icon)
        
        qq_text = SelectableLabel(f"{self.about_info.get('qq_group_1_label', 'QQ群:')} {self.about_info.get('qq_group_1', 'N/A')}")
        qq_text.setStyleSheet("color: " + Theme.TEXT_SECONDARY + "; font-size: " + Theme.FONT_SIZE_SMALL + ";")
        qq_layout.addWidget(qq_text)
        
        qq_layout.addStretch(1)
        
        # 二群信息
        qq2_text = SelectableLabel(f"{self.about_info.get('qq_group_2_prefix', '(群满加二群:')} {self.about_info.get('qq_group_2', 'N/A')}{self.about_info.get('qq_group_2_suffix', ')')}")
        qq2_text.setStyleSheet("color: " + Theme.TEXT_SECONDARY + "; font-size: 11px;")
        qq_layout.addWidget(qq2_text)
        
        contact_layout.addLayout(qq_layout)
        
        about_content_layout.addLayout(contact_layout)
        
        # 添加感谢信息
        thanks_label = QLabel("感谢您的使用和支持 ❤️")
        thanks_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        thanks_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: {Theme.FONT_SIZE_SMALL};
            margin-top: 10px;
            padding: 5px;
            background-color: rgba(43, 157, 124, 0.1);
            border-radius: 8px;
        """)
        about_content_layout.addWidget(thanks_label)
        
        # Add the content layout to the main card layout
        about_layout.addLayout(about_content_layout)
        
        # 添加卡片到容器布局
        container_layout.addWidget(about_card)
        
        # 添加容器到主布局
        main_layout.addWidget(about_container)

        # --- 创建并添加文档卡片 ---
        docs_card = self.create_docs_card_widget()
        main_layout.addWidget(docs_card)

        # 添加弹性空间到底部
        main_layout.addStretch()

    def create_card_widget(self):
        """创建具有阴影效果的卡片组件 (用于 关于卡片)"""
        card = QFrame()
        card.setObjectName("aboutCard") # Specific object name
        card.setStyleSheet(f"""
            QFrame#aboutCard {{
                background-color: {Theme.CARD_BG};
                border-radius: 12px;
                border: 1px solid {Theme.BORDER};
            }}
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 5)
        card.setGraphicsEffect(shadow)
        
        return card

    def create_docs_card_widget(self):
        """创建用于展示文档链接的卡片组件"""
        card = QFrame()
        card.setObjectName("docsCard")
        card.setStyleSheet(f"""
            QFrame#docsCard {{
                background-color: {Theme.CARD_BG};
                border-radius: 12px;
                border: 1px solid {Theme.BORDER};
            }}
        """)
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 5)
        card.setGraphicsEffect(shadow)

        docs_card_layout = QVBoxLayout(card)
        docs_card_layout.setContentsMargins(25, 25, 25, 25)
        docs_card_layout.setSpacing(15)

        # Create title and hint layout
        title_hint_layout = QHBoxLayout()
        title_hint_layout.setContentsMargins(0, 0, 0, 0)
        title_hint_layout.setSpacing(10)

        # Card Title
        docs_title_label = QLabel("📚 相关文档")
        docs_title_label.setFont(QFont(Theme.FONT_FAMILY, 18, QFont.Weight.Bold))
        docs_title_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY};")
        title_hint_layout.addWidget(docs_title_label)

        title_hint_layout.addStretch()

        # --- BEGIN Nested Layout for Hint --- 
        # Create a nested vertical layout for the hint label
        nested_hint_layout = QVBoxLayout()
        nested_hint_layout.setContentsMargins(0, 0, 0, 0)
        nested_hint_layout.setSpacing(0)

        # Add stretch first to push the label down
        nested_hint_layout.addStretch()

        # Hint Label (added to nested layout)
        hint_label = QLabel("左键点击跳转，右键点击复制")
        hint_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: 11px;
            background-color: transparent;
            border: none;
        """)
        # Add hint label to the nested layout
        nested_hint_layout.addWidget(hint_label) # No alignment flag needed here

        # Add the nested layout to the main title/hint horizontal layout
        title_hint_layout.addLayout(nested_hint_layout)
        # --- END Nested Layout for Hint --- 

        # Add the combined layout to the card
        docs_card_layout.addLayout(title_hint_layout)

        # Add spacing after title row
        docs_card_layout.addSpacing(10)

        # Define link data - Fetch from about_info
        docs_links = self.about_info.get('docs_links', [])

        # Loop to create link rows
        for link_info in docs_links:
            link_layout = QHBoxLayout()
            link_layout.setSpacing(5)

            icon_label = QLabel(link_info["icon"])
            icon_label.setStyleSheet("font-size: 14px; padding-top: 2px;")
            link_layout.addWidget(icon_label)

            desc_label = QLabel(link_info["desc"])
            desc_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL}; font-weight: bold;")
            link_layout.addWidget(desc_label)

            url = link_info["url"]
            url_label = SelectableLabel(url=url)
            link_color = Theme.ACCENT
            url_label.setText(f"<a href='#' style='color:{link_color}; text-decoration:none;'>{url}</a>")
            url_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
            url_label.setTextFormat(Qt.TextFormat.RichText)
            link_layout.addWidget(url_label)
            link_layout.addStretch()

            docs_card_layout.addLayout(link_layout)

        docs_card_layout.addStretch()
        return card 