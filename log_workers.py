"""
日志工作线程模块
=======================================

该模块提供用于后台处理日志的工作线程类

- LogLoadWorker: 用于后台加载日志文件
- LogFilterWorker: 用于后台过滤日志内容
- LogFormatterWorker: 用于后台格式化日志内容
"""

import sys
import os
import time
import traceback
from datetime import datetime
import threading # Added for Event

from PySide6.QtCore import QThread, Signal

from logger import read_today_log
from theme import Theme


class LogLoadWorker(QThread):
    """日志加载工作线程，用于后台加载日志文件"""
    
    # 日志加载完成信号
    log_loaded = Signal(str)
    
    def __init__(self):
        super().__init__()
        self._is_stopped = threading.Event() # Use threading.Event for stop flag
        
    def stop(self):
        """Requests the thread to stop."""
        self._is_stopped.set()

    def run(self):
        """执行日志加载操作"""
        try:
            # Check if stopped before starting work (optional but good practice)
            if self._is_stopped.is_set():
                print("LogLoadWorker: Stop requested before starting.")
                return

            # 读取今日日志
            log_content = read_today_log()

            # Check if stopped after potentially long operation
            if self._is_stopped.is_set():
                print("LogLoadWorker: Stop requested after loading.")
                return

            # 发送日志加载完成信号
            self.log_loaded.emit(log_content)
        except Exception as e:
            if not self._is_stopped.is_set(): # Only report error if not stopped intentionally
                print(f"日志加载线程出错: {str(e)}")
                # 发送错误信息
                self.log_loaded.emit(f"加载日志时出错: {str(e)}")


class LogFilterWorker(QThread):
    """日志过滤工作线程，用于后台过滤日志内容"""
    
    # 过滤完成信号，带有过滤后的内容和是否为空的标志
    filter_completed = Signal(str, bool)
    
    def __init__(self, log_content, filters):
        super().__init__()
        self.log_content = log_content
        self.filters = filters
        self._is_stopped = threading.Event() # Use threading.Event for stop flag
        
    def stop(self):
        """Requests the thread to stop."""
        self._is_stopped.set()

    def run(self):
        """执行日志过滤操作"""
        try:
            # Check if stopped before starting work
            if self._is_stopped.is_set():
                print("LogFilterWorker: Stop requested before starting.")
                return

            # 如果没有过滤器，返回原始内容
            if not self.filters:
                if not self._is_stopped.is_set():
                    self.filter_completed.emit(self.log_content, False)
                return
                
            # 筛选日志行
            filtered_lines = []
            lines = self.log_content.split('\n')
            i = 0
            
            # 跟踪是否处于异常堆栈跟踪模式
            in_exception_stack = False
            current_filter_level = None
            
            while i < len(lines):
                # Check stop flag periodically within the loop
                if self._is_stopped.is_set():
                    print("LogFilterWorker: Stop requested during filtering.")
                    return

                line = lines[i]
                i += 1
                
                if not line.strip():
                    continue
                
                # 检查是否为新的日志条目（包含时间戳和日志级别）
                is_new_log_entry = False
                for level in ["INFO", "WARNING", "ERROR", "DEBUG", "CRITICAL"]:
                    # Check stop flag inside inner loop too
                    if self._is_stopped.is_set(): return
                    if f"[{level}]" in line:
                        is_new_log_entry = True
                        # 新的日志条目，结束之前的异常堆栈模式
                        in_exception_stack = False
                        
                        # 检查新条目是否属于筛选条件
                        if level in self.filters:
                            filtered_lines.append(line)
                            current_filter_level = level
                            # 如果是ERROR级别且包含"异常详情"，进入异常堆栈模式
                            if level == "ERROR" and "异常详情:" in line:
                                in_exception_stack = True
                        else:
                            current_filter_level = None
                        break
                
                # 不是新的日志条目
                if not is_new_log_entry:
                    # 如果在异常堆栈模式且当前筛选包含ERROR，保留该行
                    if in_exception_stack and "ERROR" in self.filters:
                        filtered_lines.append(line)
            
            # Check if stopped before emitting result
            if self._is_stopped.is_set():
                print("LogFilterWorker: Stop requested before emitting result.")
                return

            # 如果没有匹配的行，发送空结果信号
            if not filtered_lines:
                self.filter_completed.emit("", True)
                return
            
            # 格式化并发送过滤后的日志
            filtered_content = '\n'.join(filtered_lines)
            self.filter_completed.emit(filtered_content, False)
                
        except Exception as e:
            if not self._is_stopped.is_set(): # Only report error if not stopped intentionally
                print(f"日志过滤线程出错: {str(e)}")
                # 发送错误信息
                self.filter_completed.emit(f"过滤日志时出错: {str(e)}", False)


class LogFormatterWorker(QThread):
    """日志格式化工作线程，用于后台格式化日志内容"""
    
    # 格式化完成信号，带有格式化后的HTML内容
    format_completed = Signal(str)
    # 新增分块格式化信号，参数为(块内容, 是否为最后一块)
    chunk_formatted = Signal(str, bool)
    
    def __init__(self, log_content):
        super().__init__()
        self.log_content = log_content
        self._is_stopped = threading.Event() # Use threading.Event for stop flag
        
    def stop(self):
        """Requests the thread to stop."""
        self._is_stopped.set()

    def run(self):
        """执行日志格式化操作"""
        try:
            # Check if stopped before starting work
            if self._is_stopped.is_set():
                print("LogFormatterWorker: Stop requested before starting.")
                return

            # 为不同级别的日志设置颜色
            formatted_log = ""
            
            # 日志级别对应的颜色
            level_colors = {
                "INFO": "#4fc3f7",      # 蓝色
                "WARNING": "#ffb74d",   # 橙色
                "ERROR": "#ef5350",     # 红色
                "DEBUG": "#9ccc65",     # 绿色
                "CRITICAL": "#d32f2f"   # 深红色
            }
            
            # 追踪异常详情模式
            in_exception_details = False
            exception_color = level_colors["ERROR"]  # 异常使用错误颜色
            
            lines = self.log_content.split('\n')
            
            # 定义分块大小和总块数
            CHUNK_SIZE = 500  # 每块500行
            total_chunks = (len(lines) + CHUNK_SIZE - 1) // CHUNK_SIZE
            
            # 分块处理日志行
            for chunk_idx in range(total_chunks):
                # Check stop flag at the beginning of each chunk processing
                if self._is_stopped.is_set():
                    print("LogFormatterWorker: Stop requested during chunk processing.")
                    return

                chunk_formatted_log = ""
                start_idx = chunk_idx * CHUNK_SIZE
                end_idx = min(start_idx + CHUNK_SIZE, len(lines))
                is_last_chunk = (chunk_idx == total_chunks - 1)
                
                # 处理当前块的行
                i = start_idx
                while i < end_idx:
                    # Check stop flag within the inner loop
                    if self._is_stopped.is_set(): return

                    line = lines[i]
                    i += 1
                    
                    if not line.strip():
                        continue
                    
                    # 检查是否为新的日志条目
                    new_log_entry = False
                    for level, color in level_colors.items():
                        # Check stop flag inside inner loop too
                        if self._is_stopped.is_set(): return
                        if f"[{level}]" in line:
                            new_log_entry = True
                            # 新的日志条目，关闭异常详情模式
                            in_exception_details = False
                            
                            # 检查是否是异常详情的开始
                            if level == "ERROR" and "异常详情:" in line:
                                in_exception_details = True
                            
                            # 设置时间戳为灰色，其余为对应日志级别颜色
                            timestamp_end = line.find("[")
                            if timestamp_end > 0:
                                timestamp = line[:timestamp_end]
                                rest_of_line = line[timestamp_end:]
                                colored_line = f"<span style='color: #9e9e9e;'>{timestamp}</span><span style='color: {color};'>{rest_of_line}</span>"
                            else:
                                colored_line = f"<span style='color: {color};'>{line}</span>"
                            
                            break
                    
                    # 如果不是新的日志条目
                    if not new_log_entry:
                        if in_exception_details:
                            # 在异常详情模式下，所有行都使用错误颜色
                            colored_line = f"<span style='color: {exception_color};'>{line}</span>"
                        else:
                            # 普通文本使用默认颜色
                            colored_line = f"<span style='color: {Theme.TEXT_PRIMARY};'>{line}</span>"
                    
                    chunk_formatted_log += colored_line + "<br>"
                    formatted_log += colored_line + "<br>" # Still build the full log for format_completed signal
                
                # Check if stopped before emitting chunk signal
                if self._is_stopped.is_set():
                    print("LogFormatterWorker: Stop requested before emitting chunk.")
                    return

                # 发送当前块的格式化内容
                if chunk_formatted_log:
                    chunk_html = f"<div style='line-height: 1.6;'>{chunk_formatted_log}</div>"
                    self.chunk_formatted.emit(chunk_html, is_last_chunk)
                    
                    # 每处理完一块，让出CPU时间，避免长时间占用，并检查停止标志
                    QThread.msleep(5)
                    if self._is_stopped.is_set(): return
            
            # Check if stopped before emitting final signal
            if self._is_stopped.is_set():
                print("LogFormatterWorker: Stop requested before emitting final result.")
                return

            # 为了兼容现有代码，仍然发送完整的格式化内容
            if formatted_log:
                self.format_completed.emit(f"<div style='line-height: 1.6;'>{formatted_log}</div>")
            else:
                empty_msg = "<div style='color: #9e9e9e; text-align: center; margin-top: 20px;'>没有匹配的日志记录</div>"
                self.format_completed.emit(empty_msg)
                if not self._is_stopped.is_set(): # Ensure chunk_formatted is not emitted if stopped just before
                    self.chunk_formatted.emit(empty_msg, True)
                
        except Exception as e:
            if not self._is_stopped.is_set(): # Only report error if not stopped intentionally
                error_msg = f"<div style='color: #ef5350; margin: 20px;'>格式化日志时出错: {str(e)}</div>"
                print(f"日志格式化线程出错: {str(e)}")
                # 发送错误信息
                self.format_completed.emit(error_msg)
                self.chunk_formatted.emit(error_msg, True)