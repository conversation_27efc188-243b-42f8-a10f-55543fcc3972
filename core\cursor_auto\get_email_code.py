import time
import re
from .config import Config
import imaplib
import email
from email.utils import parsedate_to_datetime
from datetime import datetime, timezone
import ssl
import sys
import os
import requests
import logging
from colorama import Fore, Style

# 默认的验证码正则表达式 (通用于所有邮箱类型)
DEFAULT_VERIFICATION_CODE_PATTERN = r"(?<![@.])\b\d{6}\b(?!\.[a-z]+)"

# 备用验证码正则表达式 (用于处理带空格的验证码，例如 "7 6 9 3 3 5")
SPACED_VERIFICATION_CODE_PATTERN = r"\b(\d\s+){5}\d\b"

# 颜色定义
class Colors:
    """统一的颜色定义"""
    RED = ''      # 错误信息
    GREEN = ''    # 成功信息
    YELLOW = ''   # 警告/提示信息
    BLUE = ''     # 框架/标题
    PURPLE = ''   # 重要数据
    CYAN = ''     # 进度信息
    WHITE = ''    # 普通文本
    NC = ''       # 结束颜色

def print_box(title="", content=None, footer=None):
    """打印消息，不带框"""
    # 在开始前添加一个空行
    print()
    
    # 如果只有标题，直接显示标题
    if title and not content and not footer:
        print(title)
        return
    
    # 显示标题
    if title:
        print(title)
    
    # 显示内容
    if content:
        if isinstance(content, str):
            if content.strip():
                print(content)
        elif isinstance(content, list):
            for line in content:
                print(line)
    
    # 显示页脚
    if footer:
        print(footer)

def log_info(msg, show_box=False):
    """输出信息日志"""
    # 跳过中间过程消息
    if "未通过标准正则匹配到验证码" in msg or "尝试匹配带空格的验证码格式" in msg:
        return
    if show_box:
        print_box(f"[信息] {msg}")
    else:
        print(f"[信息] {msg}")

def log_warn(msg, show_box=False):
    """输出警告日志"""
    if show_box:
        print_box(f"[警告] {msg}")
    else:
        print(f"[警告] {msg}")

def log_error(msg, show_box=False):
    """输出错误日志"""
    # 简化错误信息，避免输出过多详细内容
    simplified_msg = msg
    
    # 如果错误消息包含解决方法，仅输出主要错误信息
    if "解决方法" in simplified_msg:
        simplified_msg = simplified_msg.split("解决方法")[0].strip()
        
    # 移除多余的前缀，避免"邮箱连接测试失败: 邮箱连接测试失败: ..."这样的重复
    if "邮箱连接测试失败: 邮箱连接测试失败" in simplified_msg:
        simplified_msg = simplified_msg.replace("邮箱连接测试失败: 邮箱连接测试失败", "邮箱连接测试失败")
        
    # 简化错误信息
    if "IMAP邮箱登录失败" in simplified_msg:
        simplified_msg = "邮箱连接测试失败: IMAP邮箱登录失败"
        if "密码错误或IMAP服务未开启" in msg:
            simplified_msg += "，密码错误或IMAP服务未开启"
            
    # 为邮箱验证错误添加标记
    if "IMAP" in simplified_msg or "邮箱" in simplified_msg or "验证码" in simplified_msg:
        print(f"[错误][邮箱验证] {simplified_msg}")
    else:
        print(f"[错误] {simplified_msg}")
        
    if show_box:
        print_box(f"[错误] {simplified_msg}")

def log_process(msg, show_box=False):
    """输出进度日志"""
    # 跳过中间过程消息，但保留连接和认证相关的消息
    if "正在获取邮件内容" in msg or "正在解析邮件内容" in msg or \
       "正在删除已处理的邮件" in msg or "正在删除过期的验证码邮件" in msg:
        return
    
    # 保留IMAP连接和登录相关的重要消息
    if "正在连接IMAP服务器" in msg or "正在登录IMAP邮箱" in msg or \
       "正在选择邮箱文件夹" in msg or "正在搜索Cursor验证码邮件" in msg:
        print(f"[进行][重要] {msg}")
    else:
        print(f"[进行] {msg}")
        
    if show_box:
        print_box(f"[进行] {msg}")

def log_success(msg, show_box=False):
    """输出成功日志"""
    # 跳过中间过程成功消息
    if "成功匹配到带空格的验证码，处理后" in msg or "IMAP登录成功" in msg or \
       "邮箱文件夹选择成功" in msg or "找到最新的验证码邮件" in msg or \
       "过期邮件删除成功" in msg or "已安全登出IMAP" in msg:
        return
    if show_box:
        print_box(f"[成功] {msg}")
    else:
        print(f"[成功] {msg}")

class EmailVerificationHandler:
    def __init__(self, email_type=None):
        config = Config()
        self.config = config
        self.imap = config.get_imap()
        # 从配置中读取是否清理所有邮件的设置
        self._should_clean_emails = config.get_clean_all_cursor_mails()  # 直接从配置中读取设置
        self._is_login_success = True  # 用于标记IMAP登录是否成功
        self._connection_success = False  # 用于标记连接是否成功
        self.should_stop = False  # 新增：用于外部中断流程
        self._connection_error_message = "" # 保存连接错误信息
        
        # 获取验证码正则表达式配置 (通用于所有邮箱类型)
        self.verification_code_pattern = config.get_verification_code_pattern() or DEFAULT_VERIFICATION_CODE_PATTERN
        
        # 显示正则表达式配置
        if self.verification_code_pattern != DEFAULT_VERIFICATION_CODE_PATTERN:
            log_info(f"使用自定义验证码匹配规则: {self.verification_code_pattern}", show_box=True)
        
        # 根据传入的email_type参数强制设置邮箱模式
        if email_type is not None:
            # 优先使用传入的email_type参数覆盖配置
            self.use_temp_mail = (email_type.lower() == "temp")
            if self.use_temp_mail:
                log_info(f"根据参数强制使用临时邮箱模式", show_box=True)
            else:
                log_info(f"根据参数强制使用IMAP邮箱模式", show_box=True)
        else:
            # 使用配置中的设置
            self.use_temp_mail = config.is_using_temp_mail()
        
        # 临时邮箱相关配置
        if self.use_temp_mail:
            self.temp_mail = config.get_temp_mail()
            self.temp_mail_epin = config.get_temp_mail_epin()
            self.session = requests.Session()
            self.processed_mail_ids = set()  # 存储已处理的邮件ID
            log_info(f"使用临时邮箱模式: {self.temp_mail}", show_box=True)
            # 测试临时邮箱连接
            self._connection_success, self._connection_error_message = self._test_temp_mail_connection()
        else:
            # 测试IMAP连接
            self._connection_success, self._connection_error_message = self._test_imap_connection()
            
        if not self._connection_success:
            error_msg = "邮箱连接测试失败"
            if self._connection_error_message:
                error_msg = f"邮箱连接测试失败: {self._connection_error_message}"
            
            # 记录错误但不在控制台输出详细信息
            # 简化错误信息，只保留主要错误
            if "解决方法" in error_msg:
                error_msg = error_msg.split("解决方法")[0].strip()
                
            # 显示错误
            log_error(error_msg, show_box=False)
            
            # 抛出异常，让调用者处理
            raise ConnectionError(error_msg)

    def get_connection_status(self):
        """获取连接状态"""
        return self._connection_success

    def _test_imap_connection(self):
        """测试IMAP连接是否正常"""
        log_process("正在测试IMAP邮箱连接...")
        mail = None
        try:
            # 检查IMAP服务器配置
            if not self.imap['imap_server'] or not self.imap['imap_port']:
                print_box(
                    "IMAP配置错误",
                    [
                        (Colors.RED, "IMAP服务器配置无效"),
                        (Colors.WHITE, ""),
                        (Colors.WHITE, "当前配置:"),
                        (Colors.WHITE, f"• 服务器: {self.imap['imap_server'] or '未设置'}"),
                        (Colors.WHITE, f"• 端口: {self.imap['imap_port'] or '未设置'}")
                    ]
                )
                return False, "IMAP服务器配置无效，请检查服务器地址和端口是否设置正确"

            # 尝试连接IMAP服务器
            try:
                log_process(f"正在连接到 {self.imap['imap_server']}:{self.imap['imap_port']}...")
                # 设置socket超时时间为10秒
                import socket
                socket.setdefaulttimeout(10)
                mail = imaplib.IMAP4_SSL(self.imap['imap_server'], self.imap['imap_port'])
                log_success("IMAP服务器连接成功")
            except socket.timeout:
                print_box("IMAP连接超时", [(Colors.RED, "连接IMAP服务器超时，请检查网络连接")])
                return False, "IMAP连接超时，请检查网络连接或稍后重试"
            except (imaplib.IMAP4.error, ssl.SSLError, ConnectionRefusedError) as e:
                error_str = str(e)
                if "Connection refused" in error_str:
                    print_box("IMAP服务器连接被拒绝", [(Colors.RED, "请检查服务器地址和端口是否正确")])
                    return False, "IMAP服务器连接被拒绝，请检查服务器地址和端口是否正确"
                elif "certificate verify failed" in error_str:
                    print_box("IMAP服务器证书验证失败", [(Colors.RED, "可能是服务器证书过期或无效")])
                    return False, "IMAP服务器证书验证失败，可能是服务器证书过期或无效"
                elif "timed out" in error_str:
                    print_box("IMAP服务器连接超时", [(Colors.RED, "请检查网络连接和服务器状态")])
                    return False, "IMAP服务器连接超时，请检查网络连接和服务器状态"
                else:
                    print_box("IMAP服务器连接失败", [(Colors.RED, f"错误信息: {error_str}")])
                    return False, f"IMAP服务器连接失败: {error_str}"

            # 尝试登录
            try:
                log_process(f"正在使用账号 {self.imap['imap_user']} 登录...")
                mail.login(self.imap['imap_user'], self.imap['imap_pass'])
                log_success("IMAP账号登录成功")
            except imaplib.IMAP4.error as e:
                error_str = str(e)
                
                # 对QQ邮箱错误消息进行特殊处理，使其更易于理解
                if "qq.com" in self.imap['imap_server'].lower() or "qq.com" in self.imap['imap_user'].lower():
                    # 检查常见的QQ邮箱错误
                    if "Login fail" in error_str or "login fail" in error_str.lower():
                        # 更通用的IMAP邮箱授权码错误信息
                        detailed_error = (
                            "IMAP邮箱登录失败：密码错误或IMAP服务未开启\n\n"
                            "解决方法：\n"
                            "1. 请检查邮箱密码是否正确\n"
                            "2. 确认是否已在邮箱设置中开启IMAP服务\n"
                            "3. 如使用QQ邮箱，请使用授权码而非QQ密码登录"
                        )
                        return False, detailed_error
                    elif "service is not open" in error_str.lower():
                        # 更通用的IMAP服务未开启错误信息
                        detailed_error = (
                            "IMAP服务未开启\n\n"
                            "解决方法：\n"
                            "1. 请在邮箱设置中开启IMAP服务\n"
                            "2. 部分邮箱可能需要使用专用密码或授权码"
                        )
                        return False, detailed_error
                # Gmail特有错误处理
                elif "gmail.com" in self.imap['imap_server'].lower() or "gmail.com" in self.imap['imap_user'].lower():
                    print_box(
                        "Gmail登录失败",
                        [
                            (Colors.RED, "认证失败"),
                            (Colors.WHITE, ""),
                            (Colors.WHITE, "解决方法:"),
                            (Colors.WHITE, "1. 确认密码正确"),
                            (Colors.WHITE, "2. 确认已开启Gmail的低安全性应用访问"),
                            (Colors.WHITE, "3. 如使用两步验证，请使用应用专用密码")
                        ]
                    )
                # 通用错误处理
                else:
                    print_box(
                        "IMAP账号登录失败",
                        [
                            (Colors.RED, f"错误信息: {error_str}"),
                            (Colors.WHITE, ""),
                            (Colors.WHITE, "请检查:"),
                            (Colors.WHITE, "1. 邮箱账号是否正确"),
                            (Colors.WHITE, "2. 邮箱密码是否正确"),
                            (Colors.WHITE, "3. 是否已开启IMAP服务"),
                            (Colors.WHITE, ""),
                            (Colors.WHITE, "常见解决方案:"),
                            (Colors.WHITE, "• QQ邮箱: 设置 -> 账户 -> POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"),
                            (Colors.WHITE, "• Gmail: 设置 -> 查看所有设置 -> 转发和POP/IMAP -> 启用IMAP")
                        ]
                    )
                return False, f"IMAP账号登录失败: {error_str}"

            # 尝试选择邮箱目录
            try:
                log_process(f"正在选择邮箱目录 {self.imap['imap_dir']}...")
                mail.select(self.imap['imap_dir'])
                log_success("邮箱目录选择成功")
            except imaplib.IMAP4.error as e:
                error_str = str(e)
                if "nonexistent" in error_str.lower():
                    print_box(
                        "邮箱目录不存在",
                        [
                            (Colors.RED, f"目录 {self.imap['imap_dir']} 不存在，请检查目录名称是否正确"),
                            (Colors.WHITE, ""),
                            (Colors.WHITE, "常见目录名称:"),
                            (Colors.WHITE, "• INBOX - 收件箱"),
                            (Colors.WHITE, "• Sent - 已发送"),
                            (Colors.WHITE, "• Drafts - 草稿箱"),
                            (Colors.WHITE, "• Junk - 垃圾邮件")
                        ]
                    )
                else:
                    print_box("邮箱目录选择失败", [(Colors.RED, f"错误信息: {error_str}")])
                return False, f"邮箱目录选择失败: {error_str}"

            log_success("IMAP邮箱连接测试成功", show_box=True)
            return True, "IMAP邮箱连接测试成功"

        except Exception as e:
            log_error(f"IMAP连接测试出现未知错误: {str(e)}", show_box=True)
            return False, f"IMAP连接测试出现未知错误: {str(e)}"
        finally:
            if mail:
                try:
                    mail.logout()
                except:
                    pass

    def _test_temp_mail_connection(self):
        """测试临时邮箱连接是否正常"""
        log_process("正在测试临时邮箱连接...")
        try:
            # 尝试获取邮件列表来测试连接，设置超时时间
            mail_list_url = f"https://tempmail.plus/api/mails?email={self.temp_mail}&limit=1&epin={self.temp_mail_epin}"
            mail_list_response = self.session.get(mail_list_url, timeout=10)  # 设置10秒超时
            
            if mail_list_response.status_code != 200:
                log_error(f"临时邮箱连接失败，状态码: {mail_list_response.status_code}", show_box=True)
                print_box(
                    "临时邮箱连接错误",
                    [
                        (Colors.RED, f"错误信息: 服务器返回状态码 {mail_list_response.status_code}"),
                        (Colors.WHITE, ""),
                        (Colors.WHITE, "可能的原因:"),
                        (Colors.WHITE, "1. 临时邮箱地址错误"),
                        (Colors.WHITE, "2. PIN码错误"),
                        (Colors.WHITE, "3. 网络连接问题"),
                        (Colors.WHITE, ""),
                        (Colors.WHITE, "解决方案:"),
                        (Colors.WHITE, "1. 检查临时邮箱地址是否正确"),
                        (Colors.WHITE, "2. 检查PIN码是否正确"),
                        (Colors.WHITE, "3. 检查网络连接是否正常")
                    ]
                )
                return False, f"临时邮箱连接失败，状态码: {mail_list_response.status_code}"
                
            mail_list_data = mail_list_response.json()
            if not mail_list_data.get("result"):
                print_box(
                    "临时邮箱连接错误",
                    [
                        (Colors.RED, f"临时邮箱连接失败，无法获取邮件列表"),
                        (Colors.WHITE, ""),
                        (Colors.WHITE, "可能的原因:"),
                        (Colors.WHITE, "1. 临时邮箱地址错误"),
                        (Colors.WHITE, "2. PIN码错误"),
                        (Colors.WHITE, "3. 网络连接问题"),
                        (Colors.WHITE, ""),
                        (Colors.WHITE, "解决方案:"),
                        (Colors.WHITE, "1. 检查临时邮箱地址是否正确"),
                        (Colors.WHITE, "2. 检查PIN码是否正确"),
                        (Colors.WHITE, "3. 检查网络连接是否正常")
                    ]
                )
                return False, "临时邮箱连接失败，无法获取邮件列表"
                
            log_success("临时邮箱连接测试成功", show_box=True)
            return True, "临时邮箱连接测试成功"
            
        except requests.exceptions.Timeout:
            log_error("临时邮箱连接超时", show_box=True)
            return False, "临时邮箱连接超时，请检查网络连接或稍后重试"
        except requests.exceptions.ConnectionError:
            log_error("临时邮箱连接失败", show_box=True)
            return False, "临时邮箱连接失败，请检查网络连接"
        except Exception as e:
            log_error(f"临时邮箱连接测试出错: {str(e)}", show_box=True)
            return False, f"临时邮箱连接测试出错: {str(e)}"

    def clean_cursor_mails(self):
        """手动清理所有 Cursor 邮件"""
        print_box(
            "正在清理所有 Cursor 邮件...",
            [
                (Colors.WHITE, "如果长时间停留在这里，可能是因为:"),
                (Colors.YELLOW, "• 邮箱中积累了太多邮件"),
                (Colors.WHITE, "建议:"),
                (Colors.GREEN, "    1. 手动登录邮箱清理一下邮件"),
                (Colors.GREEN, "    2. 这样下次运行时就不会在这里卡很久"),
                (Colors.WHITE, ""),
                (Colors.WHITE, "如果不想自动清理邮件:"),
                (Colors.CYAN,"    • 在 .env 文件中设置关闭清理邮件:"),
                (Colors.CYAN,"    CLEAN_ALL_CURSOR_MAILS='False'")
            ]
        )

        if self.use_temp_mail:
            log_info("临时邮箱模式下不支持批量清理邮件", show_box=True)
            log_info("正在尝试清理所有临时邮箱邮件...", show_box=True)
            self._clean_all_temp_mails()
            return
            
        mail = None
        try:
            # 连接到IMAP服务器
            mail = imaplib.IMAP4_SSL(self.imap['imap_server'], self.imap['imap_port'])
            mail.login(self.imap['imap_user'], self.imap['imap_pass'])
            mail.select(self.imap['imap_dir'])
            
            # 清理邮件
            self._clean_old_cursor_mails(mail, show_tips=True)
            
        except Exception as e:
            log_error(f"清理邮件时出错: {str(e)}")
        finally:
            if mail:
                try:
                    mail.logout()
                except:
                    pass

    def get_verification_code(self, timeout=180, retry_interval=3, clean_emails=True):
        """
        获取验证码
        :param timeout: 获取验证码的超时时间（秒）
        :param retry_interval: 重试间隔（秒）
        :param clean_emails: 是否在验证完成后清理邮件，默认遵循环境变量设置
        :return: 验证码或None
        """
        start_time = time.time()
        retry_count = 0
        max_retries = int(timeout // retry_interval)
        code = None
        first_id = None
        while retry_count < max_retries:
            if self.should_stop:
                log_warn("检测到中断信号，验证码获取流程终止", show_box=True)
                return None
            # 检查是否超时
            if time.time() - start_time > timeout:
                log_error(f"获取验证码超时（{timeout}秒）", show_box=True)
                return None
            if retry_count > 0:
                log_process(f"等待验证码邮件中... (第 {retry_count + 1}/{max_retries} 次尝试)", show_box=True)
                for _ in range(retry_interval):
                    if self.should_stop:
                        log_warn("检测到中断信号，验证码获取流程终止", show_box=True)
                        return None
                    time.sleep(1)
            if self.should_stop:
                log_warn("检测到中断信号，验证码获取流程终止", show_box=True)
                return None
            # 根据配置选择使用哪种方式获取验证码
            if self.use_temp_mail:
                code, first_id = self._get_latest_mail_code()
                # 如果获取到验证码，清理邮件
                if code and clean_emails:
                    self._cleanup_mail(first_id)
                # 如果获取到验证码，记录邮件ID
                if code and first_id:
                    self.processed_mail_ids.add(first_id)
            else:
                code = self._get_mail_code_by_imap(retry_count, retry_interval)  # 传入重试间隔
                
                # 如果登录失败（返回None）且是第一次尝试，直接退出
                if code is None and retry_count == 0 and not self._is_login_success:
                    return None
            
            if code:
                return code
            
            retry_count += 1
        # 如果超过最大重试次数仍未找到验证码
        print_box(
            "未找到验证码邮件",
            [
                (Colors.RED, "未收到 Cursor 的验证码邮件"),
                (Colors.WHITE, ""),
                (Colors.WHITE, "可能的原因:"),
                (Colors.WHITE, "1. 邮件发送延迟"),
                (Colors.WHITE, "2. 部分QQ邮箱奇奇怪怪无法找到"),
                (Colors.WHITE, "3. 未配置 Cloudflare 或配置错误"),
                (Colors.WHITE, "4. 邮箱邮件过多或部分邮件过大，网络速度慢影响查找速度导致超时"),
                (Colors.WHITE, "解决方案:"),
                (Colors.WHITE, "1. 检查 Cloudflare 是否配置和配置是否正确"),
                (Colors.WHITE, "2. Mail Web 查看是否收到邮件"),
                (Colors.WHITE, "3. 把邮箱全部邮件标记为已读"),
                (Colors.WHITE, "3. 清空邮箱全部邮件"),
                (Colors.WHITE, "4. 尝试使用其他邮箱或临时邮箱")
            ]
        )
        return None

    def clean_after_verification(self):
        """在验证完成后清理邮件"""
        # 确保配置是最新的
        clean_setting = self.config.get_clean_all_cursor_mails()
        if self._should_clean_emails != clean_setting:
            log_info(f"更新清理邮件设置: {self._should_clean_emails} -> {clean_setting}", show_box=True)
            self._should_clean_emails = clean_setting
            
        if self._should_clean_emails:
            try:
                log_info(f"验证完成后清理所有邮件（根据配置）", show_box=True)
                self.clean_cursor_mails()
            except Exception as e:
                log_error(f"清理邮件失败: {str(e)}")
        else:
            log_info(f"验证完成后不清理邮件（根据配置）", show_box=True)

    # 使用imap获取邮件
    def _get_mail_code_by_imap(self, retry=0, retry_interval=3):
        """
        通过IMAP获取验证码
        :param retry: 重试次数
        :param retry_interval: 重试间隔（秒）
        :return: 验证码或None
        """
        max_retries = 30  # 最大重试次数
        if self.should_stop:
            log_warn("检测到中断信号，IMAP流程终止", show_box=True)
            return None
        if retry >= max_retries:
            log_error(f"已达到最大重试次数 ({max_retries})", show_box=True)
            return None
        if retry > 0:
            log_process(f"第 {retry} 次重试获取验证码...", show_box=True)
            for _ in range(retry_interval):
                if self.should_stop:
                    log_warn("检测到中断信号，IMAP流程终止", show_box=True)
                    return None
                time.sleep(1)
        mail = None
        try:
            if self.should_stop:
                log_warn("检测到中断信号，IMAP流程终止", show_box=True)
                return None
            log_process("正在连接IMAP服务器...")
            mail = imaplib.IMAP4_SSL(self.imap['imap_server'], self.imap['imap_port'])
            mail.socket().settimeout(30)
            if self.should_stop:
                log_warn("检测到中断信号，IMAP流程终止", show_box=True)
                return None
            log_process("正在登录IMAP邮箱...")
            mail.login(self.imap['imap_user'], self.imap['imap_pass'])
            log_success("IMAP登录成功", show_box=True)
            if self.should_stop:
                log_warn("检测到中断信号，IMAP流程终止", show_box=True)
                return None
            log_process("正在选择邮箱文件夹...")
            mail.select(self.imap['imap_dir'])
            log_success("邮箱文件夹选择成功", show_box=True)
            if self.should_stop:
                log_warn("检测到中断信号，IMAP流程终止", show_box=True)
                return None
            log_process("正在搜索Cursor验证码邮件...")
            status, messages = mail.search(None, 'FROM', '"<EMAIL>"')
            if self.should_stop:
                log_warn("检测到中断信号，IMAP流程终止", show_box=True)
                return None
            if status != 'OK':
                log_error("搜索邮件失败", show_box=True)
                return None
            mail_ids = messages[0].split()
            if not mail_ids:
                log_warn("未找到Cursor验证码邮件，准备重试...", show_box=True)
                if mail:
                    try:
                        mail.logout()
                    except:
                        pass
                if retry < max_retries - 1:
                    return self._get_mail_code_by_imap(retry=retry + 1)
                return None
            latest_mail_id = mail_ids[-1]
            log_success(f"找到最新的验证码邮件，ID: {latest_mail_id}", show_box=True)
            if self.should_stop:
                log_warn("检测到中断信号，IMAP流程终止", show_box=True)
                return None
            log_process("正在获取邮件内容...")
            status, msg_data = mail.fetch(latest_mail_id, '(RFC822)')
            if status != 'OK':
                log_error("获取邮件内容失败", show_box=True)
                return None
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)
            if self.should_stop:
                log_warn("检测到中断信号，IMAP流程终止", show_box=True)
                return None
            # 检查邮件时间是否在有效期内
            date_str = email_message['Date']
            if date_str:
                try:
                    mail_date = parsedate_to_datetime(date_str)
                    current_time = datetime.now(timezone.utc)
                    time_diff = (current_time - mail_date).total_seconds()
                    verification_timeout = self.config.get_verification_code_timeout()
                    
                    if time_diff > verification_timeout:
                        log_warn(f"验证码邮件已过期 ({int(time_diff)}秒前的邮件)", show_box=True)
                        # 删除过期邮件
                        try:
                            log_process("正在删除过期的验证码邮件...")
                            mail.store(latest_mail_id, '+FLAGS', '\\Deleted')
                            mail.expunge()
                            log_success("过期邮件删除成功", show_box=True)
                        except Exception as e:
                            log_warn(f"删除过期邮件时出错: {e}", show_box=True)
                        
                        # 安全登出
                        if mail:
                            try:
                                mail.logout()
                                log_success("已安全登出IMAP", show_box=True)
                            except:
                                pass
                                
                        # 如果还没达到最大重试次数，进行重试
                        if retry < max_retries - 1:
                            return self._get_mail_code_by_imap(retry=retry + 1, retry_interval=retry_interval)
                        return None
                        
                    # 显示邮件时间信息
                    if time_diff >= 60:
                        minutes = int(time_diff) // 60
                        seconds = int(time_diff) % 60
                        time_diff_str = f"{minutes}分{seconds}秒前"
                    else:
                        time_diff_str = f"{int(time_diff)}秒前"
                    log_info(f"邮件接收时间: {time_diff_str}", show_box=True)
                except Exception as e:
                    log_warn(f"解析邮件时间出错: {e}", show_box=True)

            # 提取邮件正文
            log_process("正在解析邮件内容...")
            body = self._extract_imap_body(email_message)
            if body:
                # 如果配置了显示邮件内容
                if self.config.get_show_verification_email_content():
                    print_box(
                        "验证码邮件内容",
                        [
                            (Colors.WHITE, "发件人: <EMAIL>"),
                            (Colors.WHITE, f"主题: {email_message['subject']}"),
                            (Colors.WHITE, ""),
                            (Colors.WHITE, "邮件正文:"),
                            (Colors.CYAN, body)
                        ]
                    )

                # 使用正则表达式查找验证码
                code_match = re.search(self.verification_code_pattern, body)
                
                # 如果使用默认或用户配置的正则表达式没找到验证码，尝试使用备用正则表达式
                if not code_match:
                    # log_info("未通过标准正则匹配到验证码，尝试匹配带空格的验证码格式...", show_box=True)
                    code_match = re.search(SPACED_VERIFICATION_CODE_PATTERN, body)
                    
                    # 如果匹配到带空格的验证码，去除空格
                    if code_match:
                        spaced_code = code_match.group()
                        code = ''.join(spaced_code.split())
                        
                        # 验证提取出的验证码长度是否正确（应为6位）
                        if len(code) != 6:
                            log_warn(f"提取的验证码长度不正确: {code}，长度为{len(code)}，应为6位", show_box=True)
                            code_match = None
                        else:
                            # log_success(f"成功匹配到带空格的验证码，处理后: {code}", show_box=True)
                            pass
                else:
                    code = code_match.group()
                
                if code_match:
                    if not 'code' in locals():  # 如果没有通过带空格格式匹配，则从code_match获取
                        code = code_match.group()
                    
                    # 成功获取验证码，显示结果
                    log_success(f"成功从IMAP邮箱获取验证码: {code}", show_box=True)
                    
                    # 删除邮件
                    try:
                        log_process("正在删除已处理的邮件...")
                        mail.store(latest_mail_id, '+FLAGS', '\\Deleted')
                        mail.expunge()
                    except Exception as e:
                        log_warn(f"删除邮件时出错: {e}", show_box=True)
                    
                    try:
                        mail.logout()
                        log_success("已安全登出IMAP", show_box=True)
                    except:
                        pass

                    return code
                else:
                    log_warn("邮件内容中未找到验证码", show_box=True)
            else:
                log_warn("无法提取邮件正文", show_box=True)
            
            if mail:
                try:
                    mail.logout()
                    log_success("已安全登出IMAP", show_box=True)
                except:
                    pass
            return None
            
        except imaplib.IMAP4.abort as e:
            log_error(f"IMAP连接中断: {e}", show_box=True)
            if mail:
                try:
                    mail.logout()
                except:
                    pass
            if retry < max_retries - 1:  # 如果还没达到最大重试次数
                return self._get_mail_code_by_imap(retry=retry + 1)
            return None
            
        except imaplib.IMAP4.error as e:
            log_error(f"IMAP操作错误: {e}", show_box=True)
            if mail:
                try:
                    mail.logout()
                except:
                    pass
            return None
            
        except Exception as e:
            log_error(f"获取验证码时发生未知错误: {e}", show_box=True)
            if mail:
                try:
                    mail.logout()
                except:
                    pass
            return None

    def _extract_imap_body(self, email_message):
        """
        从邮件中提取正文，支持HTML和纯文本格式
        :param email_message: 邮件对象
        :return: 邮件正文
        """
        # 提取邮件正文
        plain_text = ""

        # 记录消息处理信息
        log_info("正在尝试提取邮件正文...", show_box=False)
        
        # 处理多部分邮件
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))

                # 忽略附件
                if "attachment" in content_disposition:
                    continue

                # 处理纯文本部分和HTML部分（都按纯文本方式处理）
                if content_type == "text/plain" or content_type == "text/html":
                    charset = part.get_content_charset() or 'utf-8'
                    try:
                        plain_text = part.get_payload(decode=True).decode(charset, errors='ignore')
                        if content_type == "text/plain":
                            log_info("成功从text/plain部分提取邮件正文", show_box=False)
                        else:
                            log_info("成功从text/html部分提取邮件正文（按纯文本处理）", show_box=False)
                        break  # 找到内容后结束循环
                    except Exception as e:
                        log_warn(f"从{content_type}部分解码邮件正文失败: {e}", show_box=False)
                        # 尝试其他常见编码
                        try:
                            data = part.get_payload(decode=True)
                            for enc in ['utf-8', 'latin-1', 'ascii', 'iso-8859-1']:
                                try:
                                    plain_text = data.decode(enc, errors='ignore')
                                    log_info(f"使用{enc}编码成功解码{content_type}邮件正文", show_box=False)
                                    break
                                except:
                                    continue
                        except Exception as e2:
                            log_warn(f"尝试替代编码也失败: {e2}", show_box=False)
        else:
            # 处理单部分邮件
            content_type = email_message.get_content_type()
            charset = email_message.get_content_charset() or 'utf-8'

            try:
                if content_type == "text/plain" or content_type == "text/html":
                    plain_text = email_message.get_payload(decode=True).decode(charset, errors='ignore')
                    if content_type == "text/plain":
                        log_info("成功从非多部分邮件提取纯文本正文", show_box=False)
                    else:
                        log_info("成功从非多部分邮件提取HTML正文（按纯文本处理）", show_box=False)
                else:
                    log_warn(f"不支持的邮件内容类型: {content_type}", show_box=False)
            except Exception as e:
                log_warn(f"解码非多部分邮件失败: {e}", show_box=False)
                # 尝试其他常见编码
                try:
                    data = email_message.get_payload(decode=True)
                    if data:
                        for enc in ['utf-8', 'latin-1', 'ascii', 'iso-8859-1']:
                            try:
                                if content_type == "text/plain" or content_type == "text/html":
                                    plain_text = data.decode(enc, errors='ignore')
                                    log_info(f"使用{enc}编码成功解码{content_type}邮件", show_box=False)
                                    break
                            except:
                                continue
                except Exception as e2:
                    log_warn(f"尝试替代编码也失败: {e2}", show_box=False)

        # 返回结果
        if plain_text:
            return plain_text

        log_warn("未能提取到任何邮件内容", show_box=False)
        return ""

    # 临时邮箱相关方法
    def _get_latest_mail_code(self):
        """
        获取临时邮箱最新邮件中的验证码
        :return: (验证码, 邮件ID) 或 (None, None)
        """
        max_retries = 30
        retry_interval = 3
        try:
            for i in range(max_retries):
                if self.should_stop:
                    log_warn("检测到中断信号，临时邮箱流程终止", show_box=True)
                    return None, None
                # 获取邮件列表
                log_process("正在从临时邮箱获取邮件列表...")
                mail_list_url = f"https://tempmail.plus/api/mails?email={self.temp_mail}&limit=20&epin={self.temp_mail_epin}"
                mail_list_response = self.session.get(mail_list_url)
                if self.should_stop:
                    log_warn("检测到中断信号，临时邮箱流程终止", show_box=True)
                    return None, None
                if mail_list_response.status_code != 200:
                    log_error(f"获取邮件列表失败，状态码: {mail_list_response.status_code}", show_box=True)
                    return None, None
                mail_list_data = mail_list_response.json()
                if self.should_stop:
                    log_warn("检测到中断信号，临时邮箱流程终止", show_box=True)
                    return None, None
                time.sleep(0.5)
                if self.should_stop:
                    log_warn("检测到中断信号，临时邮箱流程终止", show_box=True)
                    return None, None
                if not mail_list_data.get("result"):
                    log_warn("邮件列表为空或获取失败", show_box=True)
                    return None, None
                # 获取最新邮件的ID
                first_id = mail_list_data.get("first_id")
                if not first_id:
                    log_warn("未找到最新邮件ID", show_box=True)
                    return None, None
                if first_id in self.processed_mail_ids:
                    log_info(f"邮件ID {first_id} 已处理过，跳过", show_box=True)
                    return None, None
                # 获取具体邮件内容
                log_process("正在获取邮件内容...")
                mail_detail_url = f"https://tempmail.plus/api/mails/{first_id}?email={self.temp_mail}&epin={self.temp_mail_epin}"
                mail_detail_response = self.session.get(mail_detail_url)
                if self.should_stop:
                    log_warn("检测到中断信号，临时邮箱流程终止", show_box=True)
                    return None, None
                if mail_detail_response.status_code != 200:
                    log_error(f"获取邮件内容失败，状态码: {mail_detail_response.status_code}", show_box=True)
                    return None, None
                mail_detail_data = mail_detail_response.json()
                if self.should_stop:
                    log_warn("检测到中断信号，临时邮箱流程终止", show_box=True)
                    return None, None
                time.sleep(0.5)
                if self.should_stop:
                    log_warn("检测到中断信号，临时邮箱流程终止", show_box=True)
                    return None, None
                # 检查邮件发件人是否为Cursor
                mail_from = mail_detail_data.get("from_mail", "")
                if "cursor.sh" not in mail_from.lower():
                    log_warn(f"邮件不是来自Cursor，发件人: {mail_from}", show_box=True)
                    return None, None
                # 检查邮件时间是否在有效期内
                mail_date_str = mail_detail_data.get("time", "")
                if mail_date_str:
                    try:
                        try:
                            mail_date = datetime.strptime(mail_date_str, '%a, %d %b %Y %H:%M:%S %z')
                        except ValueError:
                            try:
                                mail_date = datetime.strptime(mail_date_str, '%Y-%m-%d %H:%M:%S')
                                mail_date = mail_date.replace(tzinfo=timezone.utc)
                            except ValueError:
                                mail_date = parsedate_to_datetime(mail_date_str)
                        current_time = datetime.now(timezone.utc)
                        time_diff = (current_time - mail_date).total_seconds()
                        verification_timeout = self.config.get_verification_code_timeout()
                        if time_diff > verification_timeout:
                            log_warn(f"验证码邮件已过期 ({int(time_diff)}秒前的邮件)", show_box=True)
                            return None, None
                        if time_diff >= 60:
                            minutes = int(time_diff) // 60
                            seconds = int(time_diff) % 60
                            time_diff_str = f"{minutes}分{seconds}秒前"
                        else:
                            time_diff_str = f"{int(time_diff)}秒前"
                        log_info(f"邮件接收时间: {time_diff_str}", show_box=True)
                    except Exception as e:
                        log_warn(f"解析邮件时间出错: {str(e)}", show_box=True)
                mail_text = mail_detail_data.get("text", "")
                if self.config.get_show_verification_email_content():
                    print_box(
                        "验证码邮件内容",
                        [
                            (Colors.WHITE, f"发件人: {mail_from}"),
                            (Colors.WHITE, f"主题: {mail_detail_data.get('subject', '无主题')}"),
                            (Colors.WHITE, ""),
                            (Colors.WHITE, "邮件正文:"),
                            (Colors.CYAN, mail_text)
                        ]
                    )
                code_match = re.search(self.verification_code_pattern, mail_text)
                if not code_match:
                    # log_info("未通过标准正则匹配到验证码，尝试匹配带空格的验证码格式...", show_box=True)
                    code_match = re.search(SPACED_VERIFICATION_CODE_PATTERN, mail_text)
                    if code_match:
                        spaced_code = code_match.group()
                        code = ''.join(spaced_code.split())
                        if len(code) != 6:
                            log_warn(f"提取的验证码长度不正确: {code}，长度为{len(code)}，应为6位", show_box=True)
                            code_match = None
                        else:
                            # log_success(f"成功匹配到带空格的验证码，处理后: {code}", show_box=True)
                            pass
                else:
                    code = code_match.group()
                if code_match:
                    if not 'code' in locals():
                        code = code_match.group()
                    log_success(f"成功从临时邮箱获取验证码: {code}", show_box=True)
                    return code, first_id
                log_warn("未在邮件中找到验证码", show_box=True)
                return None, None
        except Exception as e:
            log_error(f"获取临时邮箱验证码时出错: {str(e)}", show_box=True)
            return None, None

    def _cleanup_mail(self, first_id):
        """
        清理临时邮箱中的邮件
        :param first_id: 邮件ID
        :return: 是否成功
        """
        if not first_id:
            return False
            
        try:
            # 构造删除请求的URL和数据
            log_process("正在清理临时邮箱邮件...")
            delete_url = "https://tempmail.plus/api/mails/"
            payload = {
                "email": self.temp_mail,
                "first_id": first_id,
                "epin": self.temp_mail_epin,
            }

            # 最多尝试5次
            for attempt in range(5):
                response = self.session.delete(delete_url, data=payload)
                try:
                    result = response.json().get("result")
                    if result is True:
                        log_success("临时邮箱邮件清理成功", show_box=True)
                        return True
                except:
                    pass

                # 如果失败,等待0.5秒后重试
                time.sleep(0.5)
                
            log_warn("临时邮箱邮件清理失败", show_box=True)
            return False
            
        except Exception as e:
            log_error(f"清理临时邮箱邮件时出错: {str(e)}", show_box=True)
            return False
            
    def _clean_all_temp_mails(self):
        """
        清理临时邮箱中的所有邮件
        :return: 是否成功
        """
        if not self.use_temp_mail:
            return False
            
        try:
            # 获取邮件列表
            log_process("正在获取临时邮箱所有邮件...")
            mail_list_url = f"https://tempmail.plus/api/mails?email={self.temp_mail}&limit=100&epin={self.temp_mail_epin}"
            mail_list_response = self.session.get(mail_list_url)
            
            if mail_list_response.status_code != 200:
                log_error(f"获取邮件列表失败，状态码: {mail_list_response.status_code}", show_box=True)
                return False
                
            mail_list_data = mail_list_response.json()
            
            if not mail_list_data.get("result"):
                log_info("临时邮箱中没有邮件需要清理", show_box=True)
                return True
                
            # 获取所有邮件ID
            mail_ids = mail_list_data.get("mails", [])
            if not mail_ids:
                log_info("临时邮箱中没有邮件需要清理", show_box=True)
                return True
                
            log_info(f"找到 {len(mail_ids)} 封邮件需要清理", show_box=True)
            
            # 清理每一封邮件
            success_count = 0
            for mail in mail_ids:
                mail_id = mail.get("id")
                if mail_id and self._cleanup_mail(mail_id):
                    success_count += 1
                    
            if success_count > 0:
                log_success(f"成功清理 {success_count}/{len(mail_ids)} 封临时邮箱邮件", show_box=True)
                return True
            else:
                log_warn("没有成功清理任何临时邮箱邮件", show_box=True)
                return False
                
        except Exception as e:
            log_error(f"清理所有临时邮箱邮件时出错: {str(e)}", show_box=True)
            return False

    def _clean_old_cursor_mails(self, mail, show_tips=False):
        """
        清理所有旧的Cursor邮件
        :param mail: IMAP连接
        :param show_tips: 是否显示提示
        """
        # 只在IMAP模式下有效
        if self.use_temp_mail:
            return
            
        try:
            # 搜索所有来自Cursor的邮件
            status, messages = mail.search(None, 'FROM', '"<EMAIL>"')
            if status != 'OK':
                return

            mail_ids = messages[0].split()
            if not mail_ids:
                if show_tips:
                    log_info("没有找到需要清理的Cursor邮件", show_box=True)
                return

            # 删除所有邮件
            for mail_id in mail_ids:
                mail.store(mail_id, '+FLAGS', '\\Deleted')
            
            # 提交删除操作
            mail.expunge()
            
            if show_tips:
                log_success(f"成功清理 {len(mail_ids)} 封Cursor邮件", show_box=True)
                
        except Exception as e:
            if show_tips:
                log_error(f"清理Cursor邮件时出错: {str(e)}", show_box=True)

    def set_should_stop(self, value=True):
        """设置停止标志"""
        self.should_stop = value
        # log_warn(f"验证码处理流程停止标志已设置为: {value}", show_box=True)
        
    def cleanup(self):
        """清理资源，终止所有操作，确保对象可以被安全释放"""
        # 设置停止标志
        self.should_stop = True
        # log_warn(f"验证码处理流程停止标志已设置为: {self.should_stop}", show_box=True)
        
        # 关闭会话
        if hasattr(self, 'session') and self.session:
            try:
                self.session.close()
                # log_info("已关闭临时邮箱会话", show_box=True)
            except Exception as e:
                log_warn(f"关闭临时邮箱会话时出错: {str(e)}", show_box=True)
            
            # 显式删除会话对象
            del self.session
            self.session = None
        
        # 清理IMAP连接（以防万一）
        if not self.use_temp_mail:
            try:
                # 尝试安全关闭任何可能存在的IMAP连接
                import imaplib
                for obj_name in list(dir(self)):
                    obj = getattr(self, obj_name)
                    if isinstance(obj, imaplib.IMAP4):
                        try:
                            obj.logout()
                            log_info(f"已关闭遗留的IMAP连接: {obj_name}", show_box=True)
                        except:
                            pass
            except Exception as e:
                log_warn(f"清理IMAP连接时出错: {str(e)}", show_box=True)
        
        # 清空其他引用
        if hasattr(self, 'processed_mail_ids'):
            self.processed_mail_ids.clear()
        
        # 重置所有内部状态
        self._should_clean_emails = self.config.get_clean_all_cursor_mails()  # 重置为配置的值
        self._is_login_success = True
        self._connection_success = False

        # 打印完成消息
        # log_info("EmailVerificationHandler 资源已彻底清理完成", show_box=True)
        
        # 手动触发垃圾回收
        try:
            import gc
            gc.collect()
        except:
            pass

    # 新增方法，返回连接错误信息
    def get_connection_error(self):
        """获取连接错误信息"""
        return self._connection_error_message

def test_imap_connection(imap_server, imap_port, imap_user, imap_pass, imap_dir="INBOX"):
    """公共函数：测试IMAP连接是否正常
    
    Args:
        imap_server: IMAP服务器
        imap_port: IMAP端口
        imap_user: IMAP用户名
        imap_pass: IMAP密码
        imap_dir: IMAP邮箱目录，默认为INBOX
        
    Returns:
        tuple: (success, message)，success为布尔值，message为成功或失败的消息
    """
    mail = None
    try:
        # 检查IMAP服务器配置
        if not imap_server or not imap_port:
            return False, "IMAP服务器配置无效，请检查服务器地址和端口是否设置正确"

        # 尝试连接IMAP服务器
        try:
            log_process(f"正在连接到 {imap_server}:{imap_port}...")
            mail = imaplib.IMAP4_SSL(imap_server, imap_port)
            log_success("IMAP服务器连接成功")
        except (imaplib.IMAP4.error, ssl.SSLError, ConnectionRefusedError) as e:
            error_str = str(e)
            if "Connection refused" in error_str:
                return False, "IMAP服务器连接被拒绝，请检查服务器地址和端口是否正确"
            elif "certificate verify failed" in error_str:
                return False, "IMAP服务器证书验证失败，可能是服务器证书过期或无效"
            elif "timed out" in error_str:
                return False, "IMAP服务器连接超时，请检查网络连接和服务器状态"
            else:
                return False, f"IMAP服务器连接失败: {error_str}"

        # 尝试登录
        try:
            log_process(f"正在使用账号 {imap_user} 登录...")
            mail.login(imap_user, imap_pass)
            log_success("IMAP账号登录成功")
        except imaplib.IMAP4.error as e:
            error_str = str(e)
            
            # 对QQ邮箱错误消息进行特殊处理，使其更易于理解
            if "qq.com" in imap_server.lower() or "qq.com" in imap_user.lower():
                # 检查常见的QQ邮箱错误
                if "Login fail" in error_str or "login fail" in error_str.lower():
                    # 更通用的IMAP邮箱授权码错误信息
                    detailed_error = (
                        "IMAP邮箱登录失败：密码错误或IMAP服务未开启\n\n"
                        "解决方法：\n"
                        "1. 请检查邮箱密码是否正确\n"
                        "2. 确认是否已在邮箱设置中开启IMAP服务\n"
                        "3. 如使用QQ邮箱，请使用授权码而非QQ密码登录"
                    )
                    return False, detailed_error
                elif "service is not open" in error_str.lower():
                    # 更通用的IMAP服务未开启错误信息
                    detailed_error = (
                        "IMAP服务未开启\n\n"
                        "解决方法：\n"
                        "1. 请在邮箱设置中开启IMAP服务\n"
                        "2. 部分邮箱可能需要使用专用密码或授权码"
                    )
                    return False, detailed_error
            # 通用的错误处理
            else:
                if "password is incorrect" in error_str.lower() or "authentication failed" in error_str.lower():
                    return False, f"邮箱登录失败: 用户名或密码不正确"
                elif "too many simultaneous connections" in error_str.lower():
                    return False, f"邮箱登录失败: 连接数过多，请稍后再试"
                else:
                    return False, f"邮箱登录失败: {error_str}"

        # 尝试选择邮箱目录
        try:
            log_process(f"正在选择邮箱目录 {imap_dir}...")
            mail.select(imap_dir)
            log_success("邮箱目录选择成功")
        except imaplib.IMAP4.error as e:
            error_str = str(e)
            if "nonexistent" in error_str.lower():
                return False, f"邮箱目录 {imap_dir} 不存在，请检查目录名称是否正确"
            else:
                return False, f"邮箱目录选择失败: {error_str}"

        log_success("IMAP邮箱连接测试成功", show_box=True)
        return True, "IMAP邮箱连接测试成功"

    except Exception as e:
        return False, f"IMAP连接测试出现未知错误: {str(e)}"
    finally:
        if mail:
            try:
                mail.logout()
            except:
                pass

def test_temp_mail(temp_mail, temp_mail_epin):
    """公共函数：测试临时邮箱连接是否正常
    
    Args:
        temp_mail: 临时邮箱地址
        temp_mail_epin: 临时邮箱PIN码
        
    Returns:
        tuple: (success, message)，success为布尔值，message为成功或失败的消息
    """
    try:
        # 检查临时邮箱配置
        if not temp_mail or not temp_mail_epin:
            return False, "临时邮箱配置无效"
            
        # 创建会话
        session = requests.Session()
        
        # 尝试获取邮件列表来测试连接
        log_process("正在测试临时邮箱连接...")
        mail_list_url = f"https://tempmail.plus/api/mails?email={temp_mail}&limit=1&epin={temp_mail_epin}"
        mail_list_response = session.get(mail_list_url)
        
        if mail_list_response.status_code != 200:
            return False, f"临时邮箱连接失败，状态码: {mail_list_response.status_code}"
            
        mail_list_data = mail_list_response.json()
        if not mail_list_data.get("result"):
            return False, "临时邮箱连接失败，无法获取邮件列表"
            
        log_success("临时邮箱连接测试成功", show_box=True)
        return True, "临时邮箱连接测试成功"
        
    except Exception as e:
        return False, f"临时邮箱连接测试出错: {str(e)}"

if __name__ == "__main__":
    email_handler = EmailVerificationHandler()
    code = email_handler.get_verification_code()
    print(f"获取到的验证码: {code}")
