#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试自动注册功能的警告修复
验证单独运行自动注册时不再出现导入警告
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PySide6.QtWidgets import QApplication
from widgets.auto_register_dialog import AutoRegisterWorker
from PySide6.QtCore import QThread
import threading

def test_worker_instance_setting():
    """测试worker_instance设置是否会产生警告"""
    print("🧪 测试AutoRegisterWorker的worker_instance设置...")
    
    app = QApplication(sys.argv)
    
    # 创建一个简单的测试配置
    test_config = {
        'email': '<EMAIL>',
        'password': 'test123',
        'email_type': 'temp',
        'manual_cf': False,
        'bypass_card': True
    }
    
    # 创建工作线程
    worker = AutoRegisterWorker(test_config)
    
    # 模拟execute_auto_register中的worker_instance设置部分
    print("\n📝 测试worker_instance设置逻辑...")
    
    try:
        # 检查是否已经导入了cursor_pro_keep_alive模块
        import sys
        print(f"当前已导入的模块数量: {len(sys.modules)}")
        
        cursor_modules = [name for name in sys.modules.keys() if 'cursor_pro_keep_alive' in name]
        print(f"包含cursor_pro_keep_alive的模块: {cursor_modules}")
        
        if 'core.cursor_auto.cursor_pro_keep_alive' in sys.modules:
            print("✅ 找到core.cursor_auto.cursor_pro_keep_alive模块")
            cursor_pro_keep_alive = sys.modules['core.cursor_auto.cursor_pro_keep_alive']
            cursor_pro_keep_alive.worker_instance = worker
            print("✅ 已设置全局worker_instance")
        elif 'cursor_pro_keep_alive' in sys.modules:
            print("✅ 找到cursor_pro_keep_alive模块")
            cursor_pro_keep_alive = sys.modules['cursor_pro_keep_alive']
            cursor_pro_keep_alive.worker_instance = worker
            print("✅ 已设置全局worker_instance")
        else:
            print("ℹ️ cursor_pro_keep_alive模块未导入，跳过worker_instance设置")
            print("ℹ️ 这是正常情况，单独运行时不需要设置")
            
    except Exception as e:
        print(f"⚠️ worker_instance设置出现异常: {str(e)}")
        print("ℹ️ 这个异常不影响功能")
    
    print("\n✅ 测试完成！")
    print("📋 预期结果：")
    print("   - 单独运行时应该显示'模块未导入，跳过设置'")
    print("   - 快捷模式运行时应该正常设置worker_instance")
    print("   - 不应该出现导入错误警告")
    
    return True

def test_import_paths():
    """测试不同的导入路径"""
    print("\n🔍 测试cursor_pro_keep_alive模块的导入路径...")
    
    # 测试1: 直接导入
    try:
        import cursor_pro_keep_alive
        print("✅ 直接导入cursor_pro_keep_alive成功")
    except ImportError as e:
        print(f"❌ 直接导入失败: {e}")
    
    # 测试2: 相对导入
    try:
        from core.cursor_auto import cursor_pro_keep_alive
        print("✅ 相对导入core.cursor_auto.cursor_pro_keep_alive成功")
    except ImportError as e:
        print(f"❌ 相对导入失败: {e}")
    
    # 测试3: 绝对路径导入
    try:
        import sys
        import os
        cursor_auto_path = os.path.join(os.path.dirname(__file__), "core", "cursor_auto")
        if cursor_auto_path not in sys.path:
            sys.path.insert(0, cursor_auto_path)
        import cursor_pro_keep_alive
        print("✅ 绝对路径导入cursor_pro_keep_alive成功")
    except ImportError as e:
        print(f"❌ 绝对路径导入失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试自动注册警告修复...")
    
    test_import_paths()
    test_worker_instance_setting()
    
    print("\n🎉 所有测试完成！")
    print("\n💡 如果要完全验证修复效果，请：")
    print("   1. 运行单独的自动注册功能")
    print("   2. 检查是否还有任何worker_instance相关的日志输出")
    print("   3. 运行快捷一键功能中的自动注册")
    print("   4. 验证按钮是否正常显示")
    print("\n✅ 预期结果：")
    print("   - 单独运行：完全没有worker_instance相关的日志")
    print("   - 快捷模式：静默设置worker_instance，按钮正常工作")
