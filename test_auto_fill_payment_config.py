#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试自动填写支付宝支付信息配置
验证新增的配置项是否正确工作
"""

import sys
import os
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PySide6.QtWidgets import QApplication
from widgets.auto_register_settings import AutoRegisterSettingsDialog
from utils import get_app_data_dir

def test_auto_fill_payment_config():
    """测试自动填写支付信息配置的保存和加载"""
    print("🧪 测试自动填写支付宝支付信息配置...")
    
    app = QApplication(sys.argv)
    
    # 创建设置对话框
    settings_dialog = AutoRegisterSettingsDialog()
    
    print("\n📝 测试配置项:")
    
    # 测试1: 检查UI控件是否正确创建
    print("1. 检查UI控件...")
    if hasattr(settings_dialog, 'auto_fill_payment_switch'):
        print("✅ auto_fill_payment_switch 控件已创建")
        print(f"   默认状态: {'开启' if settings_dialog.auto_fill_payment_switch.isChecked() else '关闭'}")
    else:
        print("❌ auto_fill_payment_switch 控件未找到")
        return False
    
    # 测试2: 测试配置保存
    print("\n2. 测试配置保存...")
    
    # 设置为关闭状态
    settings_dialog.auto_fill_payment_switch.setChecked(False)
    settings_dialog.save_settings()
    
    # 检查设置是否正确保存
    settings_file = os.path.join(get_app_data_dir(), "settings.json")
    if os.path.exists(settings_file):
        with open(settings_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
            auto_fill_payment = settings.get("auto_register_auto_fill_payment", True)
            print(f"✅ 配置已保存: auto_register_auto_fill_payment = {auto_fill_payment}")
            if auto_fill_payment == False:
                print("✅ 关闭状态保存正确")
            else:
                print("❌ 关闭状态保存失败")
    else:
        print("❌ 设置文件未找到")
        return False
    
    # 测试3: 设置为开启状态
    print("\n3. 测试开启状态...")
    settings_dialog.auto_fill_payment_switch.setChecked(True)
    settings_dialog.save_settings()
    
    # 检查设置是否正确保存
    with open(settings_file, 'r', encoding='utf-8') as f:
        settings = json.load(f)
        auto_fill_payment = settings.get("auto_register_auto_fill_payment", True)
        print(f"✅ 配置已保存: auto_register_auto_fill_payment = {auto_fill_payment}")
        if auto_fill_payment == True:
            print("✅ 开启状态保存正确")
        else:
            print("❌ 开启状态保存失败")
    
    # 测试4: 测试配置加载
    print("\n4. 测试配置加载...")
    
    # 创建新的设置对话框实例
    new_settings_dialog = AutoRegisterSettingsDialog()
    
    # 检查是否正确加载了配置
    loaded_state = new_settings_dialog.auto_fill_payment_switch.isChecked()
    print(f"✅ 配置加载状态: {'开启' if loaded_state else '关闭'}")
    
    if loaded_state == True:
        print("✅ 配置加载正确")
    else:
        print("❌ 配置加载失败")
    
    print("\n✅ 所有配置测试完成！")
    return True

def test_config_in_core_module():
    """测试核心模块中的配置读取"""
    print("\n🔍 测试核心模块中的配置读取...")
    
    # 模拟核心模块中的配置读取逻辑
    try:
        from utils import get_app_data_dir
        import json
        
        bypass_card_enabled = False  # 默认关闭绕过绑卡
        auto_fill_payment_enabled = True  # 默认开启自动填写支付信息
        
        settings_file = os.path.join(get_app_data_dir(), "settings.json")
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                bypass_card_enabled = settings.get("auto_register_bypass_card", False)
                auto_fill_payment_enabled = settings.get("auto_register_auto_fill_payment", True)
        
        print(f"✅ 绕过绑卡配置: {'开启' if bypass_card_enabled else '关闭'}")
        print(f"✅ 自动填写支付信息配置: {'开启' if auto_fill_payment_enabled else '关闭'}")
        
        # 模拟不同配置组合的逻辑
        print("\n📋 配置组合测试:")
        
        if bypass_card_enabled:
            print("   场景1: 绕过绑卡开启 → 直接注册普通账号，无需支付流程")
        else:
            if auto_fill_payment_enabled:
                print("   场景2: 绕过绑卡关闭 + 自动填写开启 → 自动填写支付信息后等待用户确认")
            else:
                print("   场景3: 绕过绑卡关闭 + 自动填写关闭 → 点击Continue后直接等待用户手动操作")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心模块配置读取测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试自动填写支付宝支付信息配置...")
    
    success1 = test_auto_fill_payment_config()
    success2 = test_config_in_core_module()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        print("\n💡 使用说明:")
        print("   1. 在设置 → 自动注册配置中可以看到新的'自动填写支付宝支付信息'开关")
        print("   2. 默认开启：自动填写支付信息后等待用户扫码确认")
        print("   3. 关闭后：点击Continue按钮后直接显示'我已完成绑定，继续'按钮")
        print("   4. 配置会自动保存到settings.json文件中")
    else:
        print("\n❌ 测试失败，请检查代码实现")
    
    print("\n📋 建议测试流程:")
    print("   1. 打开设置 → 自动注册配置")
    print("   2. 关闭'绕过绑卡直接注册普通账号'")
    print("   3. 测试'自动填写支付宝支付信息'开关的不同状态")
    print("   4. 运行自动注册验证行为差异")
