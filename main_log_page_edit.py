def create_log_page(self):
    """创建日志页面"""
    # 导入日志模块
    from logger import Logger, read_today_log, open_log_dir
    
    # 创建主页面容器
    page = QWidget()
    page.setStyleSheet("background: transparent;")
    main_layout = QVBoxLayout(page)
    main_layout.setContentsMargins(0, 0, 0, 0)  # 修改为0边距与功能页一致
    main_layout.setSpacing(25)  # 修改为25与功能页一致
    
    # 日志容器 - 使用StyledFrame代替QWidget，应用玻璃效果
    log_container = StyledFrame(has_glass_effect=True)
    log_container.setObjectName("log_container")
    # 使用主题定义的玻璃效果样式
    log_container.setStyleSheet(f"""
        StyledFrame {{
            background-color: {Theme.GLASS_BG};
            border-radius: {Theme.BORDER_RADIUS};
            border: 1px solid {Theme.GLASS_BORDER};
        }}
    """)
    log_layout = QVBoxLayout(log_container)
    log_layout.setContentsMargins(25, 25, 25, 25)  # 修改内边距为25px与功能页一致
    log_layout.setSpacing(15)
    
    # 顶部区域：日志级别筛选按钮和操作按钮
    top_area = QWidget()
    top_area.setStyleSheet(f"""
        background-color: {Theme.CARD_LEVEL_2};  # 使用主题定义的卡片层级2颜色
        border-radius: {Theme.BORDER_RADIUS_SMALL};
        margin: 4px 2px;
    """)
    top_layout = QHBoxLayout(top_area)
    top_layout.setContentsMargins(15, 12, 15, 12)
    top_layout.setSpacing(15)
    
    # 创建日志级别筛选按钮
    # 存储日志级别按钮的字典，以便稍后可以访问它们
    self.log_filter_buttons = {}
    self.active_log_filters = set()  # 当前激活的过滤器集合（空表示显示所有日志）
    
    # 日志级别及其颜色
    log_levels = {
        "信息": "#4fc3f7",  # 蓝色
        "警告": "#ffb74d",  # 橙色
        "错误": "#ef5350",  # 红色
        "调试": "#9ccc65",  # 绿色
        "严重": "#d32f2f"   # 深红色
    }
    
    # 创建筛选按钮
    for level_name, color in log_levels.items():
        filter_btn = QPushButton(level_name)
        filter_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        filter_btn.setCheckable(True)  # 设置按钮为可选中状态
        
        # 设置按钮样式
        filter_btn.setStyleSheet(f"""
            QPushButton {{
                color: {color};
                background-color: transparent;
                border: 1px solid {color};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 4px 8px;
                font-size: {Theme.FONT_SIZE_SMALL};
                font-weight: bold;
                min-width: 45px;
            }}
            QPushButton:hover {{
                background-color: rgba({', '.join(str(int(color.lstrip('#')[i:i+2], 16)) for i in (0, 2, 4))}, 0.2);
            }}
            QPushButton:checked {{
                background-color: {color};
                color: white;
            }}
        """)
        
        # 连接按钮点击事件 - 使用Lambda函数捕获当前的level_name和checked状态
        filter_btn.clicked.connect(lambda checked, level=level_name: self._toggle_log_filter(level, checked))
        
        # 添加到布局和按钮字典
        top_layout.addWidget(filter_btn)
        self.log_filter_buttons[level_name] = filter_btn
    
    # 添加弹性空间，让操作按钮靠右显示
    top_layout.addStretch()
    
    # 创建操作按钮
    # 打开文件夹按钮
    open_logs_btn = QPushButton("打开文件夹")
    open_logs_btn.setCursor(Qt.CursorShape.PointingHandCursor)
    open_logs_btn.setStyleSheet(f"""
        QPushButton {{
            color: {Theme.TEXT_PRIMARY};
            background-color: #d6b16a;
            border: none;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            padding: 8px 15px;
            font-size: {Theme.FONT_SIZE_SMALL};
            min-width: 80px;
            max-height: 28px;
        }}
        QPushButton:hover {{
            background-color: {Theme.WARNING};
            color: white;
        }}
        QPushButton:pressed {{
            background-color: #B89C5D;
            color: white;
        }}
    """)
    
    # 刷新按钮
    refresh_btn = QPushButton("刷新日志")
    refresh_btn.setCursor(Qt.CursorShape.PointingHandCursor)
    refresh_btn.setStyleSheet(f"""
        QPushButton {{
            background-color: {Theme.ACCENT};
            color: {Theme.TEXT_PRIMARY};
            border: none;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            padding: 8px 15px;
            font-size: {Theme.FONT_SIZE_SMALL};
            min-width: 60px;
            max-height: 28px;
        }}
        QPushButton:hover {{
            background-color: {Theme.ACCENT_HOVER};
        }}
        QPushButton:pressed {{
            background-color: #249070;
        }}
    """)
    
    # 将按钮添加到顶部区域布局中
    top_layout.addWidget(open_logs_btn)
    top_layout.addWidget(refresh_btn)
    
    # 添加顶部区域到日志容器
    log_layout.addWidget(top_area)
    
    # 日志内容区域
    self.log_text = QLabel("正在加载日志...")
    self.log_text.setStyleSheet(f"""
        font-family: 'Consolas', 'Menlo', 'Courier New', monospace;
        font-size: {Theme.FONT_SIZE_NORMAL}; 
        padding: 10px 5px;
        color: {Theme.TEXT_PRIMARY};
        background-color: transparent;
        line-height: 1.5;
    """)
    self.log_text.setWordWrap(True)
    self.log_text.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
    self.log_text.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)
    self.log_text.setTextFormat(Qt.TextFormat.RichText)
    
    # 为文本区域设置自定义上下文菜单
    self.log_text.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
    self.log_text.customContextMenuRequested.connect(self._show_log_context_menu)
    
    # 创建滚动区域
    scroll_area = QScrollArea()
    scroll_area.setWidgetResizable(True)
    scroll_area.setFrameShape(QFrame.Shape.NoFrame)
    scroll_area.setStyleSheet(f"""
        QScrollArea {{
            border: none;
            background-color: transparent;
        }}
        QScrollArea > QWidget {{
            background: transparent;
        }}
        QScrollBar:vertical {{
            border: none;
            background-color: {Theme.CARD_LEVEL_1};
            width: 10px;
            margin: 0px;
            border-radius: 5px;
        }}
        QScrollBar::handle:vertical {{
            background-color: {Theme.CARD_LEVEL_3};
            border-radius: 5px;
            min-height: 20px;
        }}
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
            background-color: {Theme.CARD_LEVEL_2};
            border-radius: 5px;
        }}
        QScrollBar:horizontal {{
            border: none;
            background-color: {Theme.CARD_LEVEL_1};
            height: 10px;
            margin: 0px;
            border-radius: 5px;
        }}
        QScrollBar::handle:horizontal {{
            background-color: {Theme.CARD_LEVEL_3};
            border-radius: 5px;
            min-width: 20px;
        }}
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            width: 0px;
        }}
        QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
            background-color: {Theme.CARD_LEVEL_2};
            border-radius: 5px;
        }}
    """)
    scroll_area.setWidget(self.log_text)
    log_layout.addWidget(scroll_area)
    
    # 添加日志容器到主布局
    main_layout.addWidget(log_container)
    
    # 连接按钮事件
    refresh_btn.clicked.connect(lambda: self._refresh_log_display())
    open_logs_btn.clicked.connect(open_log_dir)
    
    # 初始空日志 - 仅在首次切换到日志页面时加载日志
    # 避免初始化时就读取大量日志导致程序启动缓慢
    # self._refresh_log_display()  # 注释掉，通过页面切换时再加载
    
    return page