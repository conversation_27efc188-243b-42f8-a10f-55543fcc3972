# version.json 文件说明

`version.json` 是账户切换工具的版本配置文件，用于管理不同平台的版本信息、更新内容和通知。本文档将详细解释该文件的结构和各个字段的作用。

## 文件结构

`version.json` 文件采用JSON格式，主要包含以下几个部分：

1. 平台特定配置（win、mac、linux）
2. 全局配置（global）

完整的文件结构如下：

```json
{
    "win": { ... },    // Windows平台配置
    "mac": { ... },    // macOS平台配置
    "linux": { ... },  // Linux平台配置
    "global": { ... }  // 全局配置
}
```

## 平台特定配置

每个平台（win、mac、linux）的配置包含相同的字段结构，但可以针对不同平台设置不同的值。

### 版本控制

| 字段 | 类型 | 说明 |
|------|------|------|
| `latest_version` | 字符串 | 最新版本号，格式为"x.y.z" |
| `min_version` | 字符串 | 最低支持版本号，低于此版本将提示更新 |
| `force_update` | 布尔值 | 是否强制更新，若为true则低于min_version的版本将被强制退出 |
| `update_url` | 字符串 | 更新包下载地址 |
| `update_date` | 字符串 | 更新日期，格式为"YYYY-MM-DD" |

### 更新内容

| 字段 | 类型 | 说明 |
|------|------|------|
| `changes` | 数组 | 版本更新内容列表，每个元素为一条更新说明 |
| `show_update_url` | 布尔值 | 是否在更新提示中显示下载链接 |
| `show_update_notes` | 布尔值 | 是否在更新提示中显示更新内容 |

### 通知设置

| 字段 | 类型 | 说明 |
|------|------|------|
| `notification.show` | 布尔值 | 是否显示通知 |
| `notification.level` | 字符串 | 通知级别，可选值："info"、"warning"、"error" |
| `notification.message` | 字符串 | 通知消息内容 |

## 全局配置

全局配置适用于所有平台，主要包含维护状态、全局通知和免责声明。

### 维护和通知

| 字段 | 类型 | 说明 |
|------|------|------|
| `maintenance` | 布尔值 | 是否处于维护状态，若为true则所有平台均显示维护消息 |
| `maintenance_message` | 字符串 | 维护状态消息内容 |
| `global_notification.show` | 布尔值 | 是否显示全局通知 |
| `global_notification.level` | 字符串 | 全局通知级别，可选值："info"、"warning"、"error" |
| `global_notification.message` | 字符串 | 全局通知消息内容 |

### 免责声明

| 字段 | 类型 | 说明 |
|------|------|------|
| `disclaimer.show` | 布尔值 | 是否显示免责声明对话框 |
| `disclaimer.title` | 字符串 | 免责声明对话框标题 |
| `disclaimer.content` | 数组或字符串 | 免责声明具体内容，可以是字符串或数组。如果是数组，每个元素表示一个条款，将自动编号显示 |
| `disclaimer.must_agree` | 布尔值 | 是否必须同意才能使用软件，若为true则用户不同意将退出程序 |
| `disclaimer.agree_button` | 字符串 | 同意按钮文本 |
| `disclaimer.cancel_button` | 字符串 | 取消按钮文本 |

## 示例

以下是一个完整的 `version.json` 示例：

```json
{
    "win": {
        "latest_version": "1.0.1",  // 当前Windows平台最新版本号
        "min_version": "1.0.0",     // Windows最低支持版本
        "force_update": false,      // 是否强制Windows用户更新
        "update_url": "https://example.com/download/cursor_account_switch_win_v1.0.1.zip",  // Windows更新包下载地址
        "changes": [                // Windows版本更新内容列表
            "修复了账户切换时可能出现的错误",
            "优化了用户界面",
            "提高了程序稳定性"
        ],
        "show_update_url": true,    // 是否显示Windows更新链接
        "show_update_notes": true,  // 是否显示Windows更新内容
        "update_date": "2023-03-30",  // Windows版本更新日期
        "notification": {           // Windows平台通知设置
            "show": true,           // 是否显示通知
            "level": "info",        // 通知级别
            "message": "欢迎使用账户切换工具，如有问题请反馈。"  // 通知消息内容
        }
    },
    "mac": {
        "latest_version": "1.0.1",  // 当前Mac平台最新版本号
        "min_version": "1.0.0",     // Mac最低支持版本
        "force_update": false,      // 是否强制Mac用户更新
        "update_url": "https://example.com/download/cursor_account_switch_mac_v1.0.1.dmg",  // Mac更新包下载地址
        "changes": [                // Mac版本更新内容列表
            "修复了macOS特定的界面显示问题",
            "优化了用户界面",
            "提高了程序稳定性"
        ],
        "show_update_url": true,    // 是否显示Mac更新链接
        "show_update_notes": true,  // 是否显示Mac更新内容
        "update_date": "2023-03-30",  // Mac版本更新日期
        "notification": {           // Mac平台通知设置
            "show": true,           // 是否显示通知
            "level": "info",        // 通知级别
            "message": "欢迎使用账户切换工具Mac版，如有问题请反馈。"  // 通知消息内容
        }
    },
    "linux": {
        "latest_version": "1.0.1",  // 当前Linux平台最新版本号
        "min_version": "1.0.0",     // Linux最低支持版本
        "force_update": false,      // 是否强制Linux用户更新
        "update_url": "https://example.com/download/cursor_account_switch_linux_v1.0.1.tar.gz",  // Linux更新包下载地址
        "changes": [                // Linux版本更新内容列表
            "修复了Linux特定的兼容性问题",
            "优化了用户界面",
            "提高了程序稳定性"
        ],
        "show_update_url": true,    // 是否显示Linux更新链接
        "show_update_notes": true,  // 是否显示Linux更新内容
        "update_date": "2023-03-30",  // Linux版本更新日期
        "notification": {           // Linux平台通知设置
            "show": true,           // 是否显示通知
            "level": "info",        // 通知级别
            "message": "欢迎使用账户切换工具Linux版，如有问题请反馈。"  // 通知消息内容
        }
    },
    "global": {
        "maintenance": false,       // 是否处于维护状态
        "maintenance_message": "系统正在维护中，请稍后再试。",  // 维护状态消息
        "global_notification": {    // 全局通知设置
            "show": false,          // 是否显示全局通知
            "level": "warning",     // 全局通知级别
            "message": "这是一条全局通知消息，对所有用户可见。"  // 全局通知消息内容
        },
        "disclaimer": {             // 免责声明设置
            "show": true,           // 是否显示免责声明
            "title": "免责声明",     // 免责声明标题
            "content": [            // 免责声明内容(使用数组形式)
                "本软件仅供学习交流使用，不得用于商业目的。",
                "使用本软件产生的任何后果由用户自行承担。",
                "使用本软件即表示您同意此免责声明的所有条款。"
            ],
            "must_agree": true,     // 是否必须同意才能使用软件
            "agree_button": "我同意", // 同意按钮文本
            "cancel_button": "退出"  // 取消按钮文本
        }
    }
}
```

## 使用说明

1. 更新版本时，修改对应平台的 `latest_version` 和 `changes` 数组
2. 如需强制用户更新，设置 `force_update` 为 `true` 并调整 `min_version`
3. 临时维护时，设置 `global.maintenance` 为 `true` 并提供维护消息
4. 对所有用户发送通知时，设置 `global_notification.show` 为 `true` 并设置消息内容
5. 显示或隐藏免责声明，设置 `global.disclaimer.show` 为 `true` 或 `false`

## 注意事项

1. 版本号格式必须为 x.y.z 格式，且为递增关系
2. 更新内容应简明扼要，每条内容作为数组中的一个元素
3. 下载链接应确保可访问，建议使用HTTPS协议
4. 日期格式应保持一致，推荐使用"YYYY-MM-DD"格式
5. 免责声明内容建议使用数组形式，每个条款作为单独的元素，更加清晰易读 