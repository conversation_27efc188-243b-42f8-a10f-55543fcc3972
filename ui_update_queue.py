#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI更新队列模块
提供线程安全的UI更新请求队列，用于在后台线程和主线程之间传递UI更新请求
"""

import time
import queue
import threading
from typing import Dict, List, Tuple, Any, Optional

class UIUpdateQueue:
    """UI更新队列类，用于管理UI更新请求
    
    特点:
    - 线程安全: 可在不同线程间安全传递更新请求
    - 批处理: 支持一次获取多个更新请求进行批量处理
    - 类型分类: 对不同类型的更新请求进行分类
    - 统计功能: 提供队列使用情况统计
    """
    
    def __init__(self, max_size: int = 1000):
        """初始化UI更新队列
        
        Args:
            max_size: 队列最大容量，默认1000
        """
        self.queue = queue.Queue(maxsize=max_size)
        self.lock = threading.Lock()
        self.stats = {
            "total_added": 0,
            "total_processed": 0,
            "by_type": {}
        }
    
    def add_update(self, update_type: str, data: Any) -> bool:
        """添加更新请求到队列
        
        Args:
            update_type: 更新类型（如'account_quota', 'progress'等）
            data: 更新数据，根据类型不同可以是不同的数据结构
            
        Returns:
            bool: 添加成功返回True，队列满或出错返回False
        """
        try:
            # 创建更新请求对象
            update_request = {
                "type": update_type,
                "data": data,
                "timestamp": time.time()
            }
            
            # 添加到队列
            self.queue.put(update_request, block=False)
            
            # 更新统计信息
            with self.lock:
                self.stats["total_added"] += 1
                if update_type not in self.stats["by_type"]:
                    self.stats["by_type"][update_type] = 0
                self.stats["by_type"][update_type] += 1
                
            return True
        except queue.Full:
            print(f"UI更新队列已满，无法添加更新请求: {update_type}")
            return False
        except Exception as e:
            print(f"添加UI更新请求时出错: {str(e)}")
            return False
    
    def get_updates(self, batch_size: int = 10) -> List[Dict]:
        """获取更新请求列表
        
        Args:
            batch_size: 批量获取的最大数量，默认10
            
        Returns:
            List[Dict]: 更新请求列表，每项包含type, data和timestamp
        """
        updates = []
        
        try:
            # 从队列中获取指定数量的更新请求
            for _ in range(batch_size):
                try:
                    update = self.queue.get(block=False)
                    updates.append(update)
                    self.queue.task_done()
                except queue.Empty:
                    # 队列为空，跳出循环
                    break
            
            # 更新统计信息
            if updates:
                with self.lock:
                    self.stats["total_processed"] += len(updates)
            
            return updates
        except Exception as e:
            print(f"获取UI更新请求时出错: {str(e)}")
            return []
    
    def get_stats(self) -> Dict:
        """获取队列统计信息
        
        Returns:
            Dict: 包含队列使用统计的字典
        """
        with self.lock:
            # 复制统计数据，避免直接返回引用
            current_stats = self.stats.copy()
            current_stats["queue_size"] = self.queue.qsize()
            current_stats["by_type"] = self.stats["by_type"].copy()
            return current_stats
    
    def clear(self) -> None:
        """清空队列中的所有更新请求"""
        try:
            while not self.queue.empty():
                try:
                    self.queue.get(block=False)
                    self.queue.task_done()
                except queue.Empty:
                    break
            
            print("UI更新队列已清空")
        except Exception as e:
            print(f"清空UI更新队列时出错: {str(e)}")