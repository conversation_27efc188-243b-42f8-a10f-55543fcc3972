import os
import sys
import json
import requests
import platform
from packaging import version
import time
from colorama import init, Fore, Back, Style

# 初始化colorama
init()

class Colors:
    """统一的颜色定义"""
    RED = ''      # 错误信息
    GREEN = ''    # 成功信息
    YELLOW = ''   # 警告/提示信息
    BLUE = ''     # 框架/标题
    PURPLE = ''   # 重要数据
    CYAN = ''     # 进度信息
    WHITE = ''    # 普通文本
    NC = ''       # 结束颜色

def print_box(title="", content=None, footer=None):
    """打印消息，不带框"""
    # 在开始前添加一个空行
    print()
    
    # 如果只有标题，直接显示标题
    if title and not content and not footer:
        print(title)
        return
    
    # 显示标题
    if title:
        print(title)
    
    # 显示内容
    if content:
        if isinstance(content, str):
            if content.strip():
                print(content)
        elif isinstance(content, list):
            for line in content:
                print(line)
    
    # 显示页脚
    if footer:
        print(footer)

class VersionChecker:
    # 是否跳过版本检查 (True: 跳过检查，用于无网环境测试)
    SKIP_VERSION_CHECK = True
    
    # 针对不同操作系统的版本号
    VERSIONS = {
        'windows': "7.6.0",
        'darwin': "7.6.0",  # macOS
        'linux': "7.6.0"
    }
    
    # API地址 (隐藏具体URL)
    API_URL = "https://cursor.yan.vin/version/version.json"

    @staticmethod
    def get_system_type():
        """获取当前系统类型"""
        return platform.system().lower()

    @staticmethod
    def get_cursor_version():
        """获取Cursor版本号"""
        system = platform.system().lower()
        home = os.path.expanduser('~')
        
        # 定义可能的路径
        if system == 'windows':
            paths = [
                os.path.join(os.getenv('LOCALAPPDATA'), 'Programs', 'Cursor', 'resources', 'app', 'package.json'),
                os.path.join(os.getenv('PROGRAMFILES'), 'Cursor', 'resources', 'app', 'package.json'),
                os.path.join(os.getenv('PROGRAMFILES(X86)'), 'Cursor', 'resources', 'app', 'package.json')
            ]
        elif system == 'darwin':  # macOS
            paths = [
                '/Applications/Cursor.app/Contents/Resources/app/package.json'
            ]
        else:  # Linux
            paths = [
                '/usr/share/cursor/resources/app/package.json',
                '/opt/Cursor/resources/app/package.json',
                os.path.join(home, '.local', 'share', 'cursor', 'resources', 'app', 'package.json')
            ]
        
        # 检查所有可能的路径
        for path in paths:
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        return data.get('version')
                except Exception:
                    pass
        
        return "未安装"

    @staticmethod
    def print_welcome():
        # 欢迎信息已经移到 logo 中，这里不需要重复显示
        pass

    @staticmethod
    def print_changelog(changelog):
        """打印更新日志"""
        print("更新内容：")
        for change in changelog:
            print(f"  • {change}")

    @staticmethod
    def show_notice(notice):
        """显示通知"""
        print("\n提示")
        print(f"{notice.get('title', '提示')}")
        print()
        for line in notice.get('content', '').split('\n'):
            print(line)
        
        # 如果设置了显示时间，则等待
        if notice.get('show_time'):
            time.sleep(notice.get('show_time'))
        print()

    @classmethod
    def check_version(cls):
        # 如果开启了跳过检查，直接返回True
        if cls.SKIP_VERSION_CHECK:
            print("\n警告: 已开启跳过版本检查模式")
            print("警告: 此模式仅用于无网环境下的测试")
            print()
            return True
            
        # 先显示logo
        # from logo import print_logo
        # print_logo()
        
        # 获取当前系统类型
        system_type = cls.get_system_type()
        
        # 获取当前系统的版本号
        current_version = cls.VERSIONS.get(system_type)
        
        if not current_version:
            print("\n不支持的操作系统: " + system_type)
            print()
            print("请加入QQ群：631250950 联系群主")
            sys.exit(1)
        
        try:
            # 获取远程版本信息
            response = requests.get(cls.API_URL, timeout=10)
            response.raise_for_status()
            remote_info = response.json()
            
            # 获取当前系统的远程版本信息
            system_info = remote_info.get(system_type)
            if not system_info:
                raise Exception(f"无法获取 {system_type} 的版本信息")

            current_ver = version.parse(current_version)
            remote_version = version.parse(system_info['version'])
            min_version = version.parse(system_info['min_version'])

            # input("\n按回车键继续...")
            # print()

            # 显示全局提示（如果有）
            global_notice = remote_info.get('global_notice', {})
            if global_notice.get('enabled', False):
                cls.show_notice(global_notice)

            # 版本检查
            print("\n版本检查")
            print("──────────────────────────────────────────────────────")
            cursor_version = cls.get_cursor_version()
            print(f"  [信息] 脚本当前版本: v{current_version}")
            print(f"  [信息] 脚本最新版本: v{system_info['version']}")
            
            # 检查是否需要强制更新
            if current_ver < min_version:
                print(f"  [错误] 当前脚本版本过低，必须更新才能继续使用")
                print("──────────────────────────────────────────────────────")
                print()
                
                # 显示更新日志
                if 'changelog' in system_info:
                    changelog_items = []
                    for changelog_entry in system_info['changelog']:
                        changelog_items.append(f"版本 {changelog_entry['version']} ({changelog_entry['date']}):")
                        for change in changelog_entry['changes']:
                            changelog_items.append(f"• {change}")
                        changelog_items.append("")
                    
                    print_box(
                        "最新版本更新内容",
                        changelog_items
                    )
                
                print_box(
                    "强制更新提示",
                    [
                        "当前脚本版本已不再支持，请更新到最新版本",
                        "",
                        "更新方法:",
                        "1. 加入QQ群：631250950",
                        "2. 在群文件中下载最新版本",
                        "3. 使用新版本替换当前程序"
                    ]
                )
                input("\n按回车键退出程序...")
                sys.exit(1)
            elif current_ver < remote_version:
                print(f"  [警告] 发现新版本可用")
                print("──────────────────────────────────────────────────────")
                print()
                
                # 显示更新日志
                if 'changelog' in system_info:
                    changelog_items = []
                    for changelog_entry in system_info['changelog']:
                        changelog_items.append(f"版本 {changelog_entry['version']} ({changelog_entry['date']}):")
                        for change in changelog_entry['changes']:
                            changelog_items.append(f"• {change}")
                        changelog_items.append("")
                    
                    print_box(
                        "最新版本更新内容",
                        changelog_items
                    )
                
                print_box(
                    "更新提示",
                    [
                        "建议更新到最新版本以获得更好的体验",
                        "",
                        "更新方法:",
                        "1. 加入QQ群：631250950",
                        "2. 在群文件中下载最新版本",
                        "3. 使用新版本替换当前程序",
                        "",
                        "注意：更新后建议移动原有的 .env 文件，然后重新生成重新配置新的 .env"
                    ]
                )
            else:
                print(f"  [成功] 已是最新版本")
            print("──────────────────────────────────────────────────────")
            print()

            return True

        except Exception as e:
            print("\n无法连接到验证服务器")
            # 处理错误信息，移除URL
            error_msg = str(e)
            if "Not Found" in error_msg:
                error_msg = "404 错误：服务器连接失败"
            elif "Connection refused" in error_msg:
                error_msg = "连接被拒绝：服务器可能暂时不可用"
            elif "Timeout" in error_msg:
                error_msg = "连接超时：请检查网络状态"
            elif "SSLError" in error_msg:
                error_msg = "SSL错误：请检查网络设置"
            else:
                error_msg = f"错误代码：{error_msg.split(':', 1)[0]}"
            print(error_msg)
            print()
            print("请检查：")
            print("1. 网络连接是否正常")
            print("2. 是否开启了代理")
            print()
            print("如果问题持续，请加入QQ群：631250950 联系群主")
            input("\n按回车键退出程序...")
            sys.exit(1) 