#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试快捷一键功能中的手动验证按钮
用于验证修改是否正确工作
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PySide6.QtWidgets import QApplication
from widgets.quick_function_dialog import QuickFunctionProgressDialog

def test_manual_buttons():
    """测试手动验证按钮的显示和隐藏"""
    app = QApplication(sys.argv)
    
    # 创建测试配置
    config = {
        "functions": {
            "auto_register": True
        },
        "delay": 3,
        "stop_on_failure": True
    }
    
    # 创建对话框
    dialog = QuickFunctionProgressDialog(None, config)
    
    # 测试按钮初始状态（应该隐藏）
    print("测试按钮初始状态:")
    print(f"manual_cf_button.isVisible(): {dialog.manual_cf_button.isVisible()}")
    print(f"manual_card_button.isVisible(): {dialog.manual_card_button.isVisible()}")
    
    # 测试显示CF验证按钮
    print("\n测试显示CF验证按钮:")
    dialog.show_manual_cf_button()
    print(f"manual_cf_button.isVisible(): {dialog.manual_cf_button.isVisible()}")
    print(f"按钮文本: {dialog.manual_cf_button.text()}")
    print(f"进度文本: {dialog.progress_text.text()}")
    
    # 测试隐藏CF验证按钮
    print("\n测试隐藏CF验证按钮:")
    dialog.on_manual_cf_completed()
    print(f"manual_cf_button.isVisible(): {dialog.manual_cf_button.isVisible()}")
    print(f"进度文本: {dialog.progress_text.text()}")
    
    # 测试显示绑卡按钮
    print("\n测试显示绑卡按钮:")
    dialog.show_manual_card_button()
    print(f"manual_card_button.isVisible(): {dialog.manual_card_button.isVisible()}")
    print(f"按钮文本: {dialog.manual_card_button.text()}")
    print(f"进度文本: {dialog.progress_text.text()}")
    
    # 测试隐藏绑卡按钮
    print("\n测试隐藏绑卡按钮:")
    dialog.on_manual_card_completed()
    print(f"manual_card_button.isVisible(): {dialog.manual_card_button.isVisible()}")
    print(f"进度文本: {dialog.progress_text.text()}")
    
    print("\n✅ 所有按钮测试完成！")

    # 显示对话框进行视觉验证
    dialog.show()

    # 测试按钮文字显示和高度
    print("\n测试按钮文字显示和高度:")
    print(f"CF按钮高度: {dialog.manual_cf_button.height()}px (期望: 40px)")
    print(f"绑卡按钮高度: {dialog.manual_card_button.height()}px (期望: 40px)")

    # 5秒后再次显示按钮进行视觉测试
    from PySide6.QtCore import QTimer
    QTimer.singleShot(2000, dialog.show_manual_cf_button)
    QTimer.singleShot(4000, dialog.show_manual_card_button)

    print("\n📝 请检查:")
    print("   1. 按钮文字是否完整显示，没有被截断")
    print("   2. 按钮高度是否合适，不会太高或太矮")
    print("   3. 按钮整体视觉效果是否协调")
    
    return app.exec()

if __name__ == "__main__":
    test_manual_buttons()
