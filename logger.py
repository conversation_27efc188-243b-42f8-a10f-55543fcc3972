"""
日志管理模块
提供统一的日志记录和管理功能
"""

import os
import sys
import logging
import datetime
from logging.handlers import RotatingFileHandler
import platform
import subprocess
from utils import get_app_data_dir

class Logger:
    """日志管理类，提供统一的日志记录接口"""
    
    # 日志级别映射
    LEVELS = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    
    # 日志格式
    LOG_FORMAT = '%(asctime)s [%(levelname)s] - %(message)s'
    DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
    
    # 默认日志级别
    DEFAULT_LEVEL = logging.INFO
    
    # 详细日志状态
    VERBOSE_LOGGING = False
    
    def __init__(self, name='YCursor'):
        """初始化日志管理器
        
        Args:
            name: 日志记录器名称，默认为'YCursor'
        """
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(self.DEFAULT_LEVEL)
        
        # 确保只添加一次处理器
        if not self.logger.handlers:
            # 创建日志目录
            self.log_dir = self._create_log_dir()
            
            # 获取当前日期作为文件名
            today = datetime.datetime.now().strftime('%Y-%m-%d')
            self.log_file = os.path.join(self.log_dir, f"{today}.log")
            
            # 创建文件处理器
            file_handler = RotatingFileHandler(
                self.log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setLevel(self.DEFAULT_LEVEL)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(self.DEFAULT_LEVEL)
            
            # 设置格式
            formatter = logging.Formatter(self.LOG_FORMAT, self.DATE_FORMAT)
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
            
            # 记录初始化消息
            self.info(f"日志系统初始化完成，日志文件: {self.log_file}")
    
    def _create_log_dir(self):
        """创建日志目录"""
        app_data_dir = get_app_data_dir()
        log_dir = os.path.join(app_data_dir, "logs")
        
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        return log_dir
    
    def set_level(self, level):
        """设置日志级别
        
        Args:
            level: 日志级别，可以是字符串('DEBUG', 'INFO'等)或logging模块的级别常量
        """
        if isinstance(level, str):
            level = self.LEVELS.get(level.upper(), self.DEFAULT_LEVEL)
        
        self.logger.setLevel(level)
        for handler in self.logger.handlers:
            handler.setLevel(level)
        
        self.info(f"日志级别已设置为: {logging.getLevelName(level)}")
    
    def set_verbose_logging(self, enabled):
        """设置是否启用详细日志记录
        
        Args:
            enabled: 是否启用详细日志记录
        """
        Logger.VERBOSE_LOGGING = enabled
        
        # 如果启用详细日志，则设置为DEBUG级别
        if enabled:
            self.set_level(logging.DEBUG)
            self.info("详细日志记录已启用，日志级别设置为DEBUG")
        else:
            self.set_level(self.DEFAULT_LEVEL)
            self.info("详细日志记录已禁用，日志级别恢复为默认")
    
    def get_today_log_file(self):
        """获取当天的日志文件路径"""
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        return os.path.join(self.log_dir, f"{today}.log")
    
    def get_log_dir(self):
        """获取日志目录路径"""
        return self.log_dir
    
    def open_log_dir(self):
        """打开日志目录"""
        try:
            if not os.path.exists(self.log_dir):
                os.makedirs(self.log_dir)
                
            if sys.platform == "win32":
                os.startfile(self.log_dir)
            elif sys.platform == "darwin":
                subprocess.run(["open", self.log_dir])
            else:  # Linux
                subprocess.run(["xdg-open", self.log_dir])
                
            return True
        except Exception as e:
            self.error(f"打开日志目录时出错: {str(e)}")
            return False
    
    def read_today_log(self):
        """读取当天的日志内容"""
        try:
            log_file = self.get_today_log_file()
            if not os.path.exists(log_file):
                return "今日暂无日志记录"
            
            with open(log_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            self.error(f"读取日志文件时出错: {str(e)}")
            return f"读取日志文件时出错: {str(e)}"
    
    def debug(self, message):
        """记录调试级别的日志"""
        self.logger.debug(message)
    
    def info(self, message):
        """记录信息级别的日志"""
        self.logger.info(message)
    
    def warning(self, message):
        """记录警告级别的日志"""
        self.logger.warning(message)
    
    def error(self, message):
        """记录错误级别的日志"""
        self.logger.error(message)
    
    def critical(self, message):
        """记录严重错误级别的日志"""
        self.logger.critical(message)


# 创建全局日志实例
logger = Logger()

# 导出便捷函数
debug = logger.debug
info = logger.info
warning = logger.warning
error = logger.error
critical = logger.critical
set_level = logger.set_level
set_verbose_logging = logger.set_verbose_logging
get_log_dir = logger.get_log_dir
open_log_dir = logger.open_log_dir
read_today_log = logger.read_today_log