#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户额度获取模块
提供异步并发获取多个账户额度的功能
"""

import os
import threading
import asyncio
from datetime import datetime
from dateutil import parser
from dateutil.tz import tz
from PySide6.QtCore import QObject, Signal
import time # Added for sleep

from account.account_data import AccountQuota
from utils import get_app_data_dir, create_new_event_loop, safely_run_until_complete


# 异步并发获取账户额度的类
class QuotaFetcher(QObject):
    """异步并发获取账户额度的类"""
    
    # 定义信号
    account_quota_updated = Signal(str, dict)
    all_quotas_fetched = Signal()
    
    def __init__(self, accounts):
        super().__init__()
        self.accounts = accounts
        self.max_workers = min(10, os.cpu_count() * 2 + 1)  # 自动设置并发数
        self._stop_event = threading.Event()
        self._thread = None # To store the background thread reference
        self._loop = None # To store the event loop used by the thread
        self._tasks = [] # To store asyncio tasks
        
    def start_fetching(self):
        """开始获取所有账户的额度"""
        # Ensure not already running and cleanup previous state if needed
        if self._thread and self._thread.is_alive():
            print("QuotaFetcher: Fetching is already in progress. Request ignored.")
            # Or call shutdown first? Depends on desired behavior.
            # self.shutdown()
            # time.sleep(0.1) # Give shutdown a moment
            return

        self._stop_event.clear() # Reset stop event before starting
        self._tasks = [] # Clear previous tasks
        self._loop = None # Clear previous loop reference

        self._thread = threading.Thread(target=self._fetch_all_quotas)
        self._thread.daemon = True
        self._thread.start()
    
    def shutdown(self, wait=False, timeout=5.0):
        """Requests the fetching thread to stop and cleans up resources."""
        print("QuotaFetcher: Shutdown requested.")
        if not self._thread or not self._thread.is_alive():
            print("QuotaFetcher: Thread not running or already stopped.")
            return

        # Signal the thread and its asyncio tasks to stop
        self._stop_event.set()

        # --- Safely cancel asyncio tasks --- \
        # This needs to be done from within the thread's event loop.
        # We schedule the cancellation using run_coroutine_threadsafe.
        tasks_to_cancel = list(self._tasks) # Create a copy
        if self._loop and self._loop.is_running() and tasks_to_cancel:
            print(f"QuotaFetcher: Requesting cancellation of {len(tasks_to_cancel)} asyncio tasks...")
            async def cancel_tasks():
                cancelled_count = 0
                for task in tasks_to_cancel:
                    if not task.done():
                        task.cancel()
                        cancelled_count += 1
                # Allow cancellations to propagate
                if cancelled_count > 0:
                    await asyncio.sleep(0.1)
                print(f"QuotaFetcher: Requested cancellation of {cancelled_count} tasks.")

            # Schedule the cancellation
            future = asyncio.run_coroutine_threadsafe(cancel_tasks(), self._loop)
            try:
                # Wait briefly for cancellation to be scheduled/run
                future.result(timeout=1.0)
            except asyncio.TimeoutError:
                print("QuotaFetcher: Warning - Timeout waiting for task cancellation coroutine.")
            except Exception as e:
                print(f"QuotaFetcher: Error scheduling/running task cancellation: {e}")

        # --- Wait for the thread to finish --- \
        if wait:
            print(f"QuotaFetcher: Waiting for thread to finish (timeout={timeout}s)...")
            self._thread.join(timeout=timeout)
            if self._thread.is_alive():
                print("QuotaFetcher: Warning - Thread did not finish within the timeout.")
            else:
                print("QuotaFetcher: Thread finished.")
        else:
            print("QuotaFetcher: Shutdown requested (non-blocking). Thread will exit when possible.")

        # --- Final cleanup --- \
        self._thread = None
        self._loop = None
        self._tasks = []
        print("QuotaFetcher: Shutdown finished.")

    def _fetch_all_quotas(self):
        """获取所有账户的额度 (Runs in a separate thread)"""
        # Get or create an event loop for this thread
        try:
            self._loop = asyncio.get_event_loop()
            if self._loop.is_running():
                # If a loop is already running (unexpected), create a new one
                print("QuotaFetcher: Warning - Found a running loop, creating a new one.")
                self._loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self._loop)
        except RuntimeError: # No event loop set for this thread
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)

        if self._stop_event.is_set():
            print("QuotaFetcher: Stop requested before starting async fetch.")
            self.all_quotas_fetched.emit() # Ensure signal is emitted
            return

        try:
            # Run the main async task
            self._loop.run_until_complete(self._fetch_quotas_async())

        except Exception as e:
            # Avoid reporting errors if shutdown was requested during operation
            if not self._stop_event.is_set():
                print(f"获取账户额度时出错: {str(e)}")
        finally:
            # Close the event loop associated with this thread
            try:
                if self._loop and not self._loop.is_closed():
                    # Cancel any remaining tasks just in case before closing
                    all_tasks = asyncio.all_tasks(self._loop)
                    if all_tasks:
                        print(f"QuotaFetcher: Cancelling {len(all_tasks)} remaining tasks before loop close...")
                        for task in all_tasks:
                            task.cancel()
                        # Run loop briefly to allow cancellations
                        self._loop.run_until_complete(asyncio.sleep(0.1))

                    print("QuotaFetcher: Closing event loop.")
                    self._loop.close()
                else:
                    print("QuotaFetcher: Loop already closed or not initialized.")
            except Exception as e:
                print(f"关闭事件循环时出错: {str(e)}")

            # Check stop event one last time before emitting
            if not self._stop_event.is_set():
                self.all_quotas_fetched.emit()
            else:
                print("QuotaFetcher: Suppressing all_quotas_fetched signal due to shutdown.")
            # Clean up loop reference
            self._loop = None

    async def _fetch_quotas_async(self):
        """异步获取所有账户的额度"""
        self._tasks = [] # Clear tasks list for this run
        semaphore = asyncio.Semaphore(self.max_workers)
        
        for account in self.accounts:
            if self._stop_event.is_set():
                print("QuotaFetcher: Stop requested during task creation.")
                break # Exit loop if stopped

            # Create task and add to list
            task = asyncio.create_task(self._fetch_single_quota(semaphore, account))
            self._tasks.append(task)

        if not self._tasks: # No tasks created
            return

        try:
            # Wait for tasks to complete
            await asyncio.gather(*self._tasks)
        except asyncio.CancelledError:
            print("QuotaFetcher: _fetch_quotas_async cancelled.")
            # Propagate cancellation if needed or handle gracefully
            raise # Re-raise for run_until_complete to catch if necessary
        finally:
            # Clear tasks list after completion/cancellation
            self._tasks = []

    async def _fetch_single_quota(self, semaphore, account):
        """获取单个账户的额度"""
        # Check stop event before acquiring semaphore
        if self._stop_event.is_set():
            raise asyncio.CancelledError("QuotaFetcher shutdown requested")

        async with semaphore:
            # Check stop event again after acquiring semaphore
            if self._stop_event.is_set():
                raise asyncio.CancelledError("QuotaFetcher shutdown requested")
            try:
                # Use synchronous function获取额度
                email = account['email']
                
                # 传递整个账户对象而不只是cookies
                quota_data = await asyncio.to_thread(AccountQuota.get_quota, account)

                # 获取账户类型信息
                if quota_data:
                    from account.account_type import AccountType
                    membership_type, days_remaining, verified_student = await asyncio.to_thread(
                        AccountType.get_account_type, account
                    )

                    # 将账户类型信息添加到quota_data中
                    if membership_type is not None:
                        quota_data["account_type_info"] = {
                            "membershipType": membership_type,
                            "daysRemainingOnTrial": days_remaining,
                            "verifiedStudent": verified_student
                        }

                # Check stop event before emitting signal
                if self._stop_event.is_set():
                    return # Don't emit if stopped

                if quota_data:
                    self.account_quota_updated.emit(email, quota_data)
            except asyncio.CancelledError: # Catch potential cancellation from to_thread
                 print(f"QuotaFetcher: Task for {account.get('email', 'unknown')} cancelled.")
                 raise # Re-raise
            except Exception as e:
                # Avoid reporting errors if shutdown was requested
                if not self._stop_event.is_set():
                    print(f"获取账户 {account.get('email', 'unknown')} 的额度时出错: {str(e)}")

            # 为避免API限制，适当延迟
            # Check stop event before sleeping
            if self._stop_event.is_set():
                raise asyncio.CancelledError("QuotaFetcher shutdown requested")
            await asyncio.sleep(0.5)