"""
配额管理器模块
提供账户配额获取和刷新的功能
"""
import os
import time
import json
import threading
import sqlite3
from concurrent.futures import ThreadPoolExecutor, as_completed

from PySide6.QtCore import QObject, Signal, QThread, QAbstractAnimation
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
from PySide6.QtCore import Qt

from account.account_data import AccountData, AccountQuota
from account.auth import CursorAuthManager
from account.quota import QuotaFetcher
from logger import info, warning, error

class QuotaManager(QObject):
    """配额管理器类，用于管理配额获取和刷新逻辑"""
    
    # 定义信号
    quota_fetched = Signal(str, dict)  # 单个账户配额获取完成的信号
    all_quotas_fetched = Signal(bool, bool)  # 所有账户配额获取完成的信号 (is_manual_refresh, show_toast)
    progress_updated = Signal(int, int)  # 进度更新信号(当前进度, 总数)
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.fetcher = None
        self.is_fetching_quotas = False
        
    def fetch_all_accounts_quota(self, is_manual_refresh=False, show_toast=True):
        """获取所有账户的额度信息"""
        # 检查是否已经在获取额度数据中，避免重复操作
        if self.is_fetching_quotas and not is_manual_refresh:
            print("已有额度获取操作在进行中，跳过重复获取")
            return
            
        # 从数据库获取最新的当前登录邮箱
        db_email = self.main_window.auth_manager.get_current_email()
        
        # 如果邮箱发生变化，更新内存中的值并更新UI
        if db_email != self.main_window.current_email:
            print(f"账户额度获取: 检测到邮箱变化，从 {self.main_window.current_email} 变为 {db_email}")
            self.main_window.current_email = db_email
            
            # 更新账户行的当前状态
            if hasattr(self.main_window, 'account_rows'):
                for email, row in self.main_window.account_rows.items():
                    row.set_current(email == self.main_window.current_email)
        
        # 先检查是否有账户数据
        if not self.main_window.account_data.accounts:
            # 先隐藏加载内容
            if hasattr(self.main_window, 'loading_container'):
                self.main_window.loading_container.setVisible(False)
                
            if show_toast:
                self.main_window.show_toast("没有找到账户数据，请添加账户", error=True)
            return
        
        if show_toast:
            self.main_window.show_toast(f"正在获取 {len(self.main_window.account_data.accounts)} 个账户的额度信息")
        
        # 设置数据获取标志
        self.is_fetching_quotas = True
        
        # 账户管理页面：隐藏所有账户行，显示加载指示器
        if self.main_window.content_stack.currentWidget() == self.main_window.accounts_page:
            # 临时保存所有账户行，以便稍后恢复
            self.main_window.temp_account_rows = {}
            for email, row in self.main_window.account_rows.items():
                self.main_window.temp_account_rows[email] = row
                row.setVisible(False)  # 隐藏现有行
            
            # 显示加载指示器并初始化进度
            self.main_window.loading_container.setVisible(True)
            
            # 重置进度条，始终显示"正在加载中"
            if hasattr(self.main_window, 'loading_progress') and self.main_window.loading_progress:
                # 先停止任何进行中的动画
                if hasattr(self.main_window.loading_progress, '_animation') and self.main_window.loading_progress._animation.state() == QAbstractAnimation.State.Running:
                    self.main_window.loading_progress._animation.stop()
                if hasattr(self.main_window.loading_progress, '_text_animation') and self.main_window.loading_progress._text_animation.state() == QAbstractAnimation.State.Running:
                    self.main_window.loading_progress._text_animation.stop()
                    
                # 阻止信号发送避免触发多余动画
                self.main_window.loading_progress.blockSignals(True)
                self.main_window.loading_progress.setFormat("正在加载中")
                self.main_window.loading_progress.setValue(1)  # 设置为1，确保有基本样式显示
                self.main_window.loading_progress.setMaximum(len(self.main_window.account_data.accounts) or 100)  # 设置最大值为账户总数或默认100
                self.main_window.loading_progress.blockSignals(False)
        else:
            # 如果不在账户管理页面，只设置加载状态
            for email, row in self.main_window.account_rows.items():
                try:
                    row.set_loading_state(True)
                except Exception as e:
                    print(f"设置账户行加载状态时出错: {str(e)}")
        
        try:
            # 连接信号
            # 使用 UniqueConnection 确保槽函数只连接一次
            self.main_window.quota_manager.quota_fetched.connect(self.main_window.update_account_quota, Qt.ConnectionType.UniqueConnection)
            # 对于 lambda 连接，UniqueConnection 无效，但移除 disconnect 可以避免警告
            self.main_window.quota_manager.all_quotas_fetched.connect(
                lambda: self.main_window.on_all_quotas_fetched(is_manual_refresh, show_toast)
            )
            # 使用 UniqueConnection 确保槽函数只连接一次
            self.main_window.quota_manager.progress_updated.connect(self.main_window.update_fetch_progress, Qt.ConnectionType.UniqueConnection)
            
            # 开始处理账户列表
            self.main_window.quota_manager.process_accounts(self.main_window.account_data.accounts)
            
        except Exception as e:
            print(f"获取账户配额时出错: {str(e)}")
            if show_toast:
                self.main_window.show_toast("获取账户配额时出错", error=True)
    
    def _fetch_other_accounts_quota(self):
        """获取除当前账户外的所有其他账户额度
        
        由于当前账户的额度已经被优先获取，这个方法只负责获取其他账户的额度数据
        """
        # 确保不重复获取当前账户的额度，只获取其他账户
        try:
            # 过滤出非当前账户的其他账户
            other_accounts = [
                account for account in self.main_window.account_data.accounts 
                if account.get("email") != self.main_window.current_email
            ]
            
            # 如果没有其他账户，则不需要进一步操作
            if not other_accounts:
                print("没有其他账户需要获取额度")
                return
            
            # 创建只针对其他账户的额度获取器
            fetcher = QuotaFetcher(other_accounts)
            
            # 连接信号
            fetcher.account_quota_updated.connect(self.main_window.update_account_quota)
            fetcher.all_quotas_fetched.connect(
                lambda: self.main_window.on_all_quotas_fetched(is_manual_refresh=False, show_toast=False)
            )
            
            # 开始获取
            fetcher.start_fetching()
        except Exception as e:
            print(f"获取其他账户额度时出错: {str(e)}")
    
    def _fetch_current_account_quota(self, silent=False):
        """单独获取当前账户额度数据，优先更新首页显示
        
        该方法与_fetch_usage_data类似，但专为首页优先加载设计，
        独立于其他账户数据获取流程，确保首页尽快显示当前账户信息
        
        Args:
            silent: 是否静默获取（不主动更新UI），默认为False
        
        Returns:
            bool: 静默模式下，如果数据有变化返回True，否则返回False
        """
        try:
            # 保存旧数据用于比较（仅在静默模式下使用）
            old_quota = None
            if silent and self.main_window.current_email in self.main_window.account_quotas:
                old_quota = self.main_window.account_quotas.get(self.main_window.current_email, {}).copy()
            
            # 如果不是静默模式且在UI线程中执行，则创建线程调用自身
            if not silent and QThread.currentThread() is QApplication.instance().thread():
                thread = threading.Thread(target=lambda: self._fetch_and_update_ui_on_main_thread())
                thread.daemon = True
                thread.start()
                return False
            
            # 直接从数据库中获取当前用户的token
            conn = None
            access_token = None
            
            try:
                conn = sqlite3.connect(self.main_window.auth_manager.db_path)
                cursor = conn.cursor()
                
                # 查询当前登录的accessToken
                cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/accessToken'")
                result = cursor.fetchone()
                
                if result is not None:
                    access_token = result[0]
                    
            except sqlite3.Error as e:
                print(f"{'自动刷新' if silent else '首页优先加载'}：获取token时数据库错误: {str(e)}")
                return False
            finally:
                if conn:
                    conn.close()
            
            if not access_token:
                print(f"{'自动刷新' if silent else '首页优先加载'}：无法获取当前用户的accessToken")
                return False
                
            # 构建账户数据以传递给AccountQuota.get_quota
            account_data = {
                "email": self.main_window.current_email,
                "auth_info": {
                    "cursorAuth/accessToken": access_token
                }
            }
            
            # 获取额度数据
            quota_data = AccountQuota.get_quota(account_data)

            # 获取账户类型信息
            if quota_data:
                from account.account_type import AccountType
                membership_type, days_remaining, verified_student = AccountType.get_account_type(account_data)

                # 将账户类型信息添加到quota_data中
                if membership_type is not None:
                    quota_data["account_type_info"] = {
                        "membershipType": membership_type,
                        "daysRemainingOnTrial": days_remaining,
                        "verifiedStudent": verified_student
                    }

            # 更新并显示数据
            if quota_data:
                # 检查数据是否有变化（仅在静默模式下）
                data_changed = False
                if silent and old_quota:
                    # 比较关键字段
                    if (old_quota.get('used_tokens') != quota_data.get('used_tokens') or
                        old_quota.get('hard_limit_tokens') != quota_data.get('hard_limit_tokens') or
                        old_quota.get('startOfMonth') != quota_data.get('startOfMonth')):
                        data_changed = True
                        print(f"自动刷新: 账户 {self.main_window.current_email} 额度数据已变化")
                        
                        # 输出变化详情
                        old_used = old_quota.get('used_tokens', 0)
                        new_used = quota_data.get('used_tokens', 0)
                        if old_used != new_used:
                            print(f"  - 已用额度: {old_used} -> {new_used}")
                            
                        old_limit = old_quota.get('hard_limit_tokens', 0)
                        new_limit = quota_data.get('hard_limit_tokens', 0)
                        if old_limit != new_limit:
                            print(f"  - 总额度: {old_limit} -> {new_limit}")
                
                # 保存到账户额度字典中
                self.main_window.account_quotas[self.main_window.current_email] = quota_data
                
                # 更新账户数据中的注册时间
                start_of_month = quota_data.get("startOfMonth")
                if start_of_month:
                    # 遍历所有账户，找到对应的账户
                    for account in self.main_window.account_data.accounts:
                        if account.get("email") == self.main_window.current_email:
                            # 更新账户的注册时间
                            account["api_register_time"] = start_of_month
                            # 同时保存额度数据到账户数据中，方便从临时文件直接读取
                            account["quota_data"] = quota_data
                            # 只保存到临时文件，不修改主文件
                            self.main_window.account_data.save_quotas_to_temp()
                            break
                
                # 非静默模式或数据有变化时，在主线程中更新UI
                if not silent or (silent and data_changed):
                    # 使用信号通知主线程更新UI
                    if QThread.currentThread() is not QApplication.instance().thread():
                        self.main_window.api_data_fetched_signal.emit(self.main_window.current_email)
                    else:
                        # 如果在主线程中，则直接更新UI
                        self.main_window._safe_update_ui(self.main_window.current_email)
                    
                return data_changed  # 返回数据是否有变化
            
            return False
        except Exception as e:
            print(f"{'自动刷新' if silent else '首页优先加载'}：获取当前账户额度时出错: {str(e)}")
            # 出错时不显示错误提示，让后续的全局刷新再处理
            return False
    
    def _fetch_and_update_ui_on_main_thread(self):
        """在后台线程获取数据并更新UI"""
        try:
            # 在后台线程获取数据
            self._fetch_current_account_quota(silent=True)
            
            # 使用信号通知主线程更新UI
            self.main_window.api_data_fetched_signal.emit(self.main_window.current_email)
        except Exception as e:
            print(f"获取数据并更新UI时出错: {str(e)}")
    
    def _auto_refresh_current_quota(self):
        """定时自动刷新当前账户额度数据"""
        try:
            # 从数据库获取最新的邮箱
            db_email = self.main_window.auth_manager.get_current_email()
            
            # 如果数据库中没有邮箱信息
            if not db_email:
                return
                
            # 如果当前不在首页，则跳过刷新以减少不必要的资源消耗
            if self.main_window.content_stack.currentWidget() != self.main_window.home_page:
                return
                
            # 记录刷新时间，避免频繁刷新
            current_time = time.time()
            if hasattr(self, '_last_refresh_time'):
                # 如果距离上次刷新不足5秒，则跳过本次刷新
                if current_time - self._last_refresh_time < 5:
                    print(f"自动刷新: 距离上次刷新不足5秒，跳过本次刷新")
                    return
            
            # 更新最后刷新时间
            self._last_refresh_time = current_time
            
            # 检查邮箱是否发生变化
            email_changed = False
            if db_email != self.main_window.current_email:
                print(f"自动刷新: 检测到邮箱变化，从 {self.main_window.current_email} 变为 {db_email}")
                self.main_window.current_email = db_email
                email_changed = True
                
            # 创建后台线程执行刷新操作
            thread = threading.Thread(target=lambda: self._background_refresh_quota(email_changed))
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            print(f"自动刷新当前账户额度时出错: {str(e)}")
    
    def _background_refresh_quota(self, email_changed=False):
        """在后台线程中执行刷新操作
        
        Args:
            email_changed: 邮箱是否发生变化
        """
        try:
            # 如果邮箱已变化，强制更新UI
            if email_changed:
                # 通知主线程更新当前账户信息
                QTimer.singleShot(0, self.main_window.load_current_account)
                
                # 新增：在更新基础信息后，尝试从缓存更新模型使用UI
                QTimer.singleShot(10, self.main_window._try_update_usage_from_cache)
                
                # 稍作延迟后，请求API获取最新数据
                QTimer.singleShot(20, lambda: self._fetch_current_account_quota(silent=False))
                return
                
            # 正常流程：保存当前额度数据用于比较
            old_quota = None
            if self.main_window.current_email in self.main_window.account_quotas:
                old_quota = self.main_window.account_quotas.get(self.main_window.current_email, {}).copy()
                
            # 先检查临时文件是否有变化
            file_changed = self.main_window._check_temp_file_changes()
            
            # 如果临时文件没有变化，再请求API获取最新数据
            if not file_changed:
                # 静默获取数据，如果数据有变化返回True
                api_data_changed = self._fetch_current_account_quota(silent=True)
                
                # 数据变化时通过信号通知主线程
                if api_data_changed:
                    # 使用信号通知主线程数据已变化
                    self.main_window.refresh_data_changed_signal.emit(self.main_window.current_email)
            
            # 检查刷新后的数据是否有变化（即使文件变化已经处理过，也再次确认一下）
            new_quota = self.main_window.account_quotas.get(self.main_window.current_email, {})
            if old_quota and self.main_window._compare_quota_data(old_quota, new_quota):
                print(f"检测到数据变化，更新首页UI")
                # 通知主线程数据已变化
                self.main_window.refresh_data_changed_signal.emit(self.main_window.current_email)
            
        except Exception as e:
            print(f"后台刷新当前账户额度时出错: {str(e)}")
    
    def update_fetch_progress(self, current, total):
        """更新获取进度"""
        try:
            # 更新进度条值
            if hasattr(self.main_window, 'loading_progress') and self.main_window.loading_progress:
                if current == 0 or total == 0:
                    # 如果没有数据或刚开始加载，显示"正在加载中"
                    # 确保先停止任何正在进行的动画
                    if hasattr(self.main_window.loading_progress, '_animation') and self.main_window.loading_progress._animation.state() == QAbstractAnimation.State.Running:
                        self.main_window.loading_progress._animation.stop()
                    if hasattr(self.main_window.loading_progress, '_text_animation') and self.main_window.loading_progress._text_animation.state() == QAbstractAnimation.State.Running:
                        self.main_window.loading_progress._text_animation.stop()
                        
                    self.main_window.loading_progress.setFormat("正在加载中")
                    # 确保进度条仍有基本样式显示
                    self.main_window.loading_progress.setValue(1)
                    self.main_window.loading_progress.setMaximum(100)
                else:
                    # 停止任何正在进行的动画
                    if hasattr(self.main_window.loading_progress, '_animation') and self.main_window.loading_progress._animation.state() == QAbstractAnimation.State.Running:
                        self.main_window.loading_progress._animation.stop()
                    if hasattr(self.main_window.loading_progress, '_text_animation') and self.main_window.loading_progress._text_animation.state() == QAbstractAnimation.State.Running:
                        self.main_window.loading_progress._text_animation.stop()
                    
                    # 正常情况下使用Qt的内置格式化方式，如"%p%"、"%v/%m"
                    # 使用具体格式"xx/xx"而不是"%v/%m"模板
                    self.main_window.loading_progress.blockSignals(True)  # 阻止触发额外信号
                    self.main_window.loading_progress.setMaximum(total)
                    # 限制current不超过total，防止进度条显示异常
                    actual_value = min(max(1, current), total)
                    # 手动构建格式字符串，确保显示正确的进度
                    self.main_window.loading_progress.setFormat(f"{actual_value}/{total}")
                    self.main_window.loading_progress.setValue(actual_value)
                    self.main_window.loading_progress.blockSignals(False)
        except Exception as e:
            print(f"更新进度时出错: {str(e)}")
    
    def on_all_quotas_fetched(self, is_manual_refresh=False, show_toast=True):
        """所有账户额度获取完成
        
        Args:
            is_manual_refresh: 是否是手动刷新（通过刷新状态按钮）
            show_toast: 是否显示Toast提示，默认显示
        """
        try:
            # 重置数据获取标志
            self.is_fetching_quotas = False
            
            # 确保进度条显示完成状态
            if hasattr(self.main_window, 'loading_progress') and self.main_window.loading_progress:
                total_accounts = len(self.main_window.account_data.accounts)
                if total_accounts > 0:
                    # 先停止任何进行中的动画
                    if hasattr(self.main_window.loading_progress, '_animation') and self.main_window.loading_progress._animation.state() == QAbstractAnimation.State.Running:
                        self.main_window.loading_progress._animation.stop()
                    if hasattr(self.main_window.loading_progress, '_text_animation') and self.main_window.loading_progress._text_animation.state() == QAbstractAnimation.State.Running:
                        self.main_window.loading_progress._text_animation.stop()
                    
                    # 阻止信号发送避免触发多余动画
                    self.main_window.loading_progress.blockSignals(True)
                    # 手动设置格式字符串，而不是使用"%v/%m"
                    self.main_window.loading_progress.setFormat(f"{total_accounts}/{total_accounts}")
                    self.main_window.loading_progress.setMaximum(total_accounts)
                    self.main_window.loading_progress.setValue(total_accounts)
                    self.main_window.loading_progress.blockSignals(False)
            
            # 只保存额度数据到临时文件，不修改主文件
            self.main_window.account_data.save_quotas_to_temp()
            
            # 增加延迟，确保数据保存操作完成并避免与其他动画冲突
            QTimer.singleShot(150, lambda: self.main_window._update_after_fetch(is_manual_refresh, False))
        except Exception as e:
            print(f"处理账户额度数据完成事件时出错: {str(e)}")
            # 确保解锁加载状态
            self.is_fetching_quotas = False
            # 即使出错也尝试更新UI
            QTimer.singleShot(150, lambda: self.main_window._update_after_fetch(is_manual_refresh, False))
    
    def process_accounts(self, accounts):
        """处理账户列表，调用QuotaFetcherManager的处理方法"""
        try:
            # 调用QuotaFetcherManager的处理方法
            self.main_window.quota_manager.process_accounts(accounts)
        except Exception as e:
            print(f"处理账户列表时出错: {str(e)}") 