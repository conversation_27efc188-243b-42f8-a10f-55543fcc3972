#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户详情对话框模块
提供账户详细信息展示和备注编辑功能
"""

import sys
import pyperclip
import requests
import webbrowser
import re
from PySide6.QtWidgets import (
    QDialog, QFrame, QVBoxLayout, QHBoxLayout, QLabel,
    QTextEdit, QPushButton, QApplication, QWidget, QLineEdit, QGraphicsOpacityEffect, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer, QEvent, QRect, QPoint, QThread, Signal
from PySide6.QtGui import QIcon, QPixmap, QCursor

from theme import Theme
from logger import info, error
from widgets.dialog import StyledDialog
from widgets.clickable_copy_label import ClickableCopyLabel


class StripeUrlWorker(QThread):
    """获取Stripe URL的工作线程"""

    # 定义信号
    url_fetched = Signal(str)  # 成功获取URL
    error_occurred = Signal(str)  # 发生错误

    def __init__(self, cursor_token):
        super().__init__()
        self.cursor_token = cursor_token

    def run(self):
        """在后台线程中获取Stripe URL"""
        try:
            cookies = {
                'NEXT_LOCALE': 'en',
                'WorkosCursorSessionToken': self.cursor_token
            }

            headers = {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'if-none-match': '"zc6lt7pm693k"',
                'priority': 'u=1, i',
                'referer': 'https://cursor.com/dashboard',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
                'sec-ch-ua-arch': 'x86',
                'sec-ch-ua-bitness': '64',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': 'Windows',
                'sec-ch-ua-platform-version': '19.0.0',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
            }

            response = requests.get('https://cursor.com/api/stripeSession',
                                  cookies=cookies, headers=headers, timeout=15)

            if response.status_code == 200:
                stripe_url = response.text.strip().replace('"', '')
                self.url_fetched.emit(stripe_url)
            else:
                self.error_occurred.emit(f"获取Stripe会话失败: {response.status_code}")

        except Exception as e:
            self.error_occurred.emit(f"获取Stripe会话URL时出错: {str(e)}")


class AccountDetailsDialog(QDialog):
    """账户详情对话框类"""
    
    def __init__(self, account_data, main_window, parent=None):
        """初始化账户详情对话框
        
        Args:
            account_data: 账户数据字典
            main_window: 主窗口实例
            parent: 父窗口
        """
        super().__init__(parent)
        self.account_data = account_data
        self.main_window = main_window
        self.email = account_data.get("email", "")
        self.password = account_data.get("password", "")
        self.notes = account_data.get("notes", "")
        
        # 记录日志
        info(f"显示账户详情: {self.email}")
        
        # 创建账户详情对话框
        self.dialog = StyledDialog(parent, None, width=480)
        
        # 创建UI
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        # 创建内容区域的背景框架 - 使用统一的背景色
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        content_frame.setStyleSheet(f"""
            #contentFrame {{
                background-color: {Theme.CARD_LEVEL_1};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        # 使用垂直布局
        frame_layout = QVBoxLayout(content_frame)
        frame_layout.setContentsMargins(20, 20, 20, 20)
        frame_layout.setSpacing(15)
        
        # 添加轻量级的提示文本 - 放在卡片上方
        tip_layout = QHBoxLayout()
        tip_layout.setContentsMargins(5, 0, 5, 5)
        
        # 添加提示文本
        tip_text = QLabel("点击可复制到剪贴板")
        tip_text.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: 11px;
            background-color: transparent;
        """)
        tip_layout.addWidget(tip_text)
        tip_layout.addStretch(1)  # 添加弹性空间，使标签靠左显示
        
        frame_layout.addLayout(tip_layout)
        
        # 创建两个统一样式的卡片框架
        # 邮箱卡片
        email_card = self._create_copy_card(
            icon_text="📧",
            icon_image=":/icons/email.png",
            value=self.email,
            container_id="emailCard"
        )
        frame_layout.addWidget(email_card)
        
        # 密码卡片
        password_card = self._create_copy_card(
            icon_text="🔒",
            icon_image=":/icons/password.png",
            value=self.password if self.password else "该账户在JSON文件里未保存密码",
            is_copyable=bool(self.password),
            container_id="passwordCard"
        )
        frame_layout.addWidget(password_card)
        
        # Token卡片 - 添加访问令牌复制功能
        access_token = ""
        if "auth_info" in self.account_data and "cursorAuth/accessToken" in self.account_data["auth_info"]:
            access_token = self.account_data["auth_info"]["cursorAuth/accessToken"]
        
        token_card = self._create_copy_card(
            icon_text="🔑",
            icon_image=":/icons/token.png",
            value=access_token if access_token else "该账户在JSON文件里未保存Token",
            is_copyable=bool(access_token),
            container_id="tokenCard",
            label="Access Token"  # 添加标签，明确指示这是访问令牌
        )
        frame_layout.addWidget(token_card)
        
        # 备注卡片 - 与邮箱和密码卡片保持一致风格
        notes_card = QFrame()
        notes_card.setObjectName("notesCard")
        notes_card.setStyleSheet(f"""
            #notesCard {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                border: 1px solid {Theme.GLASS_BORDER};
            }}
            #notesCard:hover {{
                border: 1px solid {Theme.ACCENT};
            }}
        """)
        
        # 设置备注卡片布局
        notes_layout = QVBoxLayout(notes_card)
        notes_layout.setContentsMargins(15, 12, 15, 12)
        notes_layout.setSpacing(8)
        
        # 备注标题区域
        notes_header = QHBoxLayout()
        notes_header.setSpacing(12)
        
        # 备注图标
        notes_icon = QLabel()
        notes_icon_pixmap = QPixmap(":/icons/notes.png")
        if not notes_icon_pixmap.isNull():
            notes_icon.setPixmap(notes_icon_pixmap.scaled(22, 22, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
        else:
            notes_icon.setText("📝")
            notes_icon.setStyleSheet(f"""
                color: {Theme.TEXT_SECONDARY};
                font-size: 18px;
                background-color: transparent;
            """)
        notes_icon.setFixedSize(22, 22)
        notes_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 备注标签
        notes_label = QLabel("账户备注")
        notes_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
            background-color: transparent;
        """)
        
        # 添加图标和标签到标题区域
        notes_header.addWidget(notes_icon)
        notes_header.addWidget(notes_label)
        notes_header.addStretch(1)
        notes_layout.addLayout(notes_header)
        
        # 创建备注文本框
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("在此处添加账户相关备注...")
        self.notes_edit.setText(self.notes)
        self.notes_edit.setMinimumHeight(50)
        self.notes_edit.setMaximumHeight(50)
        self.notes_edit.setStyleSheet(f"""
            QTextEdit {{
                background-color: {Theme.CARD_LEVEL_2};
                border: none;
                color: {Theme.TEXT_PRIMARY};
                padding: 2px 0px;
            }}
            QTextEdit:focus {{
                border: none;
                outline: none;
            }}
        """)
        notes_layout.addWidget(self.notes_edit)
        
        # 添加备注卡片到主布局
        frame_layout.addWidget(notes_card)
        
        # 添加框架到对话框
        self.dialog.addWidget(content_frame)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 添加弹性空间，使按钮位于右侧
        button_layout.addStretch(1)
        
        # 添加打开StripeURL按钮
        stripe_btn = QPushButton("打开StripeURL")
        stripe_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        stripe_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #6772E5;
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: #5469D4;
            }}
            QPushButton:pressed {{
                background-color: #6772E5;
                opacity: 0.8;
            }}
        """)
        stripe_btn.clicked.connect(self._on_stripe_button_clicked)
        button_layout.addWidget(stripe_btn)

        # 添加编辑按钮
        edit_btn = QPushButton("编辑账户")
        edit_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        edit_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.WARNING};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: #D6BA6F;
            }}
            QPushButton:pressed {{
                background-color: {Theme.WARNING};
                opacity: 0.8;
            }}
        """)
        edit_btn.clicked.connect(self._on_edit_button_clicked)
        button_layout.addWidget(edit_btn)
        
        # 添加确定按钮并自定义样式
        ok_btn = QPushButton("确定")
        ok_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        ok_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: {Theme.ACCENT};
                opacity: 0.8;
            }}
        """)
        
        # 保存初始备注内容，用于比较
        self.initial_notes = self.notes
        
        # 为对话框添加事件过滤器，用于处理点击空白区域取消文本框焦点
        self.dialog.installEventFilter(self.dialog)
        
        # 覆盖对话框的事件过滤器来处理点击事件
        original_event_filter = self.dialog.eventFilter
        
        def custom_event_filter(obj, event):
            if event.type() == QEvent.Type.MouseButtonPress:
                # 如果鼠标点击事件发生在文本框外部，取消文本框的焦点
                if self.notes_edit.hasFocus():
                    cursor = QCursor.pos()
                    notes_edit_global_rect = QRect(
                        self.notes_edit.mapToGlobal(QPoint(0, 0)),
                        self.notes_edit.size()
                    )
                    if not notes_edit_global_rect.contains(cursor):
                        # 如果点击位置不在文本框内，取消文本框焦点
                        self.dialog.setFocus()
                        return True  # 事件已处理
            
            # 调用原始的事件过滤器
            if original_event_filter is not None:
                return original_event_filter(obj, event)
            return False
        
        # 替换对话框的事件过滤器
        self.dialog.eventFilter = custom_event_filter
        
        # 在对话框关闭前保存备注内容
        def save_notes_and_close():
            # 获取备注内容
            notes_content = self.notes_edit.toPlainText().strip()
            
            # 检查备注内容是否有变化
            if notes_content != self.initial_notes:
                # 如果备注不为空，则保存到账户数据中
                if notes_content:
                    # 更新内存中的账户数据
                    for account in self.main_window.account_data.accounts:
                        if account.get("email") == self.email:
                            account["notes"] = notes_content
                            break
                    
                    # 保存数据到文件
                    self.main_window.account_data.save_accounts()
                    self.main_window.show_toast("备注已保存")
                elif "notes" in self.account_data:
                    # 如果备注为空且之前有备注，则删除备注字段
                    for account in self.main_window.account_data.accounts:
                        if account.get("email") == self.email and "notes" in account:
                            account.pop("notes")
                            break
                    
                    # 保存数据到文件
                    self.main_window.account_data.save_accounts()
                    self.main_window.show_toast("备注已清空")
            
            # 关闭对话框
            self.dialog.accept()
        
        # 添加确定按钮到布局
        button_layout.addWidget(ok_btn)
        
        # 添加按钮布局到对话框
        self.dialog.addLayout(button_layout)
        
        # 直接设置按钮的点击事件，不尝试断开之前的连接
        ok_btn.clicked.connect(save_notes_and_close)
    
    def _create_copy_card(self, icon_text, icon_image, value, is_copyable=True, container_id=None, label=None):
        """创建统一样式的复制卡片
        
        Args:
            icon_text: 图标文本（当图片不可用时）
            icon_image: 图标图片路径
            value: 显示和复制的值
            is_copyable: 是否可复制
            container_id: 容器ID
            label: 标签文本
            
        Returns:
            QFrame: 创建的卡片
        """
        # 创建卡片容器
        card = QFrame()
        if container_id:
            card.setObjectName(container_id)
        
        # 设置统一的样式
        card.setStyleSheet(f"""
            #{container_id} {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                border: 1px solid {Theme.GLASS_BORDER};
            }}
            #{container_id}:hover {{
                border: 1px solid {Theme.ACCENT};
            }}
        """)
        
        # 根据是否有标签调整高度
        if label:
            card.setFixedHeight(60)  # 有标签时略微增加高度
        else:
            card.setFixedHeight(50)  # 默认高度
            
        # 是否可复制，设置鼠标样式和点击事件
        if is_copyable:
            card.setCursor(Qt.CursorShape.PointingHandCursor)
            # 为整个卡片添加点击事件
            card.mousePressEvent = lambda e: self._copy_text_to_clipboard(e, value, card, None, None) if e.button() == Qt.MouseButton.LeftButton else None
        
        # 创建水平布局
        card_layout = QHBoxLayout(card)
        card_layout.setContentsMargins(15, 0, 15, 0)
        card_layout.setSpacing(12)
        
        # 创建图标
        icon = QLabel()
        icon_pixmap = QPixmap(icon_image)
        if not icon_pixmap.isNull():
            icon.setPixmap(icon_pixmap.scaled(22, 22, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
        else:
            icon.setText(icon_text)
            icon.setStyleSheet(f"""
                color: {Theme.TEXT_SECONDARY};
                font-size: 18px;
                background-color: transparent;
            """)
        icon.setFixedSize(22, 22)
        icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        card_layout.addWidget(icon, 0, Qt.AlignmentFlag.AlignVCenter)  # 设置图标垂直居中
        
        # 创建内容容器，用于垂直布局标签和值
        content_container = QWidget()
        content_container.setStyleSheet("background-color: transparent;")
        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(0, 4, 0, 4)
        content_layout.setSpacing(2)
        
        # 添加标签（如果有）
        if label:
            label_widget = QLabel(label)
            label_widget.setStyleSheet(f"""
                color: {Theme.TEXT_SECONDARY};
                font-size: 12px;
                background-color: transparent;
            """)
            content_layout.addWidget(label_widget)
        
        # 创建内容/值部分
        if is_copyable:
            # 使用文本标签替代可复制标签，因为整个卡片已经是可点击的
            value_label = QLabel(value)
            text_color = Theme.TEXT_PRIMARY
            
            # 处理长文本
            if len(value) > 40:
                value_label.setWordWrap(False)
                fm = value_label.fontMetrics()
                
                # 对于非常长的文本（如Token），使用更极端的截断
                if len(value) > 100:  # Token通常很长
                    # 显示前10个字符和后5个字符，中间用省略号
                    displayed_text = f"{value[:10]}...{value[-5:]}"
                    # 设置工具提示，鼠标悬停时显示完整文本
                    value_label.setToolTip(value)
                else:
                    # 对于中等长度文本，使用常规的省略模式
                    elided_text = fm.elidedText(value, Qt.TextElideMode.ElideMiddle, 300)
                    displayed_text = elided_text
                
                value_label.setText(displayed_text)
            
            value_label.setStyleSheet(f"""
                color: {text_color};
                background-color: transparent;
            """)
        else:
            # 使用普通标签
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                color: {Theme.TEXT_SECONDARY};
                background-color: transparent;
            """)
        
        content_layout.addWidget(value_label)
        
        # 添加内容容器到卡片布局
        card_layout.addWidget(content_container, 1, Qt.AlignmentFlag.AlignVCenter)  # 设置内容垂直居中
        
        # 复制图标
        if is_copyable:
            copy_icon = QLabel("⧉")
            copy_icon.setStyleSheet(f"""
                color: {Theme.TEXT_SECONDARY};
                background-color: transparent;
                font-size: 16px;
            """)
            copy_icon.setFixedSize(20, 20)
            card_layout.addWidget(copy_icon, 0, Qt.AlignmentFlag.AlignVCenter)  # 设置图标垂直居中
        
        return card
    
    def _copy_text_to_clipboard(self, event, text, container, text_label=None, copy_icon=None):
        """复制文本到剪贴板并显示反馈
        
        Args:
            event: 鼠标事件
            text: 要复制的文本
            container: 容器组件
            text_label: 文本标签（可为None）
            copy_icon: 复制图标（可为None）
        """
        if event.button() == Qt.MouseButton.LeftButton:
            # 复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText(text)
            
            # 显示视觉反馈
            if text_label and copy_icon:
                text_label.setStyleSheet("color: white; background-color: transparent;")
                copy_icon.setStyleSheet("color: white; background-color: transparent; font-size: 16px;")
                copy_icon.setText("✓")
                
                # 0.5秒后恢复
                QTimer.singleShot(500, lambda: self._reset_copy_feedback(container, text_label, copy_icon))
            else:
                # 如果是整个卡片的复制，临时改变卡片边框颜色
                original_style = container.styleSheet()
                container.setStyleSheet(original_style + f"border: 1px solid {Theme.SUCCESS};")
                
                # 0.5秒后恢复
                QTimer.singleShot(500, lambda: container.setStyleSheet(original_style))
            
            # 创建适合显示的文本摘要
            display_text = text
            if len(display_text) > 20:
                # 如果文本超过20个字符，截取前10个字符和后5个字符，中间用...代替
                display_text = display_text[:10] + "..." + display_text[-5:]
            
            # 显示包含被复制内容的提示
            self.main_window.show_toast(f"「{display_text}」复制成功")
    
    def _reset_copy_feedback(self, container, text_label, copy_icon):
        """重置复制反馈，恢复原始显示"""
        # 重置容器样式
        container.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_2};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            padding: 10px;
        """)
        
        # 重置标签样式
        if text_label:
            text_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY};")
            
        # 重置图标
        if copy_icon:
            copy_icon.setText("⧉")  # 复制图标
            copy_icon.setStyleSheet(f"color: {Theme.TEXT_SECONDARY};")
    
    def _on_edit_button_clicked(self):
        """编辑按钮点击处理"""
        # 保存当前备注内容
        notes_content = self.notes_edit.toPlainText().strip()
        
        # 检查备注内容是否有变化
        if notes_content != self.initial_notes:
            # 如果备注不为空，则保存到账户数据中
            if notes_content:
                # 更新内存中的账户数据
                for account in self.main_window.account_data.accounts:
                    if account.get("email") == self.email:
                        account["notes"] = notes_content
                        break
                
                # 保存数据到文件
                self.main_window.account_data.save_accounts()
                self.main_window.show_toast("备注已保存")
            elif "notes" in self.account_data:
                # 如果备注为空且之前有备注，则删除备注字段
                for account in self.main_window.account_data.accounts:
                    if account.get("email") == self.email and "notes" in account:
                        account.pop("notes")
                        break
                
                # 保存数据到文件
                self.main_window.account_data.save_accounts()
                self.main_window.show_toast("备注已清空")
        
        # 创建编辑账户对话框
        self._create_edit_account_dialog()
    
    def _create_edit_account_dialog(self):
        """创建编辑账户对话框"""
        # 隐藏当前对话框
        self.dialog.hide()
        
        # 创建编辑账户对话框
        edit_dialog = StyledDialog(self.dialog.parent(), "编辑账户信息", width=480)
        
        # 创建内容区域的背景框架
        content_frame = QFrame()
        content_frame.setObjectName("editContentFrame")
        content_frame.setStyleSheet(f"""
            #editContentFrame {{
                background-color: {Theme.CARD_LEVEL_1};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        # 创建表单布局
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)
        
        # --- 邮箱输入字段 ---
        email_header_layout = QHBoxLayout()
        email_header_layout.setSpacing(8)
        email_icon = QLabel("📧")
        email_icon.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: 16px;")
        email_header_layout.addWidget(email_icon)
        
        email_label = QLabel("邮箱地址")
        email_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        email_header_layout.addWidget(email_label)
        email_header_layout.addStretch()
        form_layout.addLayout(email_header_layout)
        
        self.edit_email_input = QLineEdit()
        self.edit_email_input.setText(self.email)
        self.edit_email_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QLineEdit:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
        """)
        form_layout.addWidget(self.edit_email_input)
        
        # --- 密码输入字段 ---
        password_header_layout = QHBoxLayout()
        password_header_layout.setSpacing(8)
        password_icon = QLabel("🔑")
        password_icon.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: 16px;")
        password_header_layout.addWidget(password_icon)
        
        password_label = QLabel("密码")
        password_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        password_header_layout.addWidget(password_label)
        password_header_layout.addStretch()
        form_layout.addLayout(password_header_layout)
        
        self.edit_password_input = QLineEdit()
        self.edit_password_input.setText(self.password)
        self.edit_password_input.setPlaceholderText("请输入密码 (可不填)")
        self.edit_password_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QLineEdit:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
        """)
        form_layout.addWidget(self.edit_password_input)
        
        # --- Token输入字段 ---
        token_header_layout = QHBoxLayout()
        token_header_layout.setSpacing(8)
        token_icon = QLabel("🔐")
        token_icon.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: 16px;")
        token_header_layout.addWidget(token_icon)
        
        token_label = QLabel("Token")
        token_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        token_header_layout.addWidget(token_label)
        token_header_layout.addStretch()
        form_layout.addLayout(token_header_layout)
        
        # 获取Token
        access_token = ""
        if "auth_info" in self.account_data and "cursorAuth/accessToken" in self.account_data["auth_info"]:
            access_token = self.account_data["auth_info"]["cursorAuth/accessToken"]
        
        self.edit_token_input = QLineEdit()
        self.edit_token_input.setText(access_token)
        self.edit_token_input.setPlaceholderText("eyJhbGci...Rb1dQsg")
        self.edit_token_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QLineEdit:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
        """)
        form_layout.addWidget(self.edit_token_input)
        
        # 添加表单到对话框
        edit_dialog.addLayout(form_layout)
        
        # 添加按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 添加空白占位，使按钮居右
        spacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        button_layout.addItem(spacer)
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_2};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
                min-width: 80px;
            }}
            QPushButton:hover {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
            }}
            QPushButton:pressed {{
                background-color: #1A1D23;
            }}
        """)
        button_layout.addWidget(cancel_btn)
        
        # 保存按钮
        save_btn = QPushButton("保存")
        save_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        save_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: #2AAA8A;
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        button_layout.addWidget(save_btn)
        
        # 添加按钮布局到对话框
        edit_dialog.addLayout(button_layout)
        
        # 设置按钮点击事件
        cancel_btn.clicked.connect(lambda: self._on_edit_cancel(edit_dialog))
        save_btn.clicked.connect(lambda: self._on_edit_confirm_clicked(edit_dialog))
        
        # 显示编辑对话框
        edit_dialog.exec()
    
    def _on_edit_cancel(self, edit_dialog):
        """编辑取消处理"""
        # 关闭编辑对话框
        edit_dialog.reject()
        # 显示原始对话框
        self.dialog.show()
    
    def _on_edit_confirm_clicked(self, edit_dialog):
        """编辑确认按钮点击处理"""
        # 获取编辑后的数据
        new_email = self.edit_email_input.text().strip()
        new_password = self.edit_password_input.text().strip()
        new_token = self.edit_token_input.text().strip()
        
        # 验证必填字段
        if not new_email:
            self.main_window.show_toast("请输入邮箱地址", error=True)
            return
        
        if not new_token:
            self.main_window.show_toast("请输入Token", error=True)
            return
        
        # 保存编辑后的账户数据
        self._save_edited_account(new_email, new_password, new_token)
        
        # 关闭编辑对话框
        edit_dialog.accept()
        # 关闭原始对话框
        self.dialog.accept()
    
    def _save_edited_account(self, new_email, new_password, new_token):
        """保存编辑后的账户数据
        
        Args:
            new_email: 新的邮箱地址
            new_password: 新的密码
            new_token: 新的Token
        """
        try:
            # 记录日志
            info(f"开始保存编辑后的账户数据: 原邮箱={self.email}, 新邮箱={new_email}")
            
            # 找到需要更新的账户
            for account in self.main_window.account_data.accounts:
                if account.get("email") == self.email:
                    # 更新邮箱
                    if new_email != self.email:
                        account["email"] = new_email
                    
                    # 更新密码
                    if new_password:
                        account["password"] = new_password
                    elif "password" in account and not new_password:
                        # 如果新密码为空且原账户有密码，则删除密码
                        account.pop("password")
                    
                    # 更新Token
                    if "auth_info" not in account:
                        account["auth_info"] = {}
                    
                    account["auth_info"]["cursorAuth/cachedEmail"] = new_email
                    account["auth_info"]["cursorAuth/accessToken"] = new_token
                    account["auth_info"]["cursorAuth/refreshToken"] = new_token
                    
                    break
            
            # 保存到文件
            if self.main_window.account_data.save_accounts():
                info(f"账户 {self.email} 编辑并保存成功")
                self.main_window.show_toast(f"账户 {self.email} 编辑并保存成功")
                
                # 刷新UI
                # 1. 重新加载本地存储的账户数据
                self.main_window.account_data.load_accounts()
                
                # 2. 清空现有账户行
                for account_email, row in list(self.main_window.account_rows.items()):
                    self.main_window.accounts_layout.removeWidget(row)
                    row.deleteLater()
                self.main_window.account_rows.clear()
                
                # 3. 更新账户计数
                self.main_window._update_accounts_count()
                
                # 4. 重新加载账户列表
                self.main_window.load_accounts()
                
                # 5. 使用不重建UI的排序方法
                self.main_window._sort_accounts_without_rebuild_ui()
                
                # 6. 自动刷新所有账户状态
                self.main_window.fetch_all_accounts_quota(show_toast=True)
            else:
                error(f"保存编辑后的账户 {self.email} 失败")
                self.main_window.show_toast(f"保存编辑后的账户 {self.email} 失败", error=True)
        except Exception as e:
            error(f"编辑账户时出错: {str(e)}")
            self.main_window.show_toast(f"编辑账户时出错: {str(e)}", error=True)
    
    def _on_stripe_button_clicked(self):
        """处理打开StripeURL按钮点击事件"""
        try:
            # 获取账户的access token
            access_token = ""
            if "auth_info" in self.account_data and "cursorAuth/accessToken" in self.account_data["auth_info"]:
                access_token = self.account_data["auth_info"]["cursorAuth/accessToken"]

            if not access_token:
                self.main_window.show_toast("无法获取账户Token，请确保账户信息完整", error=True)
                return

            # 构建完整的token（参考quota获取逻辑）
            prefix = 'user_01000000000000000000000000%3A%3A'
            full_token = prefix + access_token

            # 显示加载提示
            self.main_window.show_toast("正在获取Stripe URL...")

            # 创建工作线程
            self.stripe_worker = StripeUrlWorker(full_token)

            # 连接信号
            self.stripe_worker.url_fetched.connect(self._on_stripe_url_fetched)
            self.stripe_worker.error_occurred.connect(self._on_stripe_error)

            # 启动线程
            self.stripe_worker.start()

        except Exception as e:
            error(f"打开Stripe URL时出错: {str(e)}")
            self.main_window.show_toast(f"打开Stripe URL时出错: {str(e)}", error=True)

    def _on_stripe_url_fetched(self, stripe_url):
        """处理成功获取Stripe URL的回调"""
        try:
            # 在浏览器中打开URL
            webbrowser.open(stripe_url)
            self.main_window.show_toast("已在浏览器中打开Stripe页面")
            info(f"已为账户 {self.email} 打开Stripe URL: {stripe_url}")
        except Exception as e:
            error(f"打开浏览器时出错: {str(e)}")
            self.main_window.show_toast(f"打开浏览器时出错: {str(e)}", error=True)

    def _on_stripe_error(self, error_message):
        """处理获取Stripe URL失败的回调"""
        error(f"获取Stripe URL失败: {error_message}")
        self.main_window.show_toast(f"获取Stripe URL失败: {error_message}", error=True)



    def exec(self):
        """显示对话框并执行其主循环"""
        return self.dialog.exec()