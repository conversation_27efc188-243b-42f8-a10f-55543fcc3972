"""
实用工具模块
提供应用程序中使用的各种工具函数和辅助类
"""

import sys
import os
import re
import subprocess
import platform
import threading
import time
import json
import asyncio

from PySide6.QtWidgets import (
    QGraphicsOpacityEffect, QApplication, QWidget, QMainWindow, QPushButton, QLabel, QFrame, QLineEdit, QMessageBox, QVBoxLayout,
    QHBoxLayout, QDialog, QComboBox
)
from PySide6.QtCore import Qt, QPoint, QAbstractAnimation
from PySide6.QtGui import QPalette, QColor

from theme import Theme
from widgets.dialog import StyledDialog


# 获取应用数据目录函数
def get_app_data_dir(app_name="YCursor"):
    """
    获取应用数据目录，根据不同操作系统返回适当的路径
    
    Args:
        app_name: 应用程序名称，默认为"YCursor"
        
    Returns:
        str: 应用数据目录的完整路径
    """
    app_dir = None
    
    try:
        if sys.platform == "win32":  # Windows
            # 使用Roaming目录，这样数据会跟随用户配置文件
            app_data = os.getenv("APPDATA")
            if app_data:
                app_dir = os.path.join(app_data, app_name)
        elif sys.platform == "darwin":  # macOS
            app_dir = os.path.expanduser(f"~/Library/Application Support/{app_name}")
        else:  # Linux和其他系统
            app_dir = os.path.expanduser(f"~/.config/{app_name}")
        
        # 确保目录存在
        if app_dir and not os.path.exists(app_dir):
            os.makedirs(app_dir)
            
        return app_dir
    except Exception as e:
        print(f"获取应用数据目录时出错: {str(e)}")
        # 如果发生错误，返回当前目录作为后备
        if getattr(sys, 'frozen', False):
            return os.path.dirname(sys.executable)
        else:
            return os.path.dirname(os.path.abspath(__file__))

# Asyncio兼容性辅助函数
def setup_asyncio_event_loop_policy():
    """
    配置适合当前平台的 asyncio 事件循环策略
    主要用于解决 Windows 平台上 Python 3.11+ 打包后的兼容性问题
    """
    if sys.platform == "win32" and sys.version_info >= (3, 11):
        # 在Windows平台上使用 WindowsSelectorEventLoopPolicy
        # 这是解决Windows上Python 3.11+打包后的asyncio问题的关键
        try:
            if not isinstance(asyncio.get_event_loop_policy(), asyncio.WindowsSelectorEventLoopPolicy):
                asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
                print("已设置Windows兼容的事件循环策略: WindowsSelectorEventLoopPolicy")
        except Exception as e:
            print(f"设置事件循环策略时出错: {str(e)}")
            
def create_new_event_loop():
    """
    创建新的事件循环并设置为当前线程的事件循环
    
    Returns:
        asyncio.AbstractEventLoop: 新创建的事件循环
    """
    # 确保使用正确的事件循环策略
    setup_asyncio_event_loop_policy()
    
    try:
        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop
    except Exception as e:
        print(f"创建事件循环时出错: {str(e)}")
        # 如果创建失败，尝试使用SelectorEventLoop
        try:
            loop = asyncio.SelectorEventLoop()
            asyncio.set_event_loop(loop)
            return loop
        except Exception as e2:
            print(f"创建备用事件循环时出错: {str(e2)}")
            return None

def safely_run_until_complete(coro):
    """
    安全地运行异步协程直到完成
    
    Args:
        coro: 要运行的协程
        
    Returns:
        任何协程的返回值
    """
    # 获取事件循环或创建新的
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = create_new_event_loop()
        
    if loop and not loop.is_closed():
        try:
            return loop.run_until_complete(coro)
        except Exception as e:
            print(f"运行协程时出错: {str(e)}")
            # 在发生错误时不关闭循环，让调用者决定是否关闭
            return None
    else:
        print("没有可用的事件循环，无法运行协程")
        return None

# 调用此函数以确保在应用程序启动时设置正确的事件循环策略
setup_asyncio_event_loop_policy()

def get_accounts_file_path(default_filename="cursor_accounts.json"):
    """
    获取账户数据文件的路径，根据用户设置决定使用自定义路径还是默认路径
    
    Args:
        default_filename: 默认文件名，默认为"cursor_accounts.json"
        
    Returns:
        str: 账户数据文件的完整路径
    """
    try:
        # 获取settings.json文件路径
        settings_file = os.path.join(get_app_data_dir(), "settings.json")
        
        # 检查设置文件是否存在
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                
                # 检查是否启用了自定义JSON文件路径
                use_custom_json = settings.get("use_custom_json_file", False)
                if use_custom_json:
                    # 获取自定义路径
                    custom_path = settings.get("custom_json_file_path", "")
                    if custom_path and os.path.exists(custom_path):
                        print(f"使用自定义账户文件路径: {custom_path}")
                        return custom_path
                    elif custom_path:
                        print(f"警告：设置的自定义路径不存在: {custom_path}，将使用默认路径")
        
        # 使用默认路径 - 应用数据目录下的account子目录
        app_data_dir = get_app_data_dir()
        account_dir = os.path.join(app_data_dir, "account")
        
        # 确保account目录存在
        if not os.path.exists(account_dir):
            os.makedirs(account_dir, exist_ok=True)
            
        # 返回默认账户文件路径
        accounts_file = os.path.join(account_dir, default_filename)
        print(f"使用默认账户文件路径: {accounts_file}")
        return accounts_file
    except Exception as e:
        print(f"获取账户文件路径时出错: {str(e)}")
        
        # 如果发生错误，返回当前目录中的默认文件名作为后备
        if getattr(sys, 'frozen', False):
            return os.path.join(os.path.dirname(sys.executable), default_filename)
        else:
            return os.path.join(os.path.dirname(os.path.abspath(__file__)), default_filename)


# 实用工具类
class Utils:
    @staticmethod
    def set_dark_theme(app):
        """设置应用程序的暗色主题"""
        dark_palette = QPalette()
        
        # 设置颜色组
        dark_palette.setColor(QPalette.ColorRole.Window, QColor(Theme.PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.WindowText, QColor(Theme.TEXT_PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.Base, QColor(Theme.SECONDARY))
        dark_palette.setColor(QPalette.ColorRole.AlternateBase, QColor(Theme.CARD_BG))
        dark_palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(Theme.TEXT_PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.ToolTipText, QColor(Theme.TEXT_PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.Text, QColor(Theme.TEXT_PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.Button, QColor(Theme.SECONDARY))
        dark_palette.setColor(QPalette.ColorRole.ButtonText, QColor(Theme.TEXT_PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.BrightText, QColor(Theme.ACCENT))
        dark_palette.setColor(QPalette.ColorRole.Link, QColor(Theme.ACCENT))
        dark_palette.setColor(QPalette.ColorRole.Highlight, QColor(Theme.SELECTION))
        dark_palette.setColor(QPalette.ColorRole.HighlightedText, QColor(Theme.TEXT_PRIMARY))
        
        # 应用暗色主题
        app.setPalette(dark_palette)
        
        # 设置应用程序样式表
        app.setStyleSheet(f"""
            QMainWindow, QWidget {{
                background-color: {Theme.PRIMARY};
                color: {Theme.TEXT_PRIMARY};
                font-family: {Theme.FONT_FAMILY};
            }}
            
            /* 全局禁用所有控件的焦点框 */
            * {{
                outline: none;
            }}
            
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            
            QScrollBar:vertical {{
                background: {Theme.PRIMARY};
                width: 8px;
                margin: 0px;
            }}
            
            QScrollBar::handle:vertical {{
                background: {Theme.BORDER};
                min-height: 20px;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            
            QScrollBar:horizontal {{
                background: {Theme.PRIMARY};
                height: 8px;
                margin: 0px;
            }}
            
            QScrollBar::handle:horizontal {{
                background: {Theme.BORDER};
                min-width: 20px;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                width: 0px;
            }}
            
            QPushButton {{
                background-color: {Theme.SECONDARY};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS};
                padding: 10px 16px;
                font-weight: bold;
                outline: none; /* 明确禁用按钮的焦点框 */
            }}
            
            QPushButton:focus {{
                outline: none; /* 确保在焦点状态下也不显示焦点框 */
                border: 1px solid {Theme.BORDER}; /* 保持普通边框 */
            }}
            
            QPushButton:hover {{
                background-color: {Theme.HOVER};
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
            }}
            
            QPushButton:pressed {{
                background-color: {Theme.ACCENT};
                color: {Theme.PRIMARY};
            }}
            
            QPushButton:disabled {{
                background-color: {Theme.DISABLED};
                color: {Theme.TEXT_SECONDARY};
                border: 1px solid {Theme.BORDER};
            }}
            
            QLineEdit {{
                background-color: {Theme.SECONDARY};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS};
                padding: 10px;
                selection-background-color: {Theme.ACCENT};
            }}
            
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
            }}
            
            QLabel {{
                color: {Theme.TEXT_PRIMARY};
            }}
            
            QProgressBar {{
                border: none;
                border-radius: {Theme.BORDER_RADIUS};
                text-align: center;
                background-color: {Theme.SECONDARY};
                font-weight: bold;
                height: 20px;
            }}
            
            QProgressBar::chunk {{
                background-color: {Theme.ACCENT};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)

    @staticmethod
    def get_cursor_version():
        """获取Cursor版本"""
        try:
            if sys.platform == "win32":  # Windows
                cursor_path = os.path.join(os.getenv("LOCALAPPDATA"), "Programs", "cursor", "Cursor.exe")
                # 使用PowerShell获取文件版本信息
                command = f'powershell -command "(Get-Item \'{cursor_path}\').VersionInfo.ProductVersion"'
                version = subprocess.check_output(command, shell=True).decode().strip()
                return version
            elif sys.platform == "darwin":  # macOS
                cursor_path = "/Applications/Cursor.app/Contents/Info.plist"
                if os.path.exists(cursor_path):
                    command = f'defaults read {cursor_path} CFBundleShortVersionString'
                    version = subprocess.check_output(command, shell=True).decode().strip()
                    return version
                else:
                    return "未找到"
            else:  # Linux
                return "未知"
        except Exception as e:
            print(f"获取Cursor版本时出错: {str(e)}")
            return "未知"

    @staticmethod
    def show_message(parent, title, text, icon=QMessageBox.Icon.Information):
        """显示消息框"""
        return StyledDialog.showInfoDialog(parent, title, text)

    @staticmethod
    def confirm_message(parent, title, text, icon=QMessageBox.Icon.Question):
        """显示确认消息框"""
        # 对于错误图标，使用红色按钮
        if icon == QMessageBox.Icon.Critical:
            confirm_color = Theme.ERROR
        # 对于警告图标，使用黄色按钮
        elif icon == QMessageBox.Icon.Warning:
            confirm_color = Theme.WARNING
        # 其他情况使用默认绿色
        else:
            confirm_color = Theme.ACCENT
            
        return StyledDialog.showConfirmDialog(parent, title, text, confirm_color=confirm_color)

    @staticmethod
    def get_cursor_cache_dir():
        """获取Cursor缓存目录"""
        try:
            if sys.platform == "win32":  # Windows
                return os.path.join(os.getenv("APPDATA"), "cursor")
            elif sys.platform == "darwin":  # macOS
                return os.path.expanduser("~/Library/Application Support/cursor")
            elif sys.platform.startswith("linux"):  # Linux
                return os.path.expanduser("~/.config/cursor")
            else:
                return None
        except Exception as e:
            print(f"获取Cursor缓存目录时出错: {str(e)}")
            return None


# 独立工具函数

def cleanup_on_exit(temp_file_path):
    """程序退出时的清理工作"""
    try:
        # 检查并删除临时文件
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
            print(f"程序退出：已删除临时文件 {temp_file_path}")
    except Exception as e:
        print(f"程序退出清理时出错: {str(e)}")


def center_window(window):
    """将窗口移动到屏幕中央，确保绝对居中"""
    # 获取窗口当前所在的屏幕
    window_center = window.frameGeometry().center()
    screen = QApplication.screenAt(window_center)
    if not screen:  # 如果无法确定窗口所在屏幕，使用主屏幕
        screen = QApplication.primaryScreen()
    
    screen_geometry = screen.availableGeometry()  # 使用可用区域而非整个屏幕
    
    # 更新窗口几何信息确保尺寸计算准确
    window.updateGeometry()
    window.adjustSize()
    
    # 获取准确的窗口尺寸包括边框
    window_frame = window.frameGeometry()
    
    # 计算居中位置，考虑屏幕可用区域
    center_point = screen_geometry.center()
    
    # 将窗口的中心点设置到屏幕中心点
    window_frame.moveCenter(center_point)
    dest_pos = window_frame.topLeft()
    
    # 对于无边框窗口，使用更准确的几何校正
    if window.windowFlags() & Qt.WindowType.FramelessWindowHint:
        window.setGeometry(
            dest_pos.x(),
            dest_pos.y(),
            window.width(),
            window.height()
        )
    else:
        window.move(dest_pos)


def demo_dialogs():
    """演示所有对话框样式（仅供参考）"""
    app = QApplication(sys.argv)
    Utils.set_dark_theme(app)
    
    # 创建一个主窗口
    main_window = QDialog()
    main_window.setWindowTitle("对话框演示")
    main_window.resize(800, 600)
    
    # 创建中心控件
    central_widget = QFrame()
    main_layout = QVBoxLayout(main_window)
    central_layout = QVBoxLayout(central_widget)
    
    # 添加展示按钮
    info_btn = QPushButton("信息对话框")
    confirm_btn = QPushButton("确认对话框")
    warning_btn = QPushButton("警告对话框")
    error_btn = QPushButton("错误对话框")
    
    # 连接信号
    info_btn.clicked.connect(lambda: StyledDialog.showInfoDialog(main_window, "信息", "这是一个信息对话框样式示例"))
    confirm_btn.clicked.connect(lambda: StyledDialog.showConfirmDialog(main_window, "确认", "您确定要继续操作吗？"))
    warning_btn.clicked.connect(lambda: StyledDialog.showWarningDialog(main_window, "警告", "操作可能引起数据丢失，请谨慎操作"))
    error_btn.clicked.connect(lambda: StyledDialog.showErrorDialog(main_window, "错误", "操作失败，请稍后重试"))
    
    # 添加到布局
    central_layout.addWidget(info_btn)
    central_layout.addWidget(confirm_btn)
    central_layout.addWidget(warning_btn)
    central_layout.addWidget(error_btn)
    central_layout.addStretch()
    
    main_layout.addWidget(central_widget)
    
    # 显示窗口
    main_window.show()
    sys.exit(app.exec())


def show_demo_quota_dialog(parent):
    """演示删除额度对话框"""
    dialog = StyledDialog(parent, None)
    
    # 添加说明文本
    description = QLabel("请选择要删除的账户条件：")
    description.setStyleSheet(f"""
        color: {Theme.TEXT_PRIMARY};
        font-size: {Theme.FONT_SIZE_NORMAL};
    """)
    dialog.addWidget(description)
    
    # 创建条件选择区域的背景框架
    conditions_frame = QFrame()
    conditions_frame.setObjectName("conditionsFrame")
    conditions_frame.setStyleSheet(f"""
        #conditionsFrame {{
            background-color: #121317;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        }}
    """)
    
    frame_layout = QVBoxLayout(conditions_frame)
    frame_layout.setContentsMargins(15, 15, 15, 15)
    
    # 创建条件选择区域
    condition_layout = QHBoxLayout()
    condition_layout.setSpacing(10)
    
    # 操作符选择
    operator_combo = QComboBox()
    operator_combo.addItems(["大于等于", "小于等于", "等于"])
    operator_combo.setStyleSheet(f"""
        QComboBox {{
            background-color: #121317;
            color: {Theme.TEXT_PRIMARY};
            border: 1px solid {Theme.BORDER};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            padding: 8px 15px 8px 15px;
            min-width: 120px;
            font-size: {Theme.FONT_SIZE_NORMAL};
        }}
        QComboBox:hover {{
            border: 1px solid {Theme.ACCENT};
            background-color: #181b21;
        }}
        QComboBox:focus {{
            border: 1px solid {Theme.ACCENT};
            background-color: #181b21;
        }}
        QComboBox::drop-down {{
            subcontrol-origin: padding;
            subcontrol-position: right center;
            width: 20px;
            border-left: none;
            padding-right: 5px;
        }}
        QComboBox::down-arrow {{
            width: 10px;
            height: 10px;
            image: none;
            border-top: 5px solid {Theme.TEXT_PRIMARY};
            border-right: 5px solid transparent;
            border-left: 5px solid transparent;
        }}
        QComboBox QAbstractItemView {{
            background-color: #121317;
            color: {Theme.TEXT_PRIMARY};
            border: 1px solid {Theme.BORDER};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            selection-background-color: {Theme.ACCENT};
            outline: none;
            padding: 5px;
        }}
        QComboBox QAbstractItemView::item {{
            padding: 8px 10px;
            min-height: 25px;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        }}
        QComboBox QAbstractItemView::item:hover {{
            background-color: #1E2128;
        }}
        QComboBox QAbstractItemView::item:selected {{
            background-color: {Theme.ACCENT};
            color: white;
        }}
    """)
    condition_layout.addWidget(operator_combo)
    
    # 额度输入
    quota_input = QLineEdit()
    quota_input.setPlaceholderText("输入额度")
    quota_input.setStyleSheet(f"""
        QLineEdit {{
            background-color: #121317;
            color: {Theme.TEXT_PRIMARY};
            border: 1px solid {Theme.BORDER};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            padding: 8px 15px;
            min-width: 120px;
            font-size: {Theme.FONT_SIZE_NORMAL};
        }}
        QLineEdit:hover {{
            border: 1px solid {Theme.ACCENT};
            background-color: #181b21;
        }}
        QLineEdit:focus {{
            border: 1px solid {Theme.ACCENT};
            background-color: #181b21;
        }}
    """)
    condition_layout.addWidget(quota_input)
    
    # 添加条件布局到框架
    frame_layout.addLayout(condition_layout)
    
    # 添加框架到对话框
    dialog.addWidget(conditions_frame)
    
    # 添加确认和取消按钮
    confirm_btn = dialog.addButtons("确认", "取消")
    confirm_btn.clicked.connect(dialog.accept)
    
    dialog.exec()

def compare_quota_data(old_quota, new_quota):
    """比较两个额度数据是否有关键字段的差异
    
    Args:
        old_quota: 旧的额度数据
        new_quota: 新的额度数据
    
    Returns:
        bool: 如果有差异返回True，否则返回False
    """
    try:
        if not old_quota or not new_quota:
            return True
            
        # 比较直接字段
        if (old_quota.get('used_tokens') != new_quota.get('used_tokens') or
            old_quota.get('hard_limit_tokens') != new_quota.get('hard_limit_tokens') or
            old_quota.get('startOfMonth') != new_quota.get('startOfMonth')):
            return True
            
        # 比较GPT-4模型数据 - 适配新的JSON格式
        old_gpt4 = old_quota.get('gpt-4', {})
        new_gpt4 = new_quota.get('gpt-4', {})
        if (old_gpt4.get('numRequests') != new_gpt4.get('numRequests') or
            old_gpt4.get('numRequestsTotal') != new_gpt4.get('numRequestsTotal') or
            old_gpt4.get('numTokens') != new_gpt4.get('numTokens') or
            old_gpt4.get('maxRequestUsage') != new_gpt4.get('maxRequestUsage') or
            old_gpt4.get('maxTokenUsage') != new_gpt4.get('maxTokenUsage')):
            return True

        # 比较GPT-3.5模型数据 - 适配新的JSON格式
        old_gpt35 = old_quota.get('gpt-3.5-turbo', {})
        new_gpt35 = new_quota.get('gpt-3.5-turbo', {})
        if (old_gpt35.get('numRequests') != new_gpt35.get('numRequests') or
            old_gpt35.get('numRequestsTotal') != new_gpt35.get('numRequestsTotal') or
            old_gpt35.get('numTokens') != new_gpt35.get('numTokens') or
            old_gpt35.get('maxRequestUsage') != new_gpt35.get('maxRequestUsage') or
            old_gpt35.get('maxTokenUsage') != new_gpt35.get('maxTokenUsage')):
            return True

        # 比较GPT-4-32k模型数据 - 新增对gpt-4-32k的支持
        old_gpt4_32k = old_quota.get('gpt-4-32k', {})
        new_gpt4_32k = new_quota.get('gpt-4-32k', {})
        if (old_gpt4_32k.get('numRequests') != new_gpt4_32k.get('numRequests') or
            old_gpt4_32k.get('numRequestsTotal') != new_gpt4_32k.get('numRequestsTotal') or
            old_gpt4_32k.get('numTokens') != new_gpt4_32k.get('numTokens') or
            old_gpt4_32k.get('maxRequestUsage') != new_gpt4_32k.get('maxRequestUsage') or
            old_gpt4_32k.get('maxTokenUsage') != new_gpt4_32k.get('maxTokenUsage')):
            return True
            
        return False
    except Exception:
        # 如果比较过程中出错，保守地返回True以触发更新
        return True

def copy_error_to_clipboard(error_info, toast_queue=None):
    """将错误信息复制到剪贴板
    
    Args:
        error_info: 错误信息字符串
        toast_queue: 可选的toast队列，用于显示提示消息
    """
    try:
        # 创建剪贴板
        clipboard = QApplication.clipboard()
        # 设置文本
        clipboard.setText(error_info)
        # 显示复制成功提示（如果提供了toast_queue）
        if toast_queue:
            toast_queue.add_message("错误信息已复制到剪贴板", duration=2000, error=False)
        return True
    except Exception as e:
        print(f"复制错误信息失败: {str(e)}")
        return False