#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
手动输入账户对话框模块
提供单个账户手动输入导入功能
"""

import json
from PySide6.QtWidgets import (
    QFrame, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QComboBox, QWidget, QCheckBox, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from theme import Theme
from widgets.dialog import StyledDialog
from logger import info, error

class ManualImportDialog(QFrame):
    """手动输入账户对话框类"""
    
    def __init__(self, main_window, parent=None):
        """初始化手动输入对话框
        
        Args:
            main_window: 主窗口实例 (CursorAccountManager)
            parent: 父窗口
        """
        super().__init__(parent)
        self.main_window = main_window
        self.dialog = StyledDialog(parent or main_window, "手动输入账户")
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        # 创建表单布局
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)
        
        # --- 邮箱输入字段 ---
        email_header_layout = QHBoxLayout()
        email_header_layout.setSpacing(8)
        email_icon = QLabel("📧")
        email_icon.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: 16px;")
        email_header_layout.addWidget(email_icon)
        
        email_label = QLabel("邮箱地址")
        email_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        email_header_layout.addWidget(email_label)
        email_header_layout.addStretch()
        form_layout.addLayout(email_header_layout)
        
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        self.email_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QLineEdit:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
        """)
        form_layout.addWidget(self.email_input)
        
        # --- 密码输入字段 (非必填) ---
        password_header_layout = QHBoxLayout()
        password_header_layout.setSpacing(8)
        password_icon = QLabel("🔑")
        password_icon.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: 16px;")
        password_header_layout.addWidget(password_icon)
        
        password_label = QLabel("密码")
        password_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        password_header_layout.addWidget(password_label)
        password_header_layout.addStretch()
        form_layout.addLayout(password_header_layout)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码 (可不填)")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QLineEdit:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
        """)
        form_layout.addWidget(self.password_input)
        
        # --- Token输入字段 ---
        token_header_layout = QHBoxLayout()
        token_header_layout.setSpacing(8)
        token_icon = QLabel("🔐")
        token_icon.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: 16px;")
        token_header_layout.addWidget(token_icon)
        
        token_label = QLabel("Token")
        token_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        token_header_layout.addWidget(token_label)
        token_header_layout.addStretch()
        form_layout.addLayout(token_header_layout)
        
        self.token_input = QLineEdit()
        self.token_input.setPlaceholderText("eyJhbGci...Rb1dQsg")
        self.token_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QLineEdit:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
        """)
        form_layout.addWidget(self.token_input)
        
        # 添加表单到对话框
        self.dialog.addLayout(form_layout)
        
        # 添加按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 添加空白占位，使按钮居右
        spacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        button_layout.addItem(spacer)
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_2};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
                min-width: 80px;
            }}
            QPushButton:hover {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
            }}
            QPushButton:pressed {{
                background-color: #1A1D23;
            }}
        """)
        cancel_btn.clicked.connect(self.dialog.reject)
        button_layout.addWidget(cancel_btn)
        
        # 确认按钮
        confirm_btn = QPushButton("导入")
        confirm_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        confirm_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: #2AAA8A;
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        confirm_btn.clicked.connect(self._on_confirm_clicked)
        button_layout.addWidget(confirm_btn)
        
        self.dialog.addLayout(button_layout)
    
    def _on_confirm_clicked(self):
        """确认按钮点击处理函数"""
        # 获取输入数据
        email = self.email_input.text().strip()
        password = self.password_input.text().strip()
        token = self.token_input.text().strip()
        
        # 验证必填字段
        if not email:
            self.main_window.show_toast("请输入邮箱地址", error=True)
            return
        
        if not token:
            self.main_window.show_toast("请输入Token", error=True)
            return
        
        # 构建账户对象
        account = {
            "email": email,
            "auth_info": {
                "cursorAuth/cachedSignUpType": "Auth_0",
                "cursorAuth/cachedEmail": email,
                "cursorAuth/accessToken": token,
                "cursorAuth/refreshToken": token
            }
        }
        
        # 如果有密码，添加到账户信息中
        if password:
            account["password"] = password
        
        # 导入账户
        self._import_account(account)
        
        # 关闭对话框
        self.dialog.accept()
    
    def _import_account(self, account):
        """导入单个账户
        
        Args:
            account: 账户数据字典
        """
        try:
            info(f"开始手动导入账户: {account.get('email', '未知')}")
            
            # 验证必要字段
            if not self._validate_account_data(account):
                self.main_window.show_toast("导入失败：账户数据格式不正确", error=True)
                error(f"导入失败：账户数据格式不正确: {account}")
                return
            
            # 检查是否已存在该账户
            email = account.get("email", "").lower()
            
            # 创建当前账户邮箱的映射（转换为小写便于不区分大小写比较）
            existing_accounts_map = {}
            for idx, acc in enumerate(self.main_window.account_data.accounts):
                acc_email = acc.get("email", "").lower()
                if acc_email:
                    existing_accounts_map[acc_email] = idx
            
            # 处理导入
            if email in existing_accounts_map:
                # 更新已存在账户信息
                existing_idx = existing_accounts_map[email]
                existing_account = self.main_window.account_data.accounts[existing_idx]
                
                # 合并账户信息（保留现有字段，添加/更新导入的新字段）
                for key, value in account.items():
                    if key != "email":  # 邮箱保持不变
                        existing_account[key] = value
                
                info(f"更新了已存在账户: {email}")
                self.main_window.show_toast(f"更新了已存在账户: {email}")
            else:
                # 添加为新账户
                self.main_window.account_data.accounts.append(account)
                info(f"添加了新账户: {email}")
                self.main_window.show_toast(f"成功添加新账户: {email}")
            
            # 保存账户数据
            self.main_window.account_data.save_accounts(allow_create=True)
            
            # 刷新UI
            # 1. 重新加载本地存储的账户数据
            self.main_window.account_data.load_accounts()
            info("手动导入完成，重新加载账户数据")
            
            # 2. 清空现有账户行，避免引用问题
            for account_email, row in list(self.main_window.account_rows.items()):
                self.main_window.accounts_layout.removeWidget(row)
                row.deleteLater()
            self.main_window.account_rows.clear()
            
            # 3. 确保加载容器隐藏，避免后续引用问题
            if hasattr(self.main_window, 'loading_container') and self.main_window.loading_container is not None:
                try:
                    self.main_window.loading_container.setVisible(False)
                except Exception as e:
                    print(f"隐藏加载容器时出错: {str(e)}")
            
            # 4. 更新账户计数
            self.main_window._update_accounts_count()
            
            # 5. 重新加载账户列表
            self.main_window.load_accounts()
            
            # 6. 仅使用不重建UI的排序方法
            self.main_window._sort_accounts_without_rebuild_ui()
            
            # 7. 在有账户的情况下刷新额度数据
            if self.main_window.account_data.accounts:
                # 使用手动刷新模式，避免过多UI重建
                self.main_window.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=False)
            
        except Exception as e:
            self.main_window.show_toast(f"导入失败：{str(e)}", error=True)
            error(f"手动导入账户时出错: {str(e)}")
    
    def _validate_account_data(self, account):
        """验证账户数据是否包含必要字段
        
        Args:
            account: 账户数据字典
            
        Returns:
            bool: 账户数据是否有效
        """
        # 检查是否包含email字段
        if "email" not in account or not account["email"]:
            error(f"无效账户数据: 缺少email字段")
            return False
        
        # 检查是否包含auth_info字段
        if "auth_info" not in account or not isinstance(account["auth_info"], dict):
            error(f"无效账户数据: 缺少auth_info字段或格式不正确, email={account.get('email', '未知')}")
            return False
        
        # 检查auth_info中是否包含必要字段
        auth_info = account["auth_info"]
        required_fields = ["cursorAuth/cachedEmail", "cursorAuth/accessToken", "cursorAuth/refreshToken"]
        for field in required_fields:
            if field not in auth_info or not auth_info[field]:
                error(f"无效账户数据: auth_info中缺少{field}字段, email={account.get('email', '未知')}")
                return False
        
        return True
    
    def exec(self):
        """显示对话框"""
        return self.dialog.exec() 