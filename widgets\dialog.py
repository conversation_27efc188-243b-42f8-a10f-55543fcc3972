#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
对话框模块
提供自定义样式对话框控件，支持无标题栏、模糊背景、确认对话框等功能
"""

import sys

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QWidget, QFrame, QGraphicsDropShadowEffect, 
    QScrollArea, QSpacerItem, QSizePolicy, QGraphicsOpacityEffect, QMainWindow, QApplication,
    QCheckBox  # <--- 添加 QCheckBox 导入
)
from PySide6.QtCore import (
    Qt, QPropertyAnimation, QEasingCurve, QRect, QSize, QTimer, QEvent, QAbstractAnimation
)
from PySide6.QtGui import (
    QIcon, QColor, QPainter, QPen, QPainterPath, QPalette, QBrush, QLinearGradient, QFont
)

from theme import Theme


class StyledDialog(QDialog):
    """样式化对话框，无标题栏，带模糊背景效果"""
    
    def __init__(self, parent=None, title=None, width=400, height=None):
        super().__init__(parent)
        
        # 无边框窗口设置
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 根据parent设置模态类型
        if parent and parent.window() != parent: # 如果parent存在且不是顶层窗口（即是一个子窗口/弹窗）
             self.setWindowModality(Qt.WindowModal)
        else: # parent是None或顶层窗口
             self.setModal(True) # 等同于 Qt.ApplicationModal
        
        # 设置固定宽度
        self.setFixedWidth(width)
        if height:
            self.setFixedHeight(height)
        
        # 保存父窗口引用
        self.parent_widget = parent
        
        # 背景遮罩
        self.overlay = None
        self.create_overlay()
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建背景框架
        self.bg_frame = QFrame(self)
        self.bg_frame.setObjectName("bg_frame")
        self.bg_frame.setStyleSheet(f"""
            #bg_frame {{
                background-color: #121317;
                border: 1px solid {Theme.GLASS_BORDER};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        # 背景框架布局
        self.bg_layout = QVBoxLayout(self.bg_frame)
        self.bg_layout.setContentsMargins(25, 25, 25, 25)
        self.bg_layout.setSpacing(20)
        
        # 将内容布局设置为背景布局
        self.content_parent_layout = self.bg_layout
        
        # 将背景框架添加到主布局
        self.main_layout.addWidget(self.bg_frame)
        
        # 初始化拖动相关变量
        self.drag_position = None
        
        # 初始化浮动提示标签为None
        self.floating_tip_label = None
    
    def add_floating_tip(self, text):
        """添加浮动在对话框底部的提示文本"""
        # 如果已经存在提示标签，先移除它
        if self.floating_tip_label:
            self.floating_tip_label.setParent(None)
            self.floating_tip_label.deleteLater()
        
        # 创建新的提示标签
        self.floating_tip_label = QLabel(text, self)
        # 设置样式 - 去掉背景和边框，减小字体大小
        self.floating_tip_label.setStyleSheet(f"""
            background-color: transparent;
            color: {Theme.TEXT_SECONDARY};
            border: none;
            padding: 0px;
            font-size: 11px;
        """)
        
        # 使标签自适应内容大小
        self.floating_tip_label.adjustSize()
        
        # 将标签放置在对话框的底部中央
        self.updateFloatingTipPosition()
        
        # 显示标签
        self.floating_tip_label.show()
        
        return self.floating_tip_label
    
    def resizeEvent(self, event):
        """窗口大小改变时调整浮动提示的位置"""
        super().resizeEvent(event)
        self.updateFloatingTipPosition()
    
    def updateFloatingTipPosition(self):
        """更新浮动提示的位置"""
        if self.floating_tip_label:
            # 获取对话框底部位置
            dialog_bottom = self.rect().bottom()
            # 计算标签位置（底部中央，偏移5px，更贴近底部）
            label_width = self.floating_tip_label.width()
            label_x = (self.width() - label_width) // 2
            label_y = dialog_bottom - self.floating_tip_label.height() - 5
            
            # 设置标签位置
            self.floating_tip_label.move(label_x, label_y)
    
    def addWidget(self, widget):
        """添加控件到内容区域"""
        self.content_parent_layout.addWidget(widget)
        return widget
        
    def addLayout(self, layout):
        """添加布局到内容区域"""
        self.content_parent_layout.addLayout(layout)
        return layout
    
    def addButtons(self, confirm_text="确认", cancel_text="取消", confirm_color=None):
        """添加按钮到对话框底部"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 使用主题色或自定义颜色
        if confirm_color is None:
            confirm_color = Theme.ACCENT
            hover_color = Theme.ACCENT_HOVER
        else:
            # 生成hover颜色（略微变亮的版本）
            hover_color = confirm_color  # 使用相同颜色，不变化
        
        # 确认按钮
        self.confirm_btn = QPushButton(confirm_text)
        self.confirm_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.confirm_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {confirm_color};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 10px 20px;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {confirm_color};
                opacity: 0.8;
            }}
        """)
        
        # 如果有取消按钮文本，添加取消按钮
        if cancel_text:
            # 取消按钮
            self.cancel_btn = QPushButton(cancel_text)
            self.cancel_btn.setCursor(Qt.CursorShape.PointingHandCursor)
            self.cancel_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: #2A2E36;
                    color: {Theme.TEXT_PRIMARY};
                    border: 1px solid {Theme.BORDER};
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    padding: 10px 20px;
                    font-weight: bold;
                    min-width: 80px;
                }}
                QPushButton:hover {{
                    border: 1px solid {Theme.ACCENT};
                    color: {Theme.ACCENT};
                }}
                QPushButton:pressed {{
                    background-color: #252830;
                }}
            """)
            self.cancel_btn.clicked.connect(self.reject)
            
            # 添加按钮到布局
            button_layout.addWidget(self.confirm_btn)
            button_layout.addWidget(self.cancel_btn)
        else:
            # 只有确认按钮时居中显示
            button_layout.addStretch()
            button_layout.addWidget(self.confirm_btn)
            button_layout.addStretch()
        
        # 添加按钮布局到内容区域
        self.content_parent_layout.addLayout(button_layout)
        
        return self.confirm_btn
    
    def mousePressEvent(self, event):
        """鼠标按下事件，用于实现窗口拖动"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件，用于实现窗口拖动"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.drag_position:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = None
            event.accept()
    
    @staticmethod
    def showConfirmDialog(parent, title, text, confirm_text="确认", cancel_text="取消", confirm_color=None):
        """显示确认对话框，返回是否确认"""
        dialog = StyledDialog(parent, None)
        
        # 创建消息文本的背景框架
        message_frame = QFrame()
        message_frame.setObjectName("messageFrame")
        message_frame.setStyleSheet(f"""
            #messageFrame {{
                background-color: #121317;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        
        message_frame_layout = QVBoxLayout(message_frame)
        message_frame_layout.setContentsMargins(20, 20, 20, 20)
        
        # 消息文本
        message_label = QLabel(text)
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            background-color: transparent;
            padding: 0px;
        """)
        message_frame_layout.addWidget(message_label)
        
        # 添加消息框架到对话框
        dialog.addWidget(message_frame)
        
        confirm_btn = dialog.addButtons(confirm_text, cancel_text, confirm_color)
        confirm_btn.clicked.connect(dialog.accept)
        
        return dialog.exec() == QDialog.DialogCode.Accepted
    
    @staticmethod
    def showConfirmDialogWithCheckbox(parent, title, text, checkbox_text, checkbox_default_checked=True, confirm_text="确认", cancel_text="取消", confirm_color=None):
        """显示带复选框的确认对话框，返回(是否确认, 复选框状态)"""
        dialog = StyledDialog(parent, None)
        
        # 创建消息文本的背景框架
        message_frame = QFrame()
        message_frame.setObjectName("messageFrame")
        message_frame.setStyleSheet(f"""
            #messageFrame {{
                background-color: #121317; /* 与对话框背景略有区别，便于区分 */
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        
        message_frame_layout = QVBoxLayout(message_frame)
        message_frame_layout.setContentsMargins(20, 20, 20, 20)
        message_frame_layout.setSpacing(15) # 增加一点间距
        
        # 消息文本
        message_label = QLabel(text)
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            background-color: transparent;
            padding: 0px;
        """)
        message_frame_layout.addWidget(message_label)
        
        # ---- 添加复选框 ----
        checkbox = QCheckBox(checkbox_text)
        checkbox.setChecked(checkbox_default_checked)
        checkbox.setStyleSheet(f"""
            QCheckBox {{
                color: {Theme.TEXT_SECONDARY}; 
                font-size: {Theme.FONT_SIZE_SMALL};
                spacing: 5px; /* 文字和框的间距 */
            }}
            QCheckBox::indicator {{
                width: 15px;
                height: 15px;
                border: 1px solid {Theme.BORDER};
                border-radius: 3px;
                background-color: #2A2E36;
            }}
            QCheckBox::indicator:checked {{
                background-color: {Theme.ACCENT};
                border: 1px solid {Theme.ACCENT};
            }}
            QCheckBox::indicator:unchecked:hover {{
                border: 1px solid {Theme.ACCENT};
            }}
            QCheckBox::indicator:checked:hover {{
                background-color: {Theme.ACCENT_HOVER};
                border: 1px solid {Theme.ACCENT_HOVER};
            }}
        """)
        message_frame_layout.addWidget(checkbox)
        # ---- 复选框结束 ----
        
        # 添加消息框架到对话框
        dialog.addWidget(message_frame)
        
        confirm_btn = dialog.addButtons(confirm_text, cancel_text, confirm_color)
        # 需要连接accept才能让按钮关闭对话框
        confirm_btn.clicked.connect(dialog.accept) 
        
        # 执行对话框并获取结果
        result = dialog.exec()
        
        # 返回结果和复选框状态
        return result == QDialog.DialogCode.Accepted, checkbox.isChecked()
    
    @staticmethod
    def showInfoDialog(parent, title, text, ok_text="确定"):
        """显示信息对话框"""
        dialog = StyledDialog(parent, None)
        
        # 创建消息文本的背景框架
        message_frame = QFrame()
        message_frame.setObjectName("messageFrame")
        message_frame.setStyleSheet(f"""
            #messageFrame {{
                background-color: #121317;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        
        message_frame_layout = QVBoxLayout(message_frame)
        message_frame_layout.setContentsMargins(20, 20, 20, 20)
        
        # 消息文本
        message_label = QLabel(text)
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            background-color: transparent;
            padding: 0px;
        """)
        message_frame_layout.addWidget(message_label)
        
        # 添加消息框架到对话框
        dialog.addWidget(message_frame)
        
        # 添加确定按钮
        ok_btn = dialog.addButtons(ok_text)
        ok_btn.clicked.connect(dialog.accept)
        
        dialog.exec()
    
    def create_overlay(self):
        """创建半透明背景遮罩"""
        # 获取应用程序的主窗口作为遮罩的父对象
        self.parent_widget = self.parent()
        main_window = self._get_main_window()
        
        if main_window:
            # 创建遮罩控件
            self.overlay = QWidget(main_window)
            self.overlay.setObjectName("dialogOverlay")
            self.overlay.setStyleSheet(f"""
                #dialogOverlay {{
                    background-color: rgba(0, 0, 0, 0.7);
                    border-radius: {Theme.BORDER_RADIUS};
                }}
            """)
            # 设置遮罩大小覆盖整个主窗口
            self.overlay.setGeometry(main_window.rect())
            # 设置层级为主窗口的所有子控件最前
            self.overlay.stackUnder(self)
            # 确保遮罩在对话框下方但在其他控件上方
            self.overlay.raise_()
            self.overlay.hide()
            
            # 创建透明度效果，用于淡入淡出动画
            self.overlay_opacity = QGraphicsOpacityEffect(self.overlay)
            self.overlay_opacity.setOpacity(0.0)  # 初始透明度为0
            self.overlay.setGraphicsEffect(self.overlay_opacity)
            
            # 创建透明度动画
            self.overlay_anim = QPropertyAnimation(self.overlay_opacity, b"opacity")
            self.overlay_anim.setDuration(300)  # 300毫秒动画
            self.overlay_anim.setStartValue(0.0)
            self.overlay_anim.setEndValue(1.0)
            self.overlay_anim.setEasingCurve(QEasingCurve.Type.OutCubic)
            
            # 设置遮罩的事件过滤，防止点击穿透
            self.overlay.mousePressEvent = lambda e: e.accept()
    
    def showEvent(self, event):
        """对话框显示时显示背景遮罩"""
        super().showEvent(event)
        
        # 显示背景遮罩并播放淡入动画
        if self.overlay:
            # 获取主窗口并更新遮罩尺寸
            main_window = self._get_main_window()
            if main_window:
                self.overlay.setGeometry(main_window.rect())
            self.overlay.show()
            
            # 启动淡入动画
            self.overlay_anim.setDirection(QAbstractAnimation.Direction.Forward)
            self.overlay_anim.start()
            
            # 将对话框提到前面
            self.raise_()
        
        # 更新浮动提示位置
        self.updateFloatingTipPosition()
    
    def hideEvent(self, event):
        """对话框隐藏时隐藏背景遮罩"""
        # 停止任何正在进行的动画并隐藏遮罩
        if self.overlay:
            self.overlay_anim.stop()
            self.overlay.hide()
        
        super().hideEvent(event)
    
    def exec(self):
        """重写exec方法，确保遮罩处理"""
        # 获取主窗口
        main_window = self._get_main_window()
        
        # 显示遮罩并播放淡入动画
        if self.overlay and main_window:
            # 确保遮罩大小与主窗口匹配
            self.overlay.setGeometry(main_window.rect())
            self.overlay.show()
            
            # 启动淡入动画
            self.overlay_anim.setDirection(QAbstractAnimation.Direction.Forward)
            self.overlay_anim.start()
            
            # 确保对话框在遮罩上层
            self.raise_()
            
        # 先处理一下所有未处理的事件，确保对话框大小已计算
        QApplication.processEvents()
        
        # 强制布局计算以确保大小正确
        self.updateGeometry()
        self.adjustSize()
        
        # 居中显示对话框
        if main_window:
            # 获取主窗口的屏幕位置和中心点
            parent_geometry = main_window.frameGeometry()
            parent_center = parent_geometry.center()
            
            # 获取对话框的几何信息
            dialog_frame = self.frameGeometry()
            
            # 将对话框中心点设置到主窗口中心点
            dialog_frame.moveCenter(parent_center)
            dest_pos = dialog_frame.topLeft()
            
            # 确保对话框在屏幕内
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            
            # 如果对话框超出屏幕边界，调整位置
            if dest_pos.x() < screen_geometry.left():
                dest_pos.setX(screen_geometry.left() + 10)
            elif dest_pos.x() + dialog_frame.width() > screen_geometry.right():
                dest_pos.setX(screen_geometry.right() - dialog_frame.width() - 10)
                
            if dest_pos.y() < screen_geometry.top():
                dest_pos.setY(screen_geometry.top() + 10)
            elif dest_pos.y() + dialog_frame.height() > screen_geometry.bottom():
                dest_pos.setY(screen_geometry.bottom() - dialog_frame.height() - 10)
                
            self.move(dest_pos)
        
        result = super().exec()
        
        # 隐藏遮罩（动画已在hideEvent中处理）
        if self.overlay:
            self.overlay_anim.stop()
            self.overlay.hide()
        
        return result

    def _get_main_window(self):
        """获取应用程序的主窗口
        
        Returns:
            QWidget: 应用程序的主窗口，如果找不到则返回直接父窗口
        """
        # 首先尝试获取QApplication的所有顶层窗口
        main_windows = [w for w in QApplication.topLevelWidgets() if isinstance(w, QMainWindow)]
        
        # 如果找到了主窗口，返回第一个
        if main_windows:
            return main_windows[0]
        
        # 如果没找到主窗口，则递归查找父窗口链，直到找到顶层窗口
        parent = self.parent()
        while parent and parent.parent():
            parent = parent.parent()
        
        # 如果找到了顶层父窗口，返回它
        if parent:
            return parent
            
        # 否则返回直接父窗口
        return self.parent_widget