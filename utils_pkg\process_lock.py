import os
import sys
import atexit
import psutil
import time
import subprocess

from PySide6.QtWidgets import QTextBrowser, QSizePolicy, QSpacerItem, QFrame, QPushButton, QDialog, QVBoxLayout, QLabel, QHBoxLayout # Add QSpacerItem, QFrame, QPushButton, QDialog, QVBoxLayout, QLabel, QHBoxLayout
from PySide6.QtGui import QFontMetrics # Add QFontMetrics
from PySide6.QtCore import Qt # Add Qt
from PySide6.QtGui import QGuiApplication # Add QGuiApplication
try:
    # Try importing from the expected location relative to the project root
    from version_checker import StyledMessageBox
except ImportError:
    # Fallback if run directly or structure differs slightly
    try:
        # Assuming version_checker might be in the parent dir if process_lock is run standalone
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)
        from version_checker import StyledMessageBox
    except ImportError:
        print("ERROR: Could not import StyledMessageBox. Process lock dialog will fail.", file=sys.stderr)
        # Define a dummy class to avoid crashing
        class StyledMessageBox: pass # Minimal dummy

try:
    # Attempt relative import first for internal use
    from . import get_app_data_dir
except ImportError:
    # Fallback for potential direct execution or different structure
    try:
        from utils import get_app_data_dir
    except ImportError:
        # If utils is not directly available, try finding it relative to main script path
        # This is less robust but might work in some deployment scenarios
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        sys.path.insert(0, parent_dir)
        try:
            from utils import get_app_data_dir
        except ImportError:
            print("ERROR: Could not import get_app_data_dir. Process lock may fail.", file=sys.stderr)
            # Define a dummy function to avoid crashing, but log an error
            def get_app_data_dir():
                print("ERROR: get_app_data_dir is not available!", file=sys.stderr)
                # Fallback to a temporary directory, though not ideal for locking
                import tempfile
                return tempfile.gettempdir()

LOCK_FILENAME = "ycursor.lock"
_lock_file_handle = None # Keep the file handle open on Windows

def get_lock_filepath():
    """Gets the full path to the lock file within the app data directory."""
    try:
        app_dir = get_app_data_dir()
        if not app_dir:
            print("ERROR: Could not determine app data directory for lock file.", file=sys.stderr)
            return None
        # Ensure the directory exists
        os.makedirs(app_dir, exist_ok=True)
        return os.path.join(app_dir, LOCK_FILENAME)
    except Exception as e:
        print(f"ERROR: Failed to get lock file path: {e}", file=sys.stderr)
        return None

def is_process_running(pid):
    """Checks if a process with the given PID is currently running."""
    if pid is None or pid <= 0:
        return False
    try:
        return psutil.pid_exists(pid)
        # Optional: Check process name/cmdline if psutil.pid_exists is True
        # process = psutil.Process(pid)
        # return "python" in process.name().lower() or "main.py" in " ".join(process.cmdline())
    except psutil.NoSuchProcess:
        return False
    except psutil.AccessDenied:
        # We might not have permission to check the process, assume it's running
        # This is a safer default to prevent accidental multiple instances
        print(f"WARN: Access denied when checking PID {pid}. Assuming it might be running.", file=sys.stderr)
        return True
    except Exception as e:
        print(f"ERROR: Unexpected error checking PID {pid}: {e}", file=sys.stderr)
        return False # Safer to assume not running if check fails unexpectedly

def acquire_lock():
    """
    Tries to acquire a lock file.

    Returns:
        tuple: (bool, int or None)
               - bool: True if lock acquired successfully, False otherwise.
               - int or None: The PID holding the lock if failed, or current PID if successful, or None if error.
    """
    global _lock_file_handle
    lock_filepath = get_lock_filepath()
    if not lock_filepath:
        return False, None # Cannot determine lock file path

    # 设置一个整体操作超时，以避免长时间阻塞
    start_time = time.time()
    max_time = 5.0  # 最多5秒总超时时间
    retries = 3
    
    # 优化Windows系统下的文件操作性能
    if sys.platform == "win32":
        # 检查锁文件是否存在，使用快速检查方式
        if os.path.exists(lock_filepath):
            try:
                # 直接读取内容，并捕获可能的异常
                try:
                    with open(lock_filepath, 'r') as f:
                        existing_pid_str = f.read().strip()
                        if existing_pid_str:
                            try:
                                existing_pid = int(existing_pid_str)
                                # 快速检查进程是否存在
                                if is_process_running(existing_pid):
                                    return False, existing_pid
                            except ValueError:
                                pass  # 无效PID，将被视为损坏的锁文件
                except (IOError, ValueError):
                    pass  # 读取失败，将删除文件
                    
                # 如果到这里，锁文件要么是空的，要么包含无效PID，要么进程不存在
                try:
                    # 尝试删除旧的锁文件
                    os.remove(lock_filepath)
                    print(f"INFO: Removed stale lock file {lock_filepath}", file=sys.stderr)
                except OSError:
                    # 如果删除失败，继续尝试创建
                    print(f"WARN: Failed to remove stale lock file {lock_filepath}, trying to overwrite", file=sys.stderr)
                    
            except Exception as e:
                print(f"WARN: Error handling existing lock file: {e}", file=sys.stderr)
                # 继续尝试创建新锁
        
        # 使用更高效的文件创建方式
        try:
            # 优先使用写入模式打开
            with open(lock_filepath, 'w') as f:
                current_pid = os.getpid()
                f.write(str(current_pid))
            print(f"INFO: Acquired lock via file write on {lock_filepath} for PID {current_pid}", file=sys.stderr)
            atexit.register(release_lock)
            return True, current_pid
        except IOError as e:
            print(f"ERROR: Could not create or write lock file {lock_filepath}: {e}", file=sys.stderr)
            return False, None
    else:
        # 非Windows系统使用原始逻辑
        for attempt in range(retries):
            # 检查是否超过总超时时间
            if time.time() - start_time > max_time:
                print(f"WARN: Lock acquisition exceeded timeout of {max_time}s", file=sys.stderr)
                break
                
            try:
                # Check for existing lock file
                if os.path.exists(lock_filepath):
                    try:
                        with open(lock_filepath, 'r') as f:
                            existing_pid_str = f.read().strip()
                            if not existing_pid_str:
                                print(f"WARN: Lock file {lock_filepath} is empty. Removing stale lock.", file=sys.stderr)
                                os.remove(lock_filepath)
                                continue # Retry acquiring the lock immediately

                            existing_pid = int(existing_pid_str)

                    except (IOError, ValueError) as e:
                        print(f"ERROR: Could not read or parse PID from lock file {lock_filepath}: {e}. Attempting removal.", file=sys.stderr)
                        try:
                            os.remove(lock_filepath)
                        except OSError as remove_err:
                            print(f"ERROR: Failed to remove corrupt lock file {lock_filepath}: {remove_err}", file=sys.stderr)
                            return False, None # Indicate failure to handle lock file
                        continue # Retry acquiring the lock immediately

                    if is_process_running(existing_pid):
                        print(f"INFO: Lock file {lock_filepath} exists and PID {existing_pid} is running.", file=sys.stderr)
                        return False, existing_pid # Lock held by another process
                    else:
                        print(f"WARN: Lock file {lock_filepath} exists but PID {existing_pid} is not running. Removing stale lock.", file=sys.stderr)
                        try:
                            os.remove(lock_filepath)
                            # Stale lock removed, proceed to create new lock in this iteration
                        except OSError as e:
                            print(f"ERROR: Failed to remove stale lock file {lock_filepath}: {e}", file=sys.stderr)
                            return False, None # Indicate failure to handle lock file

                # 在Linux/macOS上使用标准文件创建和写入
                try:
                    with open(lock_filepath, 'x') as f:
                        current_pid = os.getpid()
                        f.write(str(current_pid))
                        print(f"INFO: Acquired lock via atomic create on {lock_filepath} for PID {current_pid}", file=sys.stderr)
                        atexit.register(release_lock)
                        return True, current_pid
                except FileExistsError:
                    # File already exists, could be a race condition or stale lock missed.
                    print(f"WARN: Lock file {lock_filepath} appeared during lock attempt. Retrying.", file=sys.stderr)
                    time.sleep(0.1) # Brief pause before retry
                    continue # Go to next attempt

            except Exception as e:
                print(f"ERROR: Unexpected error during lock acquisition attempt {attempt + 1}/{retries}: {e}", file=sys.stderr)
                time.sleep(0.1) # Wait a bit before retrying after unexpected error

    print(f"ERROR: Failed to acquire lock", file=sys.stderr)
    return False, None # Failed to acquire lock after retries

def release_lock():
    """Releases the lock file if it was acquired by the current process."""
    global _lock_file_handle
    lock_filepath = get_lock_filepath()
    if not lock_filepath:
        print("ERROR: Cannot determine lock file path for release.", file=sys.stderr)
        return

    current_pid = os.getpid()
    print(f"INFO: Attempting to release lock file {lock_filepath} for PID {current_pid}", file=sys.stderr)

    try:
        # Close the handle first if open (Windows)
        if _lock_file_handle is not None:
            try:
                os.close(_lock_file_handle)
                _lock_file_handle = None
                print(f"INFO: Closed lock file handle for {lock_filepath}", file=sys.stderr)
            except Exception as close_e:
                print(f"ERROR: Failed to close lock file handle for {lock_filepath}: {close_e}", file=sys.stderr)
                # Continue to try removing the file anyway

        # Now check if file exists and remove if PID matches
        if os.path.exists(lock_filepath):
            pid_in_file = -1
            try:
                with open(lock_filepath, 'r') as f:
                    pid_in_file_str = f.read().strip()
                    if pid_in_file_str:
                       pid_in_file = int(pid_in_file_str)
                    else:
                       print(f"WARN: Lock file {lock_filepath} is empty during release.", file=sys.stderr)

            except (IOError, ValueError) as e:
                print(f"ERROR: Could not read or parse PID from lock file {lock_filepath} during release: {e}", file=sys.stderr)
                # Cannot verify PID, maybe don't delete? Or delete cautiously?
                # Let's try deleting only if handle was ours or if file is empty/corrupt
                if _lock_file_handle is None: # If handle wasn't ours, be cautious
                     print(f"WARN: Could not read PID from {lock_filepath}, and lock handle wasn't held by this process. Skipping removal.", file=sys.stderr)
                     return


            # Only remove if PID matches or file was corrupt/empty (and handle was potentially ours)
            if pid_in_file == current_pid or pid_in_file == -1:
                 try:
                     os.remove(lock_filepath)
                     print(f"INFO: Successfully released lock file {lock_filepath} for PID {current_pid}", file=sys.stderr)
                 except OSError as e:
                     print(f"ERROR: Failed to remove lock file {lock_filepath}: {e}", file=sys.stderr)
            else:
                 print(f"WARN: PID in lock file ({pid_in_file}) does not match current PID ({current_pid}). Lock file {lock_filepath} not removed by this process.", file=sys.stderr)
        else:
            print(f"INFO: Lock file {lock_filepath} not found during release (already released?).", file=sys.stderr)

    except Exception as e:
        print(f"ERROR: Unexpected error during lock release: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)

# Example usage (for testing purposes)
if __name__ == "__main__":
    print("Attempting to acquire lock...")
    locked, pid = acquire_lock()
    if locked:
        print(f"Lock acquired successfully by PID {pid}. Lock file: {get_lock_filepath()}")
        print("App would run here...")
        input("Press Enter to release lock and exit...")
        # release_lock() is called automatically by atexit
    else:
        if pid:
            print(f"Failed to acquire lock. Process {pid} seems to be holding it.")
        else:
            print("Failed to acquire lock due to an error (check logs/stderr).")
        sys.exit(1)

# ---- NEW CLASS DEFINITION ----
class ProcessLockMessageBox(StyledMessageBox):
    """Custom message box for process lock errors with adjusted height calculation."""
    def __init__(self, parent=None, title="", text="", icon_type=StyledMessageBox.CRITICAL, buttons=None, pid=None):
        # 保存PID参数，用于"就要运行"按钮功能
        self.running_pid = pid
        
        # 如果提供了PID，修改按钮列表，添加"就要运行"按钮
        if pid is not None and buttons is not None and "退出" in buttons:
            buttons = ["就要运行", "退出"]
        
        # Initialize using the parent class
        super().__init__(parent, title, text, icon_type, buttons)

        # Find the QTextBrowser created by the parent
        text_browser = self.findChild(QTextBrowser)

        if text_browser:
            print("INFO: ProcessLockMessageBox found QTextBrowser, adjusting height.", file=sys.stderr)
            try:
                fm = QFontMetrics(text_browser.font())
                min_height = fm.height()
                max_height = 300 # Keep the same max height as original

                # Ensure layout is updated to get correct document size
                text_browser.document().adjustSize()
                # Use documentLayout for more accurate height
                doc_height = text_browser.document().documentLayout().documentSize().height()

                # Use the document height directly without offset
                target_height = doc_height

                # Clamp height between min and max
                final_height = max(min_height, min(target_height, max_height))

                text_browser.setFixedHeight(int(final_height))
                text_browser.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
                print(f"INFO: ProcessLockMessageBox adjusted QTextBrowser height to {final_height}", file=sys.stderr)
            except Exception as e:
                print(f"ERROR: Failed to adjust QTextBrowser height in ProcessLockMessageBox: {e}", file=sys.stderr)
        else:
            print("ERROR: ProcessLockMessageBox could not find QTextBrowser to adjust height.", file=sys.stderr)

        # 保持与 StyledMessageBox 一致的间距设置
        print("INFO: ProcessLockMessageBox inherits spacing from StyledMessageBox.", file=sys.stderr)
        
        # 为按钮设置样式，"就要运行"按钮使用次按钮样式，"退出"按钮使用主按钮样式
        try:
            # 查找所有按钮并设置样式
            for button in self.findChildren(QPushButton):
                if button.text() == "就要运行":
                    # "就要运行"按钮使用次按钮样式（灰色）
                    from version_checker import Theme
                    button.setStyleSheet(Theme.BUTTON_SECONDARY_STYLE)
                    # 断开默认连接并重新连接到自定义处理函数
                    button.clicked.disconnect()
                    button.clicked.connect(self._on_force_run_clicked)
                    print("INFO: Connected force run button click event and applied secondary style.", file=sys.stderr)
                elif button.text() == "退出":
                    # "退出"按钮使用主按钮样式（绿色）
                    from version_checker import Theme
                    button.setStyleSheet(Theme.BUTTON_PRIMARY_STYLE)
                    # 确保"退出"按钮调用reject()
                    button.clicked.disconnect()
                    button.clicked.connect(self.reject)
                    print("INFO: Applied primary style to exit button and connected to reject().", file=sys.stderr)
        except Exception as style_error:
            print(f"ERROR: Failed to set button styles: {style_error}", file=sys.stderr)
            # 即使设置样式失败，仍然确保"就要运行"按钮连接到正确的函数
            try:
                for button in self.findChildren(QPushButton):
                    if button.text() == "就要运行":
                        button.clicked.disconnect()
                        button.clicked.connect(self._on_force_run_clicked)
                        print("INFO: Connected force run button click event.", file=sys.stderr)
                        break
            except Exception as connect_error:
                print(f"ERROR: Failed to connect button event: {connect_error}", file=sys.stderr)
    
    def _on_force_run_clicked(self):
        """处理'就要运行'按钮的点击事件，终止现有进程并重启程序"""
        if self.running_pid is None:
            print("ERROR: No PID provided for force run action.", file=sys.stderr)
            self.reject()  # 如果没有PID，直接关闭对话框
            return
            
        print(f"INFO: Force run button clicked. Attempting to terminate PID {self.running_pid}...", file=sys.stderr)
        
        # 尝试终止进程
        try:
            # 检查进程是否存在
            if not is_process_running(self.running_pid):
                print(f"INFO: Process with PID {self.running_pid} is not running anymore.", file=sys.stderr)
            else:
                # 使用跨平台的方法终止进程
                self._kill_process(self.running_pid)
                
                # 等待2秒
                from PySide6.QtCore import QTimer
                
                print("INFO: Waiting 2 seconds before continuing...", file=sys.stderr)
                
                # 先隐藏当前对话框（应用程序已运行提示）
                self.hide()
                
                # 创建一个模态化的StyledMessageBox风格的等待对话框
                from version_checker import Theme, StyledMessageBox
                waiting_dialog = QDialog(self)
                waiting_dialog.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
                waiting_dialog.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
                waiting_dialog.setModal(True)
                
                # 创建主布局
                main_layout = QVBoxLayout(waiting_dialog)
                main_layout.setContentsMargins(0, 0, 0, 0)
                main_layout.setSpacing(0)
                
                # 创建背景框架
                bg_frame = QFrame(waiting_dialog)
                bg_frame.setObjectName("bg_frame")
                bg_frame.setStyleSheet(f"""
                    #bg_frame {{
                        background-color: {Theme.PRIMARY};
                        border: 1px solid {Theme.GLASS_BORDER};
                        border-radius: {Theme.BORDER_RADIUS};
                    }}
                """)
                
                # 背景框架布局
                bg_layout = QVBoxLayout(bg_frame)
                bg_layout.setContentsMargins(25, 25, 25, 25)
                bg_layout.setSpacing(15)
                
                # 添加图标和文本
                icon_label = QLabel(waiting_dialog)
                icon_label.setStyleSheet(f"""
                    background-color: {Theme.ACCENT};
                    color: white;
                    font-size: 18px;
                    font-weight: bold;
                    border-radius: 15px;
                    min-width: 30px;
                    max-width: 30px;
                    min-height: 30px;
                    max-height: 30px;
                    qproperty-alignment: AlignCenter;
                """)
                icon_label.setText("i")
                
                title_layout = QHBoxLayout()
                title_layout.addWidget(icon_label)
                title_layout.addSpacing(12)
                
                title_label = QLabel("请稍候", waiting_dialog)
                title_label.setStyleSheet(f"""
                    color: {Theme.TEXT_PRIMARY};
                    font-size: {Theme.FONT_SIZE_TITLE};
                    font-weight: bold;
                """)
                title_layout.addWidget(title_label)
                title_layout.addStretch()
                
                bg_layout.addLayout(title_layout)
                bg_layout.addSpacing(10)
                
                # 添加等待信息
                message_label = QLabel("正在终止现有进程，即将继续启动...", waiting_dialog)
                message_label.setStyleSheet(f"""
                    color: {Theme.TEXT_SECONDARY};
                    font-size: {Theme.FONT_SIZE_NORMAL};
                """)
                message_label.setWordWrap(True)
                message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                bg_layout.addWidget(message_label)
                
                main_layout.addWidget(bg_frame)
                
                # 固定对话框大小
                waiting_dialog.setFixedSize(400, 150)
                
                # 计算居中位置
                center = QGuiApplication.primaryScreen().availableGeometry().center()
                waiting_dialog.move(center.x() - waiting_dialog.width() // 2, 
                                    center.y() - waiting_dialog.height() // 2)
                
                # 确保等待对话框显示在前台
                waiting_dialog.show()
                waiting_dialog.raise_()
                waiting_dialog.activateWindow()
                
                # 使用定时器等待2秒
                timer = QTimer()
                timer.setSingleShot(True)
                timer.timeout.connect(lambda: self._continue_startup(waiting_dialog))
                timer.start(2000)  # 2秒后继续启动
                
                # 等待对话框关闭
                waiting_dialog.exec()
                return
            
        except Exception as e:
            print(f"ERROR: Failed to terminate process: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
        
        # 继续启动程序（无需等待）
        self._continue_startup()
    
    def _kill_process(self, pid):
        """使用跨平台方式终止指定的进程"""
        try:
            # 首先尝试使用psutil（跨平台方法）
            if psutil.pid_exists(pid):
                proc = psutil.Process(pid)
                proc.terminate()  # 尝试温和终止
                
                # 给进程一些时间来终止
                try:
                    proc.wait(timeout=1)  # 等待最多1秒
                except psutil.TimeoutExpired:
                    # 如果超时，则使用强制终止
                    proc.kill()
                    print(f"INFO: Force killed process with PID {pid}.", file=sys.stderr)
                    
                print(f"INFO: Successfully terminated process with PID {pid}.", file=sys.stderr)
                return True
            
            # 平台特定的方法（备用）
            if sys.platform == "win32":
                # Windows: 使用taskkill强制终止
                subprocess.run(f'taskkill /F /PID {pid}', shell=True)
                print(f"INFO: Used taskkill to terminate PID {pid}.", file=sys.stderr)
            elif sys.platform in ["darwin", "linux"]:
                # macOS/Linux: 使用kill命令发送SIGKILL信号
                subprocess.run(f'kill -9 {pid}', shell=True)
                print(f"INFO: Used kill -9 to terminate PID {pid}.", file=sys.stderr)
                
            return True
                
        except Exception as e:
            print(f"ERROR: Failed to kill process with PID {pid}: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            return False
    
    def _continue_startup(self, waiting_dialog=None):
        """继续启动程序"""
        # 关闭等待对话框（如果有）
        if waiting_dialog:
            waiting_dialog.accept()
            
        print("INFO: Continuing with application startup...", file=sys.stderr)
        
        # 重新获取进程锁
        try:
            locked, _ = acquire_lock()
            if not locked:
                print("WARNING: Failed to re-acquire process lock after terminating previous instance.", file=sys.stderr)
        except Exception as e:
            print(f"ERROR: Exception when trying to re-acquire process lock: {e}", file=sys.stderr)
        
        # 接受对话框（等同于点击确定按钮，让程序继续运行）
        self.accept() 