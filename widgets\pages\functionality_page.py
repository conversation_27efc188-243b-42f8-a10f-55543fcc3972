from PySide6.QtWidgets import (
    Q<PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout, 
    QLabel, QLineEdit, QPushButton, QFrame, QScrollArea, QSplitter,
    QSlider, QComboBox, QFileDialog, QCheckBox, QTextEdit, QTabWidget, 
    QMessageBox, QMenu, QApplication, QSpacerItem, QSizePolicy, QToolButton,
    QWidgetAction
)
from PySide6.QtGui import QAction, QIcon, QPixmap, QPainter, QColor
from PySide6.QtCore import Qt, Signal, QTimer, QThread
import platform
import os
import json
import subprocess
import sqlite3
import sys
import zipfile
import shutil
import tempfile
import time
from datetime import datetime
import re
from PySide6.QtWidgets import QStyle

from ..styled_widgets import Styled<PERSON>rame, StyledButton, StyledSwitch, StyledLineEdit
from ..animated_widgets import AnimatedProgressBar
from ..mcp_backup_dialog import McpBackupDialog
from ..dialog import StyledDialog
from theme import Theme
from utils import Utils, get_app_data_dir
from account.auth import CursorAuthManager
from logger import info, error
from .advanced_backup_section import AdvancedBackupSection
from widgets.bubble_tip import BubbleTip

class FunctionalityBlock(QFrame):
    """功能模块组件"""
    
    # 信号定义
    button_clicked = Signal(str)  # 参数为功能ID
    
    def __init__(self, title, description, button_text, func_id, icon=None):
        super().__init__()
        self.func_id = func_id
        self.init_ui(title, description, button_text, icon)
        
    def init_ui(self, title, description, button_text, icon):
        """初始化UI"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        
        # 描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL}; 
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
            padding-bottom: 10px;
        """)
        desc_label.setWordWrap(True)
        
        # 按钮
        button = StyledButton(button_text, icon=icon)
        button.setFixedHeight(36)
        button.clicked.connect(self._on_button_clicked)
        
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        layout.addWidget(button)
        
    def _on_button_clicked(self):
        """按钮点击处理函数"""
        self.button_clicked.emit(self.func_id)


class FunctionalityPage(QWidget):
    """功能页面类"""
    
    # 定义信号
    function_selected = Signal(str)  # 参数为功能ID
    request_auto_register_dialog = Signal()
    request_mcp_backup_dialog = Signal()
    toast_request = Signal(str, bool)
    cursor_version_updated = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.main_layout = None
        self.functions_frame = None
        self.functions_layout = None
        self.system_account_frame = None
        self.system_account_layout = None
        self.init_ui()
        
    def init_ui(self):
        """初始化UI基础结构"""
        print("\n============ 功能页面初始化基础结构 ============")
        
        self.setStyleSheet("background: transparent;")
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)  # 减少间距，由内部控制

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QScrollArea.Shape.NoFrame)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)  # 隐藏垂直滚动条
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)  # 隐藏水平滚动条
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
            QScrollBar:vertical {
                width: 0px;
            }
            QScrollBar:horizontal {
                height: 0px;
            }
        """)
        
        # 创建滚动区域内容容器
        scroll_content = QWidget()
        scroll_content.setStyleSheet("background: transparent;")
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(18)
        
        # 系统信息区域 - 采用半透明玻璃效果设计
        self.functions_frame = StyledFrame(has_glass_effect=True)
        self.functions_layout = QVBoxLayout(self.functions_frame)
        self.functions_layout.setContentsMargins(25, 25, 25, 25)
        
        # 功能区域 - 使用更现代的设计
        self.feature_frame = QFrame()
        self.feature_frame.setStyleSheet("background: transparent;")
        self.feature_layout = QVBoxLayout(self.feature_frame)
        self.feature_layout.setContentsMargins(0, 0, 0, 0)
        self.feature_layout.setSpacing(18)
        
        # 创建功能区域
        self._create_function_blocks()
        
        # 将框架添加到滚动内容布局中
        scroll_layout.addWidget(self.functions_frame)
        scroll_layout.addWidget(self.feature_frame)
        
        # 添加底部弹性空间
        scroll_layout.addStretch()
        
        # 设置滚动区域的内容
        scroll_area.setWidget(scroll_content)
        
        # 将滚动区域添加到主布局
        self.main_layout.addWidget(scroll_area)
        
        # 调用refresh来填充初始内容
        self.refresh()
    
    def _get_setting(self, key, default=None):
        """安全地从settings.json读取单个设置项"""
        try:
            settings_file = os.path.join(get_app_data_dir(), "settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    return settings.get(key, default)
            return default
        except Exception as e:
            print(f"读取设置项 '{key}' 时出错: {e}")
            return default

    def refresh(self):
        """刷新功能页面内容"""
        print("\n============ 刷新功能页面内容 ============")
        print("重新获取系统信息和检查保存当前账户按钮是否应该显示")
        
        # --- 新增：检查是否需要隐藏自定义路径卡片 ---
        auto_hide = self._get_setting("auto_hide_custom_path", False)
        is_known = False
        # 尝试调用主窗口的方法检查版本状态
        if hasattr(self.window(), 'is_cursor_version_known'):
            is_known = self.window().is_cursor_version_known()
        
        should_hide_custom_path = auto_hide and is_known
        info(f"刷新功能页面: auto_hide={auto_hide}, is_version_known={is_known}, should_hide={should_hide_custom_path}")
        
        if hasattr(self, 'custom_cursor_path_card'):
            self.custom_cursor_path_card.setVisible(not should_hide_custom_path)
            info(f"自定义路径卡片可见性设置为: {not should_hide_custom_path}")
        else:
            info("自定义路径卡片引用 (self.custom_cursor_path_card) 不存在，无法设置可见性")
        # --- 结束新增逻辑 ---

        # 清理 functions_layout 中的所有现有子控件，避免重复显示
        while self.functions_layout.count() > 0:
            item = self.functions_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 如果已经有系统账户框架，则移除它
        if self.system_account_frame:
            self.functions_layout.removeWidget(self.system_account_frame)
            self.system_account_frame.deleteLater()
        
        # 获取系统类型
        system_type = ""
        system = platform.system().lower()
        if "windows" in system:
            system_type = "Windows"
        elif "darwin" in system:
            system_type = "macOS"
        else:
            system_type = "Linux"
        
        # 创建一个统一的系统信息卡片
        info_card = QFrame()
        info_card.setObjectName("systemInfoCard")
        info_card.setStyleSheet(f"""
            #systemInfoCard {{
                background-color: transparent;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        
        # 使用垂直布局安排信息卡片的内容
        info_card_layout = QVBoxLayout(info_card)
        info_card_layout.setContentsMargins(0, 0, 0, 0)
        info_card_layout.setSpacing(15)
        
        # 创建系统类型标题行
        header_frame = QFrame()
        header_frame.setStyleSheet("background-color: transparent;")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # 系统图标和标题
        # 根据系统类型选择不同的图标
        system_icon = "🪟"  # Windows默认图标
        if system_type == "macOS":
            system_icon = "🍎"  # macOS图标
        elif system_type == "Linux":
            system_icon = "🐧"  # Linux图标
        
        os_icon_label = QLabel(system_icon)
        os_icon_label.setStyleSheet("""
            font-size: 24px;
            background-color: transparent;
        """)
        
        system_title = QLabel(f"当前系统类型: {system_type}")
        system_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        
        header_layout.addWidget(os_icon_label)
        header_layout.addWidget(system_title, 1)
        
        # 添加标题行到卡片布局
        info_card_layout.addWidget(header_frame)
        
        # ===== 获取所有需要显示的信息 =====
        # 获取设备标识信息
        telemetry_device_id = ""
        storage_path = ""
        if "windows" in system:
            storage_path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "globalStorage", "storage.json")
        elif "darwin" in system:
            storage_path = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")
        else:
            storage_path = os.path.expanduser("~/.config/Cursor/User/globalStorage/storage.json")
            
        if os.path.exists(storage_path):
            try:
                with open(storage_path, 'r', encoding='utf-8') as f:
                    storage_data = json.load(f)
                    telemetry_device_id = storage_data.get("telemetry.devDeviceId", "未找到")
            except Exception:
                telemetry_device_id = "读取失败"
        else:
            telemetry_device_id = "文件不存在"
        
        # 获取系统特定的机器码
        system_id = ""
        system_id_name = ""
        
        if "windows" in system:
            system_id_name = "MachineGuid"
            try:
                import winreg
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography", 0, winreg.KEY_READ) as key:
                    system_id = winreg.QueryValueEx(key, "MachineGuid")[0]
            except Exception:
                try:
                    output = subprocess.check_output(
                        ['reg', 'query', 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography', '/v', 'MachineGuid'], 
                        universal_newlines=True
                    )
                    for line in output.splitlines():
                        if "MachineGuid" in line:
                            system_id = line.split()[-1]
                except Exception:
                    system_id = "获取失败"
                    
        elif "linux" in system:
            system_id_name = "Machine ID"
            try:
                if os.path.exists("/etc/machine-id"):
                    with open("/etc/machine-id", 'r') as f:
                        system_id = f.read().strip()
            except Exception:
                system_id = "获取失败"
                
        elif "darwin" in system:
            system_id_name = "SystemUUID"
            try:
                nvram_output = subprocess.check_output(
                    ["nvram", "SystemUUID"],
                    universal_newlines=True,
                    stderr=subprocess.DEVNULL
                ).strip()
                
                if '\t' in nvram_output:
                    system_id = nvram_output.split('\t')[-1]
            except Exception:
                system_id = "获取失败"
                
        # 读取自动注册配置
        from utils import get_app_data_dir
        auto_register_settings = {}
        settings_file = os.path.join(get_app_data_dir(), "settings.json")
        
        domain = "未配置"
        email_type = "未配置"
        email_account = "未配置"
        
        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    auto_register_settings = settings
                    
                    # 获取域名
                    domain = settings.get("auto_register_domain", "未配置")
                    if not domain:
                        domain = "未配置"
                    
                    # 获取邮箱类型和账号
                    email_type_code = settings.get("auto_register_email_type", "")
                    
                    if email_type_code == "temp":
                        email_type = "临时邮箱"
                        email_account = settings.get("auto_register_temp_mail", "未配置")
                    elif email_type_code == "imap":
                        email_type = "IMAP邮箱"
                        email_account = settings.get("auto_register_imap_user", "未配置")
                    else:
                        email_type = "未配置"
                        email_account = "未配置"
                        
                    if not email_account:
                        email_account = "未配置"
            except Exception as e:
                print(f"读取自动注册配置时出错: {str(e)}")
        
        # 创建标签和值的共同样式函数
        def create_label(text, is_value=False):
            label = QLabel(text)
            alignment = Qt.AlignmentFlag.AlignLeft if is_value else Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter
            color = Theme.TEXT_PRIMARY if is_value else Theme.TEXT_SECONDARY
            
            label.setStyleSheet(f"""
                font-size: {Theme.FONT_SIZE_SMALL}; 
                color: {color};
                background-color: transparent;
                padding: 2px 0px;
            """)
            
            label.setAlignment(alignment)
            
            if is_value:
                label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
                
            return label
            
        # 创建水平布局容器，放置左右两个信息面板
        info_panels_frame = QFrame()
        info_panels_frame.setStyleSheet("background-color: transparent;")
        info_panels_layout = QHBoxLayout(info_panels_frame)
        info_panels_layout.setContentsMargins(0, 0, 0, 0)
        info_panels_layout.setSpacing(20)  # 左右两个面板之间的间距
        
        # ===== 左侧：系统信息面板 =====
        left_panel = QFrame()
        left_panel.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_1};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        left_layout = QGridLayout(left_panel)
        left_layout.setContentsMargins(20, 15, 20, 15)  # 内边距
        left_layout.setHorizontalSpacing(10)  # 减小水平间距
        left_layout.setVerticalSpacing(12)  # 垂直间距
        
        # 添加 Telemetry DeviceID 行
        left_layout.addWidget(create_label("Telemetry DeviceID:"), 0, 0)
        left_layout.addWidget(create_label(telemetry_device_id, True), 0, 1)
        
        # 添加 系统 MachineGuid 行
        left_layout.addWidget(create_label(f"系统 {system_id_name}:"), 1, 0)
        left_layout.addWidget(create_label(system_id, True), 1, 1)
        
        # 设置列拉伸因子
        left_layout.setColumnStretch(0, 0)  # 标签列不拉伸
        left_layout.setColumnStretch(1, 1)  # 值列拉伸填充剩余空间
        
        # ===== 右侧：自动注册配置面板 =====
        right_panel = QFrame()
        right_panel.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_1};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        right_layout = QGridLayout(right_panel)
        right_layout.setContentsMargins(20, 15, 20, 15)  # 内边距
        right_layout.setHorizontalSpacing(10)  # 减小水平间距
        right_layout.setVerticalSpacing(12)  # 垂直间距
        
        # 自动注册域名截断处理
        domain_display = domain
        domain_tooltip = None
        if isinstance(domain, str) and len(domain) > 34:
            domain_display = domain[:34] + "..."
            domain_tooltip = domain
        domain_label = create_label(domain_display, True)
        if domain_tooltip:
            domain_label.setToolTip(domain_tooltip)
        right_layout.addWidget(create_label("自动注册域名:"), 0, 0)
        right_layout.addWidget(domain_label, 0, 1)
        
        # 收件邮箱类型截断处理
        email_display = f"{email_type}（{email_account}）"
        email_tooltip = None
        if isinstance(email_display, str) and len(email_display) > 22:
            email_tooltip = email_display
            email_display = email_display[:22] + "..."
        email_label = create_label(email_display, True)
        if email_tooltip:
            email_label.setToolTip(email_tooltip)
        right_layout.addWidget(create_label("收件邮箱类型:"), 1, 0)
        right_layout.addWidget(email_label, 1, 1)
        
        # 设置列拉伸因子
        right_layout.setColumnStretch(0, 0)  # 标签列不拉伸
        right_layout.setColumnStretch(1, 1)  # 值列拉伸填充剩余空间
        
        # 添加左右两个面板到水平布局容器
        info_panels_layout.addWidget(left_panel, 1)  # 左侧面板
        info_panels_layout.addWidget(right_panel, 1)  # 右侧面板
        
        # 将水平布局容器添加到卡片布局
        info_card_layout.addWidget(info_panels_frame)
        
        # 将完整的信息卡片添加到functions_layout
        self.functions_layout.addWidget(info_card)
    
    def _create_function_blocks(self):
        """创建差异化的功能模块"""
        
        # 创建自动注册功能卡片
        auto_register_card = QFrame()
        auto_register_card.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        # 使用垂直布局包含顶部内容和底部开关
        auto_register_card_layout = QVBoxLayout(auto_register_card)
        auto_register_card_layout.setContentsMargins(0, 0, 0, 0)
        auto_register_card_layout.setSpacing(0)
        
        # 顶部内容区域 - 标题、描述和按钮
        auto_register_top_frame = QFrame()
        auto_register_top_frame.setStyleSheet("background: transparent;")
        
        # 使用水平布局
        auto_register_layout = QHBoxLayout(auto_register_top_frame)
        auto_register_layout.setContentsMargins(20, 20, 20, 20)
        auto_register_layout.setSpacing(15)
        
        # 添加图标
        auto_register_icon_label = QLabel("🤖")
        auto_register_icon_label.setStyleSheet("""
            font-size: 24px;
            background-color: transparent;
        """)
        auto_register_layout.addWidget(auto_register_icon_label)
        
        # 添加标题和说明文本
        auto_register_text_container = QFrame()
        auto_register_text_container.setStyleSheet("background: transparent;")
        auto_register_text_layout = QVBoxLayout(auto_register_text_container)
        auto_register_text_layout.setContentsMargins(0, 0, 0, 0)
        auto_register_text_layout.setSpacing(4)
        
        auto_register_title_label = QLabel("自动注册Cursor账号")
        auto_register_title_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        
        auto_register_desc_label = QLabel("自动完成注册 Cursor 和验证替换并保存账号全流程")
        auto_register_desc_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
        auto_register_desc_label.setWordWrap(True)
        
        auto_register_text_layout.addWidget(auto_register_title_label)
        auto_register_text_layout.addWidget(auto_register_desc_label)
        
        auto_register_layout.addWidget(auto_register_text_container, 1)
        
        # 添加按钮
        auto_register_button = QPushButton("自动注册")
        auto_register_button.setCursor(Qt.CursorShape.PointingHandCursor)
        auto_register_button.setMinimumHeight(40)
        auto_register_button.setMaximumWidth(120)
        auto_register_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 0px 16px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        auto_register_button.clicked.connect(lambda: self._on_function_selected("auto_register"))
        
        auto_register_layout.addWidget(auto_register_button)
        
        # 添加顶部内容到卡片布局
        auto_register_card_layout.addWidget(auto_register_top_frame)
        
        # 添加分隔线
        register_divider = QFrame()
        register_divider.setFrameShape(QFrame.Shape.HLine)
        register_divider.setStyleSheet(f"background-color: {Theme.BORDER}; margin: 0px 20px;")
        register_divider.setMaximumHeight(1)
        auto_register_card_layout.addWidget(register_divider)
        
        # 底部区域 - 仅注册开关和方案选择
        auto_register_bottom_frame = QFrame()
        auto_register_bottom_frame.setStyleSheet("background: transparent;")
        auto_register_bottom_layout = QVBoxLayout(auto_register_bottom_frame)
        auto_register_bottom_layout.setContentsMargins(20, 15, 20, 15)
        auto_register_bottom_layout.setSpacing(10)  # 增加间距，使布局更清晰
        
        # "仅注册"开关行
        register_only_row = QHBoxLayout()
        
        # 添加仅注册文本说明
        register_only_label = QLabel("仅注册模式")
        register_only_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        register_only_row.addWidget(register_only_label)
        
        register_only_desc = QLabel("启用后将只注册账号，不会更新应用认证信息")
        register_only_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
        register_only_row.addWidget(register_only_desc, 1)
        
        # 从配置文件加载仅注册模式状态
        register_only_enabled = self._get_register_only_setting()
        
        # 添加开关按钮
        register_only_switch = StyledSwitch()
        register_only_switch.setChecked(register_only_enabled)
        register_only_switch.stateChanged.connect(self._update_register_only_setting)
        register_only_row.addWidget(register_only_switch)
        
        # 添加"仅注册"行到底部布局
        auto_register_bottom_layout.addLayout(register_only_row)
        
        # 添加分隔线
        register_method_divider = QFrame()
        register_method_divider.setFrameShape(QFrame.Shape.HLine)
        register_method_divider.setStyleSheet(f"background-color: {Theme.BORDER}; margin: 5px 0px;")
        register_method_divider.setMaximumHeight(1)
        auto_register_bottom_layout.addWidget(register_method_divider)
        
        # 方案选择行
        register_method_row = QHBoxLayout()
        register_method_row.setSpacing(15)
        
        # 方案选择标题
        register_method_label = QLabel("注册方案")
        register_method_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        register_method_row.addWidget(register_method_label)
        
        # 方案选择容器 - 单选按钮组
        method_options_container = QFrame()
        method_options_container.setObjectName("methodOptionsContainer")
        method_options_container.setStyleSheet(f"""
            #methodOptionsContainer {{
                background: transparent;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 0px;
            }}
        """)
        method_options_container.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        method_options_layout = QHBoxLayout(method_options_container)
        method_options_layout.setContentsMargins(0, 0, 0, 0)
        method_options_layout.setSpacing(4)  # 减小按钮之间的间距
        
        # 从配置文件加载注册方案设置
        register_method = self._get_register_method_setting()
        
        # 创建方案按钮组
        self.method_buttons = []
        
        # 方案一单选按钮（快速）- 带推荐标签
        method_one_container = QFrame()
        method_one_container.setStyleSheet("background: transparent;")
        method_one_layout = QVBoxLayout(method_one_container)
        method_one_layout.setContentsMargins(0, 0, 0, 0)
        method_one_layout.setSpacing(0)
        
        method_one_radio = QPushButton("快速 更节省时间")
        method_one_radio.setObjectName("methodOneBtn")
        method_one_radio.setCheckable(True)
        method_one_radio.setChecked(register_method == 1)
        method_one_radio.setFixedHeight(32)  # 固定高度
        method_one_radio.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)  # 设置为可扩展
        method_one_radio.setCursor(Qt.CursorShape.PointingHandCursor)  # 设置鼠标指针样式
        method_one_radio.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_SECONDARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px 15px;
                font-size: {Theme.FONT_SIZE_SMALL};
                text-align: center;
            }}
            QPushButton:checked {{
                background-color: {Theme.ACCENT};
                color: white;
                border: 2px solid {Theme.CARD_LEVEL_3};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 4px 14px;
                font-size: {Theme.FONT_SIZE_SMALL};
                text-align: center;
            }}
            QPushButton:hover:!checked {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
                background-color: rgba(36, 168, 132, 0.08);
            }}
            QPushButton:pressed {{
                background-color: rgba(36, 168, 132, 0.15);
            }}
        """)
        self.method_buttons.append(method_one_radio)
        
        # 创建堆叠布局来实现标签悬浮效果
        method_one_stack = QWidget()
        method_one_stack.setStyleSheet("background: transparent;")
        method_one_stack_layout = QVBoxLayout(method_one_stack)
        method_one_stack_layout.setContentsMargins(0, 0, 0, 0)
        method_one_stack_layout.setSpacing(0)
        method_one_stack_layout.addWidget(method_one_radio)
        
        # 创建推荐标签并添加到按钮上
        recommend_label = QLabel("推荐", method_one_stack)
        recommend_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        recommend_label.setFixedSize(28, 14)  # 减小标签尺寸
        recommend_label.setStyleSheet(f"""
            background-color: #6a5acd;  /* 紫色背景 */
            color: white;  /* 白色文字 */
            border-radius: 7px;  /* 增加圆角，接近椭圆形 */
            padding: 0px 2px;
            font-size: 9px;  /* 减小字体 */
            font-weight: bold;
            border: none;
        """)
        
        # 重写resizeEvent方法来精确定位标签
        def resizeEvent(widget, event):
            # 调用原始的resizeEvent
            QWidget.resizeEvent(widget, event)
            # 定位标签到右上角
            button_width = method_one_radio.width()
            # 将标签放在右上角
            recommend_label.move(button_width - 30, -2)  
        
        # 设置自定义的resizeEvent处理函数
        method_one_stack.resizeEvent = lambda event: resizeEvent(method_one_stack, event)
        
        # 确保标签在初始时也能正确定位
        QTimer.singleShot(100, lambda: recommend_label.move(method_one_radio.width() - 30, -2))
        recommend_label.raise_()
        
        # 添加到容器
        method_one_layout.addWidget(method_one_stack)
        
        # 方案二单选按钮
        method_two_radio = QPushButton("完整 时间偏久")
        method_two_radio.setObjectName("methodTwoBtn")
        method_two_radio.setCheckable(True)
        method_two_radio.setChecked(register_method == 2)
        method_two_radio.setFixedHeight(32)  # 固定高度
        method_two_radio.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)  # 设置为可扩展
        method_two_radio.setCursor(Qt.CursorShape.PointingHandCursor)  # 设置鼠标指针样式
        method_two_radio.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_SECONDARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px 15px;
                font-size: {Theme.FONT_SIZE_SMALL};
                text-align: center;
            }}
            QPushButton:checked {{
                background-color: {Theme.ACCENT};
                color: white;
                border: 2px solid {Theme.CARD_LEVEL_3};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 4px 14px;
                font-size: {Theme.FONT_SIZE_SMALL};
                text-align: center;
            }}
            QPushButton:hover:!checked {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
                background-color: rgba(36, 168, 132, 0.08);
            }}
            QPushButton:pressed {{
                background-color: rgba(36, 168, 132, 0.15);
            }}
        """)
        self.method_buttons.append(method_two_radio)
        
        # 连接按钮信号到处理函数
        method_one_radio.clicked.connect(lambda: self._handle_method_selection(1))
        method_two_radio.clicked.connect(lambda: self._handle_method_selection(2))
        
        # 将按钮添加到布局中
        method_options_layout.addWidget(method_one_container, 1)  # 使用1的拉伸因子
        method_options_layout.addWidget(method_two_radio, 1)  # 使用1的拉伸因子
        
        register_method_row.addWidget(method_options_container, 1)
        
        # 添加方案选择行到底部布局
        auto_register_bottom_layout.addLayout(register_method_row)
        
        # 添加底部区域到卡片布局
        auto_register_card_layout.addWidget(auto_register_bottom_frame)
        
        # 创建重置机器码功能卡片
        reset_machine_id_card = QFrame()
        reset_machine_id_card.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        reset_machine_id_card.setMinimumWidth(280)  # 确保卡片足够宽显示所有内容
        
        # 使用垂直布局
        reset_card_layout = QVBoxLayout(reset_machine_id_card)
        reset_card_layout.setContentsMargins(20, 15, 20, 15)
        reset_card_layout.setSpacing(5)
        
        # 创建一个中央容器
        center_container = QWidget()
        center_container.setStyleSheet("background: transparent;")
        center_layout = QVBoxLayout(center_container)
        center_layout.setContentsMargins(0, 5, 0, 5)
        center_layout.setSpacing(10)
        center_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 创建图标容器 - 使用QLabel与图标文本
        icon_container = QLabel("🔑")
        icon_container.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_container.setFixedSize(50, 50)  # 固定大小确保圆形效果一致
        
        # 使用内部边距调整emoji垂直位置
        icon_container.setStyleSheet(f"""
            background-color: rgba(36, 168, 132, 0.15);
            border-radius: 25px;
            font-size: 22px;
            color: #000000; /* 确保图标颜色 */
            padding-bottom: 5px; /* 底部padding偏移emoji位置 */
            margin: 0px;
            qproperty-alignment: AlignCenter;
        """)
        
        # 添加图标容器到中央容器 - 使用额外的容器确保水平居中
        icon_wrapper = QWidget()
        icon_wrapper.setStyleSheet("background: transparent;")
        icon_wrapper_layout = QHBoxLayout(icon_wrapper)
        icon_wrapper_layout.setContentsMargins(0, 0, 0, 0)
        icon_wrapper_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_wrapper_layout.addWidget(icon_container)
        
        center_layout.addWidget(icon_wrapper, 0, Qt.AlignmentFlag.AlignCenter)
        
        # 标题容器
        title_container = QFrame()
        title_container.setStyleSheet("background: transparent;")
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 5, 0, 0)
        title_layout.setSpacing(8)  # 增加标题和描述之间的间距
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 标题
        title_label = QLabel("重置Cursor机器码")
        title_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
            padding-bottom: 2px;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title_label)
        
        # 描述
        desc_label = QLabel("欺骗Cursor为新设备解除设备限制")
        desc_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
            padding-bottom: 2px;
        """)
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setWordWrap(False)  # 禁用自动换行
        desc_label.setMinimumWidth(200)  # 确保标签足够宽以容纳文本
        desc_label.setMinimumHeight(25)  # 减小高度，适应单行文本
        title_layout.addWidget(desc_label)
        
        # 设置标题容器的最小高度确保文本完全显示
        title_container.setMinimumHeight(65)
        title_container.setMinimumWidth(250)  # 确保容器足够宽以容纳完整文字
        title_container.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        
        # 将标题容器添加到中央容器
        center_layout.addWidget(title_container)
        
        # 添加中央容器到主布局
        reset_card_layout.addWidget(center_container, 1)
        
        # 重置按钮
        reset_button = StyledButton("重置机器码")
        reset_button.setFixedHeight(36)
        reset_button.setMinimumWidth(120)
        reset_button.clicked.connect(lambda: self._on_function_selected("reset_machine_id"))
        reset_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px; /* 使用 StyledButton 的 padding */
                font-size: {Theme.FONT_SIZE_NORMAL};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
            QPushButton:disabled {{
                background-color: {Theme.DISABLED};
                color: {Theme.TEXT_SECONDARY};
            }}
        """)
        
        # 添加按钮到主布局
        reset_card_layout.addWidget(reset_button, 0, Qt.AlignmentFlag.AlignCenter)
        
        # 将功能卡片添加到布局中
        # 创建水平布局，将自动注册和重置机器码放在同一行
        functions_row_layout = QHBoxLayout()
        functions_row_layout.setContentsMargins(0, 0, 0, 0)
        functions_row_layout.setSpacing(20)  # 设置卡片之间的间距
        
        # 将功能卡片添加到水平布局中，调整顺序和比例
        functions_row_layout.addWidget(reset_machine_id_card, 1)  # 左侧卡片占比更小
        functions_row_layout.addWidget(auto_register_card, 2)    # 右侧卡片占比更大，2:1的比例
        
        # 将水平布局添加到主布局
        self.feature_layout.addLayout(functions_row_layout)
        
        # ====== 创建自定义 Cursor 路径功能卡片 ======
        custom_cursor_path_card = QFrame()
        custom_cursor_path_card.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        custom_cursor_path_layout = QVBoxLayout(custom_cursor_path_card)
        custom_cursor_path_layout.setContentsMargins(20, 20, 20, 20)
        custom_cursor_path_layout.setSpacing(15)
        
        # --- 创建顶部行布局 (QHBoxLayout) ---
        top_row_layout = QHBoxLayout()
        top_row_layout.setContentsMargins(0, 0, 0, 0) # 顶部行不需要额外边距
        top_row_layout.setSpacing(10)
        
        # --- 创建左侧文本容器布局 (QVBoxLayout) ---
        text_container_layout = QVBoxLayout()
        text_container_layout.setContentsMargins(0, 0, 0, 0)
        text_container_layout.setSpacing(5) # 标题和描述之间的间距
        
        # 卡片标题 (添加到文本容器)
        custom_cursor_path_title_layout = QHBoxLayout()
        custom_cursor_path_title_layout.setContentsMargins(0, 0, 0, 0) # 标题行内部不需要额外边距
        custom_cursor_path_title_layout.setSpacing(10)
        custom_cursor_path_icon = QLabel("🔗")
        custom_cursor_path_icon.setStyleSheet("font-size: 24px; background-color: transparent;")
        custom_cursor_path_title_layout.addWidget(custom_cursor_path_icon)
        custom_cursor_path_title = QLabel("自定义 Cursor 路径")
        custom_cursor_path_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        custom_cursor_path_title_layout.addWidget(custom_cursor_path_title)
        custom_cursor_path_title_layout.addStretch() # 标题行内部拉伸（如果需要）
        text_container_layout.addLayout(custom_cursor_path_title_layout)

        # 卡片描述 (添加到文本容器)
        custom_cursor_path_desc = QLabel("选择您的 Cursor 程序位置，系统将自动帮您创建系统链接")
        custom_cursor_path_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
            padding-bottom: 0px; /* 移除底部padding，间距由text_container_layout控制 */
        """)
        custom_cursor_path_desc.setWordWrap(True)
        text_container_layout.addWidget(custom_cursor_path_desc)
        
        # --- 将文本容器添加到顶部行布局 ---
        top_row_layout.addLayout(text_container_layout, 1) # 让文本容器占据可用空间
        
        # --- 添加弹性空间和开关到顶部行布局 ---
        top_row_layout.addStretch() # 推开开关到最右侧
        self.custom_path_switch = StyledSwitch()
        top_row_layout.addWidget(self.custom_path_switch)
        
        # --- 将顶部行添加到主卡片布局 ---
        custom_cursor_path_layout.addLayout(top_row_layout)

        # 路径选择行 (只包含输入框和按钮)
        path_select_layout = QHBoxLayout()
        path_select_layout.setSpacing(10)
        
        self.custom_path_display = StyledLineEdit() # 使用自定义的LineEdit
        self.custom_path_display.setPlaceholderText("请选择 Cursor 程序路径")
        self.custom_path_display.setReadOnly(True)
        self.custom_path_display.setFixedHeight(36) # 直接设置固定高度
        # 应用与代理输入框相同的样式 (移除 height 属性)
        self.custom_path_display.setStyleSheet(f"""
            QLineEdit {{
                background-color: {Theme.CARD_LEVEL_3};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px 10px;
                /* height: 36px; */ /* 通过 setFixedHeight 控制 */
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
            }}
        """)
        path_select_layout.addWidget(self.custom_path_display, 1) # 让输入框占据更多空间
                
        self.select_custom_path_btn = StyledButton("选择路径")
        self.select_custom_path_btn.setFixedHeight(36)
        self.select_custom_path_btn.setFixedWidth(100)
        path_select_layout.addWidget(self.select_custom_path_btn)
        
        custom_cursor_path_layout.addLayout(path_select_layout)
        
        # 状态提示标签
        self.custom_path_status_label = QLabel("")
        self.custom_path_status_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
            padding-top: 5px;
        """)
        self.custom_path_status_label.setWordWrap(True)
        self.custom_path_status_label.hide() # 初始隐藏
        custom_cursor_path_layout.addWidget(self.custom_path_status_label)
        
        # 连接信号
        self.select_custom_path_btn.clicked.connect(self._on_select_custom_cursor_path)
        self.custom_path_switch.stateChanged.connect(self._on_custom_path_switch_changed)
        
        # 暂时保存卡片，但不添加到布局中（留到快捷一键卡片添加后再添加）
        # 将卡片赋值给实例变量
        self.custom_cursor_path_card = custom_cursor_path_card
        
        # 初始化自定义路径 UI 状态
        self._refresh_custom_cursor_path_ui() # 调用刷新方法
        
        # ====== 创建快捷一键功能卡片 ======
        quick_function_card = QFrame()
        quick_function_card.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        # 使用垂直布局
        quick_function_layout = QVBoxLayout(quick_function_card)
        quick_function_layout.setContentsMargins(20, 20, 20, 20)
        quick_function_layout.setSpacing(15)
        
        # 顶部区域 - 标题、描述和按钮
        quick_function_header = QHBoxLayout()
        quick_function_header.setContentsMargins(0, 0, 0, 0)
        quick_function_header.setSpacing(15)
        
        # 图标
        quick_function_icon = QLabel("⚡")
        quick_function_icon.setStyleSheet("font-size: 24px; background-color: transparent;")
        quick_function_header.addWidget(quick_function_icon)
        
        # 标题和描述
        quick_function_text = QFrame()
        quick_function_text.setStyleSheet("background: transparent;")
        quick_function_text_layout = QVBoxLayout(quick_function_text)
        quick_function_text_layout.setContentsMargins(0, 0, 0, 0)
        quick_function_text_layout.setSpacing(4)
        
        quick_function_title = QLabel("快捷一键")
        quick_function_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        
        quick_function_desc = QLabel("一键执行多个功能操作，快速续杯")
        quick_function_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
        quick_function_desc.setWordWrap(True)
        
        quick_function_text_layout.addWidget(quick_function_title)
        quick_function_text_layout.addWidget(quick_function_desc)
        
        quick_function_header.addWidget(quick_function_text, 1)
        
        # 按钮
        quick_function_button = QPushButton("快捷一键")
        quick_function_button.setCursor(Qt.CursorShape.PointingHandCursor)
        quick_function_button.setFixedHeight(36)
        quick_function_button.setFixedWidth(100)
        quick_function_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_SMALL};
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        quick_function_button.clicked.connect(self._on_quick_function)
        
        quick_function_header.addWidget(quick_function_button)
        quick_function_layout.addLayout(quick_function_header)
        
        # 先添加快捷一键卡片到功能布局中
        self.feature_layout.addWidget(quick_function_card)
        self.quick_function_card = quick_function_card
        
        # 再添加自定义Cursor路径卡片，这样快捷一键会显示在上面
        self.feature_layout.addWidget(self.custom_cursor_path_card)
        
        # ====== 创建Cursor设置功能卡片 ======
        cursor_settings_card = QFrame()
        cursor_settings_card.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        # 使用垂直布局
        cursor_settings_layout = QVBoxLayout(cursor_settings_card)
        cursor_settings_layout.setContentsMargins(20, 20, 20, 20)
        cursor_settings_layout.setSpacing(15)
        
        # 添加卡片标题
        cursor_settings_title_layout = QHBoxLayout()
        cursor_settings_title_layout.setContentsMargins(0, 0, 0, 10)
        cursor_settings_title_layout.setSpacing(10)
        
        cursor_settings_icon = QLabel("⚙️")
        cursor_settings_icon.setStyleSheet("font-size: 24px; background-color: transparent;")
        cursor_settings_title_layout.addWidget(cursor_settings_icon)
        
        cursor_settings_title = QLabel("Cursor 设置")
        cursor_settings_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        cursor_settings_title_layout.addWidget(cursor_settings_title)
        cursor_settings_title_layout.addStretch()
        
        # 将标题布局保存为类变量，方便后续刷新时访问
        self.cursor_settings_title_layout = cursor_settings_title_layout
        
        # 创建保存当前登录账户按钮，但暂不添加到布局中
        self.save_account_button = QPushButton("保存当前登录账户")
        self.save_account_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.save_account_button.setMinimumHeight(36)
        self.save_account_button.setFixedWidth(150)
        self.save_account_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 0px 8px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_SMALL};
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        self.save_account_button.clicked.connect(lambda: self._on_function_selected("save_current_account"))
        
        # 初始检查是否应该显示按钮
        self._update_save_button_visibility()
        
        cursor_settings_layout.addLayout(cursor_settings_title_layout)
        
        # 添加分隔线
        cursor_settings_divider = QFrame()
        cursor_settings_divider.setFrameShape(QFrame.Shape.HLine)
        cursor_settings_divider.setStyleSheet(f"background-color: {Theme.BORDER}; margin: 0px;")
        cursor_settings_divider.setMaximumHeight(1)
        cursor_settings_layout.addWidget(cursor_settings_divider)
        
        # ===== 添加代理设置 =====
        proxy_frame = QFrame()
        proxy_frame.setStyleSheet("background-color: transparent;")
        proxy_layout = QVBoxLayout(proxy_frame)
        proxy_layout.setContentsMargins(0, 10, 0, 10)
        proxy_layout.setSpacing(5)  # 减小间距
        
        # 顶部区域 - 标题和描述
        proxy_info_layout = QVBoxLayout()
        proxy_info_layout.setSpacing(3)  # 减小标题和描述间距
        
        # 添加标题
        proxy_title = QLabel("Cursor 代理设置")
        proxy_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        proxy_info_layout.addWidget(proxy_title)
        
        # 添加描述
        proxy_desc = QLabel("解决 地区限制 方法之一，配置了可不用开Tun模式，详情请看文档")
        proxy_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
        proxy_desc.setWordWrap(True)
        proxy_info_layout.addWidget(proxy_desc)
        
        # 创建水平布局来放置标题、描述和输入框
        proxy_input_layout = QHBoxLayout()
        proxy_input_layout.setContentsMargins(0, 5, 0, 0)  # 顶部添加5px间距
        proxy_input_layout.setSpacing(10)
        
        # 将标题和描述添加到左侧
        proxy_input_layout.addLayout(proxy_info_layout, 1)
        
        # 添加输入框到右侧
        self.cursor_proxy_input = StyledLineEdit()
        self.cursor_proxy_input.setPlaceholderText("socks5://127.0.0.1:7897")
        self.cursor_proxy_input.setText(self._get_proxy_setting())
        self.cursor_proxy_input.editingFinished.connect(self._on_proxy_changed)
        self.cursor_proxy_input.setFixedWidth(290)  # *** 减小宽度 ***
        self.cursor_proxy_input.setFixedHeight(36) # 设置固定高度
        # 恢复边框和圆角为标准QLineEdit样式，减小右侧padding
        self.cursor_proxy_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {Theme.CARD_LEVEL_3};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL}; /* 四个角都用标准圆角 */
                padding: 5px 20px 5px 10px; /* *** 减小右侧padding *** */
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT}; /* 焦点时使用标准Accent颜色 */
            }}
            /* 为嵌入的Action按钮（QToolButton）设置样式 */
            QLineEdit QToolButton {{
                background-color: transparent;
                border: none;
                padding: 0px;
                margin: 0px;
                border-radius: {Theme.BORDER_RADIUS_SMALL}; /* 可选：给按钮一点圆角 */
                color: {Theme.TEXT_SECONDARY};
                font-size: 12px; /* 确保字体大小合适显示字符 */
            }}
            QLineEdit QToolButton:hover {{
                color: {Theme.ACCENT};
                background-color: {Theme.CARD_LEVEL_2};
            }}
            QLineEdit QToolButton:pressed {{
                color: {Theme.ACCENT};
                background-color: {Theme.CARD_LEVEL_1};
            }}
        """)
        proxy_input_layout.addWidget(self.cursor_proxy_input)
        # --- 修复：初始化正则对象和气泡控件，并连接信号 ---
        self._proxy_regex = re.compile(r'^(https?|socks|socks4a?|socks5h?)://([^:]*[^@]*@)?([^:]+)(\:\d+)?/?$')
        self.proxy_bubble_tip = BubbleTip(self, "", target_widget=self.cursor_proxy_input)
        self.cursor_proxy_input.textChanged.connect(self._on_proxy_input_changed)
        
        # --- 将历史按钮嵌入输入框 --- 
        self.proxy_history_action = QAction(self) # 使用 self 作为父对象
        # self.proxy_history_action.setText("▼") # *** 移除 setText ***

        # --- 动态生成字符图标 --- 
        pixmap_size = 16 # 图标尺寸
        pixmap = QPixmap(pixmap_size, pixmap_size)
        pixmap.fill(Qt.GlobalColor.transparent) # 透明背景
        
        painter = QPainter(pixmap)
        painter.setPen(QColor(Theme.TEXT_SECONDARY)) # 设置字符颜色
        # 可以取消注释并调整字体大小
        # font = painter.font()
        # font.setPointSize(10) 
        # painter.setFont(font)
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "▼") # 绘制居中字符
        painter.end()
        
        dropdown_icon = QIcon(pixmap)
        self.proxy_history_action.setIcon(dropdown_icon) # *** 设置绘制的图标 ***
        # --- 结束动态生成图标 --- 

        self.proxy_history_action.setToolTip("查看历史记录")
        self.proxy_history_action.triggered.connect(self._show_proxy_history_menu)
        self.cursor_proxy_input.addAction(self.proxy_history_action, QLineEdit.ActionPosition.TrailingPosition)
        
        proxy_layout.addLayout(proxy_input_layout)
        cursor_settings_layout.addWidget(proxy_frame)
        
        # 添加分隔线
        auto_update_divider = QFrame()
        auto_update_divider.setFrameShape(QFrame.Shape.HLine)
        auto_update_divider.setStyleSheet(f"background-color: {Theme.BORDER}; margin: 0px;")
        auto_update_divider.setMaximumHeight(1)
        cursor_settings_layout.addWidget(auto_update_divider)
        
        # ===== 添加自动更新开关 =====
        auto_update_frame = QFrame()
        auto_update_frame.setStyleSheet("background-color: transparent;")
        auto_update_layout = QHBoxLayout(auto_update_frame)
        auto_update_layout.setContentsMargins(0, 10, 0, 10)
        auto_update_layout.setSpacing(10)
        
        # 添加左侧标题和描述
        auto_update_text_frame = QFrame()
        auto_update_text_frame.setStyleSheet("background-color: transparent;")
        auto_update_text_layout = QVBoxLayout(auto_update_text_frame)
        auto_update_text_layout.setContentsMargins(0, 0, 0, 0)
        auto_update_text_layout.setSpacing(3)  # 减小间距
        
        auto_update_title = QLabel("关闭自动更新")
        auto_update_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        auto_update_text_layout.addWidget(auto_update_title)
        
        auto_update_desc = QLabel("开启后将关闭 Cursor 的自动更新功能")
        auto_update_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
        auto_update_desc.setWordWrap(True)
        auto_update_text_layout.addWidget(auto_update_desc)
        
        auto_update_layout.addWidget(auto_update_text_frame, 1)
        
        # 添加开关
        self.cursor_auto_update_switch = StyledSwitch()
        self.cursor_auto_update_switch.setChecked(self._get_auto_update_setting())
        self.cursor_auto_update_switch.stateChanged.connect(self._on_auto_update_switch)
        auto_update_layout.addWidget(self.cursor_auto_update_switch)
        
        cursor_settings_layout.addWidget(auto_update_frame)
        
        # 添加分隔线
        http2_divider = QFrame()
        http2_divider.setFrameShape(QFrame.Shape.HLine)
        http2_divider.setStyleSheet(f"background-color: {Theme.BORDER}; margin: 0px;")
        http2_divider.setMaximumHeight(1)
        cursor_settings_layout.addWidget(http2_divider)
        
        # ===== 添加禁用HTTP2开关 =====
        http2_frame = QFrame()
        http2_frame.setStyleSheet("background-color: transparent;")
        http2_layout = QHBoxLayout(http2_frame)
        http2_layout.setContentsMargins(0, 10, 0, 10)
        http2_layout.setSpacing(10)
        
        # 添加左侧标题和描述
        http2_text_frame = QFrame()
        http2_text_frame.setStyleSheet("background-color: transparent;")
        http2_text_layout = QVBoxLayout(http2_text_frame)
        http2_text_layout.setContentsMargins(0, 0, 0, 0)
        http2_text_layout.setSpacing(3)  # 减小间距
        
        http2_title = QLabel("禁用HTTP2")
        http2_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        http2_text_layout.addWidget(http2_title)
        
        http2_desc = QLabel("开启后将禁用 Cursor 的HTTP2协议支持，频繁出现提示VPN和mode等网络情况可以禁用或启用一下")
        http2_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
        http2_desc.setWordWrap(True)
        http2_text_layout.addWidget(http2_desc)
        
        http2_layout.addWidget(http2_text_frame, 1)
        
        # 添加开关
        self.cursor_disable_http2_switch = StyledSwitch()
        self.cursor_disable_http2_switch.setChecked(self._get_disable_http2_setting())
        self.cursor_disable_http2_switch.stateChanged.connect(self._on_disable_http2_switch)
        http2_layout.addWidget(self.cursor_disable_http2_switch)
        
        cursor_settings_layout.addWidget(http2_frame)
        
        # 添加分隔线
        unlimited_max_divider = QFrame()
        unlimited_max_divider.setFrameShape(QFrame.Shape.HLine)
        unlimited_max_divider.setStyleSheet(f"background-color: {Theme.BORDER}; margin: 0px;")
        unlimited_max_divider.setMaximumHeight(1)
        cursor_settings_layout.addWidget(unlimited_max_divider)
        
        # ===== 添加开启无限MAX开关 =====
        unlimited_max_frame = QFrame()
        unlimited_max_frame.setStyleSheet("background-color: transparent;")
        unlimited_max_layout = QHBoxLayout(unlimited_max_frame)
        unlimited_max_layout.setContentsMargins(0, 10, 0, 10)
        unlimited_max_layout.setSpacing(10)
        
        # 添加左侧标题和描述
        unlimited_max_text_frame = QFrame()
        unlimited_max_text_frame.setStyleSheet("background-color: transparent;")
        unlimited_max_text_layout = QVBoxLayout(unlimited_max_text_frame)
        unlimited_max_text_layout.setContentsMargins(0, 0, 0, 0)
        unlimited_max_text_layout.setSpacing(3)  # 减小间距
        
        unlimited_max_title = QLabel("无限 MAX 模型")
        unlimited_max_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        unlimited_max_text_layout.addWidget(unlimited_max_title)
        
        unlimited_max_desc = QLabel("修改JS达到自我安慰使用无限MAX效果 | 推荐0.49.6最完美，只支持0.49/0.50，往上下版本别开 | 提示损坏不用理会不影响")
        unlimited_max_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
        unlimited_max_desc.setWordWrap(True)
        unlimited_max_text_layout.addWidget(unlimited_max_desc)
        
        unlimited_max_layout.addWidget(unlimited_max_text_frame, 1)
        
        # 添加开关
        self.cursor_unlimited_max_switch = StyledSwitch()
        self.cursor_unlimited_max_switch.setChecked(self._has_workbench_backup())  # 如果有备份文件，表示已开启
        self.cursor_unlimited_max_switch.stateChanged.connect(self._on_unlimited_max_switch)
        unlimited_max_layout.addWidget(self.cursor_unlimited_max_switch)
        
        cursor_settings_layout.addWidget(unlimited_max_frame)
        
        # ===== 移动到此处: 实例化高级备份恢复板块 ======
        self.advanced_backup_section = AdvancedBackupSection()
        self.advanced_backup_section.jump_requested.connect(self._on_advanced_backup_jump)
        
        # 添加分隔线 (复制 http2_divider 的样式)
        advanced_backup_divider = QFrame()
        advanced_backup_divider.setFrameShape(QFrame.Shape.HLine)
        advanced_backup_divider.setStyleSheet(f"background-color: {Theme.BORDER}; margin: 0px;")
        advanced_backup_divider.setMaximumHeight(1)
        cursor_settings_layout.addWidget(advanced_backup_divider)

        # 添加高级自定义备份/恢复板块
        cursor_settings_layout.addWidget(self.advanced_backup_section)
        
        # 添加分隔线
        backup_divider = QFrame()
        backup_divider.setFrameShape(QFrame.Shape.HLine)
        backup_divider.setStyleSheet(f"background-color: {Theme.BORDER}; margin: 0px;")
        backup_divider.setMaximumHeight(1)
        cursor_settings_layout.addWidget(backup_divider)
        
        # ===== 添加备份恢复设置 =====
        backup_settings_frame = QFrame()
        backup_settings_frame.setStyleSheet("background-color: transparent;")
        backup_settings_layout = QHBoxLayout(backup_settings_frame)  # 改为水平布局
        backup_settings_layout.setContentsMargins(0, 10, 0, 10)
        backup_settings_layout.setSpacing(10)
        
        # 左侧标题和描述
        backup_settings_text_frame = QFrame()
        backup_settings_text_frame.setStyleSheet("background-color: transparent;")
        backup_settings_text_layout = QVBoxLayout(backup_settings_text_frame)
        backup_settings_text_layout.setContentsMargins(0, 0, 0, 0)
        backup_settings_text_layout.setSpacing(3)  # 减小间距
        
        # 添加标题
        backup_settings_title = QLabel("备份/恢复 Cursor 设置配置")
        backup_settings_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        backup_settings_text_layout.addWidget(backup_settings_title)
        
        # 添加描述
        backup_settings_desc = QLabel("备份或恢复 Cursor 的设置配置文件、工作区、编辑器、提示词等设置配置")
        backup_settings_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
        backup_settings_desc.setWordWrap(True)
        backup_settings_text_layout.addWidget(backup_settings_desc)
        
        backup_settings_layout.addWidget(backup_settings_text_frame, 1)
        
        # 右侧按钮
        backup_settings_btn_frame = QFrame()
        backup_settings_btn_frame.setStyleSheet("background-color: transparent;")
        backup_settings_btn_layout = QHBoxLayout(backup_settings_btn_frame)
        backup_settings_btn_layout.setContentsMargins(0, 0, 0, 0)
        backup_settings_btn_layout.setSpacing(10)
        
        # 备份按钮 - 设置配置功能组使用绿色
        backup_settings_btn = QPushButton("备份")
        backup_settings_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        backup_settings_btn.setFixedSize(100, 36)
        backup_settings_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.SUCCESS};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #34B892;
            }}
            QPushButton:pressed {{
                background-color: #24856A;
            }}
        """)
        backup_settings_btn.clicked.connect(self._on_backup_settings)
        backup_settings_btn_layout.addWidget(backup_settings_btn)
        
        # 恢复按钮 - 使用蓝色
        self.cursor_restore_settings_btn = QPushButton("恢复")
        self.cursor_restore_settings_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.cursor_restore_settings_btn.setFixedSize(100, 36)
        self.cursor_restore_settings_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #2980b9;
            }}
            QPushButton:pressed {{
                background-color: #2471a3;
            }}
            QPushButton:disabled {{
                background-color: {Theme.DISABLED};
                color: {Theme.TEXT_SECONDARY};
            }}
        """)
        self.cursor_restore_settings_btn.clicked.connect(self._on_restore_settings)
        self.cursor_restore_settings_btn.setEnabled(self._has_settings_backup())
        backup_settings_btn_layout.addWidget(self.cursor_restore_settings_btn)
        
        backup_settings_layout.addWidget(backup_settings_btn_frame)
        
        cursor_settings_layout.addWidget(backup_settings_frame)
        
        # 添加分隔线
        backup_workspace_divider = QFrame()
        backup_workspace_divider.setFrameShape(QFrame.Shape.HLine)
        backup_workspace_divider.setStyleSheet(f"background-color: {Theme.BORDER}; margin: 0px;")
        backup_workspace_divider.setMaximumHeight(1)
        cursor_settings_layout.addWidget(backup_workspace_divider)
        
        # ===== 添加备份恢复会话 =====
        backup_workspace_frame = QFrame()
        backup_workspace_frame.setStyleSheet("background-color: transparent;")
        backup_workspace_layout = QHBoxLayout(backup_workspace_frame)  # 改为水平布局
        backup_workspace_layout.setContentsMargins(0, 10, 0, 10)
        backup_workspace_layout.setSpacing(10)
        
        # 左侧标题和描述
        backup_workspace_text_frame = QFrame()
        backup_workspace_text_frame.setStyleSheet("background-color: transparent;")
        backup_workspace_text_layout = QVBoxLayout(backup_workspace_text_frame)
        backup_workspace_text_layout.setContentsMargins(0, 0, 0, 0)
        backup_workspace_text_layout.setSpacing(3)  # 减小间距
        
        # 添加标题
        backup_workspace_title = QLabel("备份/恢复 Cursor 会话记录")
        backup_workspace_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        backup_workspace_text_layout.addWidget(backup_workspace_title)
        
        # 添加描述
        backup_workspace_desc = QLabel("备份或恢复 Cursor 的会话数据  |  恢复会替换掉全部会话记录，恢复备份里的")
        backup_workspace_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
        backup_workspace_desc.setWordWrap(True)
        backup_workspace_text_layout.addWidget(backup_workspace_desc)
        
        backup_workspace_layout.addWidget(backup_workspace_text_frame, 1)
        
        # 右侧按钮
        backup_workspace_btn_frame = QFrame()
        backup_workspace_btn_frame.setStyleSheet("background-color: transparent;")
        backup_workspace_btn_layout = QHBoxLayout(backup_workspace_btn_frame)
        backup_workspace_btn_layout.setContentsMargins(0, 0, 0, 0)
        backup_workspace_btn_layout.setSpacing(10)
        
        # 备份按钮 - 会话记录功能组
        backup_workspace_btn = QPushButton("备份")
        backup_workspace_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        backup_workspace_btn.setFixedSize(100, 36)
        backup_workspace_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.SUCCESS};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #34B892;
            }}
            QPushButton:pressed {{
                background-color: #24856A;
            }}
        """)
        backup_workspace_btn.clicked.connect(self._on_backup_workspace)
        backup_workspace_btn_layout.addWidget(backup_workspace_btn)
        
        # 恢复按钮 - 使用蓝色
        self.cursor_restore_workspace_btn = QPushButton("恢复")
        self.cursor_restore_workspace_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.cursor_restore_workspace_btn.setFixedSize(100, 36)
        self.cursor_restore_workspace_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #2980b9;
            }}
            QPushButton:pressed {{
                background-color: #2471a3;
            }}
            QPushButton:disabled {{
                background-color: {Theme.DISABLED};
                color: {Theme.TEXT_SECONDARY};
            }}
        """)
        self.cursor_restore_workspace_btn.clicked.connect(self._on_restore_workspace)
        self.cursor_restore_workspace_btn.setEnabled(self._has_workspace_backup())
        backup_workspace_btn_layout.addWidget(self.cursor_restore_workspace_btn)
        
        backup_workspace_layout.addWidget(backup_workspace_btn_frame)
        
        cursor_settings_layout.addWidget(backup_workspace_frame)
        
        # 创建水平布局，将初始化Cursor和备份MCP放在同一行
        cursor_tools_row_layout = QHBoxLayout()
        cursor_tools_row_layout.setContentsMargins(0, 0, 0, 0)
        cursor_tools_row_layout.setSpacing(15)  # 减小卡片之间的间距
        
        # ===== 创建初始化Cursor卡片 =====
        initialize_cursor_card = QFrame()
        initialize_cursor_card.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_1};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        # 使用垂直布局
        initialize_cursor_layout = QVBoxLayout(initialize_cursor_card)
        initialize_cursor_layout.setContentsMargins(20, 20, 20, 20)
        initialize_cursor_layout.setSpacing(15)
        
        # ===== 添加初始化Cursor功能 =====
        initialize_frame = QFrame()
        initialize_frame.setStyleSheet("background-color: transparent;")
        initialize_layout = QHBoxLayout(initialize_frame)  # 改为水平布局
        initialize_layout.setContentsMargins(0, 0, 0, 0)
        initialize_layout.setSpacing(10)
        
        # 创建中央容器，包含标题和描述
        initialize_center_container = QWidget()
        initialize_center_container.setStyleSheet("background: transparent;")
        initialize_center_layout = QVBoxLayout(initialize_center_container)
        initialize_center_layout.setContentsMargins(0, 0, 0, 0)
        initialize_center_layout.setSpacing(5)  # 稍微增加间距
        
        # 创建标题容器
        initialize_title_container = QWidget()
        initialize_title_container.setStyleSheet("background: transparent;")
        initialize_title_layout = QVBoxLayout(initialize_title_container)
        initialize_title_layout.setContentsMargins(0, 0, 0, 0)
        initialize_title_layout.setSpacing(0)
        
        # 添加标题
        initialize_title = QLabel("初始化 Cursor")
        initialize_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        initialize_title_layout.addWidget(initialize_title)
        
        # 添加描述
        initialize_desc = QLabel("每次更换试用号前初始化一次，大概率可实现不掉试用的效果")
        initialize_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
        initialize_desc.setWordWrap(True)
        initialize_title_layout.addWidget(initialize_desc)
        
        # 设置标题容器的大小策略
        initialize_title_container.setMinimumHeight(65)
        initialize_title_container.setMinimumWidth(250)  # 确保容器足够宽以容纳完整文字
        initialize_title_container.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        
        # 将标题容器添加到中央容器
        initialize_center_layout.addWidget(initialize_title_container)
        
        # 添加中央容器到主布局
        initialize_layout.addWidget(initialize_center_container, 1)
        
        # 初始化按钮 - 使用红色表示警示功能
        initialize_btn = QPushButton("初始化")
        initialize_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        initialize_btn.setFixedHeight(36)
        initialize_btn.setMinimumWidth(100)
        initialize_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ERROR};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #e74c3c;
            }}
            QPushButton:pressed {{
                background-color: #c0392b;
            }}
        """)
        initialize_btn.clicked.connect(self._on_initialize_cursor)
        
        # 添加按钮到主布局
        initialize_layout.addWidget(initialize_btn, 0, Qt.AlignmentFlag.AlignCenter)
        
        # 添加初始化功能到卡片布局
        initialize_cursor_layout.addWidget(initialize_frame)
        
        # 添加分隔线
        initialize_divider = QFrame()
        initialize_divider.setFrameShape(QFrame.Shape.HLine)
        initialize_divider.setStyleSheet(f"background-color: {Theme.BORDER}; margin: 0px;")
        initialize_divider.setMaximumHeight(1)
        initialize_cursor_layout.addWidget(initialize_divider)
        
        # 添加初始化方案选择部分
        initialize_bottom_frame = QFrame()
        initialize_bottom_frame.setStyleSheet("background: transparent;")
        initialize_bottom_layout = QVBoxLayout(initialize_bottom_frame)
        initialize_bottom_layout.setContentsMargins(0, 10, 0, 0)
        initialize_bottom_layout.setSpacing(10)  # 增加间距，使布局更清晰
        
        # 方案选择行
        initialize_method_row = QHBoxLayout()
        initialize_method_row.setSpacing(5)  # 减小标题和按钮容器之间的间距
        
        # 方案选择标题
        initialize_method_label = QLabel("初始化方案")
        initialize_method_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        initialize_method_row.addWidget(initialize_method_label)
        
        # 方案选择容器 - 单选按钮组
        initialize_options_container = QFrame()
        initialize_options_container.setObjectName("initializeOptionsContainer")
        initialize_options_container.setStyleSheet(f"""
            #initializeOptionsContainer {{
                background: transparent;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 0px;
            }}
        """)
        # 使用Expanding而不是Fixed，使容器可以根据内容伸展
        initialize_options_container.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        initialize_options_layout = QHBoxLayout(initialize_options_container)
        initialize_options_layout.setContentsMargins(0, 0, 0, 0)
        initialize_options_layout.setSpacing(5)  # 减小按钮之间的间距
        initialize_options_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 确保按钮居中对齐
        
        # 从配置文件加载初始化方案设置
        initialize_method = self._get_initialize_method_setting()
        
        # 创建方案按钮组
        self.initialize_method_buttons = []
        
        # 特殊格式化按钮（方案一）- 带推荐标签
        initialize_method_one_container = QFrame()
        initialize_method_one_container.setStyleSheet("background: transparent;")
        initialize_method_one_layout = QVBoxLayout(initialize_method_one_container)
        initialize_method_one_layout.setContentsMargins(0, 0, 0, 0)
        initialize_method_one_layout.setSpacing(0)
        
        # 方案一按钮
        initialize_method_one_radio = QPushButton("特殊格式化")
        initialize_method_one_radio.setObjectName("initializeMethodOneBtn")
        initialize_method_one_radio.setCheckable(True)
        initialize_method_one_radio.setChecked(initialize_method == 1)
        initialize_method_one_radio.setFixedHeight(32)  # 固定高度
        # 移除固定宽度设置，使按钮填满容器
        initialize_method_one_radio.setCursor(Qt.CursorShape.PointingHandCursor)  # 设置鼠标指针样式
        initialize_method_one_radio.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_SECONDARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px 15px;
                font-size: {Theme.FONT_SIZE_SMALL};
                text-align: center;
            }}
            QPushButton:checked {{
                background-color: {Theme.ACCENT};
                color: white;
                border: 2px solid {Theme.CARD_LEVEL_3};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 4px 14px;
                font-size: {Theme.FONT_SIZE_SMALL};
                text-align: center;
            }}
            QPushButton:hover:!checked {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
                background-color: rgba(36, 168, 132, 0.08);
            }}
            QPushButton:pressed {{
                background-color: rgba(36, 168, 132, 0.15);
            }}
        """)
        self.initialize_method_buttons.append(initialize_method_one_radio)
        
        # 创建堆叠布局来实现标签悬浮效果
        method_one_stack = QWidget()
        method_one_stack.setStyleSheet("background: transparent;")
        method_one_stack_layout = QVBoxLayout(method_one_stack)
        method_one_stack_layout.setContentsMargins(0, 0, 0, 0)
        method_one_stack_layout.setSpacing(0)
        method_one_stack_layout.addWidget(initialize_method_one_radio)
        
        # 创建推荐标签并添加到按钮上
        recommend_label = QLabel("推荐", method_one_stack)
        recommend_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        recommend_label.setFixedSize(28, 14)  # 减小标签尺寸
        recommend_label.setStyleSheet(f"""
            background-color: #6a5acd;  /* 紫色背景 */
            color: white;  /* 白色文字 */
            border-radius: 7px;  /* 增加圆角，接近椭圆形 */
            padding: 0px 2px;
            font-size: 9px;  /* 减小字体 */
            font-weight: bold;
            border: none;
        """)
        
        # 重写resizeEvent方法来精确定位标签
        def resizeEvent(widget, event):
            # 调用原始的resizeEvent
            QWidget.resizeEvent(widget, event)
            # 定位标签到右上角
            button_width = initialize_method_one_radio.width()
            # 将标签放在右上角
            recommend_label.move(button_width - 30, -2)  
        
        # 设置自定义的resizeEvent处理函数
        method_one_stack.resizeEvent = lambda event: resizeEvent(method_one_stack, event)
        
        # 确保标签在初始时也能正确定位
        QTimer.singleShot(100, lambda: recommend_label.move(initialize_method_one_radio.width() - 30, -2))
        recommend_label.raise_()
        
        # 添加到容器
        initialize_method_one_layout.addWidget(method_one_stack)
        
        # 方案二按钮 (完全格式化)
        initialize_method_two_radio = QPushButton("完全格式化")
        initialize_method_two_radio.setObjectName("initializeMethodTwoBtn")
        initialize_method_two_radio.setCheckable(True)
        initialize_method_two_radio.setChecked(initialize_method == 2)
        initialize_method_two_radio.setFixedHeight(32)  # 固定高度
        # 移除固定宽度设置，使按钮填满容器
        initialize_method_two_radio.setCursor(Qt.CursorShape.PointingHandCursor)  # 设置鼠标指针样式
        initialize_method_two_radio.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_SECONDARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px 15px;
                font-size: {Theme.FONT_SIZE_SMALL};
                text-align: center;
            }}
            QPushButton:checked {{
                background-color: {Theme.ACCENT};
                color: white;
                border: 2px solid {Theme.CARD_LEVEL_3};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 4px 14px;
                font-size: {Theme.FONT_SIZE_SMALL};
                text-align: center;
            }}
            QPushButton:hover:!checked {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
                background-color: rgba(36, 168, 132, 0.08);
            }}
            QPushButton:pressed {{
                background-color: rgba(36, 168, 132, 0.15);
            }}
        """)
        self.initialize_method_buttons.append(initialize_method_two_radio)
        
        # 连接按钮信号到处理函数
        initialize_method_one_radio.clicked.connect(lambda: self._handle_initialize_method_selection(1))
        initialize_method_two_radio.clicked.connect(lambda: self._handle_initialize_method_selection(2))
        
        # 将按钮添加到布局中，确保它们居中对齐
        initialize_options_layout.addWidget(initialize_method_one_container, 1)  # 使用1的拉伸因子，确保两个按钮大小一致
        initialize_options_layout.addWidget(initialize_method_two_radio, 1)  # 使用1的拉伸因子，确保两个按钮大小一致
        
        # 确保方案选择行水平居中
        initialize_method_row.addWidget(initialize_options_container, 1)  # 使用拉伸因子1，填满容器
        initialize_method_row.addStretch(0)  # 移除额外的弹性空间
        
        # 添加方案选择行到底部布局
        initialize_bottom_layout.addLayout(initialize_method_row)
        
        # 添加底部区域到卡片布局
        initialize_cursor_layout.addWidget(initialize_bottom_frame)
        
        # ===== 创建MCP备份卡片 =====
        mcp_backup_card = QFrame()
        mcp_backup_card.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_1};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        # 使用垂直布局
        mcp_backup_card_layout = QVBoxLayout(mcp_backup_card)
        mcp_backup_card_layout.setContentsMargins(15, 20, 15, 20)  # 减小左右内边距
        mcp_backup_card_layout.setSpacing(15)
        
        # ===== 添加MCP备份功能 =====
        mcp_backup_frame = QFrame()
        mcp_backup_frame.setStyleSheet("background-color: transparent;")
        mcp_backup_layout = QHBoxLayout(mcp_backup_frame)  # 改为水平布局
        mcp_backup_layout.setContentsMargins(0, 0, 0, 0)
        mcp_backup_layout.setSpacing(10)
        
        # 创建中央容器，包含标题和描述
        mcp_backup_center_container = QWidget()
        mcp_backup_center_container.setStyleSheet("background: transparent;")
        mcp_backup_center_layout = QVBoxLayout(mcp_backup_center_container)
        mcp_backup_center_layout.setContentsMargins(0, 0, 0, 0)
        mcp_backup_center_layout.setSpacing(5)  # 稍微增加间距
        
        # 创建标题容器
        mcp_backup_title_container = QWidget()
        mcp_backup_title_container.setStyleSheet("background: transparent;")
        mcp_backup_title_layout = QVBoxLayout(mcp_backup_title_container)
        mcp_backup_title_layout.setContentsMargins(0, 0, 0, 0)
        mcp_backup_title_layout.setSpacing(0)
        
        # 添加标题
        mcp_backup_title = QLabel("备份MCP")
        mcp_backup_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
        """)
        mcp_backup_title_layout.addWidget(mcp_backup_title)
        
        # 添加描述
        mcp_backup_desc = QLabel("备份和切换MCP配置，适用不同开发环境")  # 缩短描述文字
        mcp_backup_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
        mcp_backup_desc.setWordWrap(True)
        mcp_backup_title_layout.addWidget(mcp_backup_desc)
        
        # 设置标题容器的大小策略
        mcp_backup_title_container.setMinimumHeight(65)
        mcp_backup_title_container.setMinimumWidth(180)  # 减小最小宽度
        mcp_backup_title_container.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        
        # 将标题容器添加到中央容器
        mcp_backup_center_layout.addWidget(mcp_backup_title_container)
        
        # 添加中央容器到主布局
        mcp_backup_layout.addWidget(mcp_backup_center_container, 1)
        
        # MCP备份按钮 - 使用紫色表示特殊功能
        mcp_backup_btn = QPushButton("打开备份")  # 缩短按钮文字
        mcp_backup_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        mcp_backup_btn.setFixedHeight(36)
        mcp_backup_btn.setFixedWidth(100)  # 固定按钮宽度，而不是最小宽度
        mcp_backup_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #8e44ad;
            }}
            QPushButton:pressed {{
                background-color: #7d3c98;
            }}
        """)
        mcp_backup_btn.clicked.connect(lambda: self._on_function_selected("mcp_backup"))
        
        # 添加按钮到主布局
        mcp_backup_layout.addWidget(mcp_backup_btn, 0, Qt.AlignmentFlag.AlignCenter)
        
        # 添加MCP备份功能到卡片布局
        mcp_backup_card_layout.addWidget(mcp_backup_frame)
        
        # 将卡片添加到水平布局中，调整顺序和比例
        cursor_tools_row_layout.addWidget(initialize_cursor_card, 5)  # 进一步增加初始化Cursor卡片的比例
        cursor_tools_row_layout.addWidget(mcp_backup_card, 3)    # 减小MCP备份卡片的比例
        
        # 将水平布局添加到主布局
        cursor_settings_layout.addLayout(cursor_tools_row_layout)
        
        # 将Cursor设置卡片添加到布局中
        self.feature_layout.addWidget(cursor_settings_card)
        
        # ====== 添加高级自定义备份/恢复分块（一级分块） ======
        # self.advanced_backup_section = AdvancedBackupSection() # 已移动到上方
        # self.advanced_backup_section.jump_requested.connect(self._on_advanced_backup_jump) # 已移动到上方
        # self.feature_layout.addWidget(self.advanced_backup_section) # 已删除
        
        # ====== 创建自定义 Cursor 路径功能卡片 ======
    
    def _get_current_email(self):
        """获取当前登录的email"""
        try:
            # 使用更一致的方式获取当前邮箱
            auth_manager = CursorAuthManager()
            email = auth_manager.get_current_email()
            
            if email:
                print(f"从数据库获取到email: {email}")
                return email
            
            # 如果使用CursorAuthManager未获取到邮箱，则尝试原来的方法作为备选
            print("通过CursorAuthManager未获取到email，尝试原方法")
            
            # SQLite数据库路径 - 使用Utils工具类获取
            cursor_cache_dir = Utils.get_cursor_cache_dir()
            print(f"Cursor缓存目录: {cursor_cache_dir}")
            if not cursor_cache_dir:
                print("无法获取Cursor缓存目录")
                return None
                
            # 构建数据库路径
            db_path = os.path.join(cursor_cache_dir, "User", "globalStorage", "state.vscdb")
            print(f"SQLite数据库路径: {db_path}")
            if not os.path.exists(db_path):
                print(f"数据库文件不存在: {db_path}")
                return None
            
            conn = None
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 查询当前登录的email
                cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/cachedEmail'")
                result = cursor.fetchone()
                
                if result is not None:
                    print(f"从数据库直接获取到email: {result[0]}")
                    return result[0]
                
                print("数据库中未找到email信息")  
                return None
            finally:
                if conn:
                    conn.close()  # 确保数据库连接被关闭
        except Exception as e:
            print(f"获取当前登录email时出错: {str(e)}")
            return None
    
    def _account_exists_in_file(self, email):
        """检查账户是否存在于cursor_accounts.json文件中"""
        if not email:
            print("email为空，无法检查")
            return False
            
        try:
            # 使用工具函数获取正确的账户数据文件路径
            from utils import get_accounts_file_path
            from logger import info
            
            # 获取账户文件路径
            accounts_file = get_accounts_file_path()
            
            # 检查文件是否存在
            if not os.path.exists(accounts_file):
                info(f"账户文件不存在: {accounts_file}")
                return False
            
            info(f"使用账户文件: {accounts_file}")
            # 读取账户文件
            with open(accounts_file, 'r', encoding='utf-8') as f:
                accounts = json.load(f)
                
            # 检查当前email是否在列表中（不区分大小写）
            email_lower = email.lower()
            for account in accounts:
                account_email = account.get("email", "")
                if account_email.lower() == email_lower:
                    info(f"账户 {email} 已存在于文件中（忽略大小写比较）")
                    return True
            
            info(f"账户 {email} 不存在于文件中，应该显示保存按钮")        
            return False
        except Exception as e:
            print(f"检查账户文件时出错: {str(e)}")
            return False
    
    def _on_function_selected(self, func_id):
        """功能选择处理函数"""
        info(f"功能选择: {func_id}")
        
        if func_id == "mcp_backup":
            # 打开MCP备份对话框
            dialog = McpBackupDialog(self.window())
            dialog.exec()
        elif func_id == "reset_machine_id":
            # 打开重置机器码对话框
            from widgets.reset_machine_id_dialog import ResetMachineIDDialog
            dialog = ResetMachineIDDialog(self.window())
            dialog.exec()
        elif func_id == "auto_register":
            # 打开自动注册对话框
            from widgets.auto_register_dialog import AutoRegisterDialog
            dialog = AutoRegisterDialog(self.window())
            dialog.exec()
        else:
            # 转发其他功能选择信号
            self.function_selected.emit(func_id)

    def showEvent(self, event):
        """重写显示事件，在每次显示页面时刷新内容"""
        super().showEvent(event)
        self.refresh()  # 每次显示页面时刷新内容
        
        # 刷新Cursor设置UI状态
        if hasattr(self, "cursor_proxy_input"):
            self._refresh_cursor_settings_ui()

    def _get_auto_hide_setting(self):
        """获取是否自动隐藏按钮的设置"""
        try:
            from utils import get_app_data_dir
            settings_file = os.path.join(get_app_data_dir(), "settings.json")
            
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    return settings.get("auto_hide_save_button", False)
            return False
        except Exception as e:
            print(f"获取自动隐藏设置时出错: {str(e)}")
            return False

    def _get_register_only_setting(self):
        """获取仅注册模式设置"""
        try:
            from utils import get_app_data_dir
            function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(function_config_file), exist_ok=True)
            
            # 如果文件存在，读取配置
            if os.path.exists(function_config_file):
                with open(function_config_file, 'r', encoding='utf-8') as f:
                    function_settings = json.load(f)
                    return function_settings.get("register_only", False)
            
            # 如果文件不存在，创建默认配置
            function_settings = {
                "register_only": False  # 默认为 False，即正常更新认证信息
            }
            # 保存默认配置
            with open(function_config_file, 'w', encoding='utf-8') as f:
                json.dump(function_settings, f, ensure_ascii=False, indent=2)
            
            return False
        except Exception as e:
            print(f"获取仅注册模式设置时出错: {str(e)}")
            return False
    
    def _update_register_only_setting(self, state):
        """更新仅注册模式设置"""
        try:
            from utils import get_app_data_dir
            function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(function_config_file), exist_ok=True)
            
            # 读取当前配置
            function_settings = {}
            if os.path.exists(function_config_file):
                with open(function_config_file, 'r', encoding='utf-8') as f:
                    function_settings = json.load(f)
            
            # 更新仅注册模式设置
            function_settings["register_only"] = bool(state)
            
            # 保存配置
            with open(function_config_file, 'w', encoding='utf-8') as f:
                json.dump(function_settings, f, ensure_ascii=False, indent=2)
            
            # 记录日志
            info(f"已更新自动注册功能配置：仅注册模式 = {bool(state)}")
        except Exception as e:
            print(f"更新仅注册模式设置时出错: {str(e)}")

    def _get_register_method_setting(self):
        """获取注册方案设置"""
        try:
            from utils import get_app_data_dir
            function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(function_config_file), exist_ok=True)
            
            # 如果文件存在，读取配置
            if os.path.exists(function_config_file):
                with open(function_config_file, 'r', encoding='utf-8') as f:
                    function_settings = json.load(f)
                    return function_settings.get("register_method", 1)  # 默认为方案一
            
            # 如果文件不存在，创建默认配置
            function_settings = {
                "register_only": False,  # 默认为False，即正常更新认证信息
                "register_method": 1     # 默认为方案一
            }
            
            # 保存默认配置
            with open(function_config_file, 'w', encoding='utf-8') as f:
                json.dump(function_settings, f, ensure_ascii=False, indent=2)
            
            return 1  # 默认为方案一
        except Exception as e:
            print(f"获取注册方案设置时出错: {str(e)}")
            return 1  # 出错时默认为方案一
    
    def _handle_method_selection(self, method):
        """处理方案选择"""
        try:
            # 更新按钮状态，确保互斥选择
            for i, btn in enumerate(self.method_buttons):
                # 索引0对应方案一，索引1对应方案二
                method_number = i + 1
                if method_number == method:
                    btn.setChecked(True)
                else:
                    btn.setChecked(False)
            
            # 保存设置到配置文件
            from utils import get_app_data_dir
            function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(function_config_file), exist_ok=True)
            
            # 读取当前配置
            function_settings = {}
            if os.path.exists(function_config_file):
                with open(function_config_file, 'r', encoding='utf-8') as f:
                    function_settings = json.load(f)
            
            # 更新注册方案设置
            function_settings["register_method"] = method
            
            # 保存配置
            with open(function_config_file, 'w', encoding='utf-8') as f:
                json.dump(function_settings, f, ensure_ascii=False, indent=2)
            
            # 记录日志
            info(f"已更新自动注册功能配置：注册方案 = {method}")
        except Exception as e:
            print(f"处理方案选择时出错: {str(e)}")

    # ===== Cursor设置功能相关方法 =====
    
    def _get_cursor_settings_path(self):
        """获取当前系统Cursor settings.json文件路径"""
        system = platform.system().lower()
        if "windows" in system:
            return os.path.join(os.getenv("APPDATA"), "Cursor", "User", "settings.json")
        elif "darwin" in system:
            return os.path.expanduser("~/Library/Application Support/Cursor/User/settings.json")
        else:  # Linux系统
            cursor_dir_name = self._get_linux_cursor_dirname()
            if cursor_dir_name:
                return os.path.expanduser(f"~/.config/{cursor_dir_name}/User/settings.json")
            else:
                info("无法找到Linux系统下的Cursor目录")
                return None
    
    def _get_cursor_workspace_storage_path(self):
        """获取当前系统Cursor workspaceStorage文件夹路径"""
        from logger import info, error
        
        system = platform.system().lower()
        path = None
        
        # 首先检查用户是否配置了自定义路径
        custom_enabled, custom_path = self._get_custom_cursor_path_settings()
        if custom_enabled and custom_path and os.path.exists(custom_path):
            # 如果使用自定义路径，尝试从自定义路径构建workspaceStorage路径
            info(f"检测到自定义Cursor路径: {custom_path}")
            if "windows" in system:
                path = os.path.join(custom_path, "resources", "app", "out", "vs", "workbench", "workspaceStorage")
                if not os.path.exists(path):
                    path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "workspaceStorage")
            elif "darwin" in system:
                path = os.path.join(custom_path, "Contents", "Resources", "app", "out", "vs", "workbench", "workspaceStorage")
                if not os.path.exists(path):
                    path = os.path.expanduser("~/Library/Application Support/Cursor/User/workspaceStorage")
            else:  # Linux系统
                path = os.path.join(custom_path, "resources", "app", "out", "vs", "workbench", "workspaceStorage")
                if not os.path.exists(path):
                    cursor_dir_name = self._get_linux_cursor_dirname()
                    if cursor_dir_name:
                        path = os.path.expanduser(f"~/.config/{cursor_dir_name}/User/workspaceStorage")
        else:
            # 使用默认路径
            if "windows" in system:
                path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "workspaceStorage")
            elif "darwin" in system:
                path = os.path.expanduser("~/Library/Application Support/Cursor/User/workspaceStorage")
            else:  # Linux系统
                cursor_dir_name = self._get_linux_cursor_dirname()
                if cursor_dir_name:
                    path = os.path.expanduser(f"~/.config/{cursor_dir_name}/User/workspaceStorage")
                else:
                    error("无法找到Linux系统下的Cursor目录")
                    return None
        
        # 记录路径是否存在
        if path:
            if os.path.exists(path):
                info(f"找到Cursor workspaceStorage路径: {path}")
                return path
            else:
                # 如果主要路径不存在，尝试查找其他可能位置
                alternative_paths = []
                
                # Windows上的多种可能位置
                if "windows" in system:
                    # 使用大写User路径
                    alt_path = os.path.join(os.getenv("APPDATA"), "Cursor", "USER", "workspaceStorage")
                    if os.path.exists(alt_path):
                        info(f"使用替代路径: {alt_path}")
                        return alt_path
                    alternative_paths.append(alt_path)
                    
                    # 使用小写cursor路径
                    alt_path = os.path.join(os.getenv("APPDATA"), "cursor", "User", "workspaceStorage")
                    if os.path.exists(alt_path):
                        info(f"使用替代路径: {alt_path}")
                        return alt_path
                    alternative_paths.append(alt_path)
                    
                    # 检查本地路径
                    alt_path = os.path.join(os.getenv("LOCALAPPDATA"), "Cursor", "User", "workspaceStorage")
                    if os.path.exists(alt_path):
                        info(f"使用替代路径: {alt_path}")
                        return alt_path
                    alternative_paths.append(alt_path)
                    
                # 尝试创建路径
                try:
                    os.makedirs(path, exist_ok=True)
                    info(f"已创建Cursor workspaceStorage路径: {path}")
                    return path
                except Exception as e:
                    error(f"创建Cursor workspaceStorage路径失败: {path}, 错误: {e}")
                    
                error(f"Cursor workspaceStorage路径不存在: {path}")
                error(f"尝试过的替代路径: {alternative_paths}")
                
        return path
    
    def _get_cursor_root_path(self):
        """获取当前系统Cursor根目录路径"""
        system = platform.system().lower()
        if "windows" in system:
            return os.path.join(os.getenv("APPDATA"), "Cursor")
        elif "darwin" in system:
            return os.path.expanduser("~/Library/Application Support/Cursor")
        else:  # Linux系统
            cursor_dir_name = self._get_linux_cursor_dirname()
            if cursor_dir_name:
                return os.path.expanduser(f"~/.config/{cursor_dir_name}")
            else:
                info("无法找到Linux系统下的Cursor目录")
                return None
    
    def _get_linux_cursor_dirname(self):
        """获取Linux系统下的Cursor目录名称"""
        # 尝试几个常见的可能目录名
        possible_dirs = ["cursor", "Cursor"]
        
        # 首先检查.config目录下是否有这些常见名称
        config_dir = os.path.expanduser("~/.config")
        if os.path.exists(config_dir):
            for dirname in os.listdir(config_dir):
                if dirname.lower() in [d.lower() for d in possible_dirs]:
                    return dirname
        
        # 如果找不到，尝试从可能的Cursor安装位置获取
        install_dirs = [
            "/opt/Cursor",
            "/usr/lib/cursor",
            os.path.expanduser("~/.cursor")
        ]
        
        for dir_path in install_dirs:
            if os.path.isdir(dir_path):
                return os.path.basename(dir_path)
        
        return None

    def _read_cursor_settings(self):
        """读取Cursor设置文件内容"""
        settings_path = self._get_cursor_settings_path()
        if not settings_path or not os.path.exists(settings_path):
            info(f"Cursor设置文件不存在: {settings_path}")
            return {}
        
        try:
            with open(settings_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            info(f"读取Cursor设置文件失败: {str(e)}")
            return {}
    
    def _save_cursor_settings(self, settings):
        """保存Cursor设置文件内容"""
        settings_path = self._get_cursor_settings_path()
        if not settings_path:
            info("无法获取Cursor设置文件路径")
            return False
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(settings_path), exist_ok=True)
            
            # 写入设置
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4)
                
            info(f"Cursor设置已保存到: {settings_path}")
            return True
        except Exception as e:
            info(f"保存Cursor设置文件失败: {str(e)}")
            return False
    
    def _get_proxy_setting(self):
        """获取代理设置"""
        settings = self._read_cursor_settings()
        return settings.get("http.proxy", "")
    
    def _set_proxy_setting(self, proxy_value):
        """设置代理"""
        settings = self._read_cursor_settings()
        
        if proxy_value:
            settings["http.proxy"] = proxy_value
            info(f"设置Cursor代理为: {proxy_value}")
        else:
            # 如果值为空，则删除该字段
            if "http.proxy" in settings:
                del settings["http.proxy"]
                info("已删除Cursor代理设置")
        
        return self._save_cursor_settings(settings)
    
    def _get_auto_update_setting(self):
        """获取自动更新设置状态"""
        settings = self._read_cursor_settings()
        
        # 检查是否同时满足两个条件
        has_update_mode = settings.get("update.mode") == "none"
        has_windows_update = settings.get("update.enableWindowsBackgroundUpdates") == False
        
        return has_update_mode and has_windows_update
    
    def _set_auto_update_setting(self, enabled):
        """设置自动更新状态"""
        settings = self._read_cursor_settings()
        
        if enabled:
            # 添加关闭自动更新的设置
            settings["update.mode"] = "none"
            settings["update.enableWindowsBackgroundUpdates"] = False
            info("已关闭Cursor自动更新")
        else:
            # 删除这两个字段
            if "update.mode" in settings:
                del settings["update.mode"]
            if "update.enableWindowsBackgroundUpdates" in settings:
                del settings["update.enableWindowsBackgroundUpdates"]
            info("已恢复Cursor自动更新设置")
        
        return self._save_cursor_settings(settings)
    
    def _get_disable_http2_setting(self):
        """获取禁用HTTP2设置状态"""
        settings = self._read_cursor_settings()
        return settings.get("cursor.general.disableHttp2") == True
    
    def _set_disable_http2_setting(self, enabled):
        """设置禁用HTTP2状态"""
        settings = self._read_cursor_settings()
        
        if enabled:
            # 添加禁用HTTP2的设置
            settings["cursor.general.disableHttp2"] = True
            info("已禁用Cursor HTTP2")
        else:
            # 删除该字段
            if "cursor.general.disableHttp2" in settings:
                del settings["cursor.general.disableHttp2"]
            info("已启用Cursor HTTP2")
        
        return self._save_cursor_settings(settings)
    
    def _get_unlimited_max_setting(self):
        """获取开启无限MAX设置状态"""
        try:
            from utils import get_app_data_dir
            function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
            
            # 如果文件存在，读取配置
            if os.path.exists(function_config_file):
                with open(function_config_file, 'r', encoding='utf-8') as f:
                    function_settings = json.load(f)
                    return function_settings.get("unlimited_max_enabled", False)
            
            return False
        except Exception as e:
            info(f"获取无限MAX设置状态时出错: {str(e)}")
            return False
            
    def _get_workbench_js_path(self):
        """获取当前系统Cursor workbench.desktop.main.js文件路径"""
        system = platform.system().lower()
        if "windows" in system:
            return os.path.join(os.getenv("LOCALAPPDATA"), "Programs", "Cursor", "resources", "app", "out", "vs", "workbench", "workbench.desktop.main.js")
        elif "darwin" in system:
            return os.path.join("/Applications", "Cursor.app", "Contents", "Resources", "app", "out", "vs", "workbench", "workbench.desktop.main.js")
        else:  # Linux系统
            return os.path.expanduser("~/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js")
            
    def _get_workbench_backup_dir(self, create=False):
        """获取workbench.desktop.main.js备份目录
        
        Args:
            create (bool): 是否创建目录，默认为False仅获取路径不创建
            
        Returns:
            str: 备份目录路径，如果获取失败则返回None
        """
        workbench_js_path = self._get_workbench_js_path()
        if not workbench_js_path:
            return None
            
        # 获取备份目录路径（与workbench.desktop.main.js同级的backup目录）
        backup_dir = os.path.join(os.path.dirname(workbench_js_path), "backup")
        
        # 只有当create=True时才创建目录
        if create and not os.path.exists(backup_dir):
            os.makedirs(backup_dir, exist_ok=True)
            
        return backup_dir
        
    def _get_workbench_backup_path(self, create=False):
        """获取workbench.desktop.main.js备份文件路径
        
        Args:
            create (bool): 是否创建备份目录，默认为False仅获取路径不创建
            
        Returns:
            str: 备份文件路径，如果获取失败则返回None
        """
        backup_dir = self._get_workbench_backup_dir(create=create)
        if not backup_dir:
            return None
            
        return os.path.join(backup_dir, "workbench.desktop.main.js.bak")
    
    def _set_unlimited_max_setting(self, enabled):
        """设置开启无限MAX状态"""
        try:
            from utils import get_app_data_dir
            function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(function_config_file), exist_ok=True)
            
            # 读取当前配置
            function_settings = {}
            if os.path.exists(function_config_file):
                with open(function_config_file, 'r', encoding='utf-8') as f:
                    function_settings = json.load(f)
            
            # 更新无限MAX设置
            function_settings["unlimited_max_enabled"] = enabled
            
            # 保存配置
            with open(function_config_file, 'w', encoding='utf-8') as f:
                json.dump(function_settings, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            info(f"设置无限MAX状态时出错: {str(e)}")
            return False

    def _backup_cursor_settings(self):
        """备份Cursor设置文件和各种数据库设置"""
        # 备份settings.json文件
        settings_path = self._get_cursor_settings_path()
        if not settings_path or not os.path.exists(settings_path):
            info(f"Cursor设置文件不存在，无法备份: {settings_path}")
            settings_backup_success = False
        else:
            try:
                # 创建备份目录
                backup_dir = os.path.join(get_app_data_dir(), "backups", "settings")
                os.makedirs(backup_dir, exist_ok=True)
                
                # 备份文件路径
                backup_path = os.path.join(backup_dir, "settings.json")
                
                # 复制文件
                shutil.copy2(settings_path, backup_path)
                
                info(f"Cursor设置已备份到: {backup_path}")
                settings_backup_success = True
            except Exception as e:
                info(f"备份Cursor设置文件失败: {str(e)}")
                settings_backup_success = False
        
        # 定义要备份的设置类型
        setting_types = {
            "workbench": "工作区界面",
            "editor": "编辑器",
            "telemetry": "遥测数据",
            "aicontext": "AI上下文",
            "src.vs.platform": "VSCode平台"
        }
        
        # 备份各种设置类型数据
        database_backup_success = False
        
        try:
            # 获取state.vscdb路径
            global_storage_path = self._get_cursor_global_storage_path()
            state_vscdb_path = os.path.join(global_storage_path, "state.vscdb") if global_storage_path else None
            
            if not state_vscdb_path or not os.path.exists(state_vscdb_path):
                info(f"Cursor state.vscdb文件不存在，无法备份数据库设置: {state_vscdb_path}")
            else:
                # 创建备份目录
                backup_dir = os.path.join(get_app_data_dir(), "backups", "settings")
                os.makedirs(backup_dir, exist_ok=True)
                
                # 连接到数据库
                conn = sqlite3.connect(state_vscdb_path)
                cursor = conn.cursor()
                
                # 备份每种类型的设置
                for prefix, type_name in setting_types.items():
                    try:
                        # 查询以该前缀开头的所有键
                        cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE ?", (f"{prefix}%",))
                        rows = cursor.fetchall()
                        
                        if rows:
                            # 将所有匹配的键值对保存为JSON
                            settings_data = {row[0]: row[1] for row in rows}
                            backup_file_path = os.path.join(backup_dir, f"{prefix.replace('.', '_')}_settings.json")
                            
                            with open(backup_file_path, 'w', encoding='utf-8') as f:
                                json.dump(settings_data, f, ensure_ascii=False, indent=2)
                            
                            info(f"Cursor {type_name}设置已备份到: {backup_file_path}, 共 {len(rows)} 项")
                            database_backup_success = True
                        else:
                            info(f"未找到以 {prefix} 开头的设置项")
                    except Exception as e:
                        info(f"备份 {type_name}设置失败: {str(e)}")
                
                # 兼容原有备份机制，单独处理特定两项设置
                # 1. 备份aicontext.personalContext（提示词）
                try:
                    cursor.execute("SELECT value FROM ItemTable WHERE key = 'aicontext.personalContext'")
                    row = cursor.fetchone()
                    
                    if row:
                        context_backup_path = os.path.join(backup_dir, "personal_context.json")
                        with open(context_backup_path, 'w', encoding='utf-8') as f:
                            f.write(row[0])
                        info(f"Cursor 提示词已备份到: {context_backup_path}")
                except Exception as e:
                    info(f"单独备份提示词失败: {str(e)}")
                
                # 2. 备份Agent模式autoRun设置
                try:
                    cursor.execute("SELECT value FROM ItemTable WHERE key = 'src.vs.platform.reactivestorage.browser.reactiveStorageServiceImpl.persistentStorage.applicationUser'")
                    row = cursor.fetchone()
                    
                    if row:
                        application_user_data = json.loads(row[0])
                        
                        if ('composerState' in application_user_data and 
                            'modes4' in application_user_data['composerState']):
                            
                            modes4 = application_user_data['composerState']['modes4']
                            agent_mode = None
                            for mode in modes4:
                                if mode.get('id') == 'agent':
                                    agent_mode = mode
                                    break
                            
                            if agent_mode and 'autoRun' in agent_mode:
                                auto_run_value = agent_mode['autoRun']
                                agent_autorun_backup_path = os.path.join(backup_dir, "agent_autorun.json")
                                with open(agent_autorun_backup_path, 'w', encoding='utf-8') as f:
                                    json.dump({"autoRun": auto_run_value}, f)
                                info(f"Cursor Agent模式autoRun设置已备份到: {agent_autorun_backup_path}")
                except Exception as e:
                    info(f"单独备份Agent模式设置失败: {str(e)}")
                
                cursor.close()
                conn.close()
        except Exception as e:
            info(f"备份数据库设置失败: {str(e)}")
        
        # 检查备份完整性：确保所有重要组件都备份成功
        if not settings_backup_success and not database_backup_success:
            # 如果两个都失败，返回失败
            info("备份失败：settings.json和数据库设置都无法备份")
            return False
        elif not settings_backup_success:
            # 如果settings.json备份失败但数据库备份成功，记录警告但继续
            info("警告：settings.json备份失败，但数据库设置备份成功")
        elif not database_backup_success:
            # 如果数据库备份失败但settings.json备份成功，记录警告但继续
            info("警告：数据库设置备份失败，但settings.json备份成功")

        # 至少有一项备份成功就返回True，但会记录警告
        return True
    
    def _restore_cursor_settings(self):
        """从备份恢复Cursor设置文件和各种数据库设置"""
        # 先终止Cursor进程
        info("开始关闭Cursor进程，准备恢复设置")
        cursor_killed = False
        
        try:
            # 调用主窗口的kill_cursor_process方法
            cursor_killed = self.window().kill_cursor_process()
            if cursor_killed:
                info("成功关闭Cursor进程")
                # 等待2秒确保进程完全终止
                info("等待2秒确保进程完全终止...")
                time.sleep(2)
            else:
                info("未检测到运行中的Cursor进程，或关闭失败")
        except Exception as e:
            error_msg = str(e)
            info(f"终止Cursor进程失败: {error_msg}")
            # 继续执行，不影响恢复流程
        
        settings_restore_success = False
        database_restore_success = False
        
        # 恢复settings.json文件
        backup_path = os.path.join(get_app_data_dir(), "backups", "settings", "settings.json")
        if not os.path.exists(backup_path):
            info(f"Cursor设置备份文件不存在: {backup_path}")
        else:
            # 目标文件路径
            settings_path = self._get_cursor_settings_path()
            if not settings_path:
                info("无法获取Cursor设置文件路径")
            else:
                try:
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(settings_path), exist_ok=True)
                    
                    # 复制文件
                    shutil.copy2(backup_path, settings_path)
                    
                    info(f"已从备份恢复Cursor设置: {settings_path}")
                    settings_restore_success = True
                except Exception as e:
                    info(f"恢复Cursor设置文件失败: {str(e)}")
        
        # 定义要恢复的设置类型，与备份对应
        setting_types = {
            "workbench": "工作区界面",
            "editor": "编辑器",
            "telemetry": "遥测数据",
            "aicontext": "AI上下文",
            "src.vs.platform": "VSCode平台"
        }
        
        # 获取state.vscdb路径
        global_storage_path = self._get_cursor_global_storage_path()
        state_vscdb_path = os.path.join(global_storage_path, "state.vscdb") if global_storage_path else None
        
        if not state_vscdb_path:
            info("无法获取Cursor state.vscdb路径")
        else:
            # 确保目标目录存在
            os.makedirs(global_storage_path, exist_ok=True)
            
            # 检查state.vscdb文件是否存在
            if not os.path.exists(state_vscdb_path):
                info(f"Cursor state.vscdb文件不存在，需要先运行Cursor初始化")
            else:
                # 恢复每种类型的设置
                conn = sqlite3.connect(state_vscdb_path)
                cursor = conn.cursor()
                
                try:
                    # 遍历所有设置类型
                    for prefix, type_name in setting_types.items():
                        backup_file_path = os.path.join(get_app_data_dir(), "backups", "settings", f"{prefix.replace('.', '_')}_settings.json")
                        
                        if os.path.exists(backup_file_path):
                            try:
                                # 读取备份文件
                                with open(backup_file_path, 'r', encoding='utf-8') as f:
                                    settings_data = json.load(f)
                                
                                # 恢复每个键值对
                                updated_count = 0
                                for key, value in settings_data.items():
                                    try:
                                        # 检查键是否存在
                                        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key = ?", (key,))
                                        if cursor.fetchone()[0] > 0:
                                            # 更新现有记录
                                            cursor.execute("UPDATE ItemTable SET value = ? WHERE key = ?", (value, key))
                                        else:
                                            # 插入新记录
                                            cursor.execute("INSERT INTO ItemTable (key, value) VALUES (?, ?)", (key, value))
                                        updated_count += 1
                                    except Exception as e:
                                        info(f"恢复设置项 {key} 失败: {str(e)}")
                                
                                conn.commit()
                                info(f"已从备份恢复Cursor {type_name}设置，共 {updated_count} 项")
                                database_restore_success = True
                            except Exception as e:
                                info(f"恢复 {type_name}设置失败: {str(e)}")
                        else:
                            info(f"{type_name}设置备份文件不存在: {backup_file_path}")
                    
                    # 兼容原有恢复机制，单独处理特定两项设置
                    # 1. 恢复aicontext.personalContext（提示词）
                    context_backup_path = os.path.join(get_app_data_dir(), "backups", "settings", "personal_context.json")
                    if os.path.exists(context_backup_path):
                        try:
                            with open(context_backup_path, 'r', encoding='utf-8') as f:
                                context_value = f.read()
                            
                            cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key = 'aicontext.personalContext'")
                            if cursor.fetchone()[0] > 0:
                                cursor.execute("UPDATE ItemTable SET value = ? WHERE key = 'aicontext.personalContext'", 
                                              (context_value,))
                            else:
                                cursor.execute("INSERT INTO ItemTable (key, value) VALUES (?, ?)", 
                                              ('aicontext.personalContext', context_value))
                            conn.commit()
                            info(f"已从备份单独恢复Cursor提示词")
                            database_restore_success = True
                        except Exception as e:
                            info(f"单独恢复提示词失败: {str(e)}")
                    
                    # 2. 恢复Agent模式autoRun设置
                    agent_autorun_backup_path = os.path.join(get_app_data_dir(), "backups", "settings", "agent_autorun.json")
                    if os.path.exists(agent_autorun_backup_path):
                        try:
                            with open(agent_autorun_backup_path, 'r', encoding='utf-8') as f:
                                agent_data = json.load(f)
                                auto_run_value = agent_data.get("autoRun")
                            
                            if auto_run_value is not None:
                                cursor.execute("SELECT value FROM ItemTable WHERE key = 'src.vs.platform.reactivestorage.browser.reactiveStorageServiceImpl.persistentStorage.applicationUser'")
                                row = cursor.fetchone()
                                
                                if row:
                                    application_user_data = json.loads(row[0])
                                    
                                    if ('composerState' in application_user_data and 
                                        'modes4' in application_user_data['composerState']):
                                        
                                        modes4 = application_user_data['composerState']['modes4']
                                        agent_updated = False
                                        
                                        for i, mode in enumerate(modes4):
                                            if mode.get('id') == 'agent':
                                                application_user_data['composerState']['modes4'][i]['autoRun'] = auto_run_value
                                                agent_updated = True
                                                break
                                        
                                        if agent_updated:
                                            updated_value = json.dumps(application_user_data)
                                            cursor.execute("UPDATE ItemTable SET value = ? WHERE key = 'src.vs.platform.reactivestorage.browser.reactiveStorageServiceImpl.persistentStorage.applicationUser'", 
                                                          (updated_value,))
                                            conn.commit()
                                            info(f"已从备份单独恢复Cursor Agent模式autoRun设置为: {auto_run_value}")
                                            database_restore_success = True
                        except Exception as e:
                            info(f"单独恢复Agent模式设置失败: {str(e)}")
                
                except Exception as e:
                    info(f"恢复数据库设置总体失败: {str(e)}")
                finally:
                    cursor.close()
                    conn.close()
        
        # 获取自动启动设置
        auto_restart_setting = True  # 默认为True
        try:
            settings_file = os.path.join(get_app_data_dir(), "settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    auto_restart_setting = settings.get("auto_restart_cursor", True)
        except Exception as e:
            info(f"读取设置时出错: {str(e)}")
        
        # 检查恢复完整性
        if not settings_restore_success and not database_restore_success:
            # 如果两个都失败，返回失败
            return {"result": "fail", "message": "恢复Cursor设置失败：settings.json和数据库设置都无法恢复"}
        elif not settings_restore_success:
            # 如果settings.json恢复失败但数据库恢复成功，记录警告但继续
            info("警告：settings.json恢复失败，但数据库设置恢复成功")
        elif not database_restore_success:
            # 如果数据库恢复失败但settings.json恢复成功，记录警告但继续
            info("警告：数据库设置恢复失败，但settings.json恢复成功")

        # 至少有一项恢复成功，根据设置和cursor_killed状态返回不同的结果
        if cursor_killed and auto_restart_setting:
            return {"result": "success_restart", "message": ""}
        elif cursor_killed and not auto_restart_setting:
            return {"result": "success_no_restart", "message": ""}
        else:
            return {"result": "success", "message": ""}
    
    def _get_cursor_history_path(self):
        """获取当前系统Cursor History文件夹路径"""
        system = platform.system().lower()
        if "windows" in system:
            return os.path.join(os.getenv("APPDATA"), "Cursor", "User", "History")
        elif "darwin" in system:
            return os.path.expanduser("~/Library/Application Support/Cursor/User/History")
        else:  # Linux系统
            cursor_dir_name = self._get_linux_cursor_dirname()
            if cursor_dir_name:
                return os.path.expanduser(f"~/.config/{cursor_dir_name}/User/History")
            else:
                info("无法找到Linux系统下的Cursor目录")
                return None
    
    def _get_cursor_global_storage_path(self):
        """获取当前系统Cursor globalStorage文件夹路径"""
        system = platform.system().lower()
        if "windows" in system:
            return os.path.join(os.getenv("APPDATA"), "Cursor", "User", "globalStorage")
        elif "darwin" in system:
            return os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage")
        else:  # Linux系统
            cursor_dir_name = self._get_linux_cursor_dirname()
            if cursor_dir_name:
                return os.path.expanduser(f"~/.config/{cursor_dir_name}/User/globalStorage")
            else:
                info("无法找到Linux系统下的Cursor目录")
                return None

    def _backup_cursor_workspace(self):
        """备份Cursor会话记录和History"""
        from logger import info, error
        try:
            # 创建备份目录
            backup_dir = os.path.join(get_app_data_dir(), "backups", "workspaceStorage")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 检查备份目录是否创建成功
            if not os.path.exists(backup_dir):
                error(f"无法创建备份目录: {backup_dir}")
                return False
            
            # 获取workspaceStorage路径
            workspace_path = self._get_cursor_workspace_storage_path()
            if not workspace_path:
                error("无法获取Cursor会话记录文件夹路径")
                return False
                
            # 检查路径是否真实存在
            if not os.path.exists(workspace_path):
                error(f"Cursor会话记录文件夹不存在: {workspace_path}")
                # 尝试进行目录列举，记录详细信息
                try:
                    parent_dir = os.path.dirname(workspace_path)
                    if os.path.exists(parent_dir):
                        info(f"父目录存在: {parent_dir}")
                        files = os.listdir(parent_dir)
                        info(f"父目录内容: {files}")
                    else:
                        info(f"父目录不存在: {parent_dir}")
                except Exception as e:
                    info(f"列举目录内容时出错: {e}")
                return False
            
            # 获取History路径
            history_path = self._get_cursor_history_path()
            history_exists = history_path and os.path.exists(history_path)
            if not history_exists:
                info("Cursor History文件夹不存在，将只备份workspaceStorage")
            
            # 获取globalStorage路径
            global_storage_path = self._get_cursor_global_storage_path()
            state_vscdb_path = os.path.join(global_storage_path, "state.vscdb") if global_storage_path else None
            state_vscdb_exists = state_vscdb_path and os.path.exists(state_vscdb_path)
            
            # 开始备份workspaceStorage
            workspace_backup_file = os.path.join(backup_dir, "workspaceStorage.zip")
            
            # 检查workspaceStorage内容
            workspace_files_count = 0
            for root, dirs, files in os.walk(workspace_path):
                workspace_files_count += len(files)
            
            if workspace_files_count == 0:
                info(f"警告：workspaceStorage目录为空，可能没有会话记录可备份")
            
            with zipfile.ZipFile(workspace_backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 将workspaceStorage文件夹内容添加到压缩文件
                for root, dirs, files in os.walk(workspace_path):
                    for file in files:
                        try:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, os.path.dirname(workspace_path))
                            zipf.write(file_path, arcname)
                        except Exception as e:
                            error(f"备份文件失败: {file_path}, 错误: {e}")
            
            # 如果History文件夹存在，则备份
            if history_exists:
                history_backup_file = os.path.join(backup_dir, "History.zip")
                with zipfile.ZipFile(history_backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # 将History文件夹内容添加到压缩文件
                    for root, dirs, files in os.walk(history_path):
                        for file in files:
                            try:
                                file_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_path, os.path.dirname(history_path))
                                zipf.write(file_path, arcname)
                            except Exception as e:
                                error(f"备份History文件失败: {file_path}, 错误: {e}")
            
            # 如果state.vscdb文件存在，则备份cursorDiskKV表
            cursor_disk_kv_backup_file = os.path.join(backup_dir, "cursorDiskKV.json")
            cursor_disk_kv_backup_success = False
            
            if state_vscdb_exists:
                try:
                    # 连接数据库
                    conn = sqlite3.connect(state_vscdb_path)
                    cursor = conn.cursor()
                    
                    # 检查cursorDiskKV表是否存在
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cursorDiskKV'")
                    table_exists = cursor.fetchone() is not None
                    
                    if not table_exists:
                        info("cursorDiskKV表不存在，无法备份")
                    else:
                        # 查询表结构
                        cursor.execute("PRAGMA table_info(cursorDiskKV)")
                        columns = [column[1] for column in cursor.fetchall()]
                        
                        # 查询所有数据
                        cursor.execute("SELECT * FROM cursorDiskKV")
                        rows = cursor.fetchall()
                        
                        # 将数据转换为字典列表
                        table_data = []
                        for row in rows:
                            row_dict = {}
                            for i, column in enumerate(columns):
                                row_dict[column] = row[i]
                            table_data.append(row_dict)
                        
                        # 保存为JSON文件
                        with open(cursor_disk_kv_backup_file, 'w', encoding='utf-8') as f:
                            json.dump({
                                'table_name': 'cursorDiskKV',
                                'columns': columns,
                                'data': table_data
                            }, f, ensure_ascii=False, indent=2)
                        
                        info(f"已备份Cursor cursorDiskKV表数据到: {cursor_disk_kv_backup_file}")
                        cursor_disk_kv_backup_success = True
                except Exception as e:
                    error(f"备份cursorDiskKV表失败: {str(e)}")
                finally:
                    if 'conn' in locals() and conn:
                        cursor.close()
                        conn.close()
            else:
                info("Cursor state.vscdb文件不存在，跳过备份cursorDiskKV表")
            
            info(f"已备份Cursor会话记录到: {workspace_backup_file}")
            if history_exists:
                info(f"已备份Cursor History到: {os.path.join(backup_dir, 'History.zip')}")

            # 检查备份完整性：确保所有重要文件都备份成功
            backup_success = True
            backup_errors = []

            # 检查workspaceStorage备份
            if not os.path.exists(workspace_backup_file) or os.path.getsize(workspace_backup_file) == 0:
                error("workspaceStorage备份文件创建失败或为空文件")
                backup_success = False
                backup_errors.append("workspaceStorage备份失败")

            # 检查History备份（如果原始文件存在）
            if history_exists:
                history_backup_file = os.path.join(backup_dir, "History.zip")
                if not os.path.exists(history_backup_file) or os.path.getsize(history_backup_file) == 0:
                    error("History备份文件创建失败或为空文件")
                    backup_success = False
                    backup_errors.append("History备份失败")

            # 检查cursorDiskKV备份（如果原始文件存在）
            if state_vscdb_exists and cursor_disk_kv_backup_success:
                if not os.path.exists(cursor_disk_kv_backup_file) or os.path.getsize(cursor_disk_kv_backup_file) == 0:
                    error("cursorDiskKV备份文件创建失败或为空文件")
                    backup_success = False
                    backup_errors.append("cursorDiskKV备份失败")

            if not backup_success:
                error(f"备份未完全成功，失败项目: {', '.join(backup_errors)}")
                return False

            return True
                
        except OSError as e:
            error(f"备份Cursor会话记录和History失败 (操作系统错误): {str(e)}")
            # 提供更详细的错误信息
            if e.errno == 2:  # 文件或目录不存在
                error(f"指定的文件或目录不存在 (错误代码: {e.errno})")
            elif e.errno == 13:  # 权限被拒绝
                error(f"权限被拒绝，请检查访问权限 (错误代码: {e.errno})")
            elif e.errno == 22:  # 无效参数
                error(f"无效的参数 (错误代码: {e.errno})")
            return False
        except Exception as e:
            error(f"备份Cursor会话记录和History失败 (一般错误): {str(e)}")
            # 获取更详细的错误信息
            import traceback
            error(f"错误详情: {traceback.format_exc()}")
            return False
    
    def _restore_cursor_workspace(self):
        """从备份恢复Cursor会话记录和History"""
        # 先终止Cursor进程
        info("开始关闭Cursor进程，准备恢复备份")
        cursor_killed = False
        
        try:
            # 调用主窗口的kill_cursor_process方法
            cursor_killed = self.window().kill_cursor_process()
            if cursor_killed:
                info("成功关闭Cursor进程")
                # 等待2秒确保进程完全终止
                info("等待2秒确保进程完全终止...")
                time.sleep(2)
            else:
                info("未检测到运行中的Cursor进程，或关闭失败")
        except Exception as e:
            error_msg = str(e)
            info(f"终止Cursor进程失败: {error_msg}")
            # 继续执行，不影响恢复流程
        
        # 备份文件路径
        backup_dir = os.path.join(get_app_data_dir(), "backups", "workspaceStorage")
        workspace_backup_file = os.path.join(backup_dir, "workspaceStorage.zip")
        history_backup_file = os.path.join(backup_dir, "History.zip")
        cursor_disk_kv_backup_file = os.path.join(backup_dir, "cursorDiskKV.json")
        
        # 检查workspaceStorage备份是否存在
        if not os.path.exists(workspace_backup_file):
            info(f"Cursor会话记录备份文件不存在: {workspace_backup_file}")
            return {"result": "fail", "message": "备份文件不存在"}
        
        # 检查History备份是否存在
        history_backup_exists = os.path.exists(history_backup_file)
        if not history_backup_exists:
            info(f"Cursor History备份文件不存在，将只恢复workspaceStorage")
        
        # 检查cursorDiskKV备份是否存在
        cursor_disk_kv_backup_exists = os.path.exists(cursor_disk_kv_backup_file)
        if not cursor_disk_kv_backup_exists:
            info(f"Cursor cursorDiskKV备份文件不存在，将跳过恢复此表")
        
        # 获取目标目录
        workspace_path = self._get_cursor_workspace_storage_path()
        if not workspace_path:
            info("无法获取Cursor会话记录路径")
            return {"result": "fail", "message": "无法获取Cursor会话记录路径"}
        
        # 获取History目标路径
        history_path = self._get_cursor_history_path()
        if history_backup_exists and not history_path:
            info("无法获取Cursor History路径")
            return {"result": "fail", "message": "无法获取Cursor History路径"}
        
        # 获取state.vscdb目标路径
        global_storage_path = self._get_cursor_global_storage_path()
        state_vscdb_path = os.path.join(global_storage_path, "state.vscdb") if global_storage_path else None
        if cursor_disk_kv_backup_exists and not state_vscdb_path:
            info("无法获取Cursor state.vscdb路径")
            return {"result": "fail", "message": "无法获取Cursor state.vscdb路径"}
        
        try:
            # 恢复workspaceStorage - 完整替换
            # 确保父目录存在
            parent_dir = os.path.dirname(workspace_path)
            os.makedirs(parent_dir, exist_ok=True)
            
            # 如果目标父目录不存在，标记为失败
            if not os.path.exists(parent_dir):
                info(f"无法创建Cursor会话记录父目录，恢复失败")
                return {"result": "fail", "message": "无法创建目标父目录"}
            
            # 如果目标目录已存在，先删除
            if os.path.exists(workspace_path):
                shutil.rmtree(workspace_path)
                info(f"已删除原Cursor会话记录目录，准备恢复")
            
            # 解压备份文件到父目录，实现完整替换
            with zipfile.ZipFile(workspace_backup_file, 'r') as zipf:
                zipf.extractall(parent_dir)
            
            # 确认目录已恢复
            if not os.path.exists(workspace_path):
                info(f"恢复失败：解压后目标目录不存在")
                return {"result": "fail", "message": "解压后目标目录不存在"}
                
            info(f"已从备份完整替换 Cursor会话记录: {workspace_path}")
            
            # 如果有History备份，也进行完整替换
            if history_backup_exists and history_path:
                # 确保目标目录的父目录存在
                history_parent_dir = os.path.dirname(history_path)
                os.makedirs(history_parent_dir, exist_ok=True)
                
                # 如果目标目录已存在，先删除
                if os.path.exists(history_path):
                    shutil.rmtree(history_path)
                    info(f"已删除原Cursor History目录，准备恢复")
                
                # 解压备份文件到父目录，实现完整替换
                with zipfile.ZipFile(history_backup_file, 'r') as zipf:
                    zipf.extractall(history_parent_dir)
                
                # 确认目录已恢复
                if not os.path.exists(history_path):
                    info(f"警告：History解压后目标目录不存在")
                else:
                    info(f"已从备份完整替换 Cursor History: {history_path}")
            
            # 如果有cursorDiskKV备份，也进行恢复
            if cursor_disk_kv_backup_exists and state_vscdb_path and global_storage_path:
                # 确保目标目录存在
                os.makedirs(global_storage_path, exist_ok=True)
                
                # 如果目标目录创建失败，则跳过cursorDiskKV恢复
                if not os.path.exists(global_storage_path):
                    info(f"无法创建Cursor globalStorage目录，跳过cursorDiskKV恢复")
                else:
                    try:
                        # 读取备份的JSON数据
                        with open(cursor_disk_kv_backup_file, 'r', encoding='utf-8') as f:
                            backup_data = json.load(f)
                        
                        # 验证备份数据格式
                        if not all(k in backup_data for k in ['table_name', 'columns', 'data']):
                            info("cursorDiskKV备份数据格式错误，跳过恢复")
                        else:
                            # 连接数据库
                            conn = sqlite3.connect(state_vscdb_path)
                            cursor = conn.cursor()
                            
                            try:
                                # 检查表是否存在，如果不存在则创建表
                                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cursorDiskKV'")
                                if cursor.fetchone() is None:
                                    # 创建表
                                    columns = backup_data['columns']
                                    column_defs = ', '.join([f'"{col}" TEXT' for col in columns])
                                    cursor.execute(f'CREATE TABLE "cursorDiskKV" ({column_defs})')
                                    info("创建了新的cursorDiskKV表")
                                else:
                                    # 清空现有表中的所有数据
                                    cursor.execute('DELETE FROM "cursorDiskKV"')
                                    info("清空了现有的cursorDiskKV表数据")
                                
                                # 恢复数据
                                for row_data in backup_data['data']:
                                    columns = list(row_data.keys())
                                    placeholders = ', '.join(['?' for _ in columns])
                                    column_names = ', '.join([f'"{col}"' for col in columns])
                                    values = [row_data[col] for col in columns]
                                    
                                    cursor.execute(f'INSERT INTO "cursorDiskKV" ({column_names}) VALUES ({placeholders})', values)
                                
                                # 提交事务
                                conn.commit()
                                info(f"已从备份恢复Cursor cursorDiskKV表数据")
                            except Exception as e:
                                info(f"恢复cursorDiskKV表数据失败: {str(e)}")
                                conn.rollback()  # 回滚事务
                            finally:
                                cursor.close()
                                conn.close()
                    except Exception as e:
                        info(f"读取或处理cursorDiskKV备份数据失败: {str(e)}")
            
            # 检查恢复完整性
            restore_success = True
            restore_errors = []

            # 检查workspaceStorage是否恢复成功
            if not os.path.exists(workspace_path):
                restore_success = False
                restore_errors.append("workspaceStorage恢复失败")

            # 检查History是否恢复成功（如果有备份）
            if history_backup_exists and history_path:
                if not os.path.exists(history_path):
                    restore_success = False
                    restore_errors.append("History恢复失败")

            # 检查cursorDiskKV是否恢复成功（如果有备份）
            if cursor_disk_kv_backup_exists and state_vscdb_path:
                try:
                    # 检查数据库中是否有cursorDiskKV表的数据
                    conn = sqlite3.connect(state_vscdb_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM cursorDiskKV")
                    count = cursor.fetchone()[0]
                    cursor.close()
                    conn.close()
                    if count == 0:
                        info("警告：cursorDiskKV表恢复后为空")
                except Exception as e:
                    info(f"检查cursorDiskKV恢复状态失败: {str(e)}")
                    restore_success = False
                    restore_errors.append("cursorDiskKV恢复检查失败")

            if not restore_success:
                error_msg = f"恢复未完全成功，失败项目: {', '.join(restore_errors)}"
                info(error_msg)
                return {"result": "fail", "message": error_msg}

            # 获取自动启动设置
            auto_restart_setting = True  # 默认为True
            try:
                settings_file = os.path.join(get_app_data_dir(), "settings.json")
                if os.path.exists(settings_file):
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        auto_restart_setting = settings.get("auto_restart_cursor", True)
            except Exception as e:
                info(f"读取设置时出错: {str(e)}")

            # 根据设置和cursor_killed状态返回不同的结果
            if cursor_killed and auto_restart_setting:
                return {"result": "success_restart", "message": ""}
            elif cursor_killed and not auto_restart_setting:
                return {"result": "success_no_restart", "message": ""}
            else:
                return {"result": "success", "message": ""}
            
        except Exception as e:
            error_msg = str(e)
            info(f"恢复Cursor会话记录失败: {error_msg}")
            return {"result": "fail", "message": error_msg}
            
    def _has_workspace_backup(self):
        """检查是否有会话记录备份"""
        backup_dir = os.path.join(get_app_data_dir(), "backups", "workspaceStorage")
        workspace_backup_file = os.path.join(backup_dir, "workspaceStorage.zip")
        cursor_disk_kv_backup_file = os.path.join(backup_dir, "cursorDiskKV.json")
        # 只要workspaceStorage备份存在就返回True，因为cursorDiskKV备份是可选的
        return os.path.exists(workspace_backup_file)

    def _on_proxy_changed(self):
        proxy_value = self.cursor_proxy_input.text().strip()
        # 仅合法时才写入 (为空也视为合法)
        if proxy_value and not self._proxy_regex.match(proxy_value):
            self.window().show_toast("代理格式不合法，未保存", error=True)
            return
        success = self._set_proxy_setting(proxy_value)
        if success:
            if proxy_value:
                info(f"已保存Cursor代理设置：{proxy_value}")
                self.window().show_toast(f"已保存Cursor代理设置: {proxy_value}")
                # 新增：保存到历史
                self._add_proxy_to_history(proxy_value)
            else:
                info("已清除Cursor代理设置")
                self.window().show_toast("已清除Cursor代理设置")
        else:
            info("保存Cursor代理设置失败")
            self.window().show_toast("保存Cursor代理设置失败", error=True)
    
    def _on_auto_update_switch(self, state):
        """自动更新开关状态变化时的处理函数"""
        success = self._set_auto_update_setting(state)
        
        if success:
            if state:
                info("已关闭Cursor自动更新")
                self.window().show_toast("已关闭Cursor自动更新")
            else:
                info("已恢复Cursor自动更新默认设置")
                self.window().show_toast("已恢复Cursor自动更新默认设置")
    
    def _on_disable_http2_switch(self, state):
        """禁用HTTP2开关状态变化时的处理函数"""
        success = self._set_disable_http2_setting(state)
        
        if success:
            if state:
                info("已禁用Cursor HTTP2")
                self.window().show_toast("已禁用Cursor HTTP2")
            else:
                info("已启用Cursor HTTP2")
                self.window().show_toast("已启用Cursor HTTP2")
                
    def _has_workbench_backup(self):
        """检查是否存在workbench.desktop.main.js备份文件（同步方法）
        
        Returns:
            bool: 是否存在备份文件
        """
        # 获取备份文件路径但不创建目录
        backup_path = self._get_workbench_backup_path(create=False)
        # 只有当路径存在且文件存在时返回True
        return backup_path and os.path.exists(backup_path)
        
    def _check_workbench_backup_async(self, callback):
        """异步检查是否存在workbench.desktop.main.js备份文件
        
        Args:
            callback (function): 回调函数，接收状态参数
        """
        self.unlimited_max_check_worker = UnlimitedMaxWorker('check', self)
        self.unlimited_max_check_worker.status_checked.connect(callback)
        self.unlimited_max_check_worker.start()
        
    def _on_unlimited_max_switch(self, state):
        """开启无限MAX开关状态变化时的处理函数"""
        from logger import info
        
        # 保存设置
        self._set_unlimited_max_setting(state)
        
        # 如果是页面切换时自动设置状态，不进行后续操作
        sender = self.sender()
        if sender is None:
            return
        
        # 禁用开关，防止连续点击
        self.cursor_unlimited_max_switch.setEnabled(False)
        
        # 显示操作进行中的状态
        self.window().show_toast("正在处理，请稍候...", error=False)
        
        # 创建并启动工作线程
        self.unlimited_max_worker = UnlimitedMaxWorker('enable' if state else 'disable', self)
        
        # 连接信号
        self.unlimited_max_worker.message.connect(
            lambda msg: self.window().show_toast(msg, error=False)
        )
        self.unlimited_max_worker.error.connect(
            lambda msg: self.window().show_toast(msg, error=True)
        )
        
        # 处理完成的回调
        def on_finished(success, is_enable, cursor_killed):
            # 重新启用开关
            self.cursor_unlimited_max_switch.setEnabled(True)
            
            # 如果操作失败，恢复开关状态到操作前的状态
            if not success:
                # 阻止信号触发避免循环调用
                self.cursor_unlimited_max_switch.blockSignals(True)
                if is_enable:
                    # 开启操作失败，将开关恢复为关闭状态
                    self.cursor_unlimited_max_switch.setChecked(False)
                    info("开启无限MAX失败，已恢复开关状态为关闭")
                else:
                    # 关闭操作失败，将开关恢复为开启状态
                    self.cursor_unlimited_max_switch.setChecked(True)
                    info("关闭无限MAX失败，已恢复开关状态为开启")
                # 恢复信号响应
                self.cursor_unlimited_max_switch.blockSignals(False)
            
            # 根据设置判断是否自动重启Cursor
            if cursor_killed:
                # 获取自动启动设置
                auto_restart_setting = True  # 默认为True
                try:
                    from utils import get_app_data_dir
                    settings_file = os.path.join(get_app_data_dir(), "settings.json")
                    if os.path.exists(settings_file):
                        with open(settings_file, 'r', encoding='utf-8') as f:
                            settings = json.load(f)
                            auto_restart_setting = settings.get("auto_restart_cursor", True)
                except Exception as e:
                    info(f"读取重启设置时出错: {str(e)}")
                
                # 如果设置为自动重启，则重启Cursor
                if auto_restart_setting:
                    info("根据设置自动重启Cursor")
                    QTimer.singleShot(500, lambda: self.window().start_cursor_app())
        
        # 连接完成信号
        self.unlimited_max_worker.finished.connect(on_finished)
        
        # 启动工作线程
        self.unlimited_max_worker.start()
    
    def _on_backup_settings(self):
        """备份设置按钮点击处理函数（后台线程版）"""
        self.cursor_backup_worker = CursorBackupWorker('settings', 'backup', parent=self)
        self.cursor_restore_settings_btn.setEnabled(False)
        backup_btn = self.sender() if isinstance(self.sender(), QPushButton) else None
        if backup_btn:
            backup_btn.setText("备份中...")
        else:
            backup_btn = None
        self.cursor_backup_worker.message.connect(lambda msg: self.window().show_toast(msg))
        self.cursor_backup_worker.error.connect(lambda msg: self.window().show_toast(msg, error=True))
        def on_finish(success, msg):
            self.window().show_toast(msg, error=not success)
            self.cursor_restore_settings_btn.setEnabled(True)
            if backup_btn:
                backup_btn.setText("备份")
        self.cursor_backup_worker.finished.connect(on_finish)
        self.cursor_backup_worker.start()

    def _on_restore_settings(self):
        """恢复设置按钮点击处理函数（后台线程版）"""
        if not self._has_settings_backup():
            self.window().show_toast("没有可用的Cursor设置备份", error=True)
            return
        if StyledDialog.showConfirmDialog(
            self.window(), "恢复Cursor设置", "确定要恢复 Cursor 的设置吗？\n\n恢复后当前的设置将被覆盖"):
            self.cursor_backup_worker = CursorBackupWorker('settings', 'restore', parent=self)
            self.cursor_restore_settings_btn.setEnabled(False)
            self.cursor_restore_settings_btn.setText("恢复中...")
            self.cursor_backup_worker.message.connect(lambda msg: self.window().show_toast(msg))
            self.cursor_backup_worker.error.connect(lambda msg: self.window().show_toast(msg, error=True))
            def on_finish(success, msg):
                self.window().show_toast(msg, error=not success)
                self._refresh_cursor_settings_ui()
                self.cursor_restore_settings_btn.setEnabled(True)
                self.cursor_restore_settings_btn.setText("恢复")
            self.cursor_backup_worker.finished.connect(on_finish)
            self.cursor_backup_worker.start()

    def _on_backup_workspace(self):
        """备份工作区按钮点击处理函数（后台线程版）"""
        self.cursor_backup_worker = CursorBackupWorker('workspace', 'backup', parent=self)
        self.cursor_restore_workspace_btn.setEnabled(False)
        backup_btn = self.sender() if isinstance(self.sender(), QPushButton) else None
        if backup_btn:
            backup_btn.setText("备份中...")
        else:
            backup_btn = None
        self.cursor_backup_worker.message.connect(lambda msg: self.window().show_toast(msg))
        self.cursor_backup_worker.error.connect(lambda msg: self.window().show_toast(msg, error=True))
        def on_finish(success, msg):
            self.window().show_toast(msg, error=not success)
            self.cursor_restore_workspace_btn.setEnabled(True)
            if backup_btn:
                backup_btn.setText("备份")
        self.cursor_backup_worker.finished.connect(on_finish)
        self.cursor_backup_worker.start()

    def _on_restore_workspace(self):
        """恢复工作区按钮点击处理函数（后台线程版）"""
        if not self._has_workspace_backup():
            self.window().show_toast("没有可用的Cursor会话记录备份", error=True)
            return
        if StyledDialog.showConfirmDialog(
            self.window(), "恢复Cursor会话记录", "确定要恢复Cursor会话记录吗？\n\n恢复后，会话会被还原到备份时的状态"):
            self.cursor_backup_worker = CursorBackupWorker('workspace', 'restore', parent=self)
            self.cursor_restore_workspace_btn.setEnabled(False)
            self.cursor_restore_workspace_btn.setText("恢复中...")
            self.cursor_backup_worker.message.connect(lambda msg: self.window().show_toast(msg))
            self.cursor_backup_worker.error.connect(lambda msg: self.window().show_toast(msg, error=True))
            def on_finish(success, msg):
                self.window().show_toast(msg, error=not success)
                self.cursor_restore_workspace_btn.setEnabled(True)
                self.cursor_restore_workspace_btn.setText("恢复")
            self.cursor_backup_worker.finished.connect(on_finish)
            self.cursor_backup_worker.start()

    def _on_initialize_cursor(self):
        """初始化Cursor按钮点击处理函数"""
        # 使用 StyledDialog 替换 Utils.confirm_message 以获得遮罩和居中效果
        if StyledDialog.showConfirmDialog(
            self.window(), # parent
            "初始化Cursor", # title
            "该操作为危险操作，Cursor会直接变成刚安装完的样子，有需要请先做好备份\n\n你确定要继续吗？", # text
            confirm_color=Theme.ERROR # 确认按钮使用红色
        ):
            # 获取自动启动设置
            auto_restart_setting = True  # 默认为True
            try:
                settings_file = os.path.join(get_app_data_dir(), "settings.json")
                if os.path.exists(settings_file):
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        auto_restart_setting = settings.get("auto_restart_cursor", True)
            except Exception as e:
                info(f"读取设置时出错: {str(e)}")
            
            # 执行初始化操作
            result, error_msg = self._initialize_cursor(auto_restart_setting)
            
            if result == "success":
                info("成功初始化Cursor")
                self.window().show_toast("成功初始化Cursor")
            elif result == "success_restart":
                info("成功初始化Cursor，正在重启Cursor...")
                self.window().show_toast("成功初始化Cursor，正在重启Cursor...")
                # 延迟1.5秒后启动Cursor
                QTimer.singleShot(1500, lambda: self.window().start_cursor_app())
            elif result == "success_no_restart":
                info("成功初始化Cursor，请手动启动Cursor")
                self.window().show_toast("成功初始化Cursor，请手动启动Cursor以确保YCursor的部分功能正常运行")
            elif result == "fail_restart":
                info(f"初始化Cursor成功，但启动失败: {error_msg}")
                self.window().show_toast(f"初始化Cursor成功，但无法启动Cursor: {error_msg}，请手动启动", error=True)
            else:  # fail
                info(f"初始化Cursor失败: {error_msg}")
                self.window().show_toast(f"初始化Cursor失败: {error_msg}", error=True)
    
    def _initialize_cursor(self, auto_restart=True):
        """初始化Cursor
        
        Args:
            auto_restart: 是否自动重启Cursor
            
        Returns:
            tuple: (结果状态, 错误消息)
                结果状态可能是: "success", "success_restart", "success_no_restart", "fail_restart", "fail"
        """
        from logger import info
        
        # 获取初始化方案设置
        initialize_method = self._get_initialize_method_setting()
        
        # 方案一：只删除state.vscdb文件
        if initialize_method == 1:
            state_vscdb_path = self._get_state_vscdb_path()
            
            if not state_vscdb_path or not os.path.exists(state_vscdb_path):
                info(f"state.vscdb文件不存在: {state_vscdb_path}")
                return "fail", "state.vscdb文件不存在"
                
            # 先终止Cursor进程
            info("开始关闭Cursor进程，准备初始化")
            cursor_killed = False
            
            try:
                # 调用主窗口的kill_cursor_process方法
                cursor_killed = self.window().kill_cursor_process()
                if cursor_killed:
                    info("成功关闭Cursor进程")
                    # 等待2秒确保进程完全终止
                    info("等待2秒确保进程完全终止...")
                    time.sleep(2)
                else:
                    info("未检测到运行中的Cursor进程，或关闭失败")
            except Exception as e:
                error_msg = str(e)
                info(f"终止Cursor进程失败: {error_msg}")
                # 继续执行，不影响初始化流程
            
            try:
                # 删除state.vscdb文件
                os.remove(state_vscdb_path)
                
                info(f"已初始化Cursor(特殊)")
                
                # 根据设置决定是否自动重启
                if auto_restart:
                    info("根据设置将自动重启Cursor")
                    return "success_restart", ""
                else:
                    info("根据设置不自动重启Cursor")
                    return "success_no_restart", ""
                    
            except Exception as e:
                error_msg = str(e)
                info(f"初始化Cursor失败: {error_msg}")
                return "fail", error_msg
        # 方案二：删除整个Cursor文件夹
        else:
            cursor_path = self._get_cursor_root_path()
            
            if not cursor_path or not os.path.exists(cursor_path):
                info(f"Cursor文件夹不存在: {cursor_path}")
                return "fail", "Cursor文件夹不存在"
                
            # 先终止Cursor进程
            info("开始关闭Cursor进程，准备初始化")
            cursor_killed = False
            
            try:
                # 调用主窗口的kill_cursor_process方法
                cursor_killed = self.window().kill_cursor_process()
                if cursor_killed:
                    info("成功关闭Cursor进程")
                    # 等待2秒确保进程完全终止
                    info("等待2秒确保进程完全终止...")
                    time.sleep(2)
                else:
                    info("未检测到运行中的Cursor进程，或关闭失败")
            except Exception as e:
                error_msg = str(e)
                info(f"终止Cursor进程失败: {error_msg}")
                # 继续执行，不影响初始化流程
            
            try:
                # 删除整个文件夹
                shutil.rmtree(cursor_path)
                
                info(f"已初始化Cursor(完整)")
                
                # 根据设置决定是否自动重启
                if auto_restart:
                    info("根据设置将自动重启Cursor")
                    return "success_restart", ""
                else:
                    info("根据设置不自动重启Cursor")
                    return "success_no_restart", ""
                    
            except Exception as e:
                error_msg = str(e)
                info(f"初始化Cursor失败: {error_msg}")
                return "fail", error_msg
    
    def _refresh_cursor_settings_ui(self):
        """刷新Cursor设置UI状态"""
        # 更新代理设置
        self.cursor_proxy_input.setText(self._get_proxy_setting())
        
        # 更新自动更新开关
        self.cursor_auto_update_switch.setChecked(self._get_auto_update_setting())
        
        # 更新禁用HTTP2开关
        self.cursor_disable_http2_switch.setChecked(self._get_disable_http2_setting())
        
        # 异步更新无限MAX开关状态
        def update_switch_status(has_backup):
            # 更新开关状态但不触发事件
            self.cursor_unlimited_max_switch.blockSignals(True)
            self.cursor_unlimited_max_switch.setChecked(has_backup)
            self.cursor_unlimited_max_switch.blockSignals(False)
        
        # 启动异步检查
        self._check_workbench_backup_async(update_switch_status)
        
        # 更新恢复按钮状态
        self.cursor_restore_settings_btn.setEnabled(self._has_settings_backup())
        self.cursor_restore_workspace_btn.setEnabled(self._has_workspace_backup())
        
        # 更新保存当前登录账户按钮的可见性
        self._update_save_button_visibility()

    def _get_initialize_method_setting(self):
        """获取初始化方案设置"""
        try:
            from utils import get_app_data_dir
            function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
            
            # 如果文件存在，读取配置
            if os.path.exists(function_config_file):
                with open(function_config_file, 'r', encoding='utf-8') as f:
                    function_settings = json.load(f)
                    # 兼容旧配置，添加默认值
                    function_settings.setdefault("initialize_method", 1) 
                    function_settings.setdefault("register_only", False) 
                    function_settings.setdefault("register_method", 1)
                    return function_settings.get("initialize_method", 1)  # 默认为方案一
            
            # 如果文件不存在或配置不完整，创建/更新默认配置
            function_settings = {
                "register_only": False,  # 默认为False，即正常更新认证信息
                "register_method": 1,    # 默认为方案一
                "initialize_method": 1    # 默认为方案一
            }
            
            # 确保目录存在
            os.makedirs(os.path.dirname(function_config_file), exist_ok=True)
            # 保存默认配置
            with open(function_config_file, 'w', encoding='utf-8') as f:
                json.dump(function_settings, f, ensure_ascii=False, indent=2)
            
            return 1  # 默认为方案一
        except Exception as e:
            print(f"获取初始化方案设置时出错: {str(e)}")
            return 1  # 出错时默认为方案一

    def _get_custom_cursor_path_settings(self):
        """获取自定义 Cursor 路径设置"""
        enabled = False
        path = ""
        try:
            from utils import get_app_data_dir
            function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
            
            if os.path.exists(function_config_file):
                with open(function_config_file, 'r', encoding='utf-8') as f:
                    function_settings = json.load(f)
                    enabled = function_settings.get("custom_cursor_path_enabled", False)
                    path = function_settings.get("custom_cursor_path", "")
        except Exception as e:
            print(f"获取自定义 Cursor 路径设置时出错: {str(e)}")
            # 出错时返回默认值
        return enabled, path

    def _save_custom_cursor_path_settings(self, enabled, path):
        """保存自定义 Cursor 路径设置"""
        try:
            from utils import get_app_data_dir
            function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(function_config_file), exist_ok=True)
            
            # 读取当前配置，如果文件不存在则创建一个空字典
            function_settings = {}
            if os.path.exists(function_config_file):
                try:
                    with open(function_config_file, 'r', encoding='utf-8') as f:
                        function_settings = json.load(f)
                except json.JSONDecodeError:
                    print(f"警告: {function_config_file} 文件格式错误，将重新创建")
                    function_settings = {} # 如果文件损坏，则重置
            
            # 更新自定义路径设置
            function_settings["custom_cursor_path_enabled"] = enabled
            function_settings["custom_cursor_path"] = path
            
            # 保存配置
            with open(function_config_file, 'w', encoding='utf-8') as f:
                json.dump(function_settings, f, ensure_ascii=False, indent=2)
            info(f"已更新自定义 Cursor 路径配置: enabled={enabled}, path='{path}'")
        except Exception as e:
            print(f"保存自定义 Cursor 路径设置时出错: {str(e)}")
            # 可以考虑在这里添加错误提示给用户
            self.toast_request.emit("保存自定义路径配置失败", "error")
            
    def _handle_initialize_method_selection(self, method):
        """处理初始化方案选择"""
        try:
            # 更新按钮状态，确保互斥选择
            for i, btn in enumerate(self.initialize_method_buttons):
                # 索引0对应方案一，索引1对应方案二
                method_number = i + 1
                if method_number == method:
                    btn.setChecked(True)
                else:
                    btn.setChecked(False)
            
            # 保存设置到配置文件
            from utils import get_app_data_dir
            function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(function_config_file), exist_ok=True)
            
            # 读取当前配置
            function_settings = {}
            if os.path.exists(function_config_file):
                with open(function_config_file, 'r', encoding='utf-8') as f:
                    function_settings = json.load(f)
            
            # 更新初始化方案设置
            function_settings["initialize_method"] = method
            
            # 保存配置
            with open(function_config_file, 'w', encoding='utf-8') as f:
                json.dump(function_settings, f, ensure_ascii=False, indent=2)
            
            # 记录日志
            info(f"已更新初始化Cursor功能配置：初始化方案 = {method}")
        except Exception as e:
            print(f"处理初始化方案选择时出错: {str(e)}")

    def _get_state_vscdb_path(self):
        """获取当前系统Cursor state.vscdb文件路径"""
        global_storage_path = self._get_cursor_global_storage_path()
        if global_storage_path and os.path.exists(global_storage_path):
            state_vscdb_path = os.path.join(global_storage_path, "state.vscdb")
            return state_vscdb_path
        return None

    def _update_save_button_visibility(self):
        """更新保存当前登录账户按钮的可见性"""
        from logger import info
        
        # 检查成员变量是否存在
        if not hasattr(self, "save_account_button") or not hasattr(self, "cursor_settings_title_layout"):
            info("保存当前登录按钮或标题布局不存在，无法更新按钮可见性")
            return
        
        # 获取当前登录邮箱
        current_email = self._get_current_email()
        
        # 获取自动隐藏设置
        auto_hide = self._get_auto_hide_setting()
        should_show_button = True
        
        if auto_hide:
            info(f"自动隐藏设置已开启，检查当前email是否存在于账户文件中: {current_email}")
            should_show_button = not self._account_exists_in_file(current_email)
        
        # 判断按钮当前是否已在布局中
        button_in_layout = self.save_account_button.parent() is not None
        
        # 只有当按钮应该显示且有当前email时才显示保存按钮
        if should_show_button and current_email:
            info(f"显示保存当前登录账户按钮（自动隐藏={auto_hide}, email={current_email}）")
            # 如果按钮不在布局中，添加它
            if not button_in_layout:
                self.cursor_settings_title_layout.addWidget(self.save_account_button)
                info("已添加保存当前登录账户按钮到布局")
        else:
            # 如果按钮在布局中，移除它
            if button_in_layout:
                self.cursor_settings_title_layout.removeWidget(self.save_account_button)
                self.save_account_button.setParent(None)  # 从父级组件中移除
                info(f"已从布局中移除保存当前登录账户按钮（自动隐藏={auto_hide}, email={current_email}）")

    def _refresh_custom_cursor_path_ui(self):
        """刷新自定义Cursor路径UI状态"""
        enabled, path = self._get_custom_cursor_path_settings()
        
        self.custom_path_switch.setChecked(enabled)
        if path:
            self.custom_path_display.setText(path)
            self.custom_path_display.setToolTip(path) # 添加 Tooltip 显示完整路径
        else:
            self.custom_path_display.setText("") # 清空文本
            # 根据操作系统设置不同的提示信息
            system = platform.system().lower()
            placeholder = "未设置自定义路径"
            if "windows" in system:
                placeholder = "选择您的安装目录 如： D:\AIEditor\cursor"
            elif "darwin" in system:
                placeholder = "请选择Cursor.app所在位置 如：/Applications/Cursor.app"
            else: # Linux
                placeholder = "请选择Cursor.AppImage所在位置 如：~/Desktop/Cursor-0.49.5-x86_64.AppImage"
            self.custom_path_display.setPlaceholderText(placeholder)
            self.custom_path_display.setToolTip("")
        
        # 根据开关状态更新控件启用状态
        self.custom_path_display.setEnabled(enabled)
        # self.select_custom_path_btn.setEnabled(enabled) # [删除] 按钮始终启用
        # 初始时隐藏状态标签
        self.custom_path_status_label.hide()

    def _on_select_custom_cursor_path(self):
        """处理选择自定义 Cursor 路径按钮点击事件"""
        current_path = self.custom_path_display.text()
        system = platform.system().lower()
        selected_path = None
        
        dialog_title = "选择 Cursor "
        file_filter = ""
        
        try:
            if "windows" in system:
                dialog_title += "安装目录"
                selected_path = QFileDialog.getExistingDirectory(
                    self, 
                    dialog_title,
                    current_path if current_path else os.path.expanduser("~") # 默认打开用户目录
                )
            elif "darwin" in system:
                dialog_title += "应用程序 (.app)"
                file_filter = "应用程序 (*.app)"
                selected_path, _ = QFileDialog.getOpenFileName(
                    self,
                    dialog_title,
                    current_path if current_path else "/Applications", # 默认打开应用目录
                    file_filter
                )
            else: # Linux
                dialog_title += "AppImage 文件"
                file_filter = "AppImage 文件 (*.AppImage);;所有文件 (*)"
                selected_path, _ = QFileDialog.getOpenFileName(
                    self,
                    dialog_title,
                    current_path if current_path else os.path.expanduser("~"), # 默认打开用户目录
                    file_filter
                )
            
            if selected_path:
                # 确保selected_path是字符串类型
                try:
                    if not isinstance(selected_path, str):
                        # 如果是文件对象，尝试获取名称
                        if hasattr(selected_path, 'read') or hasattr(selected_path, 'name'):
                            selected_path = selected_path.name if hasattr(selected_path, 'name') else str(selected_path)
                        else:
                            selected_path = str(selected_path)  # 其他类型强制转换为字符串
                    # 确保我们有一个有效的字符串
                    selected_path = str(selected_path)
                except Exception as e:
                    info(f"处理路径类型时出错: {e}")
                    selected_path = str(selected_path)  # 强制转换为字符串
                
                info(f"用户选择了新的 Cursor 路径: {selected_path}")
                # 保存配置 (开关状态保持不变)
                current_enabled_state = self.custom_path_switch.isChecked()
                self._save_custom_cursor_path_settings(current_enabled_state, selected_path)
                
                # 更新 UI 显示
                self.custom_path_display.setText(selected_path)
                self.custom_path_display.setToolTip(selected_path)
                
                # 如果开关是开启状态，则更新软链接
                if current_enabled_state:
                    self._update_cursor_symlink() # 触发软链接更新
                    
            else:
                info("用户取消了路径选择")
                
        except Exception as e:
            error_msg = f"选择路径时出错: {str(e)}"
            print(error_msg)
            info(error_msg)
            self.custom_path_status_label.setText(f"错误: {error_msg}")
            self.custom_path_status_label.show()
            self.toast_request.emit("选择路径时发生错误", "error")

    def _on_custom_path_switch_changed(self, state):
        """处理自定义路径开关状态变化事件"""
        info(f"自定义路径开关状态改变为: {'启用' if state else '禁用'}")
        # 读取当前保存的路径
        _, current_path = self._get_custom_cursor_path_settings()
        
        # 保存新的开关状态和当前路径
        self._save_custom_cursor_path_settings(state, current_path)
        
        # 更新 UI 控件的启用状态
        self.custom_path_display.setEnabled(state)
        # self.select_custom_path_btn.setEnabled(state) # [删除] 按钮始终启用
        
        # 触发软链接更新/删除
        self._update_cursor_symlink()

    def _get_target_symlink_path(self, custom_path=""):
        """根据操作系统获取目标软链接路径"""
        system = platform.system().lower()
        link_path = ""
        try:
            if "windows" in system:
                local_app_data = os.getenv('LOCALAPPDATA')
                if local_app_data:
                    # 注意：目标是 Programs 目录下的 Cursor 目录链接
                    link_path = os.path.join(local_app_data, "Programs", "Cursor") 
                else:
                    print("错误：无法获取 LOCALAPPDATA 环境变量")
                    return None
            elif "darwin" in system:
                link_path = "/Applications/Cursor.app"
            else: # Linux
                if custom_path and os.path.basename(custom_path):
                    link_path = os.path.join("/opt/Cursor", os.path.basename(custom_path))
                else:
                    # 如果 custom_path 无效，无法确定 Linux 链接名，返回 None
                    # 或者在删除时，可以尝试列出 /opt/Cursor 下的链接并删除？暂时返回 None
                    print("错误：Linux 下需要有效的 custom_path 来确定链接文件名")
                    return None
            return link_path
        except Exception as e:
            print(f"获取目标链接路径时出错: {e}")
            return None
            
    def _run_command(self, command):
        """执行系统命令并返回结果"""
        try:
            # 注意：在 Windows 上创建/删除链接通常需要管理员权限
            # shell=True 在 Windows 上可能更容易执行 mklink/rmdir，但在 Linux/Mac 上可能带来风险
            # 考虑为不同系统使用不同的执行方式，或明确告知用户权限要求
            info(f"执行命令: {command}")
            result = subprocess.run(command, shell=True, capture_output=True, text=True, check=False) # check=False 避免失败时抛出异常
            info(f"命令执行结果: returncode={result.returncode}, stdout={result.stdout}, stderr={result.stderr}")
            return result
        except Exception as e:
            error_msg = f"执行命令时出错: {str(e)}"
            print(error_msg)
            info(error_msg)
            return None # 返回 None 表示执行本身出错
            
    def _is_empty_directory(self, path):
        """检查指定路径是否为空目录"""
        if not os.path.exists(path) or not os.path.isdir(path):
            return False
        return len(os.listdir(path)) == 0

    def _delete_cursor_symlink(self, custom_path):
        """删除 Cursor 软链接"""
        link_path = self._get_target_symlink_path(custom_path) # Linux需要custom_path
        if not link_path:
            return False, "无法确定目标链接路径"
        
        info(f"尝试删除链接: {link_path}")
        status_msg = ""
        success = False
        
        # 检查路径是否存在以及是否为链接
        if os.path.islink(link_path):
            system = platform.system().lower()
            command = ""
            if "windows" in system:
                # Windows 用 rmdir 删除目录符号链接
                command = f'rmdir "{link_path}"' 
            elif "darwin" in system or "linux" in system:
                command = f'rm "{link_path}"' 
            
            if command:
                result = self._run_command(command)
                if result is not None:
                    if result.returncode == 0:
                        success = True
                        status_msg = "链接已成功删除"
                        info(status_msg)
                    else:
                        status_msg = f"删除链接失败: {result.stderr.strip()}"
                        info(status_msg)
                        # 检查是否为权限错误
                        if "权限不够" in result.stderr or "Operation not permitted" in result.stderr:
                            status_msg += " (可能需要管理员权限)"
                else:
                    status_msg = "执行删除命令时发生内部错误"
                    info(status_msg)
            else:
                status_msg = "不支持的操作系统，无法删除链接"
                info(status_msg)
        elif not os.path.exists(link_path):
            success = True # 目标路径不存在，视为删除成功
            status_msg = "链接不存在，无需删除"
            info(status_msg)
        else:
            # 路径存在但不是链接
            success = False
            status_msg = f"错误: 目标路径 '{link_path}' 存在但不是链接，请手动处理"
            info(status_msg)
            
        return success, status_msg

    def _create_cursor_symlink(self, custom_path):
        """创建 Cursor 软链接"""
        link_path = self._get_target_symlink_path(custom_path)
        if not link_path:
            return False, "无法确定目标链接路径"
        if not custom_path or not os.path.exists(custom_path):
             return False, f"源路径无效或不存在: {custom_path}"
        
        info(f"尝试创建链接: 源='{custom_path}', 链接='{link_path}'")
        status_msg = ""
        success = False
        system = platform.system().lower()
        
        # 检查目标链接路径是否存在，如果存在且不是指向 custom_path 的链接，则尝试删除
        if os.path.lexists(link_path): # 使用 lexists 检查链接本身是否存在
            if os.path.islink(link_path):
                try:
                    # 检查链接指向的目标是否与 custom_path 相同
                    if os.path.realpath(link_path) == os.path.realpath(custom_path):
                        info("链接已存在且指向正确的目标，无需操作")
                        return True, "链接已是最新状态"
                    else:
                        info(f"目标链接 '{link_path}' 已存在但指向不同位置，将尝试删除旧链接")
                        delete_success, delete_msg = self._delete_cursor_symlink(custom_path) # Linux 删除需要 custom_path
                        if not delete_success:
                            return False, f"无法删除旧链接: {delete_msg}"
                except Exception as e:
                    info(f"检查或删除现有链接时出错: {e}，将继续尝试创建")
                    # 如果检查或删除失败，仍然尝试创建，命令本身可能会报错
            else:
                # 目标路径存在但不是链接，检查是否为空目录
                if self._is_empty_directory(link_path):
                    try:
                        # 尝试删除空目录
                        os.rmdir(link_path)
                        info(f"已自动删除目标路径的空目录: {link_path}")
                    except Exception as e:
                        # 删除失败
                        info(f"无法自动删除目标路径的空目录: {e}")
                        return False, f"目标路径是空目录但无法自动删除，请手动删除 '{link_path}' 后重试"
                else:
                    # 目录不为空，检查是否是有效的Cursor目录
                    cursor_exe = os.path.join(link_path, "Cursor.exe")
                    has_cursor_exe = os.path.exists(cursor_exe)
                    
                    if has_cursor_exe:
                        msg = f"目标路径已有Cursor安装，请先卸载或手动删除 '{link_path}'"
                    else:
                        msg = f"目标路径已存在且不是空目录，请手动删除 '{link_path}'"
                    
                    info(f"无法创建链接: {msg}")
                    return False, msg

        # 确保目标目录存在 (主要针对 Linux /opt/Cursor)
        link_dir = os.path.dirname(link_path)
        if not os.path.exists(link_dir):
            if "linux" in system:
                info(f"目标目录 '{link_dir}' 不存在，尝试使用 sudo 创建")
                mkdir_cmd = f'sudo mkdir -p "{link_dir}"' 
                mkdir_result = self._run_command(mkdir_cmd)
                if mkdir_result is None or mkdir_result.returncode != 0:
                    msg = f"创建目标目录 '{link_dir}' 失败: {mkdir_result.stderr.strip() if mkdir_result else '命令执行错误'} (可能需要手动创建或提供 sudo 权限)"
                    info(msg)
                    return False, msg
            else:
                # 其他系统一般不需要手动创建 Program Files 或 Applications 目录
                 os.makedirs(link_dir, exist_ok=True) # 尝试创建，如果失败下面链接命令也会失败
        
        # 构建创建链接的命令
        command = ""
        if "windows" in system:
            # Windows: mklink /D 创建目录链接
            command = f'mklink /D "{link_path}" "{custom_path}"' 
        elif "darwin" in system or "linux" in system:
            # macOS/Linux: ln -s 创建符号链接
            # Linux 的 link_path 已经包含了 AppImage 文件名
            command = f'ln -s "{custom_path}" "{link_path}"' 
            if "linux" in system:
                 # 检查是否需要sudo (如果 /opt/Cursor 没有写入权限)
                 if not os.access(link_dir, os.W_OK):
                     info(f"目录 '{link_dir}' 可能需要 sudo 权限来创建链接")
                     command = f'sudo {command}' # 在命令前添加 sudo

        if command:
            result = self._run_command(command)
            if result is not None:
                if result.returncode == 0:
                    success = True
                    status_msg = "链接创建成功"
                    info(status_msg)
                else:
                    status_msg = f"创建链接失败: {result.stderr.strip()}"
                    info(status_msg)
                    # 检查是否为权限错误
                    if "权限不够" in result.stderr or "Operation not permitted" in result.stderr or "管理员权限" in result.stderr or "权限" in result.stderr:
                        status_msg += " (请尝试使用管理员权限运行此程序)"
                    elif "文件已存在" in result.stderr or "File exists" in result.stderr:
                         status_msg += " (目标链接可能已存在或创建冲突)"
            else:
                status_msg = "执行创建命令时发生内部错误"
                info(status_msg)
        else:
            status_msg = "不支持的操作系统，无法创建链接"
            info(status_msg)
            
        return success, status_msg

    def _update_cursor_symlink(self):
        """根据当前设置创建或删除 Cursor 软链接"""
        enabled, custom_path = self._get_custom_cursor_path_settings()
        self.custom_path_status_label.hide() # 先隐藏旧状态
        
        success = False
        status_msg = ""
        
        # 避免重复操作，先判断当前状态
        link_path = self._get_target_symlink_path(custom_path)
        
        if enabled:
            if not custom_path:
                status_msg = "请先选择正确的 Cursor 路径"
                info(status_msg)
                success = False
                # 如果没有路径，将开关状态设置为关闭
                self._silently_set_switch_off(custom_path)
            else:
                # 检查是否已经存在正确指向的链接
                if os.path.islink(link_path) and os.path.exists(custom_path):
                    try:
                        if os.path.realpath(link_path) == os.path.realpath(custom_path):
                            info("链接已存在且指向正确，无需创建")
                            success = True
                            status_msg = "链接已是最新状态"
                            # 获取版本
                            version = self._get_cursor_version(custom_path)
                            self.cursor_version_updated.emit(version if version else "未知")
                        else:
                            # 链接存在但指向不同位置，需要重新创建
                            success, status_msg = self._create_cursor_symlink(custom_path)
                            if success:
                                version = self._get_cursor_version(custom_path)
                                self.cursor_version_updated.emit(version if version else "未知")
                            else:
                                self._silently_set_switch_off(custom_path)
                    except Exception as e:
                        info(f"检查链接时出错: {e}")
                        success, status_msg = self._create_cursor_symlink(custom_path)
                        if not success:
                            self._silently_set_switch_off(custom_path)
                else:
                    # 链接不存在，创建新链接
                    success, status_msg = self._create_cursor_symlink(custom_path)
                    if success:
                        # 创建成功，尝试获取版本并更新
                        version = self._get_cursor_version(custom_path)
                        self.cursor_version_updated.emit(version if version else "未知")
                    else:
                        # 创建失败，将开关状态设置为关闭
                        self._silently_set_switch_off(custom_path)
        else:
            # 仅当存在链接时才尝试删除
            if os.path.islink(link_path):
                success, status_msg = self._delete_cursor_symlink(custom_path)
                if success:
                    # 删除成功，将版本设置为未知
                    self.cursor_version_updated.emit("未知")
            else:
                # 若不是链接但路径存在（可能是常规目录），不进行操作但显示提示
                if os.path.exists(link_path):
                    status_msg = f"目标路径不是链接，无需删除"
                    info(status_msg)
                    success = True
        
        # 显示操作结果 - 使用 Toast（只有在有消息时才显示）
        if status_msg:
            self.toast_request.emit(status_msg, success)
    
    def _silently_set_switch_off(self, custom_path):
        """无声地关闭开关（不触发事件）"""
        # 阻断信号连接以防止触发事件
        self.custom_path_switch.blockSignals(True)
        self.custom_path_switch.setChecked(False)
        self.custom_path_switch.blockSignals(False)
        
        # 更新设置
        self._save_custom_cursor_path_settings(False, custom_path)
        
        # 更新UI控件状态
        self.custom_path_display.setEnabled(False)

    def _get_cursor_version(self, cursor_path):
        """尝试从给定路径获取Cursor版本号"""
        system = platform.system().lower()
        version = None
        import re
        # 获取系统编码，用于解码 subprocess 输出
        try:
            encoding = sys.stdout.encoding if hasattr(sys.stdout, 'encoding') else 'utf-8'
        except Exception:
            encoding = 'utf-8'

        try:
            if "windows" in system:
                # 尝试 package.json
                package_json_path = os.path.join(cursor_path, "resources", "app", "package.json")
                if os.path.exists(package_json_path):
                    try:
                        with open(package_json_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            version = data.get("version")
                            if version: 
                                info(f"从 package.json 获取到版本: {version}")
                                return version
                    except Exception as e:
                        info(f"读取或解析 package.json 失败: {e}")
                
                # 尝试运行 Cursor.exe --version
                executable_path = os.path.join(cursor_path, "Cursor.exe")
                if os.path.exists(executable_path):
                    info(f"尝试运行: {executable_path} --version")
                    # 获取原始字节输出
                    result = subprocess.run([executable_path, "--version"], capture_output=True, shell=False, timeout=5, creationflags=subprocess.CREATE_NO_WINDOW)
                    # 使用系统编码解码
                    stdout = result.stdout.decode(encoding, errors='ignore')
                    stderr = result.stderr.decode(encoding, errors='ignore')
                    if result.returncode == 0 and stdout:
                        match = re.search(r"\d+\.\d+\.\d+", stdout)
                        if match:
                            version = match.group(0)
                            info(f"从 --version 获取到版本: {version}")
                            return version
                        else:
                            info(f"--version 输出未匹配到版本号: {stdout.strip()}")
                    else:
                        info(f"运行 --version 失败或无输出: code={result.returncode}, stderr={stderr}")
                else:
                     info(f"未找到 Cursor.exe于: {executable_path}")

            elif "darwin" in system:
                # 尝试 package.json
                package_json_path = os.path.join(cursor_path, "Contents", "Resources", "app", "package.json")
                if os.path.exists(package_json_path):
                    try:
                        with open(package_json_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            version = data.get("version")
                            if version: 
                                info(f"从 package.json 获取到版本: {version}")
                                return version
                    except Exception as e:
                        info(f"读取或解析 package.json 失败: {e}")
                
                # 尝试运行 Contents/MacOS/Cursor --version
                executable_path = os.path.join(cursor_path, "Contents", "MacOS", "Cursor")
                if os.path.exists(executable_path):
                    info(f"尝试运行: {executable_path} --version")
                    # 获取原始字节输出
                    result = subprocess.run([executable_path, "--version"], capture_output=True, shell=False, timeout=5)
                    # 使用系统编码解码
                    stdout = result.stdout.decode(encoding, errors='ignore')
                    stderr = result.stderr.decode(encoding, errors='ignore')                    
                    if result.returncode == 0 and stdout:
                        match = re.search(r"\d+\.\d+\.\d+", stdout)
                        if match:
                            version = match.group(0)
                            info(f"从 --version 获取到版本: {version}")
                            return version
                        else:
                            info(f"--version 输出未匹配到版本号: {stdout.strip()}")
                    else:
                        info(f"运行 --version 失败或无输出: code={result.returncode}, stderr={stderr}")
                else:
                     info(f"未找到 Cursor 执行文件于: {executable_path}")

            else: # Linux (AppImage)
                if os.path.exists(cursor_path):
                    try:
                        os.chmod(cursor_path, os.stat(cursor_path).st_mode | 0o111)
                    except Exception as e:
                        info(f"为 AppImage 设置执行权限失败: {e}")
                    
                    info(f"尝试运行: {cursor_path} --version")
                    try:
                        # 获取原始字节输出
                        result = subprocess.run([cursor_path, "--version"], capture_output=True, shell=False, timeout=10)
                        # 使用系统编码解码
                        stdout = result.stdout.decode(encoding, errors='ignore')
                        stderr = result.stderr.decode(encoding, errors='ignore')                          
                        if result.returncode == 0 and stdout:
                            match = re.search(r"\d+\.\d+\.\d+", stdout)
                            if match:
                                version = match.group(0)
                                info(f"从 --version 获取到版本: {version}")
                                return version
                            else:
                                info(f"--version 输出未匹配到版本号: {stdout.strip()}")
                        else:
                            info(f"运行 AppImage --version 失败或无输出: code={result.returncode}, stderr={stderr}")
                    except subprocess.TimeoutExpired:
                         info(f"运行 AppImage --version 超时")
                    except Exception as e:
                         info(f"运行 AppImage --version 出错: {e}")
                else:
                    info(f"AppImage 文件不存在: {cursor_path}")

        except Exception as e:
            error(f"获取 Cursor 版本时发生意外错误: {e}")

        info("无法获取 Cursor 版本号")
        return None # 获取失败

    def _on_advanced_backup_jump(self):
        # 通知主窗口切换到高级自定义备份/恢复页面
        if hasattr(self.window(), 'show_advanced_backup_page'):
            self.window().show_advanced_backup_page()

    def _on_proxy_input_changed(self, text):
        text = text.strip()
        if text and not self._proxy_regex.match(text):
            # 不为空且不合法，显示气泡和红色样式
            self.proxy_bubble_tip.setTargetWidget(self.cursor_proxy_input)
            self.proxy_bubble_tip.showTip("值必须匹配 regex ^(https?|socks|socks4a?|socks5h?)://([^:]*[^@]*@)?([^:]+)(\:\d+)?/?$")
            self.cursor_proxy_input.setStyleSheet(f"""
                QLineEdit {{
                    background-color: rgba(188,74,89,0.18);
                    color: {Theme.TEXT_PRIMARY};
                    border: 2px solid {Theme.ERROR};
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    padding: 5px 10px;
                    height: 36px;
                }}
                QLineEdit:focus {{
                    border: 2px solid {Theme.ERROR};
                }}
            """)
        else:
            # 合法或为空，隐藏气泡，恢复样式
            self.proxy_bubble_tip.hideTip()
            self.cursor_proxy_input.setStyleSheet(f"""
                QLineEdit {{
                    background-color: {Theme.CARD_LEVEL_3};
                    color: {Theme.TEXT_PRIMARY};
                    border: 1px solid {Theme.BORDER};
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    padding: 5px 10px;
                    height: 36px;
                }}
                QLineEdit:focus {{
                    border: 1px solid {Theme.ACCENT};
                }}
            """)

    def _on_proxy_changed(self):
        proxy_value = self.cursor_proxy_input.text().strip()
        # 仅合法时才写入 (为空也视为合法)
        if proxy_value and not self._proxy_regex.match(proxy_value):
            self.window().show_toast("代理格式不合法，未保存", error=True)
            return
        success = self._set_proxy_setting(proxy_value)
        if success:
            if proxy_value:
                info(f"已保存Cursor代理设置：{proxy_value}")
                self.window().show_toast(f"已保存Cursor代理设置: {proxy_value}")
                # 新增：保存到历史
                self._add_proxy_to_history(proxy_value)
            else:
                info("已清除Cursor代理设置")
                self.window().show_toast("已清除Cursor代理设置")
        else:
            info("保存Cursor代理设置失败")
            self.window().show_toast("保存Cursor代理设置失败", error=True)

    def hideEvent(self, event):
        super().hideEvent(event)
        if hasattr(self, 'proxy_bubble_tip') and self.proxy_bubble_tip:
            self.proxy_bubble_tip.hideTip()

    def _get_function_config_path(self):
        """获取function.json配置文件路径"""
        from utils import get_app_data_dir
        return os.path.join(get_app_data_dir(), "config", "function.json")

    def _read_proxy_history(self):
        """读取代理历史记录，返回去重后的列表"""
        config_path = self._get_function_config_path()
        if not os.path.exists(config_path):
            return []
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            history = data.get("proxy_history", [])
            # 去重，保持顺序
            seen = set()
            result = []
            for item in history:
                if item and item not in seen:
                    seen.add(item)
                    result.append(item)
            return result
        except Exception:
            return []

    def _write_proxy_history(self, history_list):
        """写入代理历史记录，history_list为去重后的字符串列表"""
        config_path = self._get_function_config_path()
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = {}
            data["proxy_history"] = history_list
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception:
            return False

    def _add_proxy_to_history(self, proxy_value, max_count=10):
        """将新代理添加到历史，自动去重并限制最大条数"""
        if not proxy_value:
            return
        history = self._read_proxy_history()
        # 新项置顶，去重
        new_history = [proxy_value] + [item for item in history if item != proxy_value]
        new_history = new_history[:max_count]
        self._write_proxy_history(new_history)

    def _remove_proxy_from_history(self, proxy_value):
        """从历史中删除指定代理"""
        history = self._read_proxy_history()
        new_history = [item for item in history if item != proxy_value]
        self._write_proxy_history(new_history)

    def _show_proxy_history_menu(self):
        """弹出历史代理菜单（自定义每行带删除按钮）"""
        history = self._read_proxy_history()
        input_width = self.cursor_proxy_input.width() # 获取当前输入框宽度
        padding_offset = 10 # 菜单左右内边距/边框的估计值
        menu = QMenu(self)
        # Apply Theme-based styling to the menu
        menu.setStyleSheet(f"""
            QMenu {{
                background-color: {Theme.CARD_LEVEL_2}; /* Slightly darker background */
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 4px; /* Padding around the items */
                color: {Theme.TEXT_PRIMARY};
            }}
            /* Style for the QWidgetAction container, not the item itself */
            QMenu::item {{
                /* QWidgetAction does not directly use this, but set defaults */
                padding: 0px; /* Reset padding as we handle it in the widget */
                margin: 0px; /* Remove margin, handled by row_widget fixed height */
                background-color: transparent;
            }}
        """)

        # Remove placeholder if history is not empty
        if history:
            for action in menu.actions():
                if action.text() == "暂无历史记录，代理保存后会自动添加历史":
                    menu.removeAction(action)
                    action.deleteLater()
                    break

        if not history:
            action = QAction("暂无历史记录，代理保存后会自动添加历史", menu)
            action.setEnabled(False)
            menu.addAction(action)
        else:
            for proxy in history:
                row_widget = QWidget()
                # 设置宽度为输入框宽度减去偏移量
                row_widget.setFixedWidth(input_width - padding_offset) 
                row_widget.setFixedHeight(36) # Set fixed height for uniformity
                row_layout = QHBoxLayout(row_widget)
                # Adjust margins and spacing for better visual balance
                row_layout.setContentsMargins(8, 4, 4, 4) # Add vertical padding
                row_layout.setSpacing(8) # Increase spacing between label and button
                label = QLabel(proxy)
                label.setToolTip(proxy) # Show full text on hover
                # Slightly smaller font, allow wrapping if needed
                label.setStyleSheet(f"font-size: 12px; color: {Theme.TEXT_SECONDARY}; background-color: transparent;")
                label.setWordWrap(False) # Disable word wrap
                # label.setTextElideMode(Qt.TextElideMode.ElideRight) # Elide long text
                # label.setMinimumWidth(180) # Remove fixed minimum width
                label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)

                btn = QPushButton("✕")
                # Increase size slightly for easier clicking
                btn.setFixedSize(20, 20)
                # Enhanced styling for delete button
                btn.setStyleSheet(f"""
                    QPushButton {{ 
                        border: none; 
                        color: #F06292; /* Brighter pink/red */
                        background-color: transparent; 
                        font-size: 14px; 
                        font-weight: bold;
                        padding: 1px; /* Small padding */
                    }} 
                    QPushButton:hover {{ 
                        color: #F44336; /* Brighter red on hover */
                        background-color: rgba(255, 82, 82, 0.2); /* More opaque red background on hover */
                        border-radius: 3px; 
                    }}
                    QPushButton:pressed {{ 
                        background-color: rgba(255, 82, 82, 0.2); /* Slightly darker on press */
                    }}
                """)
                btn.setCursor(Qt.CursorShape.PointingHandCursor)
                # Make the label clickable like the rest of the row
                row_widget.mousePressEvent = lambda e, p=proxy: (self._on_select_proxy_history(p), menu.close())
                row_widget.setCursor(Qt.CursorShape.PointingHandCursor)
                # Add hover effect to the entire row widget for visual feedback
                row_widget.setStyleSheet(f"""
                    QWidget {{ background-color: transparent; border-radius: {Theme.BORDER_RADIUS_SMALL}; }}
                    QWidget:hover {{ background-color: {Theme.CARD_LEVEL_3}; }}
                """)

                row_layout.addWidget(label, 1) # Label takes expanding space
                row_layout.addWidget(btn) # Button takes fixed space
                row_widget.setLayout(row_layout)

                act = QWidgetAction(menu)
                act.setDefaultWidget(row_widget)
                menu.addAction(act)
                
                # Connect delete button, passing menu and action
                btn.clicked.connect(lambda checked, p=proxy, m=menu, a=act: self._on_delete_proxy_history(p, m, a))

        # Position the menu below the proxy input field
        # 使用输入框定位，而不是之前的按钮
        menu_pos = self.cursor_proxy_input.mapToGlobal(self.cursor_proxy_input.rect().bottomLeft())
        # 微调Y坐标，使其紧贴输入框下方
        menu_pos.setY(menu_pos.y() + 1)
        menu.exec(menu_pos)

    def _on_select_proxy_history(self, proxy):
        """选择历史代理，填充输入框"""
        self.cursor_proxy_input.setText(proxy)
        # 可选：自动触发保存
        self._on_proxy_changed()

    def _on_delete_proxy_history(self, proxy, menu, action_to_delete):
        """删除历史代理并从当前菜单中移除"""
        # 1. Remove from storage
        self._remove_proxy_from_history(proxy)
        # self.window().show_toast("已删除历史代理", error=False) # *** 移除 Toast 提示 ***

        # 2. Remove action from menu and cleanup
        if menu and action_to_delete:
            widget_to_delete = action_to_delete.defaultWidget()
            menu.removeAction(action_to_delete)
            action_to_delete.deleteLater() # Clean up the action
            if widget_to_delete:
                widget_to_delete.deleteLater() # Clean up the widget

            # 3. Check if the menu is now empty and add placeholder
            visible_items = [a for a in menu.actions() if isinstance(a, QWidgetAction) and a.isVisible()]
            if not visible_items:
                # Double check placeholder doesn't exist
                has_placeholder = any(a.text() == "暂无历史记录，代理保存后会自动添加历史" for a in menu.actions())
                if not has_placeholder:
                    placeholder_action = QAction("暂无历史记录，代理保存后会自动添加历史", menu)
                    placeholder_action.setEnabled(False)
                    menu.addAction(placeholder_action)
        # No QTimer needed for refresh, modification is done in-place

    def _has_settings_backup(self):
        """检查是否有settings.json备份或Cursor设置备份"""
        backup_dir = os.path.join(get_app_data_dir(), "backups", "settings")
        
        # 检查基本设置文件备份
        settings_backup = os.path.join(backup_dir, "settings.json")
        settings_backup_exists = os.path.exists(settings_backup)
        
        # 检查数据库设置备份，定义与备份函数相同的设置类型
        setting_types = {
            "workbench": "工作区界面",
            "editor": "编辑器",
            "telemetry": "遥测数据", 
            "aicontext": "AI上下文",
            "src.vs.platform": "VSCode平台"
        }
        
        # 检查是否存在任何类型的设置备份
        db_backup_exists = False
        for prefix in setting_types:
            backup_file_path = os.path.join(backup_dir, f"{prefix.replace('.', '_')}_settings.json")
            if os.path.exists(backup_file_path):
                db_backup_exists = True
                break
        
        # 向下兼容检查: 检查提示词备份和Agent模式设置备份
        context_backup = os.path.join(backup_dir, "personal_context.json")
        agent_autorun_backup = os.path.join(backup_dir, "agent_autorun.json")
        
        # 只要有一个备份文件存在就返回True
        return settings_backup_exists or db_backup_exists or os.path.exists(context_backup) or os.path.exists(agent_autorun_backup)
    
    def _on_quick_function(self):
        """处理快捷一键功能按钮点击
        打开快捷一键配置对话框
        """
        from widgets.quick_function_dialog import QuickFunctionConfigDialog
        dialog = QuickFunctionConfigDialog(self)
        dialog.exec()

class CursorBackupWorker(QThread):
    progress = Signal(int, int)  # 当前进度, 总进度
    finished = Signal(bool, str)  # 是否成功, 消息
    error = Signal(str)  # 错误消息
    message = Signal(str)  # 普通消息

    def __init__(self, mode, action, parent=None):
        super().__init__(parent)
        self.mode = mode  # 'settings' or 'workspace'
        self.action = action  # 'backup' or 'restore'

    def run(self):
        try:
            if self.mode == 'settings':
                if self.action == 'backup':
                    self.message.emit('正在备份Cursor设置...')
                    result = self.parent()._backup_cursor_settings()
                    if result:
                        self.finished.emit(True, '成功备份Cursor设置')
                    else:
                        self.finished.emit(False, '备份Cursor设置失败')
                elif self.action == 'restore':
                    self.message.emit('正在恢复Cursor设置...')
                    # 删除恢复前自动备份的代码
                    result = self.parent()._restore_cursor_settings()
                    if isinstance(result, dict):
                        if result.get('result', '') in ['success', 'success_restart', 'success_no_restart']:
                            self.finished.emit(True, '成功恢复Cursor设置')
                        else:
                            self.finished.emit(False, result.get('message', '恢复Cursor设置失败'))
                    elif result:
                        self.finished.emit(True, '成功恢复Cursor设置')
                    else:
                        self.finished.emit(False, '恢复Cursor设置失败')
            elif self.mode == 'workspace':
                if self.action == 'backup':
                    self.message.emit('正在备份Cursor会话记录...')
                    result = self.parent()._backup_cursor_workspace()
                    if result:
                        self.finished.emit(True, '成功备份Cursor会话记录')
                    else:
                        self.finished.emit(False, '备份Cursor会话记录失败')
                elif self.action == 'restore':
                    self.message.emit('正在恢复Cursor会话记录...')
                    # 删除恢复前自动备份的代码
                    result = self.parent()._restore_cursor_workspace()
                    if isinstance(result, dict):
                        if result.get('result', '') in ['success', 'success_restart', 'success_no_restart']:
                            self.finished.emit(True, '成功恢复Cursor会话记录')
                        else:
                            self.finished.emit(False, result.get('message', '恢复Cursor会话记录失败'))
                    elif result:
                        self.finished.emit(True, '成功恢复Cursor会话记录')
                    else:
                        self.finished.emit(False, '恢复Cursor会话记录失败')
        except Exception as e:
            self.error.emit(f'操作异常: {str(e)}')
            self.finished.emit(False, f'操作异常: {str(e)}')


class UnlimitedMaxWorker(QThread):
    finished = Signal(bool, bool, str)  # 是否成功, 操作类型(True=开启，False=关闭), 消息
    status_checked = Signal(bool)  # 传递是否存在备份文件的状态
    error = Signal(str)  # 错误消息
    message = Signal(str)  # 普通消息
    
    def __init__(self, action, parent=None):
        """
        初始化无限MAX工作线程
        
        Args:
            action (str): 'check' - 只检查状态, 'enable' - 开启无限MAX, 'disable' - 关闭无限MAX
            parent (QObject, optional): 父对象
        """
        super().__init__(parent)
        self.action = action
        
    def run(self):
        """线程执行函数"""
        from logger import info
        
        try:
            # 获取文件路径 - 检查状态时不创建目录
            workbench_js_path = self._get_workbench_js_path()
            backup_path = self._get_workbench_backup_path(create=False)
            
            # 检查状态 - 只检查备份文件是否存在，不创建目录
            if self.action == 'check':
                # 计算预期的备份文件路径但不创建目录
                has_backup = backup_path and os.path.exists(backup_path)
                self.status_checked.emit(has_backup)
                return
                
            # 先尝试原始的相对路径（开发环境）
            patch_js_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "core", "cursor_auto", "turnstilePatch", "workbench.js")
            
            # 如果找不到文件，尝试Mac打包环境的路径
            if not os.path.exists(patch_js_path):
                from logger import info
                # info(f"在开发环境路径未找到补丁文件: {patch_js_path}")
                
                # 尝试 Mac 打包后的路径
                if platform.system().lower() == "darwin":
                    # 方法1: 基于运行路径
                    base_path = os.path.abspath(os.path.join(os.path.dirname(sys.executable), ".."))
                    possible_paths = [
                        # 打包时的位置 - MacOS目录下
                        os.path.join(base_path, "MacOS", "core", "cursor_auto", "turnstilePatch", "workbench.js"),
                        # 可能的资源目录下
                        os.path.join(base_path, "Resources", "core", "cursor_auto", "turnstilePatch", "workbench.js"),
                    ]
                    
                    # 方法2: 绝对路径尝试 - Mac特定
                    if os.path.exists("/Applications/YCursor.app"):
                        possible_paths.extend([
                            "/Applications/YCursor.app/Contents/MacOS/core/cursor_auto/turnstilePatch/workbench.js",
                            "/Applications/YCursor.app/Contents/Resources/core/cursor_auto/turnstilePatch/workbench.js",
                        ])
                    
                    # 检查可能的路径
                    for path in possible_paths:
                        if os.path.exists(path):
                            # info(f"在Mac打包环境找到补丁文件: {path}")
                            patch_js_path = path
                            break
                
                # Windows打包后的路径
                elif platform.system().lower() == "windows":
                    exe_dir = os.path.dirname(sys.executable)
                    alt_path = os.path.join(exe_dir, "core", "cursor_auto", "turnstilePatch", "workbench.js")
                    if os.path.exists(alt_path):
                        # info(f"在Windows打包环境找到补丁文件: {alt_path}")
                        patch_js_path = alt_path
            
            if not os.path.exists(workbench_js_path):
                info(f"Cursor workbench.desktop.main.js文件不存在: {workbench_js_path}")
                self.error.emit(f"Cursor workbench.desktop.main.js文件不存在")
                self.finished.emit(False, self.action == 'enable', "文件不存在")
                return
                
            if not os.path.exists(patch_js_path):
                info(f"无限MAX补丁文件不存在")
                self.error.emit(f"无限MAX补丁文件不存在")
                self.finished.emit(False, self.action == 'enable', "补丁文件不存在")
                return
            
            # 先终止Cursor进程
            info("开始关闭Cursor进程，准备修改workbench.desktop.main.js")
            cursor_killed = False
            try:
                # 使用系统命令终止进程
                system = platform.system().lower()
                if "windows" in system:
                    os.system("taskkill /F /IM Cursor.exe 2>nul")
                    os.system("taskkill /F /IM Cursor.exe 2>nul") # 再次执行以确保所有进程都被终止
                elif "darwin" in system:  # macOS
                    os.system("pkill -9 Cursor 2>/dev/null")
                    os.system("pkill -9 Cursor 2>/dev/null")
                else:  # Linux
                    os.system("pkill -9 Cursor 2>/dev/null")
                    os.system("pkill -9 Cursor 2>/dev/null")
                    
                cursor_killed = True
                info("已尝试关闭Cursor进程")
                # 等待2秒确保进程完全终止
                info("等待2秒确保进程完全终止...")
                time.sleep(2)
            except Exception as e:
                error_msg = str(e)
                info(f"终止Cursor进程失败: {error_msg}")
                # 继续执行，不影响操作流程
                
            if self.action == 'enable':  # 开启无限MAX
                # 启用时需要创建备份目录
                backup_path = self._get_workbench_backup_path(create=True)
                
                # 检查是否已有备份
                if not os.path.exists(backup_path):
                    # 创建备份
                    shutil.copy2(workbench_js_path, backup_path)
                    info(f"已备份workbench.desktop.main.js: {backup_path}")
                
                # 复制补丁文件替换原文件
                shutil.copy2(patch_js_path, workbench_js_path)
                info(f"已启用无限MAX（替换为补丁文件）")
                self.message.emit("已启用无限MAX")
                self.finished.emit(True, True, cursor_killed)
            elif self.action == 'disable':  # 关闭无限MAX
                # 检查是否有备份
                if os.path.exists(backup_path):
                    # 恢复备份
                    shutil.copy2(backup_path, workbench_js_path)
                    info(f"已关闭无限MAX（恢复备份文件）")
                    
                    # 删除备份文件，确保开关状态正确判断
                    try:
                        os.remove(backup_path)
                        info(f"已删除备份文件: {backup_path}")
                    except Exception as e:
                        info(f"删除备份文件失败: {str(e)}")
                        
                    self.message.emit("已关闭无限MAX")
                    self.finished.emit(True, False, cursor_killed)
                else:
                    info(f"无法关闭无限MAX：找不到备份文件，但设置状态已更新")
                    self.error.emit("设置已更新，但未找到备份文件（可能之前未正常开启）")
                    self.finished.emit(False, False, cursor_killed)
                    
        except Exception as e:
            error_msg = str(e)
            info(f"执行无限MAX操作时出错: {error_msg}")
            self.error.emit(f"执行无限MAX操作时出错: {error_msg}")
            self.finished.emit(False, self.action == 'enable', "操作失败")
    
    def _get_workbench_js_path(self):
        """获取当前系统Cursor workbench.desktop.main.js文件路径"""
        system = platform.system().lower()
        if "windows" in system:
            return os.path.join(os.getenv("LOCALAPPDATA"), "Programs", "Cursor", "resources", "app", "out", "vs", "workbench", "workbench.desktop.main.js")
        elif "darwin" in system:
            return os.path.join("/Applications", "Cursor.app", "Contents", "Resources", "app", "out", "vs", "workbench", "workbench.desktop.main.js")
        else:  # Linux系统
            return os.path.expanduser("~/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js")
            
    def _get_workbench_backup_dir(self, create=False):
        """获取workbench.desktop.main.js备份目录
        
        Args:
            create (bool): 是否创建目录，默认为False仅获取路径不创建
        """
        workbench_js_path = self._get_workbench_js_path()
        if not workbench_js_path:
            return None
            
        # 获取备份目录路径（与workbench.desktop.main.js同级的backup目录）
        backup_dir = os.path.join(os.path.dirname(workbench_js_path), "backup")
        
        # 只有当create=True时才创建目录
        if create and not os.path.exists(backup_dir):
            os.makedirs(backup_dir, exist_ok=True)
            
        return backup_dir
        
    def _get_workbench_backup_path(self, create=False):
        """获取workbench.desktop.main.js备份文件路径
        
        Args:
            create (bool): 是否创建备份目录，默认为False仅获取路径不创建
        """
        backup_dir = self._get_workbench_backup_dir(create=create)
        if not backup_dir:
            return None
            
        return os.path.join(backup_dir, "workbench.desktop.main.js.bak")