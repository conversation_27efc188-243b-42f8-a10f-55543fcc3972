PySide6>=6.0.0
requests>=2.28.0
python-dateutil>=2.8.2
pyinstaller>=6.0.0
psutil
pyperclip
rich
cryptography

# 国内镜像源配置说明
# 使用以下命令配置镜像源：
# pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/

# 安装命令
# Windows: python -m pip install -r requirements.txt
# Linux/Mac: pip3 install -r requirements.txt

# 阿里云 http://mirrors.aliyun.com/pypi/simple/ 
# 中国科技大学 https://pypi.mirrors.ustc.edu.cn/simple/ 
# 豆瓣(douban) http://pypi.douban.com/simple/ 
# 清华大学 https://pypi.tuna.tsinghua.edu.cn/simple/ 
# 中国科学技术大学 http://pypi.mirrors.ustc.edu.cn/simple/
# pip config set global.index-url https://pypi.mirrors.ustc.edu.cn/simple/
# pip3 config set global.index-url https://pypi.mirrors.ustc.edu.cn/simple/


# pip3 install DrissionPage
# pip3 install colorama
# pip3 install python-dotenv
# pip3 install nuitka
# pip3 install ordered-set
# pip3 install zstandard
# pip3 install requests
# pip3 install selenium
# pip3 install websockets
# pip3 install asyncio
# pip3 install pillow
# pip3 install packaging
# pip3 install black
# pip3 install isort
# pip3 install flake8
# pip3 install psutil
# pip3 install DrissionPage
# pip3 install pycryptodome
# pip3 install pyarmor --trusted-host mirrors.aliyun.com  

# pip install DrissionPage
# pip install colorama
# pip install python-dotenv
# pip install nuitka
# pip install ordered-set
# pip install zstandard
# pip install requests
# pip install selenium
# pip install websockets
# pip install asyncio
# pip install pillow
# pip install packaging
# pip install black
# pip install isort
# pip install flake8
# pip install psutil
# pip install DrissionPage
# pip install pycryptodome
# pip install pyarmor --trusted-host mirrors.aliyun.com