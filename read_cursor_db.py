import os
import sqlite3
import json
import datetime

# 获取%APPDATA%路径
appdata_path = os.environ.get('APPDATA')
db_path = os.path.join(appdata_path, 'Cursor', 'User', 'globalStorage', 'state.vscdb')
output_file = 'cursor_db_content.txt'

def main():
    try:
        # 连接SQLite数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名和视图名
        cursor.execute("SELECT name, type FROM sqlite_master WHERE type IN ('table', 'view');")
        objects = cursor.fetchall()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"Cursor数据库内容导出 - {datetime.datetime.now()}\n")
            f.write(f"数据库路径: {db_path}\n\n")
            
            # 遍历每个表/视图并导出内容
            for obj_name, obj_type in objects:
                f.write(f"{obj_type.upper()}: {obj_name}\n")
                f.write("-" * 80 + "\n")
                
                # 获取对象结构
                try:
                    cursor.execute(f"PRAGMA table_info({obj_name})")
                    columns_info = cursor.fetchall()
                    
                    if columns_info:
                        # 打印详细的列信息
                        f.write("列信息:\n")
                        for col in columns_info:
                            col_id, col_name, col_type, not_null, default_val, pk = col
                            f.write(f"  {col_id}: {col_name} ({col_type})")
                            if pk:
                                f.write(" [主键]")
                            if not_null:
                                f.write(" [非空]")
                            if default_val is not None:
                                f.write(f" [默认值: {default_val}]")
                            f.write("\n")
                        f.write("\n")
                        
                        # 获取数据
                        try:
                            cursor.execute(f"SELECT * FROM {obj_name}")
                            rows = cursor.fetchall()
                            column_names = [col[1] for col in columns_info]
                            
                            if rows:
                                f.write(f"数据 ({len(rows)}行):\n")
                                for i, row in enumerate(rows):
                                    f.write(f"  行 {i+1}:\n")
                                    for col_idx, col_name in enumerate(column_names):
                                        value = row[col_idx]
                                        # 保持原始数据格式
                                        if value is None:
                                            f.write(f"    {col_name}: NULL\n")
                                        else:
                                            # 对于字符串类型，尝试判断是否为JSON
                                            if isinstance(value, str) and (value.startswith('{') or value.startswith('[')):
                                                try:
                                                    # 尝试解析JSON但保留原始格式信息
                                                    json_obj = json.loads(value)
                                                    f.write(f"    {col_name}: <JSON-STRING> {type(value).__name__}\n")
                                                    f.write(f"      {json.dumps(json_obj, indent=4, ensure_ascii=False)}\n")
                                                except json.JSONDecodeError:
                                                    # 不是有效的JSON
                                                    f.write(f"    {col_name}: <STRING> {type(value).__name__}\n")
                                                    if len(value) > 1000:
                                                        f.write(f"      {value[:1000]}... (已截断，总长度: {len(value)})\n")
                                                    else:
                                                        f.write(f"      {value}\n")
                                            else:
                                                f.write(f"    {col_name}: <{type(value).__name__}>\n")
                                                f.write(f"      {value}\n")
                                    f.write("\n")
                            else:
                                f.write("数据: 表为空\n")
                        except sqlite3.Error as e:
                            f.write(f"无法获取数据: {e}\n")
                    else:
                        f.write("无法获取列信息\n")
                except sqlite3.Error as e:
                    f.write(f"获取表结构时出错: {e}\n")
                
                # 获取索引
                try:
                    cursor.execute(f"PRAGMA index_list({obj_name})")
                    indexes = cursor.fetchall()
                    
                    if indexes:
                        f.write("索引:\n")
                        for idx in indexes:
                            idx_seq, idx_name, idx_unique = idx[:3]
                            f.write(f"  {idx_name} (唯一: {'是' if idx_unique else '否'})\n")
                            
                            # 获取索引详情
                            cursor.execute(f"PRAGMA index_info({idx_name})")
                            idx_info = cursor.fetchall()
                            for info in idx_info:
                                col_rank, col_idx, col_name = info
                                f.write(f"    列: {col_name}\n")
                        f.write("\n")
                except sqlite3.Error:
                    # 视图可能不支持索引列表
                    pass
                    
                # 如果是视图，尝试获取视图定义
                if obj_type == 'view':
                    try:
                        cursor.execute(f"SELECT sql FROM sqlite_master WHERE name = '{obj_name}'")
                        view_def = cursor.fetchone()
                        if view_def and view_def[0]:
                            f.write("视图定义:\n")
                            f.write(f"  {view_def[0]}\n\n")
                    except sqlite3.Error as e:
                        f.write(f"获取视图定义时出错: {e}\n")
                
                f.write("\n" + "=" * 80 + "\n\n")
            
        print(f"数据库内容已成功导出到 {output_file}")
        
    except sqlite3.Error as e:
        print(f"SQLite错误: {e}")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main() 