import asyncio
import requests
from PySide6.QtCore import QObject, Signal
from logger import info, error
import threading

class LogoutResult:
    SUCCESS = 'success'
    DISABLED = 'disabled'
    REPEATED = 'repeated'
    FAILED = 'failed'

    @staticmethod
    def classify(response_json):
        if response_json == {}:
            return LogoutResult.SUCCESS
        if 'error' in response_json:
            details = response_json['error'].get('details', [])
            for detail in details:
                error_type = detail.get('error', '')
                error_title = detail.get('details', {}).get('title', '')
                if error_type == 'ERROR_UNAUTHORIZED' and 'Unauthorized' in error_title:
                    return LogoutResult.DISABLED
                if error_type == 'ERROR_NOT_LOGGED_IN' and 'Not logged in' in error_title:
                    return LogoutResult.REPEATED
        return LogoutResult.FAILED

class LogoutBatcher(QObject):
    account_logout_finished = Signal(str, str)  # email, result_type
    all_logout_finished = Signal(dict)  # {email: result_type}

    def __init__(self, accounts, parent=None):
        super().__init__(parent)
        self.accounts = accounts  # List of dicts, each with 'email' and token info
        self.max_workers = 10
        self._results = {}
        info(f"[注销] 批量注销流程开始，待注销账户共 {len(accounts)} 个：" + ', '.join([acc.get('email','') for acc in accounts]))

    def start_logout(self):
        info(f"[注销] 开始批量注销 {len(self.accounts)} 个账户...")
        def run():
            try:
                asyncio.run(self._logout_all())
            except Exception as e:
                error(f"[注销] 后台线程注销批量任务异常: {str(e)}")
        t = threading.Thread(target=run, daemon=True)
        t.start()

    async def _logout_all(self):
        sem = asyncio.Semaphore(self.max_workers)
        tasks = [self._logout_one(acc, sem) for acc in self.accounts]
        await asyncio.gather(*tasks)
        # 统计汇总日志
        stat = {'success':0, 'disabled':0, 'repeated':0, 'failed':0}
        for res in self._results.values():
            if res in stat:
                stat[res] += 1
        info(f"[注销] 批量注销结束，成功 {stat['success']}，被禁用 {stat['disabled']}，重复注销 {stat['repeated']}，失败 {stat['failed']}")
        self.all_logout_finished.emit(self._results)

    async def _logout_one(self, account, sem):
        async with sem:
            email = account.get('email', '')
            token = self._get_token(account)
            info(f"[注销] 开始注销账户: {email}")
            if not token:
                self._results[email] = LogoutResult.FAILED
                error(f"[注销] 账户 {email} 缺少token，注销失败")
                self.account_logout_finished.emit(email, LogoutResult.FAILED)
                return
            try:
                result_type = await asyncio.to_thread(self._logout_api, token)
                self._results[email] = result_type
                info(f"[注销] 账户 {email} 注销结果: {result_type}")
                self.account_logout_finished.emit(email, result_type)
            except Exception as e:
                self._results[email] = LogoutResult.FAILED
                error(f"[注销] 账户 {email} 注销异常: {str(e)}")
                self.account_logout_finished.emit(email, LogoutResult.FAILED)

    def _get_token(self, account):
        auth_info = account.get('auth_info', {})
        return auth_info.get('cursorAuth/accessToken')

    def _logout_api(self, token):
        prefix = 'user_01000000000000000000000000%3A%3A'
        full_token = prefix + token
        cookies = {'WorkosCursorSessionToken': full_token}
        headers = {
            'authority': 'www.cursor.com',
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://www.cursor.com',
            'referer': 'https://www.cursor.com/cn/settings',
            'sec-ch-ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
        }
        data = {}
        try:
            resp = requests.post('https://www.cursor.com/api/dashboard/delete-account', cookies=cookies, headers=headers, json=data, timeout=15)
            try:
                resp_json = resp.json()
            except Exception:
                if resp.text.strip() == '{}':
                    resp_json = {}
                else:
                    error(f"[注销] 账户API响应无法解析为JSON: {resp.text}")
                    return LogoutResult.FAILED
            return LogoutResult.classify(resp_json)
        except Exception as e:
            error(f"[注销] 账户注销API异常: {str(e)}")
            return LogoutResult.FAILED 