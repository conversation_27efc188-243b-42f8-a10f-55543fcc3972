import os
import time
import traceback
import json
import sqlite3
from datetime import datetime
from PySide6.QtCore import QThread, Signal
from core.cursor_auto.advanced_backup_manager import BackupStep, BackupLogger
import shutil
import re # Import re for pattern matching
# --- 添加: 导入全局日志函数 ---
from logger import info as log_info, warning as log_warning, error as log_error, debug as log_debug
# --- 添加结束 ---

class AdvancedBackupWorker(QThread):
    # 信号定义：进度、状态、日志、完成
    progress_signal = Signal(int, int)  # 当前步, 总步数
    status_signal = Signal(int, str, str)  # 步骤索引, 状态(success/fail/running), 消息
    log_signal = Signal(str, str)  # level, message
    finished_signal = Signal(bool)  # 是否全部成功

    def __init__(self, steps: list, mode: str = 'restore', scheme_dir: str = None, parent=None):
        super().__init__(parent)
        self.steps = steps
        self.mode = mode
        self.logger = BackupLogger()
        self._stopped = False
        self.success = True
        self.scheme_dir = scheme_dir  # 方案目录路径
        if self.scheme_dir:
            self.backup_dir = os.path.join(self.scheme_dir, 'backups')
            os.makedirs(self.backup_dir, exist_ok=True)
        else:
            self.backup_dir = None

    def stop(self):
        self._stopped = True

    def _log_and_emit(self, message: str, level: str = 'INFO', original_exception: Exception = None, extra: dict = None):
        """记录日志、进行中文翻译（简化版）并发出信号"""
        # 1. 保留原始日志记录（打印到控制台）
        self.logger.log(message, level)

        # 2. 中文翻译逻辑 (简化示例，可根据需要扩展)
        translated_message = message # 默认为原始消息
        level_prefix = f"[{level}] "
        error_suffix = ""
        if original_exception:
            # 如果有原始异常，附加到末尾
            error_suffix = f" 原始错误: {original_exception}"

        if level == 'INFO':
            level_prefix = "[信息] "
            if message.startswith("开始执行步骤: "):
                step_type = message.split(": ")[1]
                step_map = {'backup': '备份', 'restore': '恢复', 'kill': '结束进程', 'restart': '重启应用', 'delay': '等待'}
                translated_message = f"开始执行步骤: {step_map.get(step_type, step_type)}"
            elif message.startswith("已备份文件到: "):
                path = message.split(": ")[1]
                translated_message = f"成功备份文件到: {path}"
            elif message.startswith("已备份文件夹到: "):
                path = message.split(": ")[1]
                translated_message = f"成功备份文件夹到: {path}"
            elif message.startswith("已备份数据库嵌套key到: "):
                path = message.split(": ")[1]
                translated_message = f"成功备份数据库嵌套键值对到: {path}"
            elif message.startswith("已备份数据库 Key"):
                 match = re.search(r"Key '(.+?)' 的值到: (.+)", message)
                 if match:
                     key, path = match.groups()
                     translated_message = f"成功备份数据库键 '{key}' 的值到: {path}"
            elif message.startswith("已备份数据库表到: "):
                path = message.split(": ")[1]
                translated_message = f"成功备份数据库表到: {path}"
            elif message.startswith("已恢复文件: "):
                match = re.search(r"文件: (.+?) -> (.+)", message)
                if match:
                    src, dest = match.groups()
                    translated_message = f"成功恢复文件: 从 {src} 到 {dest}"
            elif message.startswith("已恢复文件夹: "):
                 match = re.search(r"文件夹: (.+?) -> (.+)", message)
                 if match:
                     src, dest = match.groups()
                     translated_message = f"成功恢复文件夹: 从 {src} 到 {dest}"
            elif message.startswith("已恢复数据库嵌套key: "):
                 path = message.split(": ")[1]
                 translated_message = f"成功恢复数据库嵌套键值对: {path}"
            elif message.startswith("已恢复数据库表: "):
                 path = message.split(": ")[1]
                 translated_message = f"成功恢复数据库表: {path}"
            elif message == "模拟kill Cursor":
                 translated_message = "正在结束相关进程..."
            elif message == "模拟restart Cursor":
                 translated_message = "正在重启应用程序..."
        elif level == 'WARNING':
            level_prefix = "[警告] "
            if message.startswith("任务被用户中断"):
                 translated_message = "任务已被用户手动中断"
            elif message.startswith("未指定备份目录，跳过备份"):
                 translated_message = "未指定有效备份目录，跳过备份操作"
            elif message.startswith("未指定备份目录，跳过恢复"):
                 translated_message = "未指定有效备份目录，跳过恢复操作"
            elif message.startswith("源文件未找到，跳过备份: "):
                 path = message.split(": ")[1]
                 translated_message = f"源文件未找到，跳过备份: {path}"
            elif message.startswith("源文件夹未找到，跳过备份: "):
                 path = message.split(": ")[1]
                 translated_message = f"源文件夹未找到，跳过备份: {path}"
            elif message.startswith("未找到备份文件: "):
                 prefix = message.split(": ")[1]
                 translated_message = f"未找到对应的备份文件，前缀: {prefix}"
            elif message.startswith("未知的备份项类型: "):
                 item_type = message.split(": ")[1]
                 translated_message = f"遇到未知的备份项类型: {item_type}"
            elif message.startswith("未知的恢复项类型: "):
                 item_type = message.split(": ")[1]
                 translated_message = f"遇到未知的恢复项类型: {item_type}"
            elif message.startswith("未找到用于嵌套查找的 Key: "):
                 key = message.split(": ")[1]
                 translated_message = f"数据库嵌套查找失败，未找到顶层键: {key}"
            elif message.startswith("未找到指定的 Key: "):
                 key = message.split(": ")[1]
                 translated_message = f"数据库查找失败，未找到指定的键: {key}"
            elif message.startswith("目标文件夹已存在，将先删除: "):
                 path = message.split(": ")[1]
                 translated_message = f"目标文件夹已存在，将先删除再恢复: {path}"
            elif "嵌套路径中的列表索引" in message:
                 translated_message = message # 保持原始信息，较复杂
            elif "中间遇到非字典/列表值" in message:
                 translated_message = message # 保持原始信息
            elif message.startswith("未找到指定key: "): # For restore
                 key = message.split(": ")[1]
                 translated_message = f"数据库恢复失败，未找到指定键: {key}"
        elif level == 'ERROR':
            level_prefix = "[错误] "
            if message.startswith("步骤失败: "):
                 match = re.search(r"步骤失败: (.+?), 错误: (.+)", message)
                 if match:
                     step, err = match.groups()
                     translated_message = f"执行步骤 '{step}' 失败"
                 else: # Fallback if regex fails
                    translated_message = "执行步骤时发生未知错误"
            elif message.startswith("文件/文件夹备份失败"):
                 match = re.search(r"备份失败 \((.+?): '(.+?)'\): (.+)", message)
                 if match:
                     itype, ipath, _ = match.groups() # Error is in original_exception
                     translated_message = f"{itype} 备份失败: {ipath}"
                 else:
                     translated_message = "文件或文件夹备份时发生错误"
            elif message.startswith("数据库备份失败"):
                 match = re.search(r"备份失败 \((.+?)\): (.+)", message)
                 if match:
                     itype, _ = match.groups()
                     translated_message = f"数据库备份失败 ({itype})"
                 else:
                     translated_message = "数据库备份时发生错误"
            elif message.startswith("文件/文件夹恢复失败"):
                 match = re.search(r"恢复失败 \((.+?): '(.+?)' -> '(.+?)'\): (.+)", message)
                 if match:
                    itype, src, dest, _ = match.groups()
                    translated_message = f"{itype} 恢复失败: 从 {src} 到 {dest}"
                 else:
                    translated_message = "文件或文件夹恢复时发生错误"
            elif message.startswith("数据库恢复失败: "):
                 translated_message = "数据库恢复时发生错误"
            elif message.startswith("无法删除旧备份"):
                translated_message = "清理旧备份文件时发生错误"


        # 组合最终消息
        final_message = f"{level_prefix}{translated_message}{error_suffix}"

        # --- 修改：添加写入中心日志文件 ---
        # 3. 将日志写入中心日志文件 (使用翻译后的消息)
        if level == 'INFO':
            log_info(final_message)
        elif level == 'WARNING':
            log_warning(final_message)
        elif level == 'ERROR':
            log_error(final_message)
        elif level == 'DEBUG':
             log_debug(final_message)
        # --- 修改结束 ---

        # 4. 发出信号 (用于实时显示在进度对话框)
        self.log_signal.emit(level, final_message)

    def run(self):
        total = len(self.steps)
        for idx, step in enumerate(self.steps):
            if self._stopped:
                self._log_and_emit(f"任务被用户中断", 'WARNING')
                self.status_signal.emit(idx, 'fail', '任务被中断')
                self.finished_signal.emit(False)
                return
            try:
                self.progress_signal.emit(idx + 1, total)
                self.status_signal.emit(idx, 'running', f"执行: {step.step_type}")
                self._log_and_emit(f"开始执行步骤: {step.step_type}", 'INFO', extra={'step': idx})
                
                step_success = True # Assume success unless proven otherwise
                # 具体步骤分发
                if step.step_type == 'kill':
                    self._kill_cursor()
                elif step.step_type == 'restart':
                    self._restart_cursor()
                elif step.step_type == 'delay':
                    seconds_to_wait = step.extra.get('seconds', 2)
                    self._log_and_emit(f"等待 {seconds_to_wait} 秒...", 'INFO')
                    time.sleep(seconds_to_wait)
                    self._log_and_emit(f"等待结束", 'INFO')
                elif step.step_type == 'backup':
                    step_success = self._backup_item(step.item)
                    if step_success:
                        keep_val = getattr(step.item, 'keep_count', 1)
                        self._log_and_emit(f"准备清理旧备份，保留最新的 {keep_val} 个...", 'DEBUG')
                        self._prune_old_backups(step.item.get_backup_filename(), keep=keep_val)
                elif step.step_type == 'restore':
                    step_success = self._restore_item(step.item)
                else:
                    self._log_and_emit(f"未知步骤类型: {step.step_type}", 'WARNING')
                    step_success = False # Treat unknown steps as failure

                if step_success:
                     self.status_signal.emit(idx, 'success', f"完成: {step.step_type}")
                else:
                    self.status_signal.emit(idx, 'fail', f"失败: {step.step_type}")
                    self.success = False

            except Exception as e:
                self._log_and_emit(f"步骤失败: {step.step_type}, 错误: {e}", 'ERROR', original_exception=e, extra={'trace': traceback.format_exc()})
                self.status_signal.emit(idx, 'fail', f"失败: {step.step_type}, {e}")
                self.success = False
        self.finished_signal.emit(self.success)

    def _kill_cursor(self):
        # TODO: 实现跨平台kill Cursor进程
        self._log_and_emit("模拟kill Cursor", 'INFO')
        time.sleep(1)

    def _restart_cursor(self):
        # TODO: 实现跨平台重启 Cursor
        self._log_and_emit("模拟restart Cursor", 'INFO')
        time.sleep(1)

    def _backup_item(self, item):
        if not self.backup_dir:
            self._log_and_emit("未指定备份目录，跳过备份", 'WARNING')
            return False
        
        self._log_and_emit(f"开始备份项: {item.name} (类型: {item.item_type}, 路径: {item.path})", 'INFO')
        
        ts = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{item.get_backup_filename()}__{ts}"
        backup_path = os.path.join(self.backup_dir, filename)
        if item.item_type.startswith('db'):
            try:
                self._log_and_emit(f"正在连接数据库: {item.path}", 'INFO')
                conn = sqlite3.connect(item.path)
                cursor = conn.cursor()
                
                data_to_write = None # Initialize to None
                log_msg_suffix = ""

                if item.item_type == 'db_nested_key' and item.db_table and item.db_key_path:
                    log_msg_suffix = f"嵌套键 '{'/'.join(item.db_key_path)}' (表: {item.db_table})"
                    self._log_and_emit(f"正在查询 {log_msg_suffix}...", 'INFO')
                    cursor.execute(f"SELECT value FROM {item.db_table} WHERE key=?", (item.db_key_path[0],))
                    row = cursor.fetchone()
                    if row:
                        value = row[0]
                        # 递归解析嵌套key
                        data = json.loads(value)
                        ref = data
                        valid_path = True
                        for k in item.db_key_path[1:]:
                            if isinstance(ref, list):
                                try:
                                    idx = int(k)
                                    if 0 <= idx < len(ref):
                                        ref = ref[idx]
                                    else:
                                        self._log_and_emit(f"数据库备份警告: 嵌套路径 '{'/'.join(item.db_key_path)}' 中的列表索引 '{k}' 无效或越界", 'WARNING')
                                        ref = None
                                        valid_path = False
                                        break
                                except ValueError:
                                    self._log_and_emit(f"数据库备份警告: 嵌套路径 '{'/'.join(item.db_key_path)}' 中的列表索引 '{k}' 格式无效", 'WARNING')
                                    ref = None
                                    valid_path = False
                                    break
                            elif isinstance(ref, dict):
                                if k in ref:
                                     ref = ref.get(k)
                                else:
                                    self._log_and_emit(f"数据库备份警告: 嵌套路径 '{'/'.join(item.db_key_path)}' 中的键 '{k}' 未找到", 'WARNING')
                                    ref = None
                                    valid_path = False
                                    break
                            else:
                                self._log_and_emit(f"数据库备份警告: 在路径 '{'/'.join(item.db_key_path)}' 中间遇到非字典/列表值，无法继续查找 '{k}'", 'WARNING')
                                ref = None
                                valid_path = False
                                break
                        if valid_path and ref is not None:
                            data_to_write = ref
                        else:
                            self._log_and_emit(f"数据库备份警告: 未能成功提取嵌套键 '{'/'.join(item.db_key_path)}' 的值", 'WARNING')
                    else:
                        self._log_and_emit(f"数据库备份警告: 未找到用于嵌套查找的顶层 Key: {item.db_key_path[0]}", 'WARNING')
                elif item.item_type == 'db_key' and item.db_table and item.db_key_path:
                    key_to_find = item.db_key_path[0]
                    log_msg_suffix = f"键 '{key_to_find}' (表: {item.db_table})"
                    self._log_and_emit(f"正在查询 {log_msg_suffix}...", 'INFO')
                    cursor.execute(f"SELECT value FROM {item.db_table} WHERE key=?", (key_to_find,))
                    row = cursor.fetchone()
                    if row:
                        value = row[0]
                        # 将原始值包装在字典中写入 JSON 文件
                        data_to_write = {"value": value}
                    else:
                        self._log_and_emit(f"数据库备份警告: 未找到指定的 Key: {key_to_find}", 'WARNING')
                elif item.item_type == 'db_table' and item.db_table:
                    log_msg_suffix = f"表 '{item.db_table}'"
                    self._log_and_emit(f"正在查询 {log_msg_suffix} 的所有数据...", 'INFO')
                    cursor.execute(f"SELECT * FROM {item.db_table}")
                    rows = cursor.fetchall()
                    col_names = [desc[0] for desc in cursor.description]
                    data_to_write = [dict(zip(col_names, row)) for row in rows]
                    self._log_and_emit(f"查询到 {len(data_to_write)} 条记录", 'INFO')
                
                # Check if we have something to write before opening file
                if data_to_write is not None:
                    self._log_and_emit(f"正在将 {log_msg_suffix} 写入备份文件: {backup_path}", 'INFO')
                    with open(backup_path, 'w', encoding='utf-8') as f:
                        json.dump(data_to_write, f, ensure_ascii=False, indent=2)
                    # Choose the correct base message based on item type for success log
                    if item.item_type == 'db_nested_key':
                         self._log_and_emit(f"已备份数据库嵌套key到: {backup_path}", 'INFO')
                    elif item.item_type == 'db_key':
                         self._log_and_emit(f"已备份数据库 Key '{item.db_key_path[0]}' 的值到: {backup_path}", 'INFO')
                    elif item.item_type == 'db_table':
                        self._log_and_emit(f"已备份数据库表到: {backup_path}", 'INFO')
                else:
                    # If data_to_write is still None, it means a warning occurred or key wasn't found.
                    # The warnings are already logged. We should return False to indicate backup failure.
                    self._log_and_emit(f"数据库备份跳过: 由于之前的警告或未找到数据，未生成备份文件 {backup_path}", 'WARNING')
                    conn.close()
                    return False

                conn.close()
                self._log_and_emit(f"数据库连接已关闭: {item.path}", 'INFO')
            except Exception as e:
                self._log_and_emit(f"数据库备份失败 ({item.item_type}): {e}", 'ERROR', original_exception=e)
                return False
        else:
            # 实现：真实文件/文件夹备份
            source_path = item.path
            target_desc = f"{item.item_type} '{source_path}' 到 '{backup_path}'"
            self._log_and_emit(f"准备备份 {target_desc}...", 'INFO')
            try:
                if item.item_type == 'file':
                    if os.path.isfile(source_path):
                        os.makedirs(os.path.dirname(backup_path), exist_ok=True) # Ensure backup dir exists
                        self._log_and_emit(f"正在复制文件...", 'INFO')
                        shutil.copy2(source_path, backup_path)
                        self._log_and_emit(f"已备份文件到: {backup_path}", 'INFO')
                    else:
                        self._log_and_emit(f"源文件未找到，跳过备份: {source_path}", 'WARNING')
                        return False # Indicate skipped/failed
                elif item.item_type == 'folder':
                    if os.path.isdir(source_path):
                        # Copytree will create the final directory (backup_path)
                        self._log_and_emit(f"正在复制文件夹 (可能需要一些时间)...", 'INFO')
                        shutil.copytree(source_path, backup_path, dirs_exist_ok=True)
                        self._log_and_emit(f"已备份文件夹到: {backup_path}", 'INFO')
                    else:
                        self._log_and_emit(f"源文件夹未找到，跳过备份: {source_path}", 'WARNING')
                        return False # Indicate skipped/failed
                else:
                    self._log_and_emit(f"未知的备份项类型: {item.item_type}", 'WARNING')
                    return False # Indicate skipped/failed
            except (shutil.Error, OSError, FileNotFoundError, PermissionError) as e:
                self._log_and_emit(f"文件/文件夹备份失败 ({item.item_type}: '{source_path}'): {e}", 'ERROR', original_exception=e)
                return False # Indicate failure
            # 实现结束
        self._log_and_emit(f"完成备份项: {item.name}", 'INFO')
        return True # Indicate success

    def _restore_item(self, item):
        if not self.backup_dir:
            self._log_and_emit("未指定备份目录，跳过恢复", 'WARNING')
            return False
            
        self._log_and_emit(f"开始恢复项: {item.name} (类型: {item.item_type}, 目标路径: {item.path})", 'INFO')
        # 查找最新的备份文件
        prefix = item.get_backup_filename()
        self._log_and_emit(f"正在查找备份文件，前缀: {prefix} in {self.backup_dir}", 'INFO')
        files = [f for f in os.listdir(self.backup_dir) if f.startswith(prefix)]
        if not files:
            self._log_and_emit(f"未找到备份文件: {prefix}", 'WARNING')
            return False
        files.sort(reverse=True)
        backup_path = os.path.join(self.backup_dir, files[0])
        self._log_and_emit(f"找到最新备份文件: {backup_path}", 'INFO')
        
        if item.item_type.startswith('db'):
            try:
                self._log_and_emit(f"正在连接数据库: {item.path}", 'INFO')
                conn = sqlite3.connect(item.path)
                cursor = conn.cursor()
                
                log_msg_suffix = ""
                operation_performed = False
                
                if item.item_type == 'db_nested_key' and item.db_table and item.db_key_path:
                    log_msg_suffix = f"嵌套键 '{'/'.join(item.db_key_path)}' (表: {item.db_table})"
                    self._log_and_emit(f"准备恢复 {log_msg_suffix} 从 {backup_path}", 'INFO')
                    # 嵌套key导入
                    with open(backup_path, 'r', encoding='utf-8') as f:
                        data_from_backup = json.load(f)
                    # 先查出原始value
                    top_key = item.db_key_path[0]
                    self._log_and_emit(f"正在查询数据库中当前的顶层键: {top_key}", 'INFO')
                    cursor.execute(f"SELECT value FROM {item.db_table} WHERE key=?", (top_key,))
                    row = cursor.fetchone()
                    if row:
                        value_str = row[0]
                        try:
                            obj = json.loads(value_str)
                            # 递归写入嵌套key
                            ref = obj
                            valid_path = True
                            for k in item.db_key_path[1:-1]: # Iterate to the second last key
                                if isinstance(ref, list):
                                    try:
                                        idx = int(k)
                                        if 0 <= idx < len(ref):
                                            ref = ref[idx]
                                        else:
                                            self._log_and_emit(f"数据库恢复警告: 嵌套路径中的列表索引 '{k}' 无效或越界，无法定位目标位置", 'WARNING')
                                            valid_path = False
                                            break
                                    except ValueError:
                                        self._log_and_emit(f"数据库恢复警告: 嵌套路径中的列表索引 '{k}' 格式无效，无法定位目标位置", 'WARNING')
                                        valid_path = False
                                        break
                                elif isinstance(ref, dict):
                                    if k in ref:
                                         ref = ref.get(k)
                                    else:
                                         # If the intermediate key doesn't exist, maybe create it?
                                         # For now, let's treat it as an error condition for restore.
                                         self._log_and_emit(f"数据库恢复警告: 嵌套路径中的键 '{k}' 不存在，无法定位目标位置", 'WARNING')
                                         valid_path = False
                                         break
                                else:
                                    self._log_and_emit(f"数据库恢复警告: 在路径 '{'/'.join(item.db_key_path)}' 中间遇到非字典/列表值，无法继续查找 '{k}'", 'WARNING')
                                    valid_path = False
                                    break
                                    
                            if valid_path:
                                last_key = item.db_key_path[-1]
                                # Now check if the final level target (ref) is a dict or list
                                if isinstance(ref, dict):
                                    self._log_and_emit(f"正在将备份数据写入字典键: {last_key}", 'INFO')
                                    ref[last_key] = data_from_backup
                                    operation_performed = True
                                elif isinstance(ref, list):
                                     try:
                                         idx = int(last_key)
                                         if 0 <= idx < len(ref):
                                             self._log_and_emit(f"正在将备份数据写入列表索引: {idx}", 'INFO')
                                             ref[idx] = data_from_backup
                                             operation_performed = True
                                         else:
                                             self._log_and_emit(f"数据库恢复警告: 最终目标列表索引 '{last_key}' 无效或越界", 'WARNING')
                                     except ValueError:
                                          self._log_and_emit(f"数据库恢复警告: 最终目标列表索引 '{last_key}' 格式无效", 'WARNING')
                                else:
                                     self._log_and_emit(f"数据库恢复警告: 无法向非字典/列表类型写入键/索引 '{last_key}'", 'WARNING')
                                     
                                if operation_performed:
                                    new_value = json.dumps(obj, ensure_ascii=False)
                                    self._log_and_emit(f"正在更新数据库中的顶层键: {top_key}", 'INFO')
                                    cursor.execute(f"UPDATE {item.db_table} SET value=? WHERE key=?", (new_value, top_key))
                                    conn.commit()
                                    self._log_and_emit(f"已恢复数据库嵌套key: {backup_path}", 'INFO')
                                else:
                                     # Previous logs explain why operation_performed is False
                                     self._log_and_emit(f"数据库恢复跳过: 未能将备份数据写入目标路径", 'WARNING')
                        except json.JSONDecodeError as json_e:
                             self._log_and_emit(f"数据库恢复错误: 无法解析数据库中的原始JSON数据 (key: {top_key}) - {json_e}", 'ERROR', original_exception=json_e)
                             # Cannot proceed if base JSON is invalid
                    else:
                        self._log_and_emit(f"数据库恢复警告: 未找到用于恢复的顶层 Key: {top_key}", 'WARNING')
                elif item.item_type == 'db_key' and item.db_table and item.db_key_path:
                     key_to_restore = item.db_key_path[0]
                     log_msg_suffix = f"键 '{key_to_restore}' (表: {item.db_table})"
                     self._log_and_emit(f"准备恢复 {log_msg_suffix} 从 {backup_path}", 'INFO')
                     try:
                         with open(backup_path, 'r', encoding='utf-8') as f:
                             data_from_backup = json.load(f)
                             # Expecting {"value": actual_value} format based on backup logic
                             value_to_restore = data_from_backup.get("value")
                             if value_to_restore is not None:
                                 self._log_and_emit(f"正在更新数据库键 '{key_to_restore}' 的值...", 'INFO')
                                 # Use INSERT OR REPLACE to handle both insert and update cases
                                 cursor.execute(f"INSERT OR REPLACE INTO {item.db_table} (key, value) VALUES (?, ?)", (key_to_restore, value_to_restore))
                                 conn.commit()
                                 self._log_and_emit(f"成功恢复数据库键 '{key_to_restore}' 的值", 'INFO')
                                 operation_performed = True
                             else:
                                 self._log_and_emit(f"数据库恢复警告: 备份文件 {backup_path} 中未找到有效的 'value' 字段", 'WARNING')
                     except FileNotFoundError:
                         self._log_and_emit(f"数据库恢复错误: 备份文件未找到: {backup_path}", 'ERROR')
                     except json.JSONDecodeError as json_e:
                         self._log_and_emit(f"数据库恢复错误: 无法解析备份文件 {backup_path} - {json_e}", 'ERROR', original_exception=json_e)

                elif item.item_type == 'db_table' and item.db_table:
                    log_msg_suffix = f"表 '{item.db_table}'"
                    self._log_and_emit(f"准备恢复 {log_msg_suffix} 从 {backup_path}", 'INFO')
                    # 整个表导入
                    try:
                        with open(backup_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        if isinstance(data, list):
                            # 清空表
                            self._log_and_emit(f"正在清空数据库表: {item.db_table}", 'INFO')
                            cursor.execute(f"DELETE FROM {item.db_table}")
                            # 插入数据
                            self._log_and_emit(f"正在向表 {item.db_table} 插入 {len(data)} 条记录...", 'INFO')
                            inserted_count = 0
                            for row in data:
                                if isinstance(row, dict) and row: # Ensure row is a non-empty dict
                                    cols = ','.join(row.keys())
                                    placeholders = ','.join(['?'] * len(row))
                                    values = list(row.values())
                                    try:
                                        cursor.execute(f"INSERT INTO {item.db_table} ({cols}) VALUES ({placeholders})", values)
                                        inserted_count += 1
                                    except sqlite3.Error as insert_err:
                                         self._log_and_emit(f"数据库恢复警告: 插入行失败 (表: {item.db_table}, 数据: {row}): {insert_err}", 'WARNING', original_exception=insert_err)
                                else:
                                    self._log_and_emit(f"数据库恢复警告: 跳过无效的行数据 (表: {item.db_table}, 数据: {row})", 'WARNING')
                            conn.commit()
                            self._log_and_emit(f"成功向表 {item.db_table} 插入 {inserted_count} 条记录", 'INFO')
                            self._log_and_emit(f"已恢复数据库表: {backup_path}", 'INFO')
                            operation_performed = True
                        else:
                             self._log_and_emit(f"数据库恢复错误: 备份文件 {backup_path} 的内容不是有效的列表格式", 'ERROR')
                    except FileNotFoundError:
                        self._log_and_emit(f"数据库恢复错误: 备份文件未找到: {backup_path}", 'ERROR')
                    except json.JSONDecodeError as json_e:
                        self._log_and_emit(f"数据库恢复错误: 无法解析备份文件 {backup_path} - {json_e}", 'ERROR', original_exception=json_e)
                
                # Check if the intended operation was actually performed before closing connection
                if not operation_performed:
                    # Warnings/Errors should have been logged already in the specific block
                    self._log_and_emit(f"数据库恢复未完成: {log_msg_suffix}", 'WARNING')
                    conn.close()
                    return False # Indicate failure for this item
                    
                conn.close()
                self._log_and_emit(f"数据库连接已关闭: {item.path}", 'INFO')
            except Exception as e:
                self._log_and_emit(f"数据库恢复失败: {e}", 'ERROR', original_exception=e)
                return False
        else:
            # 实现：文件/文件夹恢复
            target_path = item.path
            target_desc = f"{item.item_type} 从 '{backup_path}' 到 '{target_path}'"
            self._log_and_emit(f"准备恢复 {target_desc}...", 'INFO')
            try:
                if item.item_type == 'file':
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    self._log_and_emit(f"正在复制文件...", 'INFO')
                    shutil.copy2(backup_path, target_path)
                    self._log_and_emit(f"已恢复文件: {backup_path} -> {target_path}", 'INFO')
                elif item.item_type == 'folder':
                    if os.path.exists(target_path):
                        # Be careful: removing existing target directory
                        self._log_and_emit(f"目标文件夹已存在，将先删除: {target_path}", 'WARNING')
                        try:
                            shutil.rmtree(target_path)
                            self._log_and_emit(f"已删除旧的目标文件夹: {target_path}", 'INFO')
                        except OSError as del_e:
                             self._log_and_emit(f"删除目标文件夹失败: {target_path}, 错误: {del_e}", 'ERROR', original_exception=del_e)
                             return False # Cannot proceed if deletion fails
                    self._log_and_emit(f"正在复制文件夹 (可能需要一些时间)...", 'INFO')
                    shutil.copytree(backup_path, target_path)
                    self._log_and_emit(f"已恢复文件夹: {backup_path} -> {target_path}", 'INFO')
                else:
                    self._log_and_emit(f"未知的恢复项类型: {item.item_type}", 'WARNING')
                    return False
            except (shutil.Error, OSError, FileNotFoundError, PermissionError) as e:
                self._log_and_emit(f"文件/文件夹恢复失败 ({item.item_type}: '{backup_path}' -> '{target_path}'): {e}", 'ERROR', original_exception=e)
                return False
            # 实现结束
        self._log_and_emit(f"完成恢复项: {item.name}", 'INFO')
        return True # Indicate success

    def _prune_old_backups(self, item_base_filename, keep=5):
        """清理指定备份项的旧备份，只保留最新的 N 个"""
        if not self.backup_dir:
            self._log_and_emit("未指定备份目录，无法清理旧备份", 'WARNING')
            return
        if keep <= 0:
            self._log_and_emit(f"保留数量设置为 {keep}，不清理旧备份: {item_base_filename}", 'INFO')
            return

        self._log_and_emit(f"开始清理旧备份: {item_base_filename}, 保留最新的 {keep} 个", 'INFO')
        try:
            prefix = item_base_filename
            files = [f for f in os.listdir(self.backup_dir) if f.startswith(prefix)]
            files.sort(reverse=True) # Sort newest first

            if len(files) > keep:
                files_to_delete = files[keep:]
                self._log_and_emit(f"找到 {len(files_to_delete)} 个旧备份需要删除", 'INFO')
                for f_del in files_to_delete:
                    path_to_del = os.path.join(self.backup_dir, f_del)
                    try:
                        if os.path.isfile(path_to_del):
                            os.remove(path_to_del)
                            self._log_and_emit(f"已删除旧备份文件: {f_del}", 'INFO')
                        elif os.path.isdir(path_to_del):
                            shutil.rmtree(path_to_del)
                            self._log_and_emit(f"已删除旧备份文件夹: {f_del}", 'INFO')
                    except OSError as e:
                        self._log_and_emit(f"无法删除旧备份 '{f_del}': {e}", 'ERROR', original_exception=e)
            else:
                 self._log_and_emit(f"备份数量 ({len(files)}) 未超过限制 ({keep})，无需清理", 'INFO')
        except Exception as e:
            self._log_and_emit(f"清理旧备份时发生错误: {e}", 'ERROR', original_exception=e) 