#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导入选择对话框模块
提供账户导入方式选择功能
"""

from PySide6.QtWidgets import (
    QFrame, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt

from theme import Theme
from widgets.dialog import StyledDialog
from ui.dialogs.import_accounts_dialog import ImportAccountsDialog
from ui.dialogs.manual_import_dialog import ManualImportDialog
from logger import info

class ImportSelectDialog(QFrame):
    """导入选择对话框类"""
    
    def __init__(self, main_window, parent=None):
        """初始化导入选择对话框
        
        Args:
            main_window: 主窗口实例 (CursorAccountManager)
            parent: 父窗口
        """
        super().__init__(parent)
        self.main_window = main_window
        self.dialog = StyledDialog(parent or main_window, "选择导入方式")
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        # 创建说明标签
        description_label = QLabel("请选择导入方式：")
        description_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            margin-bottom: 10px;
        """)
        self.dialog.addWidget(description_label)
        
        # 创建主垂直布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        
        # 创建第一行按钮布局
        first_row_layout = QHBoxLayout()
        first_row_layout.setSpacing(10)
        
        # 文件导入按钮
        file_import_btn = QPushButton("📄 文件导入")
        file_import_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        file_import_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_2};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 12px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
                text-align: center;
            }}
            QPushButton:hover {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
            }}
            QPushButton:pressed {{
                background-color: #1A1D23;
            }}
        """)
        file_import_btn.clicked.connect(self._on_file_import_clicked)
        first_row_layout.addWidget(file_import_btn)
        
        # 手动输入按钮
        manual_import_btn = QPushButton("✏️ 手动输入")
        manual_import_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        manual_import_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_2};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 12px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
                text-align: center;
            }}
            QPushButton:hover {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
            }}
            QPushButton:pressed {{
                background-color: #1A1D23;
            }}
        """)
        manual_import_btn.clicked.connect(self._on_manual_import_clicked)
        first_row_layout.addWidget(manual_import_btn)
        
        # 将第一行添加到主布局
        main_layout.addLayout(first_row_layout)
        
        # 创建第二行按钮布局
        second_row_layout = QHBoxLayout()
        
        # 保存当前登录账户按钮
        save_current_btn = QPushButton("💾 保存当前账户")
        save_current_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        save_current_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 12px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
                text-align: center;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        save_current_btn.clicked.connect(self._on_save_current_clicked)
        second_row_layout.addWidget(save_current_btn)
        
        # 将第二行添加到主布局
        main_layout.addLayout(second_row_layout)
        
        # 将主布局添加到对话框
        self.dialog.addLayout(main_layout)
        
        # 添加ESC键提示
        esc_hint = QLabel("按 ESC键 关闭对话框")
        esc_hint.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: {Theme.FONT_SIZE_SMALL};
            margin-top: 15px;
            text-align: center;
        """)
        esc_hint.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.dialog.addWidget(esc_hint)
    
    def _on_file_import_clicked(self):
        """文件导入按钮点击处理函数"""
        # 关闭选择对话框
        self.dialog.accept()
        
        # 创建并显示文件导入对话框
        info("用户选择了文件导入方式")
        dialog = ImportAccountsDialog(self.main_window)
        dialog.show_dialog()
    
    def _on_manual_import_clicked(self):
        """手动输入按钮点击处理函数"""
        # 关闭选择对话框
        self.dialog.accept()
        
        # 创建并显示手动输入对话框
        info("用户选择了手动输入方式")
        dialog = ManualImportDialog(self.main_window, self.main_window)
        dialog.exec()
    
    def _on_save_current_clicked(self):
        """保存当前登录账户按钮点击处理函数"""
        # 关闭选择对话框
        self.dialog.accept()
        
        # 调用主窗口的保存当前账户方法
        info("用户选择了保存当前登录账户")
        self.main_window.save_current_account()
    
    def exec(self):
        """显示对话框"""
        return self.dialog.exec() 