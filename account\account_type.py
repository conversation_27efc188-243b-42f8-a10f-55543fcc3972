#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户类型获取模块
通过调用Cursor的stripe API获取账户的membershipType信息
"""

import requests
import json
from typing import Dict, Optional, Tuple


class AccountType:
    """获取账户类型的类"""
    
    @staticmethod
    def get_account_type(account_data, max_retries=3, retry_delay=2) -> <PERSON>ple[Optional[str], Optional[int], Optional[bool]]:
        """
        获取账户类型信息
        
        Args:
            account_data: 账户数据，可以是cookies数组或包含auth_info的账户字典
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
            
        Returns:
            Tuple[membershipType, daysRemainingOnTrial, verifiedStudent]:
            - membershipType: 账户类型 ("free_trial", "pro", "free", 等)
            - daysRemainingOnTrial: 试用剩余天数
            - verifiedStudent: 是否为认证学生
        """
        # 获取账户的邮箱，用于日志记录
        email = account_data.get('email', 'unknown') if isinstance(account_data, dict) else 'unknown'
        
        # 定义重试计数器
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                # 修改后采用拼接方式构建token值
                fixed_prefix = "user_01000000000000000000000000%3A%3A"
                
                # 获取accessToken的方式1：从auth_info中获取
                access_token = None
                
                # 检查account_data是否是字典类型且包含auth_info
                if isinstance(account_data, dict) and 'auth_info' in account_data:
                    auth_info = account_data.get('auth_info', {})
                    access_token = auth_info.get('cursorAuth/accessToken')
                    print(f"从auth_info中获取到账户 {email} 的accessToken，长度: {len(access_token) if access_token else 0}")
                # 获取accessToken的方式2：从cookies中获取
                elif isinstance(account_data, list):  # 旧方式，传入的是cookies数组
                    cookies = account_data
                    for cookie in cookies:
                        if cookie.get('name') == 'WorkosCursorSessionToken':
                            # 尝试从cookie中提取access_token部分
                            cookie_value = cookie.get('value', '')
                            if "%3A%3A" in cookie_value:
                                access_token = cookie_value.split("%3A%3A")[-1]
                                print(f"从cookies中提取账户的accessToken，长度: {len(access_token) if access_token else 0}")
                            break
                
                if not access_token:
                    print(f"账户 {email} 无法获取accessToken")
                    return None, None, None
                
                # 构建完整的token
                full_token = fixed_prefix + access_token
                
                # 设置请求头
                headers = {
                    "accept": "*/*",
                    "accept-language": "zh-CN,zh;q=0.9",
                    "content-type": "application/json",
                    "if-none-match": "\"99yw4unpyu29\"",
                    "priority": "u=1, i",
                    "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
                    "sec-ch-ua-arch": "\"x86\"",
                    "sec-ch-ua-bitness": "\"64\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-ch-ua-platform-version": "\"19.0.0\"",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-origin",
                    "cookie": f"WorkosCursorSessionToken={full_token}",
                    "referer": "https://www.cursor.com/dashboard",
                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                }
                
                # 发送请求
                print(f"开始获取账户 {email} 的类型信息...")
                response = requests.get(
                    "https://cursor.com/api/auth/stripe",
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"成功获取账户 {email} 的类型信息")
                        
                        # 提取关键信息
                        membership_type = data.get("membershipType")
                        days_remaining = data.get("daysRemainingOnTrial")
                        verified_student = data.get("verifiedStudent", False)
                        
                        print(f"成功获取账户 {email} 类型信息: membershipType={membership_type}, daysRemainingOnTrial={days_remaining}, verifiedStudent={verified_student}")

                        return membership_type, days_remaining, verified_student
                        
                    except json.JSONDecodeError as e:
                        print(f"解析账户 {email} 类型信息JSON时出错: {str(e)}")
                        print(f"响应内容: {response.text[:200]}...")
                        return None, None, None
                        
                elif response.status_code == 401:
                    print(f"账户 {email} 认证失败，可能token已过期")
                    return None, None, None
                    
                elif response.status_code == 429:
                    print(f"账户 {email} 请求过于频繁，等待重试...")
                    import time
                    time.sleep(retry_delay * 2)  # 被限流时等待更长时间
                    
                else:
                    print(f"获取账户 {email} 类型信息失败，状态码: {response.status_code}")
                    print(f"响应内容: {response.text[:200]}...")
                    
            except requests.exceptions.Timeout:
                print(f"获取账户 {email} 类型信息超时，重试中...")
                
            except requests.exceptions.RequestException as e:
                print(f"获取账户 {email} 类型信息时网络错误: {str(e)}")
                
            except Exception as e:
                print(f"获取账户 {email} 类型信息时出错: {str(e)}")
                
            # 重试逻辑
            retry_count += 1
            if retry_count <= max_retries:
                print(f"账户 {email} 类型信息获取失败，{retry_delay}秒后重试 ({retry_count}/{max_retries})")
                import time
                time.sleep(retry_delay)
            else:
                print(f"账户 {email} 类型信息获取失败，已达到最大重试次数")
                
        return None, None, None

    @staticmethod
    def format_account_type_display(membership_type: Optional[str], days_remaining: Optional[int] = None) -> Tuple[str, str]:
        """
        格式化账户类型显示文本
        
        Args:
            membership_type: 账户类型
            days_remaining: 剩余天数（仅对free_trial有效）
            
        Returns:
            Tuple[display_text, label_text]: (显示文本, 标签文本)
        """
        if membership_type == "free_trial":
            if days_remaining is not None:
                return f"{days_remaining}", "剩余天数:"
            else:
                return "试用中", "账户类型:"
                
        elif membership_type == "pro":
            return "尊贵的有钱人Pro用户", "账户类型:"
            
        elif membership_type == "free":
            return "普通账户", "账户类型:"
            
        else:
            # 其他类型或未知类型
            if membership_type:
                return f"其他类型账户({membership_type})", "账户类型:"
            else:
                return "未知", "账户类型:"

    @staticmethod
    def get_account_type_color(membership_type: Optional[str]):
        """
        获取账户类型对应的颜色

        Args:
            membership_type: 账户类型

        Returns:
            颜色主题
        """
        try:
            from theme import Theme

            if membership_type == "pro":
                return Theme.GOLD  # 金色 - Pro用户
            elif membership_type == "free_trial":
                return Theme.SUCCESS  # 绿色 - 试用用户
            elif membership_type == "free":
                return Theme.TEXT_SECONDARY  # 灰色 - 免费用户
            else:
                return Theme.TEXT_SECONDARY  # 灰色 - 其他/未知类型
        except ImportError:
            # 如果无法导入Theme，返回字符串描述
            if membership_type == "pro":
                return "GOLD"  # 金色 - Pro用户
            elif membership_type == "free_trial":
                return "SUCCESS"  # 绿色 - 试用用户
            elif membership_type == "free":
                return "TEXT_SECONDARY"  # 灰色 - 免费用户
            else:
                return "TEXT_SECONDARY"  # 灰色 - 其他/未知类型
