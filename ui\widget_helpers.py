"""
UI组件辅助函数模块
提供创建和操作UI组件的通用函数
"""

from PySide6.QtWidgets import QWidget, QFrame, QLabel, QHBoxLayout, QVBoxLayout, QApplication
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QCursor

from theme import Theme

def create_copy_card(main_window, icon_text, icon_image, value, is_copyable=True, container_id=None, label=None):
    """创建统一样式的复制卡片
    
    Args:
        main_window: 主窗口实例，用于调用主窗口的方法
        icon_text: 图标文本（当图片不可用时）
        icon_image: 图标图片路径
        value: 显示和复制的值
        is_copyable: 是否可复制
        container_id: 容器ID
        label: 标签文本
        
    Returns:
        QFrame: 创建的卡片
    """
    # 创建卡片容器
    card = QFrame()
    if container_id:
        card.setObjectName(container_id)
    
    # 设置统一的样式
    card.setStyleSheet(f"""
        #{container_id} {{
            background-color: {Theme.CARD_LEVEL_2};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            border: 1px solid {Theme.GLASS_BORDER};
        }}
        #{container_id}:hover {{
            border: 1px solid {Theme.ACCENT};
        }}
    """)
    
    # 根据是否有标签调整高度
    if label:
        card.setFixedHeight(60)  # 有标签时略微增加高度
    else:
        card.setFixedHeight(50)  # 默认高度
        
    # 是否可复制，设置鼠标样式和点击事件
    if is_copyable:
        card.setCursor(Qt.CursorShape.PointingHandCursor)
        # 为整个卡片添加点击事件
        card.mousePressEvent = lambda e: copy_text_to_clipboard(main_window, e, value, card, None, None) if e.button() == Qt.MouseButton.LeftButton else None
    
    # 创建水平布局
    card_layout = QHBoxLayout(card)
    card_layout.setContentsMargins(15, 0, 15, 0)
    card_layout.setSpacing(12)
    
    # 创建图标
    icon = QLabel()
    icon_pixmap = QPixmap(icon_image)
    if not icon_pixmap.isNull():
        icon.setPixmap(icon_pixmap.scaled(22, 22, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
    else:
        icon.setText(icon_text)
        icon.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: 18px;
            background-color: transparent;
        """)
    icon.setFixedSize(22, 22)
    icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
    card_layout.addWidget(icon, 0, Qt.AlignmentFlag.AlignVCenter)  # 设置图标垂直居中
    
    # 创建内容容器，用于垂直布局标签和值
    content_container = QWidget()
    content_container.setStyleSheet("background-color: transparent;")
    content_layout = QVBoxLayout(content_container)
    content_layout.setContentsMargins(0, 4, 0, 4)
    content_layout.setSpacing(2)
    
    # 添加标签（如果有）
    if label:
        label_widget = QLabel(label)
        label_widget.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: 12px;
            background-color: transparent;
        """)
        content_layout.addWidget(label_widget)
    
    # 创建内容/值部分
    if is_copyable:
        # 使用文本标签替代可复制标签，因为整个卡片已经是可点击的
        value_label = QLabel(value)
        text_color = Theme.TEXT_PRIMARY
        
        # 处理长文本
        if len(value) > 40:
            value_label.setWordWrap(False)
            fm = value_label.fontMetrics()
            
            # 对于非常长的文本（如Token），使用更极端的截断
            if len(value) > 100:  # Token通常很长
                # 显示前10个字符和后5个字符，中间用省略号
                displayed_text = f"{value[:10]}...{value[-5:]}"
                # 设置工具提示，鼠标悬停时显示完整文本
                value_label.setToolTip(value)
            else:
                # 对于中等长度文本，使用常规的省略模式
                elided_text = fm.elidedText(value, Qt.TextElideMode.ElideMiddle, 300)
                displayed_text = elided_text
            
            value_label.setText(displayed_text)
        
        value_label.setStyleSheet(f"""
            color: {text_color};
            background-color: transparent;
        """)
    else:
        # 使用普通标签
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
        """)
    
    content_layout.addWidget(value_label)
    
    # 添加内容容器到卡片布局
    card_layout.addWidget(content_container, 1, Qt.AlignmentFlag.AlignVCenter)  # 设置内容垂直居中
    
    # 复制图标
    if is_copyable:
        copy_icon = QLabel("⧉")
        copy_icon.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            background-color: transparent;
            font-size: 16px;
        """)
        copy_icon.setFixedSize(20, 20)
        card_layout.addWidget(copy_icon, 0, Qt.AlignmentFlag.AlignVCenter)  # 设置图标垂直居中
    
    return card
    
def copy_text_to_clipboard(main_window, event, text, container, text_label=None, copy_icon=None):
    """复制文本到剪贴板并显示反馈
    
    Args:
        main_window: 主窗口实例，用于调用主窗口的方法
        event: 鼠标事件
        text: 要复制的文本
        container: 容器组件
        text_label: 文本标签（可为None）
        copy_icon: 复制图标（可为None）
    """
    if event.button() == Qt.MouseButton.LeftButton:
        # 复制到剪贴板
        clipboard = QApplication.clipboard()
        clipboard.setText(text)
        
        # 显示视觉反馈
        if text_label and copy_icon:
            text_label.setStyleSheet("color: white; background-color: transparent;")
            copy_icon.setStyleSheet("color: white; background-color: transparent; font-size: 16px;")
            copy_icon.setText("✓")
            
            # 0.5秒后恢复
            QTimer.singleShot(500, lambda: reset_copy_feedback(container, text_label, copy_icon))
        else:
            # 如果是整个卡片的复制，临时改变卡片边框颜色
            original_style = container.styleSheet()
            container.setStyleSheet(original_style + f"border: 1px solid {Theme.SUCCESS};")
            
            # 0.5秒后恢复
            QTimer.singleShot(500, lambda: container.setStyleSheet(original_style))
        
        # 创建适合显示的文本摘要
        display_text = text
        if len(display_text) > 20:
            # 如果文本超过20个字符，截取前10个字符和后5个字符，中间用...代替
            display_text = display_text[:10] + "..." + display_text[-5:]
        
        # 显示包含被复制内容的提示
        main_window.show_toast(f"「{display_text}」复制成功")

def reset_copy_feedback(container, text_label, copy_icon):
    """重置复制反馈，恢复原始显示
    
    Args:
        container: 容器组件
        text_label: 文本标签（可为None）
        copy_icon: 复制图标（可为None）
    """
    # 重置容器样式
    container.setStyleSheet(f"""
        background-color: {Theme.CARD_LEVEL_2};
        border-radius: {Theme.BORDER_RADIUS_SMALL};
        padding: 10px;
    """)
    
    # 重置标签样式
    if text_label:
        text_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY};")
        
    # 重置图标
    if copy_icon:
        copy_icon.setText("⧉")  # 复制图标
        copy_icon.setStyleSheet(f"color: {Theme.TEXT_SECONDARY};") 