#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户管理组件模块
提供账户信息行显示、交互和状态管理功能，支持账户切换和删除操作
"""

import sys
import math
from datetime import datetime, timezone, timedelta
import time

from PySide6.QtWidgets import (
    QWidget, QLabel, QHBoxLayout, QVBoxLayout, QFrame, QSpacerItem, QSizePolicy, 
    QPushButton, QMenu
)
from PySide6.QtCore import Qt, Signal
from dateutil import parser

from theme import Theme
from widgets.animated_widgets import AnimatedNumberLabel
from widgets.animated_widgets import AnimatedProgressBar


# 账户信息行类，用于账户管理页面
class AccountRowWidget(QWidget):
    """账户信息行组件"""
    
    # 定义信号
    switch_account_signal = Signal(dict)
    delete_account_signal = Signal(str)
    show_details_signal = Signal(dict)  # 新增显示详情信号
    
    def __init__(self, account_data, is_current=False, parent=None):
        super().__init__(parent)
        self.account_data = account_data
        self.is_current = is_current
        self.is_loading = False
        self.init_ui()
    
    def update_account_data(self, new_account_data):
        """更新账户数据
        
        Args:
            new_account_data: 新的账户数据
        """
        self.account_data = new_account_data
        
        # 更新UI显示
        email = self.account_data.get("email", "")
        account_type = self.account_data.get("account_type", "未知")
        username = self.account_data.get("username", "未知")
        
        # 更新标签文本
        if hasattr(self, 'email_label'):
            self.email_label.setText(email)
        
        if hasattr(self, 'type_label'):
            self.type_label.setText(f"类型：{account_type}")
            
        if hasattr(self, 'username_label'):
            self.username_label.setText(f"用户名：{username}")
    
    def set_current(self, is_current):
        """设置账户是否为当前账户
        
        Args:
            is_current: 是否为当前账户
        """
        if self.is_current == is_current:
            return  # 状态没有变化，无需更新
            
        self.is_current = is_current
        
        # 更新样式
        bg_color = Theme.CARD_LEVEL_2 if self.is_current else Theme.CARD_LEVEL_1
        border_style = f"border-left: 4px solid {Theme.ACCENT};" if self.is_current else ""
        
        self.setStyleSheet(f"""
            AccountRowWidget {{
                background-color: {bg_color};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                {border_style}
                margin: 4px 2px;
                padding: 0px;
            }}
            
            AccountRowWidget:hover {{
                background-color: {Theme.HOVER};
            }}
            
            QLabel {{
                padding: 0px;
                margin: 0px;
            }}
        """)
        
        # 更新邮箱标签样式
        if hasattr(self, 'email_label'):
            self.email_label.setStyleSheet(f"""
                font-size: {Theme.FONT_SIZE_NORMAL};
                font-weight: {'bold' if self.is_current else 'normal'};
                color: {Theme.SUCCESS if self.is_current else Theme.TEXT_PRIMARY};
                background-color: transparent;
                padding: 5px 0;
            """)
        
        # 更新切换按钮状态
        if hasattr(self, 'switch_btn'):
            # 如果是当前账户，禁用切换按钮
            if self.is_current:
                self.switch_btn.setText("更换")
                self.switch_btn.setEnabled(False)
                self.switch_btn.setCursor(Qt.CursorShape.ArrowCursor)
                self.switch_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {Theme.DISABLED};
                    color: {Theme.TEXT_SECONDARY};
                    border: none;
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: normal;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
                }}
                """)
            else:
                self.switch_btn.setText("更换")
                self.switch_btn.setEnabled(True)
                self.switch_btn.setCursor(Qt.CursorShape.PointingHandCursor)
                self.switch_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {Theme.ACCENT};
                    color: white;
                    border: none;
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: bold;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
                }}
                QPushButton:hover {{
                    background-color: {Theme.ACCENT_HOVER};
                }}
                QPushButton:pressed {{
                    background-color: #238970;
                }}
                """)
        
        # 更新删除按钮状态
        if hasattr(self, 'delete_btn'):
            # 如果是当前账户，禁用删除按钮
            if self.is_current:
                self.delete_btn.setEnabled(False)
                self.delete_btn.setCursor(Qt.CursorShape.ArrowCursor)
                self.delete_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {Theme.DISABLED};
                    color: {Theme.TEXT_SECONDARY};
                    border: none;
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: normal;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
                }}
                """)
            else:
                # 非当前账户，启用删除按钮
                self.delete_btn.setEnabled(True)
                self.delete_btn.setCursor(Qt.CursorShape.PointingHandCursor)
                self.delete_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    color: {Theme.ERROR};
                    border: 1px solid {Theme.ERROR};
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: normal;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
                }}
                QPushButton:hover {{
                    background-color: {Theme.ERROR};
                    color: white;
                }}
                QPushButton:pressed {{
                    background-color: #A8414E;
                    color: white;
                }}
                """)
    
    def init_ui(self):
        """初始化UI"""
        self.setFixedHeight(90)  # 增加行高，使布局更宽松
        
        # 主卡片效果
        bg_color = Theme.CARD_LEVEL_2 if self.is_current else Theme.CARD_LEVEL_1
        border_style = f"border-left: 4px solid {Theme.ACCENT};" if self.is_current else ""
        
        self.setStyleSheet(f"""
            AccountRowWidget {{
                background-color: {bg_color};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                {border_style}
                margin: 4px 2px;
                padding: 0px;
            }}
            
            AccountRowWidget:hover {{
                background-color: {Theme.HOVER};
            }}
            
            QLabel {{
                padding: 0px;
                margin: 0px;
            }}
        """)
        
        # 主布局
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(15, 12, 15, 12)
        self.main_layout.setSpacing(5)  # 减少组件间的间距
        
        # 左侧 - 账户信息区域
        left_container = QWidget()
        left_container.setStyleSheet("background: transparent;")
        left_layout = QVBoxLayout(left_container)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(6)
        
        # 邮箱信息
        self.email_label = QLabel(self.account_data.get("email", "未知"))
        self.email_label.setTextFormat(Qt.TextFormat.PlainText)
        # 不再使用TextSelectableByMouse标志，以确保点击事件能够正确传递到父控件
        # self.email_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        self.email_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: {'bold' if self.is_current else 'normal'};
            color: {Theme.SUCCESS if self.is_current else Theme.TEXT_PRIMARY};
            background-color: transparent;
            padding: 5px 0;
        """)
        left_layout.addWidget(self.email_label)
        
        # 创建注册时间布局
        time_layout = QHBoxLayout()
        time_layout.setSpacing(6)
        time_layout.setContentsMargins(0, 0, 0, 0)
        
        register_time_label = QLabel("注册时间:")
        register_time_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        time_layout.addWidget(register_time_label)
        
        self.register_time_label = QLabel("--")
        self.register_time_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        time_layout.addWidget(self.register_time_label)
        
        time_layout.addStretch()
        
        # 添加时间布局到左侧容器
        left_layout.addLayout(time_layout)
        
        # 中间部分 - 包含剩余天数和进度条
        center_container = QWidget()
        center_container.setStyleSheet("background: transparent;")
        center_layout = QVBoxLayout(center_container)
        center_layout.setContentsMargins(0, 0, 0, 0)
        center_layout.setSpacing(6)
        
        # 剩余天数行
        days_layout = QHBoxLayout()
        days_layout.setSpacing(0)  # 将间距从6改为0，减少标签与数值之间的距离
        days_layout.setContentsMargins(0, 0, 0, 0)
        
        self.days_title = QLabel("剩余天数:")
        self.days_title.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        days_layout.addWidget(self.days_title)
        
        # 使用动画数字标签显示剩余天数
        self.remaining_days_label = AnimatedNumberLabel(prefix="", suffix="天", duration=2000)
        self.remaining_days_label.setSpecialText("未知")
        self.remaining_days_label.setColor(Theme.SUCCESS)
        self.remaining_days_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL}; 
            padding: 0px;
            background-color: transparent;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            font-weight: bold;
            color: {Theme.SUCCESS};
        """)
        days_layout.addWidget(self.remaining_days_label)
        
        days_layout.addStretch()
        
        # 添加剩余天数布局到中间容器
        center_layout.addLayout(days_layout)
        
        # 额度行
        quota_layout = QHBoxLayout()
        quota_layout.setSpacing(6)
        quota_layout.setContentsMargins(0, 0, 0, 0)
        quota_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  # 设置为左对齐
        
        quota_title = QLabel("高级模型:")
        quota_title.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        quota_layout.addWidget(quota_title)
        
        # 创建动画进度条，使用更短的动画时间以保持UI响应性
        self.quota_bar = AnimatedProgressBar(text_duration=1500, value_duration=1000)
        self.quota_bar.setMinimumHeight(18)
        self.quota_bar.setMaximumHeight(18)
        self.quota_bar.setFixedWidth(150)    # 减小宽度到150像素
        self.quota_bar.setRange(0, 100)  # 修改为0-100而不是0-150
        self.quota_bar.setValue(0)
        self.quota_bar.setFormat("未知")
        
        quota_layout.addWidget(self.quota_bar, 0)  # 使用0作为伸展因子，不会拉伸
        quota_layout.addStretch(1)  # 添加伸展空间，将前面的组件推到左侧
        
        # 添加额度布局到中间容器
        center_layout.addLayout(quota_layout)
        
        # 右侧 - 操作按钮
        right_container = QWidget()
        right_container.setStyleSheet("background: transparent;")
        right_layout = QVBoxLayout(right_container)  # 改为垂直布局
        right_layout.setContentsMargins(0, 16, 0, 0)  # 增加顶部边距，向下移动按钮
        right_layout.setSpacing(8)  # 设置垂直间距
        right_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  # 左对齐
        
        # 水平布局用于放置两个按钮
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(15, 0, 0, 0)  # 增加左侧边距
        buttons_layout.setSpacing(5)  # 减少间距
        buttons_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)  # 左对齐
        
        # 根据当前用户状态显示不同的按钮
        if self.is_current:
            # 当前账户 - 改为禁用的更换按钮
            self.switch_btn = QPushButton("更换")
            self.switch_btn.setFixedSize(65, 28)  # 稍微增加按钮尺寸
            self.switch_btn.setEnabled(False)  # 设置为禁用状态
            self.switch_btn.setCursor(Qt.CursorShape.ArrowCursor)
            self.switch_btn.setStyleSheet(f"""
            QPushButton {{
                    background-color: {Theme.DISABLED};
                    color: {Theme.TEXT_SECONDARY};
                    border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: normal;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
                }}
            """)
            buttons_layout.addWidget(self.switch_btn)
        else:
            # 切换按钮
            self.switch_btn = QPushButton("更换")
            self.switch_btn.setFixedSize(65, 28)  # 稍微增加按钮尺寸
            self.switch_btn.setCursor(Qt.CursorShape.PointingHandCursor)
            self.switch_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {Theme.ACCENT};
                    color: white;
                    border: none;
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: bold;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
            }}
            QPushButton:hover {{
                    background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                    background-color: #238970;
                }}
            """)
            buttons_layout.addWidget(self.switch_btn)

        # 确保信号始终连接，无论初始状态如何
        self.switch_btn.clicked.connect(self.on_switch_clicked)

        # 删除按钮
        self.delete_btn = QPushButton("删除")
        self.delete_btn.setFixedSize(65, 28)  # 稍微增加按钮尺寸
        
        if self.is_current:
            # 当前账户的删除按钮设为禁用状态
            self.delete_btn.setEnabled(False)
            self.delete_btn.setCursor(Qt.CursorShape.ArrowCursor)
            self.delete_btn.setStyleSheet(f"""
                QPushButton {{
                background-color: {Theme.DISABLED};
                color: {Theme.TEXT_SECONDARY};
                    border: none;
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: normal;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
                }}
            """)
        else:
            # 非当前账户的删除按钮
            self.delete_btn.setCursor(Qt.CursorShape.PointingHandCursor)
            self.delete_btn.setStyleSheet(f"""
            QPushButton {{
                    background-color: transparent;
                color: {Theme.ERROR};
                    border: 1px solid {Theme.ERROR};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: normal;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
            }}
            QPushButton:hover {{
                background-color: {Theme.ERROR};
                color: white;
            }}
            QPushButton:pressed {{
                background-color: #A8414E;
                color: white;
                }}
            """)
        self.delete_btn.clicked.connect(self.on_delete_clicked)
        
        buttons_layout.addWidget(self.delete_btn)
        
        # 将按钮布局添加到右侧容器
        right_layout.addLayout(buttons_layout)
        
        # 添加到主布局，调整比例使中间部分更靠近左侧
        self.main_layout.addWidget(left_container, 4)  # 增加左侧比例
        self.main_layout.addWidget(center_container, 4)  # 增加中间比例
        self.main_layout.addWidget(right_container, 3)  # 增加右侧比例，确保按钮显示完整
    
    def enterEvent(self, event):
        """鼠标进入控件区域"""
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开控件区域"""
        self.setCursor(Qt.CursorShape.ArrowCursor)
        super().leaveEvent(event)
    
    def on_switch_clicked(self):
        """当点击更换按钮时触发"""
        self.switch_account_signal.emit(self.account_data)
    
    def on_delete_clicked(self):
        """当点击删除按钮时触发"""
        self.delete_account_signal.emit(self.account_data.get("email", ""))
    
    def set_loading_state(self, is_loading):
        """设置加载状态"""
        self.is_loading = is_loading
        if is_loading:
            if hasattr(self, 'quota_bar') and self.quota_bar is not None:
                self.quota_bar.setFormat("加载中...")
                self.quota_bar.setValue(0)  # 清除进度条填充，重置为0
            
            # 禁用按钮
            if not self.is_current and hasattr(self, 'switch_btn'):
                self.switch_btn.setEnabled(False)
            if not self.is_current and hasattr(self, 'delete_btn'):
                self.delete_btn.setEnabled(False)
        else:
            # 恢复按钮状态，但当前账户的按钮始终保持禁用
            if not self.is_current and hasattr(self, 'switch_btn'):
                self.switch_btn.setEnabled(True)
            if not self.is_current and hasattr(self, 'delete_btn'):
                self.delete_btn.setEnabled(True)
        
    def update_quota(self, quota_data):
        """更新账户额度"""
        try:
            # 停止加载状态，但使用动画过渡
            self.set_loading_state(False)  # 停止加载动画
            
            # 更新注册时间和剩余天数
            start_of_month = quota_data.get("startOfMonth")
            
            # 设置标签样式 - 移除transition属性
            if hasattr(self, 'email_label'):
                self.email_label.setStyleSheet(f"""
                    font-size: {Theme.FONT_SIZE_NORMAL};
                    font-weight: {'bold' if self.is_current else 'normal'};
                    color: {Theme.SUCCESS if self.is_current else Theme.TEXT_PRIMARY};
                    background-color: transparent;
                    padding: 5px 0;
                """)
            
            # 如果有真实注册时间，更新注册时间显示
            if start_of_month:
                try:
                    # 解析ISO 8601格式的时间字符串
                    # 使用dateutil解析器兼容多种时间格式
                    reg_date = parser.parse(start_of_month)

                    # 时区处理 - 转换为中国时间 (UTC+8)
                    china_tz = timezone(timedelta(hours=8))
                    reg_date_china = reg_date.astimezone(china_tz)

                    # 格式化注册时间显示 (使用中国时间)
                    formatted_time = reg_date_china.strftime("%Y-%m-%d %H:%M:%S")
                    if hasattr(self, 'register_time_label'):
                        self.register_time_label.setText(formatted_time)

                    # 保存到账户数据
                    self.account_data["real_register_time"] = start_of_month
                except Exception as e:
                    print(f"解析注册时间时出错: {str(e)}")
                    if hasattr(self, 'register_time_label'):
                        self.register_time_label.setText("--")
            else:
                # 没有真实注册时间
                if hasattr(self, 'register_time_label'):
                    self.register_time_label.setText("--")
                if hasattr(self, 'remaining_days_label'):
                    self.remaining_days_label.setSpecialText("未知")
            
            # 高级模型 (GPT-4) - 适配新的JSON格式
            gpt4_data = quota_data.get("gpt-4", {})
            usage = gpt4_data.get("numRequests", 0)  # 使用numRequests而不是numRequestsTotal
            max_usage = gpt4_data.get("maxRequestUsage")  # 保持不变

            # 使用新的账户类型API获取账户类型信息
            account_type_info = quota_data.get("account_type_info")

            if account_type_info:
                membership_type = account_type_info.get("membershipType")
                days_remaining = account_type_info.get("daysRemainingOnTrial")

                # 使用AccountType类格式化显示
                from account.account_type import AccountType
                display_text, label_text = AccountType.format_account_type_display(membership_type, days_remaining)
                color = AccountType.get_account_type_color(membership_type)

                if hasattr(self, 'remaining_days_label'):
                    if membership_type == "free_trial" and days_remaining is not None:
                        # 试用账户显示剩余天数
                        self.remaining_days_label.prefix = ""
                        self.remaining_days_label.suffix = "天"
                        self.remaining_days_label.setValue(days_remaining, animate=True)
                        self.remaining_days_label.setColor(color)
                    else:
                        # 其他类型显示账户类型
                        self.remaining_days_label.prefix = ""
                        self.remaining_days_label.suffix = ""
                        self.remaining_days_label.setSpecialText(display_text)
                        self.remaining_days_label.setColor(color)

                if hasattr(self, 'days_title'):
                    self.days_title.setText(label_text)
            else:
                # 如果没有账户类型信息，显示获取失败
                if hasattr(self, 'remaining_days_label'):
                    self.remaining_days_label.prefix = ""
                    self.remaining_days_label.suffix = ""
                    self.remaining_days_label.setSpecialText("获取失败")
                    self.remaining_days_label.setColor(Theme.TEXT_SECONDARY)

                if hasattr(self, 'days_title'):
                    self.days_title.setText("账户类型:")
            
            if hasattr(self, 'quota_bar') and self.quota_bar is not None:
                # 确保进度条使用动画
                self.quota_bar._skip_animation = False

                if max_usage is not None and isinstance(max_usage, (int, float)) and not isinstance(max_usage, bool) and max_usage > 0:
                    # 有具体的使用限制
                    self.quota_bar.setRange(0, max_usage)
                    self.quota_bar.setValue(usage)  # 这会触发动画

                    # 根据使用情况设置不同颜色
                    usage_percent = usage / max_usage
                    if usage_percent > 0.9:
                        # 已用超过90%，显示红色警告
                        self.quota_bar.setChunkColor(Theme.ERROR)
                    elif usage_percent > 0.7:
                        # 已用超过70%，显示黄色警告
                        self.quota_bar.setChunkColor(Theme.WARNING)
                    else:
                        # 默认绿色
                        self.quota_bar.setChunkColor(Theme.ACCENT)

                    # 设置显示格式
                    self.quota_bar.setFormat(f"{usage}/{max_usage}")
                else:
                    # maxRequestUsage为null或0，表示无限制
                    self.quota_bar.setRange(0, 100)
                    self.quota_bar.setValue(100)
                    self.quota_bar.setFormat("无限制")
                    self.quota_bar.setChunkColor(Theme.ACCENT)  # 使用绿色显示无限制
            
            # 更新账户数据中的额度 - 不使用默认值，保持与显示一致
            self.account_data["real_usage"] = usage if usage is not None else None
            self.account_data["real_max_usage"] = max_usage
                
        except Exception as e:
            print(f"更新账户额度时出错: {str(e)}")
            if hasattr(self, 'quota_bar') and self.quota_bar is not None:
                self.quota_bar.setRange(0, 100)
                self.quota_bar.setValue(0)
                self.quota_bar.setFormat("无法获取")
                self.quota_bar.setChunkColor(Theme.TEXT_SECONDARY)  # 使用灰色
            if hasattr(self, 'register_time_label'):
                self.register_time_label.setText("--")
            if hasattr(self, 'remaining_days_label'):
                self.remaining_days_label.setSpecialText("未知")
                self.remaining_days_label.setColor(Theme.TEXT_SECONDARY)
            
            # 异常情况下，设置账户数据为None，不使用默认值
            self.account_data["real_usage"] = None
            self.account_data["real_max_usage"] = None

    def mouseReleaseEvent(self, event):
        """鼠标释放事件，用于处理点击行显示账户详情"""
        # 只处理左键点击
        if event.button() == Qt.MouseButton.LeftButton:
            # 检查点击是否发生在按钮上，如果是，不触发显示详情
            child_widget = self.childAt(event.position().toPoint())
            if isinstance(child_widget, QPushButton):
                # 如果点击的是按钮，不触发详情显示，交给按钮自己的事件处理
                return super().mouseReleaseEvent(event)
            
            # 发送显示详情信号
            self.show_details_signal.emit(self.account_data)
        
        super().mouseReleaseEvent(event)