function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 鼠标坐标随机化 - 重要的Turnstile绕过
let screenX = getRandomInt(800, 1200);
let screenY = getRandomInt(400, 600);

Object.defineProperty(MouseEvent.prototype, 'screenX', { value: screenX });
Object.defineProperty(MouseEvent.prototype, 'screenY', { value: screenY });

// 额外的Canvas指纹保护
const originToDataURL = HTMLCanvasElement.prototype.toDataURL;
HTMLCanvasElement.prototype.toDataURL = function(type) {
    // 应用微小扰动
    const canvas = this;
    const context = canvas.getContext('2d');
    
    // 只有在canvas有内容时才修改
    try {
        const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
        if (imageData && imageData.data && imageData.data.some(val => val !== 0)) {
            // 在不可见区域添加随机噪点
            const x = canvas.width - 1;
            const y = canvas.height - 1;
            if (x > 0 && y > 0) {
                context.fillStyle = `rgba(${Math.floor(Math.random()*255)},${Math.floor(Math.random()*255)},${Math.floor(Math.random()*255)},0.01)`;
                context.fillRect(x, y, 1, 1);
            }
        }
    } catch(e) {}
    
    return originToDataURL.apply(this, arguments);
};