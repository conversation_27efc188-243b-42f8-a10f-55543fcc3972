import platform
import subprocess
import ast
import sys
import uuid # For WiX GUIDs
import shutil # For tool checking (shutil.which)
import xml.etree.ElementTree as ET # For generating WXS file
from pathlib import Path # For easier path manipulation

# Imports for rich console display
from rich.console import Console

# Global Console instance
console = Console()

# --- Code Protection Note ---
# This script integrates Nuitka compilation.
# Removed PyArmor and UPX integration.

def check_tool(tool_name):
    """Checks if a tool exists in the system's PATH."""
    console.print(f"    检查工具: [cyan]{tool_name}[/]...", end="")
    path = shutil.which(tool_name)
    if path:
        console.print(f" [bold green][找到: {path}][/]")
        return True
    else:
        console.print(" [bold red][未找到][/]")
        # Handle specific tool not found messages
        if platform.system() == "Windows" and tool_name in ["candle", "light"]:
            console.print(f"[bold red]错误:[/bold red] 必需的工具 [cyan]'{tool_name}'[/cyan] 未在系统的 PATH 环境变量中找到。")
            console.print("请从以下地址下载并安装 [bold]WiX Toolset v3[/bold]:")
            console.print("[link=https://wixtoolset.org/releases/]https://wixtoolset.org/releases/[/link] (下载 v3.x 版本，例如 v3.11.2)")
            console.print("安装后请确保 WiX 的 bin 目录已添加到 PATH。")
        elif platform.system() == "Darwin" and tool_name == "create-dmg":
            console.print(f"[bold red]错误:[/bold red] 必需的工具 [cyan]'{tool_name}'[/cyan] 未在系统的 PATH 环境变量中找到。")
            console.print("请使用 Homebrew 安装: [bold]brew install create-dmg[/]")
        else:
             # Generic message for other tools (like upx)
             console.print(f"[yellow]提示:[/yellow] 可选工具 [cyan]'{tool_name}'[/cyan] 未在系统的 PATH 环境变量中找到。依赖此工具的功能将被跳过。")
        return False

def get_platform_info():
    """Detects OS and Architecture, maps and normalizes them."""
    system = platform.system()
    machine = platform.machine()

    os_name = ""
    if system == "Windows":
        os_name = "win"
    elif system == "Linux":
        os_name = "linux"
    elif system == "Darwin":
        os_name = "mac"
    else:
        console.print(f"[bold red]错误：[/bold red]不支持的操作系统: {system}")
        sys.exit(1)

    # Normalize architecture
    arch = ""
    machine_lower = machine.lower()
    if machine_lower in ["amd64", "x86_64"]:
        arch = "x86_64"
    elif machine_lower in ["arm64", "aarch64"]:
        arch = "arm64"
    elif machine_lower == "x86": # 32-bit Windows?
         arch = "x86"
    else:
        console.print(f"[yellow]警告：[/yellow]未知的架构: [cyan]{machine}[/cyan]，将尝试使用它。")
        arch = machine_lower # Use as is if unknown

    return os_name, arch

def get_version(platform_key, version_file="version_checker.py"):
    """Safely extracts the version for the given platform from the version file using AST."""
    try:
        with open(version_file, 'r', encoding='utf-8') as f:
            file_content = f.read()

        tree = ast.parse(file_content)
        version_dict = None

        for node in ast.walk(tree):
            # Find assignment to CURRENT_VERSIONS within the VersionChecker class
            if isinstance(node, ast.ClassDef) and node.name == 'VersionChecker':
                 for class_node in node.body:
                     if isinstance(class_node, ast.Assign):
                         for target in class_node.targets:
                             if isinstance(target, ast.Name) and target.id == 'CURRENT_VERSIONS':
                                 if isinstance(class_node.value, ast.Dict):
                                     version_dict = class_node.value
                                     break
                         if version_dict:
                             break
            # Fallback: Find assignment directly if not in class (less likely based on file structure)
            elif isinstance(node, ast.Assign):
                 for target in node.targets:
                      if isinstance(target, ast.Name) and target.id == 'CURRENT_VERSIONS':
                          if isinstance(node.value, ast.Dict):
                              version_dict = node.value
                              break
                 if version_dict:
                     break


        if version_dict:
            # Extract the dictionary content
            versions = {}
            for i in range(len(version_dict.keys)):
                key_node = version_dict.keys[i]
                value_node = version_dict.values[i]
                # Ensure keys and values are simple strings
                if isinstance(key_node, ast.Constant) and isinstance(key_node.value, str) and \
                   isinstance(value_node, ast.Constant) and isinstance(value_node.value, str):
                    versions[key_node.value] = value_node.value

            if platform_key in versions:
                return versions[platform_key]
            else:
                console.print(f"[bold red]错误：[/bold red]在 [cyan]{version_file}[/] 的 CURRENT_VERSIONS 中找不到平台 [cyan]'{platform_key}'[/cyan] 的版本。")
                return None
        else:
            console.print(f"[bold red]错误：[/bold red]在 [cyan]{version_file}[/] 中找不到 CURRENT_VERSIONS 字典定义。")
            return None

    except FileNotFoundError:
        console.print(f"[bold red]错误：[/bold red]版本文件 [cyan]'{version_file}'[/cyan] 未找到。")
        return None
    except Exception as e:
        console.print(f"[bold red]错误：[/bold red]解析版本文件 [cyan]'{version_file}'[/cyan] 时出错: {e}")
        return None


def build_nuitka_command(os_name, arch, version, entry_point, use_upx: bool):
    """Builds the Nuitka command list based on platform, arch, version, entry point, and UPX choice."""
    # Nuitka may ignore output-filename for certain outputs like macOS .app bundle
    # It primarily affects the main executable inside the bundle/dist folder.

    # Define icon paths relative to the script location
    icon_dir = Path("icons")
    win_icon = icon_dir / "yan.ico"
    mac_icon = icon_dir / "yan32x32.icns"
    linux_icon = icon_dir / "yan.png"

    # Define data directories
    cursor_auto_dir = Path("core/cursor_auto")

    # UPX check is now done in main based on user input
    # upx_enabled = False
    # if check_tool('upx'): # Check if UPX exists on any platform
    #     upx_enabled = True
    #     console.print("    [green]启用 Nuitka UPX 插件进行压缩。[/]")
    # else:
    #     console.print("    [yellow]未找到 UPX 工具，将跳过 UPX 压缩。[/]")

    command = [
        sys.executable,  # Use sys.executable to ensure the correct python interpreter is used
        '-m',
        'nuitka',
        '--standalone',
        f'--output-dir=dist', # Nuitka puts results in dist/<entry_point_name>.dist or .app
        f'--product-name=YCursor',
        f'--company-name=Yan', # Changed from YAPAN
        f'--file-version={version}',
        f'--product-version={version}',
        '--enable-plugin=pyside6', # Enable PySide6 plugin (macOS compatibility)
        '--include-data-dir=icons=icons', # Include the icons directory
        # '--jobs=N' # Consider adding --jobs= to speed up compilation using N cores
    ]

    # Add UPX plugin if user chose to use it (and it was detected in main)
    if use_upx:
        command.extend([
            '--enable-plugin=upx',
        ])

    # Include core/cursor_auto if it exists
    if cursor_auto_dir.is_dir():
         command.append(f'--include-data-dir={cursor_auto_dir}=core/cursor_auto')
    else:
         console.print(f"[yellow]警告:[/yellow] 未找到数据目录 [cyan]{cursor_auto_dir}[/cyan]，将不会被包含。")

    # Platform specific options
    if os_name == 'win':
        command.extend([
            '--windows-disable-console', # Assuming GUI application
            f'--windows-icon-from-ico={win_icon}', # Use Path object
            '--windows-uac-admin', # Request administrator privileges on launch
             # Force executable name to YCursor.exe / YCursor
            f'--output-filename=YCursor'
        ])
        # For MSI, external tools like WiX are needed after Nuitka finishes.
        # Nuitka doesn't create MSI directly.
    elif os_name == 'mac':
        command.extend([
            '--macos-create-app-bundle',
            f'--macos-app-icon={mac_icon}', # Use Path object
             # '--macos-sign-identity="Your Developer ID"' # Optional: for code signing
            # Nuitka typically names the .app bundle based on product name or entry point
            # So output-filename might not apply to the .app bundle name itself.
        ])
        # For DMG, external tools like 'create-dmg' are needed after Nuitka finishes.
    elif os_name == 'linux':
        # Nuitka's deb/rpm packaging is experimental and needs tools installed
        command.extend([
            '--linux-package-deb',
            '--linux-package-rpm',
             # No --linux-icon specified as no PNG icon was found
             # Add Linux icon if it exists
            f'--output-filename=YCursor' # Set executable name within package
        ])
        if linux_icon.exists():
             command.append(f'--linux-icon={linux_icon}')
        else:
             console.print(f"[yellow]警告:[/yellow] 找不到 Linux 图标文件 [cyan]{linux_icon}[/cyan]，将不为包添加图标。")

    command.append(entry_point)
    return command

# --- License RTF Generation ---
def generate_license_rtf(output_dir):
    """Generates the license agreement in RTF format."""
    rtf_path = output_dir / "license_zh-cn.rtf"
    # Basic EULA Text (Chinese)
    eula_title = "最终用户许可协议 (EULA) - YCursor"
    eula_content = [
        "感谢您选择 YCursor！",
        "在使用本软件前，请仔细阅读以下条款。安装、复制或以其他方式使用本软件即表示您同意接受本协议各项条款的约束。如果您不同意本协议的条款，请不要安装或使用本软件。",
        "1. 许可授予：开发者 Yan 授予您一份非排他性的、个人的许可，允许您在单台设备上安装和使用 YCursor 软件（\"本软件\"），仅用于非商业目的。",
        "2. 限制：您不得对本软件进行反向工程、反编译或反汇编。您不得出租、租赁、销售或再许可本软件。未经开发者 Yan 事先书面同意，您不得将本软件用于任何商业用途。您不得移除或更改本软件中的任何版权或其他所有权声明。",
        "3. 免责声明：本软件按\"现状\"提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途适用性和不侵权的保证。开发者 Yan 不保证本软件的功能将满足您的要求，或者本软件的运行将不间断或没有错误。",
        "4. 责任限制：在任何情况下，开发者 Yan 均不对因使用或无法使用本软件而导致的任何损害（包括但不限于业务利润损失、业务中断、业务信息丢失或其他金钱损失）承担责任，即使开发者已被告知发生此类损害的可能性。",
        "5. 协议终止：如果您未能遵守本协议的任何条款和条件，本协议将自动终止。协议终止时，您必须立即停止使用本软件，并销毁本软件的所有副本及其所有组成部分。",
        "6. 版权：本软件受版权法和国际版权条约以及其他知识产权法律和条约的保护。开发者 Yan 保留所有未明确授予的权利。",
        "安装本软件即表示您已阅读、理解并同意上述所有条款。"
    ]

    # Basic RTF Structure
    rtf_header = "{\\rtf1\\ansi\\ansicpg936\\deff0{\\fonttbl{\\f0\\fnil\\fcharset134 SimSun;}}\\viewkind4\\uc1\\pard\\lang2052\\f0\\fs24 "
    rtf_footer = "\\par}"
    rtf_body = ""

    # Add Title (Bold)
    rtf_body += "{\\b " + eula_title + "}\\par\\par "
    # Add Content paragraphs
    for line in eula_content:
        # Basic escaping for RTF special characters: \ { }
        escaped_line = line.replace('\\', '\\\\').replace('{', '\\{').replace('}', '\\}')
        rtf_body += escaped_line + "\\par "

    full_rtf = rtf_header + rtf_body + rtf_footer

    try:
        with open(rtf_path, 'w', encoding='utf-8') as f:
            f.write(full_rtf)
        console.print(f"    成功生成许可证文件 (RTF): [green]{rtf_path}[/]")
        return rtf_path
    except Exception as e:
        console.print(f"[bold red]错误:[/bold red] 生成许可证 RTF 文件失败: {e}")
        return None

# --- MSI Creation (Windows Only) ---

def generate_wxl(output_dir):
    """Generates a basic zh-CN localization file for WixUI_InstallDir."""
    wxl_path = output_dir / "zh-cn.wxl"
    content = """<?xml version="1.0" encoding="utf-8"?>
<WixLocalization Culture="zh-cn" xmlns="http://schemas.microsoft.com/wix/2006/localization">
    <String Id="ProductName">YCursor</String>
    <String Id="InstallDirDlg_Title">{\*\*\*\*\*\*\*\*\*\*VRTF 安装位置}</String>
    <String Id="InstallDirDlg_Description">选择 YCursor 的安装文件夹。</String>
    <String Id="InstallDirDlg_Browse">浏览(&amp;B)...</String>
    <String Id="InstallDirDlg_Install">安装(&amp;I)</String>
    <String Id="InstallDirDlg_Cancel">取消(&amp;C)</String>
    <String Id="InstallDirDlg_Directory">安装目录(&amp;F)</String>
    <String Id="VerifyReadyDlg_Title">{\*\*\*\*\*\*\*\*\*\*VRTF 准备安装}</String>
    <String Id="VerifyReadyDlg_Description">安装程序已准备好安装 YCursor。</String>
    <String Id="VerifyReadyDlg_Install">安装(&amp;I)</String>
    <String Id="VerifyReadyDlg_Cancel">取消(&amp;C)</String>
    <String Id="VerifyReadyDlg_Back">&lt; 上一步(&amp;B)</String>
    <String Id="WelcomeDlg_Title">{\*\*\*\*\*\*\*\*\*\*VRTF 欢迎使用 YCursor 安装向导}</String>
    <String Id="WelcomeDlg_Description">此向导将引导您完成 YCursor 的安装过程。建议在继续之前关闭其他应用程序。</String>
    <String Id="WelcomeDlg_Next">下一步(&amp;N) &gt;</String>
    <String Id="WelcomeDlg_Cancel">取消(&amp;C)</String>
    <String Id="ProgressDlg_Title">{\*\*\*\*\*\*\*\*\*\*VRTF 安装 YCursor}</String>
    <String Id="ProgressDlg_Cancel">取消(&amp;C)</String>
    <String Id="ExitDialog_Title">{\*\*\*\*\*\*\*\*\*\*VRTF 安装完成}</String>
    <String Id="ExitDialog_Description">YCursor 已成功安装。</String>
    <String Id="ExitDialog_Finish">完成(&amp;F)</String>
</WixLocalization>"""
    try:
        with open(wxl_path, 'w', encoding='utf-8') as f:
            f.write(content)
        console.print(f"    成功生成本地化文件: [green]{wxl_path}[/]")
        return wxl_path
    except Exception as e:
        console.print(f"[bold red]错误:[/bold red] 生成本地化文件失败: {e}")
        return None

def generate_wxs(output_dir, nuitka_dist_dir, version, arch, entry_executable_name):
    """Generates the WiX source file (.wxs) dynamically."""
    wxs_path = output_dir / "YCursor.wxs"
    product_guid = str(uuid.uuid4()).upper()
    # Use a FIXED UpgradeCode for the YCursor product line to enable major upgrades
    # IMPORTANT: Keep this GUID consistent across all versions of YCursor!
    upgrade_code_guid = "{8D2B3B5A-6F3A-4C8C-9F1A-D8E3A6B9C7E1}"
    root_dir_id = "INSTALLFOLDER"
    source_dir_abs = nuitka_dist_dir.resolve()

    # Start XML structure
    wix = ET.Element("Wix", xmlns="http://schemas.microsoft.com/wix/2006/wi")
    product = ET.SubElement(wix, "Product", Id=product_guid, Name="YCursor", Language="2052",
                             Version=version, Manufacturer="Yan", UpgradeCode=upgrade_code_guid)
    # Set package platform based on architecture
    package_platform = "x86" # Default
    if arch == "x86_64":
        package_platform = "x64"
    elif arch == "arm64":
        package_platform = "arm64"
    ET.SubElement(product, "Package", InstallerVersion="200", Compressed="yes", InstallScope="perMachine", Platform=package_platform)
    ET.SubElement(product, "MajorUpgrade", AllowSameVersionUpgrades="yes", DowngradeErrorMessage="检测到较新版本，无法安装此版本。")
    ET.SubElement(product, "MediaTemplate", EmbedCab="yes")

    # Add WixVariable for the License RTF file
    ET.SubElement(product, "WixVariable", Id="WixUILicenseRtf", Value="license_zh-cn.rtf")

    # Define UI and Icon
    ui = ET.SubElement(product, "UI")
    # Use WixUI_Minimal instead of WixUI_FeatureTree for a simpler flow (Welcome -> License -> Progress -> Finish)
    ET.SubElement(ui, "UIRef", Id="WixUI_Minimal")
    # Property required by WixUI_Minimal/InstallDir for default install path
    ET.SubElement(product, "Property", Id="WIXUI_INSTALLDIR", Value=root_dir_id)
    # Ensure icon path is relative to WXS or absolute
    win_icon_path_abs = Path("icons/yan.ico").resolve()
    icon_id = None # Initialize icon_id
    if win_icon_path_abs.exists():
        icon_id = "YCursorIcon"
        ET.SubElement(product, "Icon", Id=icon_id, SourceFile=str(win_icon_path_abs))
        ET.SubElement(product, "Property", Id="ARPPRODUCTICON", Value=icon_id)
    else:
        console.print(f"[yellow]警告:[/yellow] 找不到 Windows 图标文件 [cyan]{win_icon_path_abs}[/cyan]，MSI 将不包含图标。")

    # Define Directory Structure
    target_dir = ET.SubElement(product, "Directory", Id="TARGETDIR", Name="SourceDir")
    # Use ProgramFiles64Folder if possible, fallback to ProgramFilesFolder
    prog_files_folder_id = "ProgramFiles64Folder" if arch == "x86_64" else "ProgramFilesFolder"
    prog_files_dir = ET.SubElement(target_dir, "Directory", Id=prog_files_folder_id)
    install_folder = ET.SubElement(prog_files_dir, "Directory", Id=root_dir_id, Name="YCursor")

    # Recursive function to add directories and files
    components = []
    def add_directory(parent_element, dir_path, current_feature, target_arch, is_root_call=False):
        # Determine the WiX directory element to add children to
        # If it's the root call, children go directly into the parent (INSTALLFOLDER)
        # Otherwise, create a new directory element for the current subdir
        current_wix_directory_element = None
        if is_root_call:
            current_wix_directory_element = parent_element
        else:
            dir_id = "dir_" + str(uuid.uuid4()).replace('-', '')
            current_wix_directory_element = ET.SubElement(parent_element, "Directory", Id=dir_id, Name=dir_path.name)

        is_64bit_arch = target_arch in ["x86_64", "arm64"]
        for item in dir_path.iterdir():
            if item.is_dir():
                # Always pass False for recursive calls
                add_directory(current_wix_directory_element, item, current_feature, target_arch, is_root_call=False)
            elif item.is_file():
                comp_id = "comp_" + str(uuid.uuid4()).replace('-', '')
                comp_guid = str(uuid.uuid4()).upper()
                comp_attrs = {"Id": comp_id, "Guid": comp_guid}
                if is_64bit_arch:
                    comp_attrs["Win64"] = "yes"
                component = ET.SubElement(current_wix_directory_element, "Component", **comp_attrs)
                # Check if this is the main executable and assign a fixed File ID
                file_id = "file_" + comp_id
                if item.name == entry_executable_name:
                    file_id = "YCursorExecutable" # Fixed ID for the main executable
                ET.SubElement(component, "File", Id=file_id, Source=str(item.resolve()), KeyPath="yes")
                components.append(comp_id)

    # Define Feature and add components
    feature = ET.SubElement(product, "Feature", Id="ProductFeature", Title="YCursor", Level="1")
    # Pass the arch and set is_root_call=True for the initial call
    add_directory(install_folder, source_dir_abs, feature, arch, is_root_call=True)
    for comp_ref in components:
        ET.SubElement(feature, "ComponentRef", Id=comp_ref)

    # --- Define Shortcut Components Separately --- 
    is_64bit_arch_flag = arch in ["x86_64", "arm64"]

    # Add Start Menu directory structure & Shortcut Component
    prog_menu_dir = ET.SubElement(target_dir, "Directory", Id="ProgramMenuFolder")
    app_prog_menu_dir = ET.SubElement(prog_menu_dir, "Directory", Id="AppProgramMenuFolder", Name="YCursor")

    # Component for the Start Menu Shortcut itself
    start_menu_comp_id = "comp_StartMenuShortcut"
    start_menu_comp_guid = str(uuid.uuid4()).upper()
    start_menu_comp_attrs = {"Id": start_menu_comp_id, "Guid": start_menu_comp_guid}
    if is_64bit_arch_flag:
        start_menu_comp_attrs["Win64"] = "yes"
    start_menu_comp = ET.SubElement(app_prog_menu_dir, "Component", **start_menu_comp_attrs)
    shortcut_attrs = {
        "Id": "StartMenuShortcut",
        "Name": "YCursor",
        "Description": "启动 YCursor",
        "Target": "[#YCursorExecutable]", # Changed target to use fixed File ID
        "WorkingDirectory": root_dir_id
    }
    if icon_id:
        shortcut_attrs["Icon"] = icon_id
    ET.SubElement(start_menu_comp, "Shortcut", **shortcut_attrs)
    # Registry key for this component's KeyPath
    ET.SubElement(start_menu_comp, "RegistryValue", Root="HKCU", Key="Software\\Yan\\YCursor", Name="StartMenuShortcutInstalled", Type="integer", Value="1", KeyPath="yes")
    # Add RemoveFolder instruction for the shortcut itself (optional but good practice)
    ET.SubElement(start_menu_comp, "RemoveFolder", Id="RemoveStartMenuShortcutItself", Directory="AppProgramMenuFolder", On="uninstall")
    # Add reference to this component in the Feature
    ET.SubElement(feature, "ComponentRef", Id=start_menu_comp_id)

    # Component to manage the AppProgramMenuFolder directory removal (Fix for ICE64)
    app_menu_dir_comp_id = "comp_AppProgramMenuFolder"
    app_menu_dir_comp_guid = str(uuid.uuid4()).upper()
    app_menu_dir_comp_attrs = {"Id": app_menu_dir_comp_id, "Guid": app_menu_dir_comp_guid}
    if is_64bit_arch_flag:
        app_menu_dir_comp_attrs["Win64"] = "yes"
    app_menu_dir_component = ET.SubElement(app_prog_menu_dir, "Component", **app_menu_dir_comp_attrs)
    ET.SubElement(app_menu_dir_component, "RemoveFolder", Id="RemoveAppProgramMenuFolder", On="uninstall")
    ET.SubElement(app_menu_dir_component, "RegistryValue", Root="HKCU", Key=f"Software\\Yan\\YCursor", Name="StartMenuFolderCreated", Type="integer", Value="1", KeyPath="yes")
    ET.SubElement(feature, "ComponentRef", Id=app_menu_dir_comp_id)

    # Add Desktop directory & Shortcut Component
    desktop_folder_dir = ET.SubElement(target_dir, "Directory", Id="DesktopFolder", Name="Desktop")
    # Component for the Desktop Shortcut
    desktop_comp_id = "comp_DesktopShortcut"
    desktop_comp_guid = str(uuid.uuid4()).upper()
    desktop_comp_attrs = {"Id": desktop_comp_id, "Guid": desktop_comp_guid}
    if is_64bit_arch_flag:
        desktop_comp_attrs["Win64"] = "yes"
    desktop_comp = ET.SubElement(desktop_folder_dir, "Component", **desktop_comp_attrs)
    desktop_shortcut_attrs = {
        "Id": "DesktopShortcut",
        "Name": "YCursor",
        "Description": "启动 YCursor",
        "Target": "[#YCursorExecutable]", # Changed target to use fixed File ID
        "WorkingDirectory": root_dir_id
    }
    if icon_id:
        desktop_shortcut_attrs["Icon"] = icon_id
    ET.SubElement(desktop_comp, "Shortcut", **desktop_shortcut_attrs)
    # Registry key for this component's KeyPath
    ET.SubElement(desktop_comp, "RegistryValue", Root="HKCU", Key="Software\\Yan\\YCursor", Name="DesktopShortcutInstalled", Type="integer", Value="1", KeyPath="yes")
    # Add reference to this component in the Feature
    ET.SubElement(feature, "ComponentRef", Id=desktop_comp_id)

    # Explicitly schedule RemoveExistingProducts earlier for cleaner upgrades
    # install_exec_sequence = ET.SubElement(product, "InstallExecuteSequence")
    # ET.SubElement(install_exec_sequence, "RemoveExistingProducts", After="InstallValidate") # Removed: MajorUpgrade handles this automatically

    # Write WXS file
    try:
        tree = ET.ElementTree(wix)
        ET.indent(tree, space="  ", level=0) # Pretty print XML
        tree.write(wxs_path, encoding="utf-8", xml_declaration=True)
        console.print(f"    成功生成 WiX 配置文件: [green]{wxs_path}[/]")
        return wxs_path
    except Exception as e:
        console.print(f"[bold red]错误:[/bold red] 生成 WXS 文件失败: {e}")
        return None

def create_msi(nuitka_dist_dir, version, arch, output_dir, entry_executable_name):
    """Creates an MSI installer using WiX Toolset."""
    console.print("\n[bold magenta]--- 5a. 开始创建 MSI 安装包 (使用 WiX) ---[/]")
    if not check_tool("candle") or not check_tool("light"):
        console.print("[bold red]错误:[/bold red] WiX Toolset 工具链不完整，无法创建 MSI。")
        return False

    # Generate License RTF before generating WXS
    license_rtf_file = generate_license_rtf(output_dir)
    if not license_rtf_file:
        console.print("[bold red]错误:[/bold red] 未能生成许可证文件，无法继续创建 MSI。")
        return False

    console.print("    [cyan]准备 WiX 文件...[/]")
    wxs_file = generate_wxs(output_dir, nuitka_dist_dir, version, arch, entry_executable_name)
    wxl_file = generate_wxl(output_dir)
    if not wxs_file or not wxl_file:
        console.print("[bold red]错误:[/bold red] 生成 WiX 配置文件失败。")
        return False

    wixobj_file = output_dir / "YCursor.wixobj"
    msi_file = output_dir / f"YCursor_win_{arch}_v{version}.msi"

    try:
        console.print(f"    执行 Candle 编译: [cyan]{wxs_file}[/]...")
        # Note: SourceDir variable passed to candle refers to the location of files to be included.
        candle_cmd = [
            "candle",
            str(wxs_file),
            "-ext", "WixUIExtension",
            f"-dSourceDir={nuitka_dist_dir.resolve()}", # Define SourceDir variable
            "-out", str(wixobj_file)
        ]
        console.print(f"      [dim]命令: {' '.join(candle_cmd)}[/]")
        result_candle = subprocess.run(candle_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
        if result_candle.returncode != 0:
            console.print("[bold red]错误:[/bold red] Candle 编译失败:")
            if result_candle.stdout: console.print(f"[red]{result_candle.stdout}[/]")
            if result_candle.stderr: console.print(f"[red]{result_candle.stderr}[/]")
            return False
        console.print("    [green]Candle 编译成功.[/]")

        console.print(f"    执行 Light 链接: [cyan]{wixobj_file}[/] -> [cyan]{msi_file}[/]...")
        light_cmd = [
            "light",
            str(wixobj_file),
            "-ext", "WixUIExtension",
            "-cultures:zh-cn", # Keep cultures for light command
            "-out", str(msi_file)
        ]
        console.print(f"      [dim]命令: {' '.join(light_cmd)}[/]")
        result_light = subprocess.run(light_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
        if result_light.returncode != 0:
            console.print("[bold red]错误:[/bold red] Light 链接失败:")
            if result_light.stdout: console.print(f"[red]{result_light.stdout}[/]")
            if result_light.stderr: console.print(f"[red]{result_light.stderr}[/]")
            return False
        console.print(f"    [bold green]MSI 文件创建成功:[/bold green] [cyan]{msi_file}[/]")
        return True

    except Exception as e:
        console.print(f"[bold red]错误:[/bold red] 创建 MSI 时发生异常: {e}")
        return False

# --- DMG Creation (macOS Only) ---

def create_dmg(app_bundle_path, version, arch, output_dir):
    """Creates a DMG disk image using create-dmg."""
    console.print("\n[bold magenta]--- 5a. 开始创建 DMG 磁盘映像 (使用 create-dmg) ---[/]")
    if not check_tool("create-dmg"):
        console.print("[bold red]错误:[/bold red] create-dmg 工具未找到，无法创建 DMG。")
        return False

    dmg_filename = output_dir / f"YCursor_mac_{arch}_v{version}.dmg"
    volname = f"YCursor v{version}"
    app_name = app_bundle_path.name # Should be YCursor.app

    # Ensure output directory exists
    output_dir.mkdir(parents=True, exist_ok=True)

    try:
        console.print(f"    创建 DMG: [cyan]{dmg_filename}[/]...")
        cmd = [
            "create-dmg",
            "--volname", volname,
            "--window-pos", "200", "120",
            "--window-size", "600", "400",
            "--icon-size", "100",
            "--icon", app_name, "150", "180", # Position of app icon
            "--hide-extension", app_name,
            "--app-drop-link", "450", "180", # Position of Applications link
            str(dmg_filename), # Output file path
            str(app_bundle_path.parent) # Source folder containing the .app bundle
        ]
        console.print(f"      [dim]命令: {' '.join(cmd)}[/]")
        # We need to run create-dmg from the directory containing the .app for paths to work correctly
        result = subprocess.run(cmd, cwd=app_bundle_path.parent, capture_output=True, text=True, encoding='utf-8', errors='replace')

        if result.returncode != 0:
            console.print(f"[bold red]错误:[/bold red] create-dmg 执行失败 (返回码 {result.returncode}):")
            if result.stdout: console.print(f"[red]{result.stdout}[/]")
            if result.stderr: console.print(f"[red]{result.stderr}[/]")
            # create-dmg might fail if the output file already exists, try removing it first
            if dmg_filename.exists():
                console.print(f"[yellow]警告:[/yellow] 输出文件 [cyan]{dmg_filename}[/cyan] 可能已存在，请尝试手动删除后再试。")
            return False
        else:
            # Check stdout/stderr for potential warnings even on success
            if result.stdout:
                 console.print("      create-dmg 输出:")
                 console.print(f"[dim]{result.stdout}[/]")
            if result.stderr:
                 console.print("      create-dmg 错误输出 (可能包含警告): ")
                 console.print(f"[yellow]{result.stderr}[/]")
            console.print(f"    [bold green]DMG 文件创建成功:[/bold green] [cyan]{dmg_filename}[/]")
            return True
    except FileNotFoundError:
         console.print(f"[bold red]错误:[/bold red] 找不到命令 [cyan]'create-dmg'[/cyan]。请确保已安装并添加到 PATH。")
         console.print("      (尝试: [bold]brew install create-dmg[/])")
         return False
    except Exception as e:
        console.print(f"[bold red]错误:[/bold red] 创建 DMG 时发生异常: {e}")
        return False

def main():
    original_entry_point = "YCursor.py" # Keep original for reference and naming
    source_dir = Path(".") # Assume source code is in the root directory
    version_file = "version_checker.py"
    output_base_dir = Path("dist")
    # Define temporary build directory - Removed PyArmor related dirs

    entry_point_stem = Path(original_entry_point).stem
    nuitka_out_dir_name = f"{entry_point_stem}.dist" # Nuitka output dir name based on original script
    nuitka_build_dir_name = f"{entry_point_stem}.build" # Nuitka build cache dir name
    app_bundle_name = "YCursor.app" # Expected .app name

    entry_point_for_nuitka = str(source_dir / original_entry_point) # Always use original source path now

    # Define full paths for cleanup
    nuitka_build_dir = output_base_dir / nuitka_build_dir_name
    nuitka_dist_dir = output_base_dir / nuitka_out_dir_name
    wxs_path = output_base_dir / "YCursor.wxs"
    wxl_path = output_base_dir / "zh-cn.wxl"
    wixobj_path = output_base_dir / "YCursor.wixobj"
    license_rtf_path = output_base_dir / "license_zh-cn.rtf"

    # Ensure build_temp is cleaned up at the end
    try:
        # --- 0. Tool Checks ---
        console.print("\n[bold magenta]--- 0. 检查所需工具 ---[/]")

        # Interactive UPX Check
        use_upx_compression = False # Default to False
        upx_path = shutil.which('upx')
        if upx_path:
            console.print(f"    检测到 UPX 工具: [cyan]{upx_path}[/]")
            try:
                response = input("    是否启用 UPX 压缩以减小文件大小？(y/N): ").strip().lower()
                if response == 'y':
                    use_upx_compression = True
                    console.print("    [green]已选择启用 UPX 压缩。[/]")
                else:
                    console.print("    [yellow]已选择不使用 UPX 压缩。[/]")
            except EOFError: # Handle non-interactive environments gracefully
                console.print("    [yellow]无法读取用户输入，默认不使用 UPX 压缩。[/]")
                use_upx_compression = False
        else:
            console.print("    [yellow]未检测到 UPX 工具，将跳过压缩。[/]")

        # Check for UPX early for user feedback (relevant for all builds now)
        # check_tool('upx') # Removed: Handled by interactive check above
        # WiX/create-dmg checks are done within their respective functions
        # PyArmor check removed

        # --- 1. Platform Info --- (Step number adjusted)
        console.print("\n[bold magenta]--- 1. 检测平台和架构 ---[/]")
        os_name, arch = get_platform_info()
        console.print(f"    操作系统: [cyan]{os_name}[/]")
        console.print(f"    架构: [cyan]{arch}[/]")

        # --- 2. Get Version --- (Step number adjusted)
        console.print(f"\n[bold magenta]--- 2. 从 {version_file} 读取版本 ---[/]")
        version = get_version(os_name, version_file)
        if not version:
            console.print(f"[bold red]错误：[/bold red]无法从 [cyan]{version_file}[/] 获取 [cyan]'{os_name}'[/cyan] 的版本。脚本终止。")
            sys.exit(1)
        console.print(f"    版本: [cyan]{version}[/]")

        # Determine expected entry executable name based on fixed output name
        entry_executable_name = "YCursor.exe" if os_name == 'win' else "YCursor"

        # --- 3. Build Nuitka Command --- (Step number adjusted)
        console.print("\n[bold magenta]--- 3. 构建 Nuitka 命令 ---[/]")
        # Pass the original entry point and UPX choice to Nuitka command builder
        nuitka_command = build_nuitka_command(os_name, arch, version, entry_point_for_nuitka, use_upx=use_upx_compression)
        console.print("    [bold]将要执行的命令:[/]")
        console.print(f"      [dim]{' '.join(nuitka_command)}[/]")

        # Define base_filename for installer naming
        base_filename_installer = f"YCursor_{os_name}_{arch}_v{version}"

        # --- 4. Execute Nuitka Build --- (Step number adjusted)
        console.print("\n[bold blue]--- 4. 开始执行 Nuitka 构建 (这可能需要较长时间) ---[/]")
        console.print("[cyan]正在执行 Nuitka 编译，将显示详细输出信息...[/]")
        nuitka_success = False
        try:
            # Using Popen to stream output in real-time
            process = subprocess.Popen(
                nuitka_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Merge stderr into stdout for unified output
                text=True,
                encoding='utf-8',
                errors='replace',
                bufsize=1  # Line buffered
                # Note: Nuitka might behave differently depending on cwd.
                # Running from project root should be safe.
            )

            # Read and print output line by line in real-time
            console.print("[dim]--- Nuitka 详细输出开始 ---[/]")
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    # Print each line directly to show detailed compilation progress
                    print(output.rstrip())  # Use print() instead of console.print() for raw output

            return_code = process.wait()
            console.print("[dim]--- Nuitka 详细输出结束 ---[/]")

            if return_code != 0:
                console.print(f"\n[bold red]错误：Nuitka 构建失败，返回代码 {return_code}[/]")
                nuitka_success = False  # Mark as failed
            else:
                console.print(f"\n[bold green]Nuitka 构建成功完成！[/]")
                nuitka_success = True

        except FileNotFoundError:
            console.print(f"[red]错误：找不到命令 '{nuitka_command[0]}' 或 'nuitka'。[/red]")
            console.print("[red]请确保 Python 和 Nuitka 已正确安装并包含在系统的 PATH 环境变量中。[/red]")
            nuitka_success = False # Mark as failed
        except Exception as e:
            console.print(f"\n[bold red]Nuitka 构建过程中发生意外错误:[/bold red] {e}")
            nuitka_success = False # Mark as failed

        # --- 5. Post-Nuitka Processing (MSI/DMG Creation) --- (Step number adjusted)
        # Only proceed if Nuitka build was successful
        if nuitka_success:
            console.print("\n[bold blue]--- 5. Nuitka 后处理 ---[/]")
            final_installer_path = None
            executable_location = None # Path to the main executable or .app bundle generated by Nuitka

            # --- 5a. Find Executable/App Bundle Location ---
            if os_name == 'win':
                nuitka_output_path = nuitka_dist_dir
                if nuitka_output_path.is_dir():
                    found_executable = nuitka_output_path / entry_executable_name
                    if found_executable.exists():
                        executable_location = nuitka_output_path # Directory containing the exe
                    else:
                         console.print(f"[yellow]警告:[/yellow] 在 [cyan]{nuitka_output_path}[/] 中未找到预期的可执行文件 [cyan]{entry_executable_name}[/]")
                else:
                     console.print(f"[yellow]警告:[/yellow] 找不到 Nuitka 输出目录: [cyan]{nuitka_output_path}[/cyan]，无法执行 MSI 创建。")
                     nuitka_success = False # Mark post-processing as failed if output dir is missing
            elif os_name == 'mac':
                # Find .app bundle first
                app_bundle_in_dist = nuitka_dist_dir / app_bundle_name
                app_bundle_in_nuitka_dir = nuitka_dist_dir / nuitka_out_dir_name / app_bundle_name
                fallback_app_path = nuitka_dist_dir / f"{Path(original_entry_point).stem}.app"
                nuitka_app_path = None
                if app_bundle_in_dist.exists() and app_bundle_in_dist.is_dir(): nuitka_app_path = app_bundle_in_dist
                elif app_bundle_in_nuitka_dir.exists() and app_bundle_in_nuitka_dir.is_dir(): nuitka_app_path = app_bundle_in_nuitka_dir
                elif fallback_app_path.exists() and fallback_app_path.is_dir(): nuitka_app_path = fallback_app_path

                if nuitka_app_path:
                    executable_location = nuitka_app_path # Path to the .app bundle
                else:
                    console.print(f"[yellow]警告:[/yellow] 找不到 Nuitka 输出的 .app 包，无法执行 DMG 创建。")
                    nuitka_success = False
            elif os_name == 'linux':
                # Linux doesn't have a separate installer creation step here
                # Nuitka directly creates .deb/.rpm in 'dist' if requested
                executable_location = nuitka_dist_dir # Assume location for potential later steps if any
                # Verify executable exists (optional check)
                nuitka_output_path = nuitka_dist_dir
                if nuitka_output_path.is_dir():
                     found_executable = nuitka_output_path / entry_executable_name
                     if not found_executable.exists():
                        console.print(f"[yellow]警告:[/yellow] 在 [cyan]{nuitka_output_path}[/] 中未找到预期的可执行文件 [cyan]{entry_executable_name}[/]")
                else:
                    executable_in_base = nuitka_dist_dir / entry_executable_name
                    if not executable_in_base.exists():
                         console.print(f"[yellow]警告:[/yellow] 找不到 Nuitka 输出目录或直接输出的可执行文件。")


            # --- 5b. Create MSI / DMG ---
            # Continue only if Nuitka output location was determined correctly earlier
            if nuitka_success and executable_location: # Check the flag and location again
                if os_name == 'win':
                    console.print("[bold blue]--- 5b. 创建 MSI 安装包 ---[/]") # Renumbered
                    if create_msi(executable_location, version, arch, output_base_dir, entry_executable_name):
                        final_installer_path = output_base_dir / f"{base_filename_installer}.msi"
                    else:
                        console.print("[bold red]错误:[/bold red] MSI 创建步骤失败。")
                elif os_name == 'mac':
                    console.print("[bold blue]--- 5b. 创建 DMG 磁盘映像 ---[/]") # Renumbered
                    console.print(f"    找到 Nuitka 输出的 .app 包: [cyan]{executable_location}[/]")
                    if create_dmg(executable_location, version, arch, output_base_dir):
                        final_installer_path = output_base_dir / f"{base_filename_installer}.dmg"
                    else:
                        console.print("[bold red]错误:[/bold red] DMG 创建步骤失败。")
                elif os_name == 'linux':
                    console.print("[blue]--- 5b. Linux 包创建 ---[/]") # Renumbered
                    console.print(f"    [dim]说明：Nuitka 已尝试在 'dist' 目录下生成 .deb 和 .rpm 包。[/]")
                    console.print(f"          [dim]包文件应为: {base_filename_installer}.deb 和 {base_filename_installer}.rpm[/]")
                    console.print("          [dim](请检查 'dist' 目录确认是否生成成功)[/]")
                    deb_path = output_base_dir / f"{base_filename_installer}.deb"
                    rpm_path = output_base_dir / f"{base_filename_installer}.rpm"
                    if deb_path.exists() or rpm_path.exists():
                         final_installer_path = f"[cyan]dist/{base_filename_installer}.deb / .rpm[/]"
                    else:
                         console.print("[yellow]警告:[/yellow] 未在 dist 目录中找到预期的 .deb 或 .rpm 文件。")
                         final_installer_path = "(未找到)"
            elif not executable_location and nuitka_success:
                 console.print("[bold red]错误:[/bold red] 由于无法定位 Nuitka 输出，跳过安装包创建步骤。")


            console.print("\n[bold blue]--- 构建流程结束 ---[/]")
            if final_installer_path and final_installer_path != "(未找到)":
                console.print(f"[bold green]最终产物 (或已尝试生成):[/] {final_installer_path}")
            else:
                console.print("[bold yellow]构建未完全成功或未生成最终安装包。请检查上面的错误信息。[/]")
        else:
            # Nuitka build failed earlier
            console.print("\n[bold red]--- 构建流程结束 (失败) ---[/]")
            console.print("[red]由于 Nuitka 构建失败，未进行后处理步骤。[/]")

    except KeyboardInterrupt:
        console.print("\n[bold yellow]用户中断了构建过程。正在清理...[/]")
        # Pass silently to the finally block

    finally:
        # Cleanup - Removed PyArmor temp dir logic
        # Always attempt to clean up Nuitka intermediate and WiX temporary files
        console.print("\n[bold yellow]--- 开始清理构建临时文件 ---[/]")
        cleaned_something = False
        if nuitka_build_dir.exists():
            console.print(f"    删除 Nuitka 构建目录: [cyan]{nuitka_build_dir}[/]")
            shutil.rmtree(nuitka_build_dir, ignore_errors=True)
            cleaned_something = True
        if nuitka_dist_dir.exists():
            # Check if it contains the final .app or .exe before deleting?
            # For now, assume .dist is intermediate if the final product exists outside it
            # A safer approach might be needed depending on Nuitka output variations
            console.print(f"    删除 Nuitka 输出目录: [cyan]{nuitka_dist_dir}[/]")
            shutil.rmtree(nuitka_dist_dir, ignore_errors=True)
            cleaned_something = True
        # Clean WiX temporary files
        if wxs_path.exists():
            console.print(f"    删除 WiX 文件: [cyan]{wxs_path}[/]")
            try: wxs_path.unlink()
            except OSError: console.print(f"      [yellow]警告:[/yellow] 删除 {wxs_path} 失败 (可能仍在使用中)")
            cleaned_something = True
        if wxl_path.exists():
            console.print(f"    删除 WiX 文件: [cyan]{wxl_path}[/]")
            try: wxl_path.unlink()
            except OSError: console.print(f"      [yellow]警告:[/yellow] 删除 {wxl_path} 失败 (可能仍在使用中)")
            cleaned_something = True
        if wixobj_path.exists():
            console.print(f"    删除 WiX 文件: [cyan]{wixobj_path}[/]")
            try: wixobj_path.unlink()
            except OSError: console.print(f"      [yellow]警告:[/yellow] 删除 {wixobj_path} 失败 (可能仍在使用中)")
            cleaned_something = True
        if license_rtf_path.exists():
            console.print(f"    删除许可证文件: [cyan]{license_rtf_path}[/]")
            try: license_rtf_path.unlink()
            except OSError: console.print(f"      [yellow]警告:[/yellow] 删除 {license_rtf_path} 失败 (可能仍在使用中)")
            cleaned_something = True

        if cleaned_something:
             console.print("[bold green]--- 临时文件清理完毕 ---[/]")
        else:
             console.print("    未找到需要清理的临时文件。")

        console.print("构建脚本执行完毕。") # Ensure correct indentation

if __name__ == "__main__":
    main() 