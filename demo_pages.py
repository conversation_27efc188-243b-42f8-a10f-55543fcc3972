import sys
from PySide6.QtWidgets import (
    QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QFrame, QPushButton, QStackedWidget
)
from PySide6.QtCore import Qt

# Import the theme
from theme import Theme

# Import our pages
from widgets.pages import FunctionalityPage, LogPage

class DemoApp(QMainWindow):
    """演示应用"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("YCursor 页面模块演示")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 侧边栏
        sidebar = QFrame()
        sidebar.setFixedWidth(200)
        sidebar.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.GLASS_BG};
                border-radius: {Theme.BORDER_RADIUS};
                border: 1px solid {Theme.GLASS_BORDER};
            }}
        """)
        
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(10, 20, 10, 20)
        sidebar_layout.setSpacing(10)
        
        # 侧边栏标题
        title_label = QLabel("YCursor模块演示")
        title_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_TITLE};
            font-weight: bold;
            color: {Theme.ACCENT};
            padding: 10px;
            background-color: transparent;
        """)
        
        # 侧边栏按钮样式
        button_style = f"""
            QPushButton {{
                text-align: left;
                padding: 12px 20px;
                font-size: 16px;
                border: none;
                background-color: transparent;
                color: {Theme.TEXT_SECONDARY};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            QPushButton:hover {{
                background-color: {Theme.SECONDARY};
                color: {Theme.TEXT_PRIMARY};
            }}
            QPushButton:checked {{
                background-color: {Theme.ACCENT};
                color: {Theme.TEXT_PRIMARY};
                font-weight: bold;
            }}
        """
        
        # 功能页面按钮
        self.functionality_btn = QPushButton("  功能中心")
        self.functionality_btn.setCheckable(True)
        self.functionality_btn.setChecked(True)
        self.functionality_btn.clicked.connect(lambda: self.switch_page(0))
        self.functionality_btn.setStyleSheet(button_style)
        
        # 日志页面按钮
        self.log_btn = QPushButton("  系统日志")
        self.log_btn.setCheckable(True)
        self.log_btn.clicked.connect(lambda: self.switch_page(1))
        self.log_btn.setStyleSheet(button_style)
        
        # 添加标题和按钮到侧边栏
        sidebar_layout.addWidget(title_label)
        sidebar_layout.addSpacing(20)
        sidebar_layout.addWidget(self.functionality_btn)
        sidebar_layout.addWidget(self.log_btn)
        sidebar_layout.addStretch()
        
        # 内容区域
        content_area = QFrame()
        content_area.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.BG_COLOR};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建堆叠部件
        self.stack = QStackedWidget()
        
        # 创建功能页面和日志页面
        self.functionality_page = FunctionalityPage()
        self.log_page = LogPage()
        
        # 将功能页面和日志页面添加到堆叠部件
        self.stack.addWidget(self.functionality_page)
        self.stack.addWidget(self.log_page)
        
        # 添加堆叠部件到内容区域
        content_layout.addWidget(self.stack)
        
        # 添加侧边栏和内容区域到主布局
        main_layout.addWidget(sidebar)
        main_layout.addWidget(content_area, 1)
        
        # 连接信号
        self.functionality_page.function_selected.connect(self.on_function_selected)
        
    def switch_page(self, index):
        """切换页面"""
        self.stack.setCurrentIndex(index)
        
        # 更新按钮状态
        self.functionality_btn.setChecked(index == 0)
        self.log_btn.setChecked(index == 1)
        
    def on_function_selected(self, func_id):
        """处理功能选择"""
        print(f"选择了功能: {func_id}")
        
        # 处理功能选择
        if func_id == "batch_delete":
            print("执行批量删除账户")
        elif func_id == "refresh_quota":
            print("执行刷新账户额度")
        elif func_id == "about":
            print("显示关于信息")
        # 其他功能...


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 创建并显示主窗口
    window = DemoApp()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()