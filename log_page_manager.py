#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志页面管理器模块
提供日志页面的逻辑和动画管理
"""

import sys
import re
from PySide6.QtCore import Qt, QTimer, QEasingCurve, QAbstractAnimation, QObject, QEvent
from PySide6.QtWidgets import QApplication, QStyleOptionSlider, QStyle
from PySide6.QtGui import QWheelEvent, QTextCursor

from smooth_scroll_animation import SmoothScrollAnimation
from theme import Theme
from log_workers import LogLoadWorker, LogFilterWorker, LogFormatterWorker


class LogPageManager(QObject):
    """日志页面管理器，处理日志页面的逻辑和动画效果"""
    
    def __init__(self, parent, ui_update_thread):
        """初始化日志页面管理器
        
        Args:
            parent: 父窗口对象
            ui_update_thread: UI更新线程
        """
        super().__init__(parent)
        self.parent = parent
        self.ui_update_thread = ui_update_thread
        self.full_log_content = ""
        self.previous_log_content = ""  # 添加：保存上一次的日志内容
        self.active_filters = set()
        self.is_log_page_visible = False  # 添加标记，表示日志页面是否显示
        self.real_first_visit = True     # 标记真正的首次访问
        self.last_scroll_position = 0     # 保存上次离开日志页面时的滚动位置
        self.last_scroll_percent = 0.0    # 保存上次离开时的滚动百分比
        
        # Keep track of active workers and animations
        self._active_workers = []
        self._scroll_animation = None
        self._internal_timers = [] # Keep track of timers created internally
        
        # 添加处理日志更新的信号处理器
        self.ui_update_thread.log_update_signal.connect(self._handle_log_update)
        
        # 初始化后设置滚动条事件
        # 使用单次定时器延迟设置，确保UI已经初始化
        init_timer = QTimer(self) # Assign parent
        init_timer.setSingleShot(True)
        init_timer.timeout.connect(self._init_scrollbar_settings)
        init_timer.start(500)
        self._internal_timers.append(init_timer) # Track this timer
    
    def set_log_page_visible(self, visible):
        """设置日志页面是否可见
        
        Args:
            visible: 是否可见
        """
        # 之前是否可见
        was_visible = self.is_log_page_visible
        
        # 如果即将不可见，保存当前滚动位置
        if was_visible and not visible:
            self._save_current_scroll_position()
        
        # 更新可见状态
        self.is_log_page_visible = visible
        
        # 如果从不可见变为可见
        if visible and not was_visible:
            # 判断是否是真正的首次访问
            if self.real_first_visit:
                print("日志页面首次显示，将强制滚动到底部")
                
                # 标记远程传递
                self.is_first_visit = True
                
                # 延迟执行，确保页面切换完成
                QTimer.singleShot(300, self._direct_scroll_to_bottom_on_first_visit)
                
                # 标记已不再是首次访问
                self.real_first_visit = False
            else:
                print("日志页面再次显示，将恢复上次滚动位置")
                
                # 延迟执行，确保页面切换完成
                QTimer.singleShot(300, self._restore_last_scroll_position)
    
    def _handle_log_update(self, operation, params):
        """处理日志更新请求
        
        Args:
            operation: 操作类型
            params: 操作参数
        """
        if operation == "refresh":
            # 检查是否需要强制滚动到底部
            force_scroll = params.get("force_scroll", False)
            if force_scroll:
                # 先刷新日志内容
                self._refresh_log_with_force_scroll()
            else:
                # 普通刷新
                self._refresh_log()
        elif operation == "filter":
            self._apply_filters(params.get("filters", []))
        elif operation == "scroll":
            position = params.get("position")
            animated = params.get("animated", True)
            if position == "bottom":
                self._scroll_to_bottom(animated)
            elif position == "top":
                self._scroll_to_top(animated)
            else:
                self._scroll_to_position(position, animated)
    
    def refresh_log(self):
        """刷新日志内容"""
        # 检查日志页面是否可见，如果不可见则不执行刷新操作
        if not self.is_log_page_visible:
            return
        
        print("用户请求刷新日志，将执行平滑滚动到底部")
        # 标记为需要显示滚动动画
        self.show_scroll_animation = True
            
        # 将日志刷新操作添加到UI更新线程
        self.ui_update_thread.add_update_request("log_operation", {
            "operation": "refresh",
            "params": {"show_animation": True}
        })
    
    def refresh_log_with_force_scroll(self):
        """刷新日志内容并强制滚动到底部，用于首次加载时"""
        # 检查日志页面是否可见，如果不可见则不执行刷新操作
        if not self.is_log_page_visible:
            return
            
        # 将日志刷新操作添加到UI更新线程，并指定强制滚动标志
        self.ui_update_thread.add_update_request("log_operation", {
            "operation": "refresh",
            "params": {"force_scroll": True}
        })
    
    def _refresh_log(self):
        """实际执行日志刷新操作"""
        try:
            print("开始执行日志刷新操作...")
            # 检查当前滚动位置
            scroll_bar = self._get_scroll_bar()
            
            # 标记需要刷新后滚动到底部
            self.refresh_need_scroll_to_bottom = True
            
            # 保存状态，表示需要执行明显的滚动动画
            if hasattr(self, 'show_scroll_animation'):
                self.force_scroll_animation = True
                print("刷新后将执行超长滚动动画")
            
            # 保存当前滚动位置
            if scroll_bar:
                max_value = scroll_bar.maximum()
                current_value = scroll_bar.value()
                print(f"刷新前当前滚动条值: {current_value}/{max_value}")
                
                # 保存当前精确位置作为滚动动画的起点
                self.refresh_start_position = current_value
                
                if max_value > 0:
                    # 保存滚动位置的相对百分比
                    self.scroll_position_percent = current_value / max_value
                    print(f"保存滚动位置百分比: {self.scroll_position_percent * 100:.2f}%")
                else:
                    self.scroll_position_percent = 0
                    print("滚动条最大值为0，设置滚动位置百分比为0")
                
                # 判断是否已经处于底部或接近底部（距离底部10%以内）
                self.is_at_bottom = self.scroll_position_percent >= 0.9
                print(f"是否处于底部: {self.is_at_bottom} (阈值: 90%)")
            else:
                print("未找到滚动条，无法保存滚动位置")
                self.scroll_position_percent = 0
                self.is_at_bottom = False
                self.refresh_start_position = 0
            
            # 创建并启动日志加载线程
            self.log_loader = LogLoadWorker()
            self.log_loader.log_loaded.connect(self._on_log_loaded)
            self._start_worker(self.log_loader) # Use helper
            
        except Exception as e:
            error_msg = f"启动日志加载时出错: {str(e)}"
            self._set_log_text(f"<div style='color: #ef5350; margin: 20px;'>{error_msg}</div>")
            print(error_msg)
            
    def _compare_and_get_new_content(self, old_content, new_content):
        """比较新旧日志内容，返回新增的内容和处理策略
        
        Args:
            old_content: 旧日志内容
            new_content: 新日志内容
            
        Returns:
            tuple: (新增内容, 是否完全替换, 是否有更新)
        """
        print("开始比较新旧日志内容...")
        
        # 处理空内容情况
        if not old_content:
            print("旧内容为空，需要完全替换")
            return new_content, True, bool(new_content)
            
        # 内容相同的情况
        if old_content == new_content:
            print("新旧内容完全相同，无需更新")
            return "", False, False
        
        # 检查是否是简单追加情况
        if new_content.startswith(old_content):
            # 简单追加
            added_content = new_content[len(old_content):]
            print(f"检测到简单追加情况，新增内容大小: {len(added_content)}字节")
            return added_content, False, bool(added_content.strip())
        
        # 查找最后几行的匹配
        old_lines = old_content.split('\n')
        new_lines = new_content.split('\n')
        
        # 计算旧内容和新内容的行数
        old_line_count = len(old_lines)
        new_line_count = len(new_lines)
        
        print(f"旧内容: {old_line_count}行, 新内容: {new_line_count}行")
        
        # 如果新内容行数显著少于旧内容(可能是日志被截断)
        if new_line_count < old_line_count * 0.8:  # 80%阈值
            print(f"新内容行数明显少于旧内容，可能是日志被截断")
            return new_content, True, True
        
        # 查找最后几行的匹配点
        check_lines = min(50, old_line_count)  # 最多检查50行
        if check_lines > 0:
            old_tail = '\n'.join(old_lines[-check_lines:])
            
            # 在新内容中查找旧内容的尾部
            tail_pos = new_content.find(old_tail)
            if tail_pos >= 0:
                # 找到匹配，计算追加内容
                tail_end_pos = tail_pos + len(old_tail)
                if tail_end_pos < len(new_content):
                    added_content = new_content[tail_end_pos:]
                    print(f"找到尾部匹配，新增内容大小: {len(added_content)}字节")
                    return added_content, False, bool(added_content.strip())
                else:
                    print("尾部匹配到末尾，无新增内容")
                    return "", False, False
        
        # 无法找到明确的追加模式，使用完全替换
        print("无法找到明确的内容追加模式，将使用完全替换")
        return new_content, True, True
    
    def _on_log_loaded(self, log_content):
        """当日志加载完成后的处理"""
        try:
            if not log_content or log_content == "今日暂无日志记录":
                self._set_log_text("<div style='color: #9e9e9e; text-align: center; margin-top: 20px;'>今日暂无日志记录</div>")
                self.full_log_content = ""
                self.previous_log_content = ""  # 清空上一次内容
                return
            
            # 比较新旧日志内容
            new_content, need_full_replace, has_update = self._compare_and_get_new_content(
                self.previous_log_content, log_content
            )
            
            # 保存当前日志内容为下次比较基准
            self.previous_log_content = log_content
            self.full_log_content = log_content
            
            # 根据比较结果决定处理方式
            if not has_update:
                # 无变化，不更新UI
                print("日志内容无变化，不更新显示")
                return
                
            # 有过滤器时，必须重新处理全部内容
            if self.active_filters:
                print("存在活动过滤器，需要重新处理全部内容")
                self._apply_filters_threaded(list(self.active_filters))
                return
                
            # 决定是否需要完全替换
            if need_full_replace:
                # 需要完全替换的情况
                print("日志内容需要完全替换")
                self._format_and_display_log_threaded(log_content)
            else:
                # 可以增量更新的情况
                print("日志内容可以增量更新")
                self._format_and_append_new_content(new_content)
            
        except Exception as e:
            error_msg = f"处理加载完成的日志时出错: {str(e)}"
            self._set_log_text(f"<div style='color: #ef5350; margin: 20px;'>{error_msg}</div>")
            print(error_msg)
    
    def apply_filters(self, filters):
        """应用日志过滤器
        
        Args:
            filters: 过滤器集合
        """
        self.ui_update_thread.add_update_request("log_operation", {
            "operation": "filter",
            "params": {"filters": list(filters)}
        })
    
    def _apply_filters(self, filters):
        """实际执行过滤操作
        
        Args:
            filters: 过滤器列表
        """
        self.active_filters = set(filters)
        
        # 如果没有原始日志内容，刷新日志
        if not self.full_log_content:
            self._refresh_log()
            return
            
        # 保存当前滚动位置
        scroll_bar = self._get_scroll_bar()
        if scroll_bar and scroll_bar.maximum() > 0:
            self.scroll_position_percent = scroll_bar.value() / scroll_bar.maximum()
        else:
            self.scroll_position_percent = 0
            
        # 使用线程处理
        self._apply_filters_threaded(filters)
    
    def _apply_filters_threaded(self, filters):
        """使用线程处理过滤操作
        
        Args:
            filters: 过滤器列表
        """
        # 如果没有激活的过滤器，显示所有日志
        if not filters:
            self._format_and_display_log_threaded(self.full_log_content)
            return
            
        # 创建过滤线程
        self.filter_worker = LogFilterWorker(self.full_log_content, filters)
        self.filter_worker.filter_completed.connect(self._on_filter_completed)
        self._start_worker(self.filter_worker) # Use helper
    
    def _on_filter_completed(self, filtered_content, is_empty):
        """过滤完成回调"""
        # 如果过滤结果为空
        if is_empty:
            self._set_log_text("<div style='color: #9e9e9e; text-align: center; margin-top: 20px;'>没有匹配的日志记录</div>")
            return
            
        # 格式化过滤后的内容
        self._format_and_display_log_threaded(filtered_content)
    
    def _format_and_display_log_threaded(self, log_content):
        """使用线程格式化并显示日志内容
        
        Args:
            log_content: 原始日志内容
        """
        # 在启动线程前，再次保存当前的滚动位置，避免在线程启动的短暂延迟中位置发生变化
        scroll_bar = self._get_scroll_bar()
        if scroll_bar and scroll_bar.maximum() > 0:
            current_value = scroll_bar.value()
            max_value = scroll_bar.maximum()
            self.thread_start_scroll_percent = current_value / max_value
            self.thread_start_is_at_bottom = self.thread_start_scroll_percent >= 0.9
            print(f"线程启动前保存滚动位置: {current_value}/{max_value} ({self.thread_start_scroll_percent * 100:.2f}%)")
            print(f"线程启动前是否在底部: {self.thread_start_is_at_bottom}")
        
        # 创建并启动格式化线程
        self.formatter_worker = LogFormatterWorker(log_content)
        
        # 连接分块格式化信号
        self.formatter_worker.chunk_formatted.connect(self._on_chunk_formatted)
        
        # 保留原有信号连接，但优先使用分块处理
        # 只有在没有连接chunk_formatted信号的情况下才使用format_completed
        self.formatter_worker.format_completed.connect(self._on_format_completed)
        
        # 初始化分块处理状态
        self.current_log_chunks = []
        self.is_receiving_chunks = True
        
        # 启动线程
        self._start_worker(self.formatter_worker) # Use helper
    
    def _on_chunk_formatted(self, chunk_html, is_final_chunk):
        """处理分块格式化的HTML内容
        
        Args:
            chunk_html: 当前块的HTML内容
            is_final_chunk: 是否为最后一块
        """
        try:
            # 检查是否为首次访问
            is_first_visit = hasattr(self, 'is_first_visit') and self.is_first_visit
            
            # 如果父组件已经改为QTextBrowser
            if hasattr(self.parent, 'log_text') and hasattr(self.parent.log_text, 'append'):
                # 第一块时清空内容
                if not hasattr(self, 'chunk_counter') or self.chunk_counter == 0:
                    self.parent.log_text.clear()
                    self.chunk_counter = 1
                else:
                    self.chunk_counter += 1
                
                # 追加新的块内容
                self.parent.log_text.append(chunk_html)
                
                # 仅在最后一块时执行滚动操作
                if is_final_chunk:
                    # 标记分块接收完成
                    self.is_receiving_chunks = False
                    
                    # 延迟处理，确保内容完全渲染
                    if is_first_visit:
                        print("分块格式化完成后检测到首次访问标记，将直接滚动到底部")
                        # 强制设置标记
                        self.force_scroll_to_bottom = True
                        # 仍然使用延迟确保首次访问时内容完全渲染
                        QTimer.singleShot(300, self._guaranteed_scroll_to_bottom)
                    # 刷新后的滚动在_set_log_text中已处理，这里不需要再处理
                    elif not hasattr(self, 'refresh_need_scroll_to_bottom') or not self.refresh_need_scroll_to_bottom:
                        # 只在非刷新情况下恢复滚动位置
                        print("分块格式化完成，恢复滚动位置")
                        QTimer.singleShot(300, self._restore_scroll_position)
                    else:
                        print("分块格式化完成，滚动将由_set_log_text方法直接处理")
                    
                    # 重置计数器，准备下一次分块处理
                    self.chunk_counter = 0
            else:
                # 如果仍在使用QLabel，则收集所有块，在最后一块时一次性显示
                # 这是为了兼容现有代码，实际上这种情况下不会有性能提升
                
                # 存储当前块
                self.current_log_chunks.append(chunk_html)
                
                # 仅在最后一块时更新UI
                if is_final_chunk:
                    # 合并所有块
                    combined_html = "".join(self.current_log_chunks)
                    
                    # 清空存储
                    self.current_log_chunks = []
                    self.is_receiving_chunks = False
                    
                    # 调用原有方法更新UI
                    self._on_format_completed(combined_html)
        except Exception as e:
            error_msg = f"处理分块日志内容时出错: {str(e)}"
            print(error_msg)
            
            # 在异常情况下，尝试显示错误信息
            if hasattr(self.parent, 'log_text'):
                error_html = f"<div style='color: #ef5350; margin: 20px;'>{error_msg}</div>"
                self._set_log_text(error_html)
    
    def _on_format_completed(self, formatted_html):
        """格式化完成回调（原有方法，保留用于兼容）"""
        # 如果已经使用分块处理，则忽略此回调
        if hasattr(self, 'is_receiving_chunks') and self.is_receiving_chunks:
            return
            
        # 保存旧内容用于比较
        # 根据组件类型获取内容
        if hasattr(self.parent.log_text, 'toHtml'):  # QTextBrowser
            old_content = self.parent.log_text.toHtml()
        else:  # QLabel
            old_content = self.parent.log_text.text()
        
        # 检查是否为首次访问
        is_first_visit = hasattr(self, 'is_first_visit') and self.is_first_visit
        
        # 仅当内容不同时才更新，避免闪烁
        if old_content != formatted_html:
            # 显示格式化后的HTML
            self._set_log_text(formatted_html)
            
            # 延迟处理，确保内容完全渲染
            if is_first_visit:
                print("格式化完成后检测到首次访问标记，将直接滚动到底部")
                # 强制设置标记
                self.force_scroll_to_bottom = True
                # 仍然使用延迟确保首次访问时内容完全渲染
                QTimer.singleShot(300, self._guaranteed_scroll_to_bottom)
            # 刷新后的滚动在_set_log_text中已处理，这里不需要再处理
            elif not hasattr(self, 'refresh_need_scroll_to_bottom') or not self.refresh_need_scroll_to_bottom:
                # 只在非刷新情况下恢复滚动位置
                print("格式化完成，恢复滚动位置")
                QTimer.singleShot(300, self._restore_scroll_position)
            else:
                print("格式化完成，滚动将由_set_log_text方法直接处理")
    
    def scroll_to_bottom(self, animated=True):
        """滚动到底部
        
        Args:
            animated: 是否使用动画效果
        """
        self.ui_update_thread.add_update_request("log_operation", {
            "operation": "scroll",
            "params": {"position": "bottom", "animated": animated}
        })
    
    def _scroll_to_bottom(self, animated=True):
        """实际执行滚动到底部操作
        
        Args:
            animated: 是否使用动画效果
        """
        scroll_bar = self._get_scroll_bar()
        if not scroll_bar:
            print("无法获取垂直滚动条")
            return
        
        max_value = scroll_bar.maximum()
        current_value = scroll_bar.value()
        
        # 如果已经在底部，不需要再滚动
        if current_value == max_value:
            return
        
        if animated:
            # 如果当前位置不在顶部附近，就从当前位置开始滚动，而不是强制从顶部开始
            start_pos = current_value
            
            # 当点击刷新按钮时，使用更长的动画时间但不强制从顶部开始
            if hasattr(self, 'force_scroll_animation') or (hasattr(self, 'show_scroll_animation') and self.show_scroll_animation):
                print("检测到滚动动画标记，使用更长动画时间但保持当前位置")
            # 只有当当前位置在顶部10%范围内时，才从顶部开始滚动
            elif current_value < max_value * 0.1:
                start_pos = 0
                scroll_bar.setValue(0)  # 设置到顶部
            
            # 使用动画滚动
            if not self._scroll_animation: # Use tracked instance
                self._scroll_animation = SmoothScrollAnimation(scroll_bar)
            
            # 统一使用超长/超丝滑动画时间
            animation_duration = 2500  # 统一使用2.5秒
            print(f"使用超长/超丝滑动画时间: {animation_duration}ms")
                
            # 设置动画时间和曲线
            self._scroll_animation._animation.setDuration(animation_duration)
            # 使用更平滑的缓动曲线
            self._scroll_animation._animation.setEasingCurve(QEasingCurve.Type.OutQuint)
            
            # 记录滚动信息
            print(f"日志平滑滚动：从 {start_pos} 到 {max_value}，动画时间: {animation_duration}ms")
            
            # 启动滚动动画
            self._scroll_animation.scroll_to(max_value)
            
            # 清除标记
            if hasattr(self, 'force_scroll_animation'):
                delattr(self, 'force_scroll_animation')
            if hasattr(self, 'show_scroll_animation'):
                delattr(self, 'show_scroll_animation')
        else:
            # 直接滚动到底部，不使用动画
            print(f"日志直接滚动到位置：{max_value}")
            scroll_bar.setValue(max_value)
    
    def scroll_to_position(self, position, animated=True):
        """滚动到指定位置
        
        Args:
            position: 滚动位置
            animated: 是否使用动画效果
        """
        self.ui_update_thread.add_update_request("log_operation", {
            "operation": "scroll",
            "params": {"position": position, "animated": animated}
        })
    
    def _scroll_to_position(self, position, animated=True):
        """实际执行滚动到指定位置操作
        
        Args:
            position: 滚动位置
            animated: 是否使用动画效果
        """
        scroll_bar = self._get_scroll_bar()
        if not scroll_bar:
            print("无法获取垂直滚动条")
            return
        
        if animated:
            # 获取当前位置，用于计算滚动距离
            current_position = scroll_bar.value()
            max_value = scroll_bar.maximum()
            
            # 计算滚动距离比例，用于调整动画时间
            distance = abs(position - current_position)
            if max_value > 0:
                distance_ratio = distance / max_value
                # 统一使用超长/超丝滑动画时间
                animation_duration = 2500  # 统一使用2.5秒
                print(f"滚动动画持续时间: {animation_duration}ms，距离: {distance}")
            else:
                animation_duration = 2500  # 统一使用2.5秒
            
            # 使用动画滚动
            if not self._scroll_animation: # Use tracked instance
                self._scroll_animation = SmoothScrollAnimation(scroll_bar)
                
            # 使用更平滑的缓动曲线
            self._scroll_animation._animation.setEasingCurve(QEasingCurve.Type.OutQuint)
            
            # 设置更长的动画时间
            self._scroll_animation.scroll_to(position, duration=animation_duration)
        else:
            # 直接滚动
            scroll_bar.setValue(position)

    def _refresh_log_with_force_scroll(self):
        """强制刷新日志并滚动到底部，用于首次加载时"""
        try:
            # 标记需要滚动到底部
            self.force_scroll_to_bottom = True
            self.scroll_position_percent = 1.0  # 100%，即底部
            print("开始强制滚动日志请求，目标位置: 底部")
            
            # 创建并启动日志加载线程
            self.log_loader = LogLoadWorker()
            self.log_loader.log_loaded.connect(self._on_force_scroll_log_loaded)
            self._start_worker(self.log_loader) # Use helper
            
        except Exception as e:
            error_msg = f"启动强制滚动日志加载时出错: {str(e)}"
            self._set_log_text(f"<div style='color: #ef5350; margin: 20px;'>{error_msg}</div>")
            print(error_msg)
    
    def _format_and_append_new_content(self, new_content):
        """格式化并追加新增内容
        
        Args:
            new_content: 新增的日志内容
        """
        try:
            print(f"开始格式化新增内容 ({len(new_content)}字节)...")
            # 创建格式化工作线程
            self.formatter = LogFormatterWorker(new_content)
            
            # 连接信号
            self.formatter.format_completed.connect(self._append_formatted_content)
            
            # 启动线程
            self._start_worker(self.formatter) # Use helper
        except Exception as e:
            print(f"启动格式化新增内容线程时出错: {str(e)}")
            # 出错时回退到全量更新
            self._format_and_display_log_threaded(self.full_log_content)

    def _append_formatted_content(self, formatted_html):
        """追加格式化后的内容
        
        Args:
            formatted_html: 格式化后的HTML内容
        """
        try:
            # 如果内容为空，无需处理
            if not formatted_html or formatted_html.strip() == "":
                print("没有新内容需要追加")
                return
                
            print("准备追加格式化后的内容...")
            
            # 获取当前滚动位置
            scroll_bar = self._get_scroll_bar()
            exact_position = 0
            max_value = 0
            is_at_bottom = False
            
            if scroll_bar:
                exact_position = scroll_bar.value()
                max_value = scroll_bar.maximum()
                # 判断是否接近底部(90%)
                is_at_bottom = (max_value > 0 and exact_position >= max_value * 0.9)
                print(f"追加前滚动位置: {exact_position}/{max_value}, 是否在底部: {is_at_bottom}")
                
                # 阻止滚动条发出信号
                scroll_bar.blockSignals(True)
            
            success = False
            
            # 方法1: 尝试使用文档API进行追加
            if hasattr(self.parent, 'log_text') and hasattr(self.parent.log_text, 'document'):
                try:
                    print("尝试使用文档API方法追加内容...")
                    # 获取文档对象
                    document = self.parent.log_text.document()
                    
                    # 保存当前光标位置
                    old_cursor = self.parent.log_text.textCursor()
                    current_position = old_cursor.position()
                    
                    # 创建新的光标并移动到文档末尾
                    from PySide6.QtGui import QTextCursor
                    cursor = QTextCursor(document)
                    cursor.movePosition(cursor.MoveOperation.End)
                    
                    # 解析并插入HTML内容
                    cursor.insertHtml(formatted_html)
                    
                    # 还原原来的光标位置
                    if not is_at_bottom:
                        old_cursor.setPosition(current_position)
                        self.parent.log_text.setTextCursor(old_cursor)
                    
                    success = True
                    print("成功使用文档API追加内容")
                except Exception as doc_error:
                    print(f"使用文档API追加内容时出错: {str(doc_error)}")
                    success = False
            
            # 方法2: 如果文档API失败，尝试HTML拼接方法
            if not success and hasattr(self.parent, 'log_text') and hasattr(self.parent.log_text, 'toHtml'):
                try:
                    print("尝试使用HTML拼接方法追加内容...")
                    # 获取当前HTML
                    current_html = self.parent.log_text.toHtml()
                    
                    # 找到</body>标签的位置
                    body_end_pos = current_html.rfind("</body>")
                    if body_end_pos > 0:
                        # 在</body>前插入新内容
                        # 去除新内容中的<div>外层包装以防止嵌套问题
                        content_to_insert = formatted_html
                        if content_to_insert.startswith("<div") and content_to_insert.endswith("</div>"):
                            div_start_end = content_to_insert.find(">") + 1
                            content_to_insert = content_to_insert[div_start_end:-6]
                        
                        new_html = current_html[:body_end_pos] + content_to_insert + current_html[body_end_pos:]
                        
                        # 设置新HTML
                        self.parent.log_text.setHtml(new_html)
                        success = True
                        print("成功使用HTML拼接方法追加内容")
                except Exception as html_error:
                    print(f"使用HTML拼接追加内容时出错: {str(html_error)}")
                    success = False
                    
            # 如果所有尝试都失败，使用完全替换
            if not success:
                print("所有追加方法失败，将使用全量替换方式")
                # 重新格式化并显示完整内容
                self._format_and_display_log_threaded(self.full_log_content)
                return  # 提前返回，避免后续处理

            # 更新处理
            QApplication.processEvents()
            
            # 恢复滚动位置
            if scroll_bar:
                # 解除信号阻止
                scroll_bar.blockSignals(False)
                
                # 根据之前的位置决定滚动行为
                if is_at_bottom:
                    # 如果之前在底部，滚动到新的底部
                    new_max = scroll_bar.maximum()
                    print(f"原位置在底部，滚动到新底部: {new_max}")
                    if new_max > max_value:  # 确保有新内容增加
                        self._scroll_to_bottom(animated=True)
                else:
                    # 否则保持原位置
                    print(f"保持原滚动位置: {exact_position}")
                    scroll_bar.setValue(exact_position)
        except Exception as e:
            print(f"追加格式化内容时出错: {str(e)}")
            if scroll_bar:
                scroll_bar.blockSignals(False)

    def _on_force_scroll_log_loaded(self, log_content):
        """强制滚动模式下日志加载完成的处理"""
        try:
            if not log_content or log_content == "今日暂无日志记录":
                self._set_log_text("<div style='color: #9e9e9e; text-align: center; margin-top: 20px;'>今日暂无日志记录</div>")
                self.full_log_content = ""
                self.previous_log_content = ""  # 清空上一次内容
                return
            
            # 保存原始日志内容
            self.full_log_content = log_content
            self.previous_log_content = log_content  # 同步保存为上一次内容
            
            # 标记为底部状态，确保后续会滚动
            self.is_at_bottom = True
            self.force_scroll_to_bottom = True  # 增加这个标记确保实现强制滚动
            self.scroll_position_percent = 1.0  # 设置为底部
            
            # 应用筛选器或直接格式化
            if self.active_filters:
                self._apply_filters_threaded(list(self.active_filters))
            else:
                self._format_and_display_log_threaded(log_content)            
        except Exception as e:
            error_msg = f"处理强制滚动模式下加载完成的日志时出错: {str(e)}"
            self._set_log_text(f"<div style='color: #ef5350; margin: 20px;'>{error_msg}</div>")
            print(error_msg)

    def scroll_to_top(self, animated=True):
        """滚动到顶部
        
        Args:
            animated: 是否使用动画效果
        """
        self.ui_update_thread.add_update_request("log_operation", {
            "operation": "scroll",
            "params": {"position": "top", "animated": animated}
        })
    
    def _restore_scroll_position(self):
        """恢复滚动位置"""
        try:
            print("开始执行_restore_scroll_position方法...")
            # 获取滚动条
            scroll_bar = self._get_scroll_bar()
            if not scroll_bar:
                print("未找到滚动条，无法恢复滚动位置")
                return
                
            # 计算目标位置
            max_value = scroll_bar.maximum()
            if max_value <= 0:
                print("内容不足以需要滚动，滚动条最大值为0，跳过滚动恢复操作")
                return
            
            current_value = scroll_bar.value()
            print(f"当前滚动条值: {current_value}/{max_value}, 当前位置百分比: {current_value/max_value*100 if max_value > 0 else 0:.2f}%")
            
            # 确定目标位置
            target_position = current_value  # 默认不改变
            
            if hasattr(self, 'force_scroll_to_bottom') and self.force_scroll_to_bottom:
                # 强制滚动到底部
                target_position = max_value
                print(f"执行强制滚动到底部: {max_value}")
                self.force_scroll_to_bottom = False  # 重置标记以避免重复滚动
            elif hasattr(self, 'is_at_bottom') and self.is_at_bottom:
                # 如果用户在底部，继续保持在底部
                target_position = max_value
                print(f"保持在底部位置: {max_value}")
            elif hasattr(self, 'scroll_position_percent'):
                # 恢复到之前的相对位置
                target_position = int(max_value * self.scroll_position_percent)
                print(f"恢复到相对位置: {self.scroll_position_percent * 100:.2f}%, 即 {target_position}/{max_value}")
            elif hasattr(self, 'pre_update_is_at_bottom') and self.pre_update_is_at_bottom:
                # 使用内容更新前保存的底部标志
                target_position = max_value
                print(f"使用内容更新前的底部标志，滚动到底部: {max_value}")
            elif hasattr(self, 'pre_update_scroll_percent'):
                # 使用内容更新前保存的相对位置
                target_position = int(max_value * self.pre_update_scroll_percent)
                print(f"使用内容更新前的相对位置: {self.pre_update_scroll_percent * 100:.2f}%, 即 {target_position}/{max_value}")
            elif hasattr(self, 'thread_start_is_at_bottom') and self.thread_start_is_at_bottom:
                # 使用线程启动前保存的底部标志
                target_position = max_value
                print(f"使用线程启动前的底部标志，滚动到底部: {max_value}")
            elif hasattr(self, 'thread_start_scroll_percent'):
                # 使用线程启动前保存的相对位置
                target_position = int(max_value * self.thread_start_scroll_percent)
                print(f"使用线程启动前的相对位置: {self.thread_start_scroll_percent * 100:.2f}%, 即 {target_position}/{max_value}")
            else:
                # 默认不改变位置
                print("没有找到滚动位置信息，保持当前位置")
                return
                
            # 如果目标位置与当前位置相同，不需要滚动
            if target_position == current_value:
                print(f"目标位置与当前位置相同({target_position})，不需要滚动")
                return
                
            # 停止任何正在进行的动画
            if self._scroll_animation and self._scroll_animation._animation.state() == QAbstractAnimation.State.Running: # Use tracked instance
                self._scroll_animation.stop()
                print("停止正在进行的滚动动画")
            
            # 使用动画滚动到目标位置
            print(f"创建新动画，从{current_value}滚动到{target_position}")
            if not self._scroll_animation: # Use tracked instance
                self._scroll_animation = SmoothScrollAnimation(scroll_bar)
            
            # 添加完成后的额外检查
            # Check if connection already exists before adding
            try: self._scroll_animation._animation.finished.disconnect() # Disconnect previous
            except: pass
            self._scroll_animation._animation.finished.connect(lambda: self._check_scroll_restoration(target_position))
            
            # 启动滚动动画
            self._scroll_animation.scroll_to(target_position)
        except Exception as e:
            print(f"恢复滚动位置时出错: {str(e)}")
            
    def _check_scroll_restoration(self, target_position):
        """检查滚动恢复是否成功，如果不成功则直接设置"""
        try:
            # 获取滚动条
            scroll_bar = self._get_scroll_bar()
            if not scroll_bar:
                return
                
            current_value = scroll_bar.value()
            
            # 如果与目标位置不同，直接设置
            if current_value != target_position:
                print(f"滚动恢复不完全({current_value} vs {target_position})，直接设置")
                scroll_bar.setValue(target_position)
        except Exception as e:
            print(f"检查滚动恢复时出错: {str(e)}")
    
    def _direct_scroll_to_bottom_on_first_visit(self):
        """首次访问时直接滚动到底部"""
        print("执行首次访问强制滚动到底部")
        
        # 获取滚动条
        scroll_bar = self._get_scroll_bar()
        if not scroll_bar:
            print("无法获取滚动条，强制滚动失败")
            return
            
        # 直接获取最大值并设置
        max_value = scroll_bar.maximum()
        if max_value > 0:
            print(f"直接滚动到底部位置: {max_value}")
            
            # 使用平滑动画滚动到底部
            if not self._scroll_animation: # Use tracked instance
                self._scroll_animation = SmoothScrollAnimation(scroll_bar)
                
            # 设置超长动画时间，确保滚动效果明显可见
            self._scroll_animation._animation.setDuration(2000)  # 增加到2000毫秒
            self._scroll_animation._animation.setEasingCurve(QEasingCurve.Type.OutQuint)
            
            # 添加动画完成后的额外检查
            from PySide6.QtCore import QAbstractAnimation
            try: self._scroll_animation._animation.finished.disconnect() # Disconnect previous
            except: pass
            self._scroll_animation._animation.finished.connect(self._final_scroll_check)
            
            # 确保从顶部开始滚动
            scroll_bar.setValue(0)
            print(f"设置滚动条到顶部位置0，准备滚动到底部{max_value}")
            
            # 延迟一小段时间启动动画，确保UI已更新
            delayed_timer = QTimer(self) # Assign parent
            delayed_timer.setSingleShot(True)
            delayed_timer.timeout.connect(lambda: self._start_delayed_scroll_animation(max_value))
            delayed_timer.start(100)
            self._internal_timers.append(delayed_timer) # Track timer
        else:
            # 内容不足以需要滚动
            print("内容不足以需要滚动，滚动条最大值为0，跳过滚动操作")
            return
    
    def _start_delayed_scroll_animation(self, position):
        """延迟启动滚动动画，确保动画可见"""
        # 获取滚动条
        scroll_bar = self._get_scroll_bar()
        if not scroll_bar:
            return
            
        # 记录滚动前状态
        current = scroll_bar.value()
        max_value = scroll_bar.maximum()
        print(f"开始超长平滑滚动动画：从 {current} 到 {position}，动画时间 2000ms")
        
        # 确保滚动动画存在
        if not self._scroll_animation: # Use tracked instance
            self._scroll_animation = SmoothScrollAnimation(scroll_bar)
            self._scroll_animation._animation.setDuration(2000)
            self._scroll_animation._animation.setEasingCurve(QEasingCurve.Type.OutQuint)
        
        # 启动滚动动画
        self._scroll_animation.scroll_to(position)
    
    def _final_scroll_check(self):
        """动画完成后的额外检查，确保真正到达底部"""
        try:
            # 当动画完成后，使用小延迟再次检查并确保到达真正的底部
            final_check_timer = QTimer(self) # Assign parent
            final_check_timer.setSingleShot(True)
            final_check_timer.timeout.connect(self._ensure_at_bottom)
            final_check_timer.start(50)
            self._internal_timers.append(final_check_timer) # Track timer
        except Exception as e:
            print(f"最终滚动检查时出错: {str(e)}")
    
    def _save_current_scroll_position(self):
        """保存当前滚动位置，用于下次显示日志页面时恢复"""
        try:
            scroll_bar = self._get_scroll_bar()
            if not scroll_bar:
                print("无法获取滚动条，无法保存当前位置")
                return
                
            # 保存精确位置
            self.last_scroll_position = scroll_bar.value()
            
            # 计算并保存百分比位置
            max_value = scroll_bar.maximum()
            if max_value > 0:
                self.last_scroll_percent = self.last_scroll_position / max_value
            else:
                self.last_scroll_percent = 0.0
                
            print(f"保存离开时滚动位置: {self.last_scroll_position}, 百分比: {self.last_scroll_percent:.2f}")
        except Exception as e:
            print(f"保存滚动位置时出错: {str(e)}")
            self.last_scroll_percent = 0
            self.last_scroll_position = 0
    
    def _restore_last_scroll_position(self):
        """恢复到上次离开日志页面时的滚动位置"""
        try:
            # 先检查滚动条
            scroll_bar = self._get_scroll_bar()
            if not scroll_bar:
                print("无法获取滚动条，无法恢复上次位置")
                # 延迟再次尝试恢复，可能还在初始化中
                QTimer.singleShot(300, self._restore_last_scroll_position)
                return
            
            # 检查最大值
            max_value = scroll_bar.maximum()
            if max_value <= 0:
                print("内容不足以需要滚动，滚动条最大值为0，跳过滚动恢复操作")
                return
            
            # 检查是否有保存的位置信息
            if not hasattr(self, 'last_scroll_percent') or self.last_scroll_percent <= 0:
                print("没有有效的上次位置信息，保持当前位置")
                return
                
            # 根据百分比计算位置
            target_position = int(max_value * self.last_scroll_percent)
            current_position = scroll_bar.value()
            
            # 如果当前已经在大约的目标位置附近，不做改变
            if abs(current_position - target_position) < max_value * 0.05:
                print(f"当前位置({current_position})已接近目标位置({target_position})，无需调整")
                return
                
            print(f"恢复到上次离开时位置: {current_position} -> {target_position} (百分比: {self.last_scroll_percent:.2f})")
            
            # 使用平滑动画滚动到目标位置
            if not self._scroll_animation: # Use tracked instance
                self._scroll_animation = SmoothScrollAnimation(scroll_bar)
            
            # 设置适当的动画时间
            animation_duration = 2500  # 统一使用超长/超丝滑动画
            self._scroll_animation._animation.setDuration(animation_duration)
            self._scroll_animation._animation.setEasingCurve(QEasingCurve.Type.OutQuint)
            
            # 启动滚动动画
            self._scroll_animation.scroll_to(target_position)
        except Exception as e:
            print(f"恢复上次滚动位置时出错: {str(e)}")

    def _ensure_at_bottom(self):
        """确保滚动条已经到达真正的底部"""
        # 移除强制设置到底部的检测逻辑
        pass
            
    def _guaranteed_scroll_to_bottom(self):
        """确保滚动到底部，即使需要多次尝试"""
        # 获取滚动条
        scroll_bar = self._get_scroll_bar()
        if not scroll_bar:
            print("无法获取滚动条，强制滚动失败")
            return
        
        # 获取最大值
        max_value = scroll_bar.maximum()
        
        # 如果最大值为0，可能是因为内容尚未完全渲染或布局尚未更新
        # 进行多次尝试，等待内容渲染完成
        if max_value == 0:
            print("内容不足以需要滚动，滚动条最大值为0，跳过滚动操作")
            # 重置重试计数，避免循环重试
            if hasattr(self, '_scroll_retry_count'):
                self._scroll_retry_count = 0
            return
            
        # 执行平滑滚动到底部
        print(f"执行绝对滚动到底部操作")
        print(f"当前滚动条位置: {scroll_bar.value()}, 最大值: {max_value}")
        
        # 滚动到底部
        print(f"开始平滑滚动到底部位置: {max_value}")
        self._scroll_to_bottom(animated=True)
    
    def _scroll_to_top(self, animated=True):
        """实际执行滚动到顶部操作
        
        Args:
            animated: 是否使用动画效果
        """
        scroll_bar = self._get_scroll_bar()
        if not scroll_bar:
            print("无法获取垂直滚动条")
            return
        
        current_value = scroll_bar.value()
        
        # 如果已经在顶部，不需要再滚动
        if current_value == 0:
            return
        
        if animated:
            # 使用动画滚动
            if not self._scroll_animation: # Use tracked instance
                self._scroll_animation = SmoothScrollAnimation(scroll_bar)
                # 使用超长/超丝滑动画
                self._scroll_animation._animation.setDuration(2500)  # 2500毫秒
                # 使用更平滑的缓动曲线
                self._scroll_animation._animation.setEasingCurve(QEasingCurve.Type.OutQuint)
            
            # 记录滚动信息
            print(f"日志平滑滚动：从 {current_value} 到 0")
            
            # 启动滚动动画
            self._scroll_animation.scroll_to(0)
        else:
            # 直接滚动到顶部，不使用动画
            print(f"日志直接滚动到位置：0")
            scroll_bar.setValue(0)
            
    def setup_scroll_bar_events(self):
        """设置滚动条事件（拦截并增强滚动条行为）"""
        # 确保这些变量不会被多次引用
        if hasattr(self, "_scroll_bar_events_setup"):
            print("滚动条事件已经设置，跳过")
            return
            
        try:
            # 获取滚动条
            scroll_bar = self._get_scroll_bar()
            if not scroll_bar:
                print("无法获取滚动条，无法设置滚动条事件")
                return
            
            # 标记滚动条事件已设置
            self._scroll_bar_events_setup = True
                
            # 保存原始事件过滤器
            self.original_event_filter = scroll_bar.eventFilter
            
            # 连接滚动条值改变信号，以检测滚动事件
            scroll_bar.valueChanged.connect(self._handle_user_scroll)
            
            # 设置事件过滤器，以增强滚动条行为
            scroll_bar.installEventFilter(self)
            
            # 禁用焦点处理
            scroll_bar.setFocusPolicy(Qt.FocusPolicy.NoFocus)
            
            # 确保滚动条可见
            scroll_bar.setVisible(True)
            
            # 马上应用用户自定义的滚动行为
            if hasattr(self.parent, 'log_text'):
                self.parent.log_text.wheelEvent = self._handle_wheel_event
                # 确保日志文本可以接收滚轮事件
                self.parent.log_text.setMouseTracking(True)
            
            print("成功设置滚动条事件")
            
        except Exception as e:
            # 记录错误但不要中断程序
            print(f"设置滚动条事件时出错: {str(e)}")
            # 标记设置失败，允许下次重试
            self._scroll_bar_events_setup = False

    def _handle_scrollbar_click(self, obj, event):
        """处理滚动条单击事件，点击滚动条哪里就滚动到哪里
        
        Args:
            obj: 事件对象
            event: 事件
        
        Returns:
            bool: 事件是否已处理
        """
        try:
            # 使用统一的方式获取滚动条
            scroll_bar = self._get_scroll_bar()
            if not scroll_bar or obj != scroll_bar:
                # 不是滚动条的事件，不处理
                return False
            
            # 如果是鼠标左键点击事件
            if event.type() == QEvent.Type.MouseButtonPress and event.button() == Qt.MouseButton.LeftButton:
                # 计算点击位置对应的值
                opt = QStyleOptionSlider()
                scroll_bar.initStyleOption(opt)
                
                # 获取滚动条的几何信息
                groove_rect = scroll_bar.style().subControlRect(
                    QStyle.ComplexControl.CC_ScrollBar, opt, QStyle.SubControl.SC_ScrollBarGroove, scroll_bar)
                slider_rect = scroll_bar.style().subControlRect(
                    QStyle.ComplexControl.CC_ScrollBar, opt, QStyle.SubControl.SC_ScrollBarSlider, scroll_bar)
                
                # 检查点击是否在滑槽内
                if groove_rect.contains(event.pos()):
                    # 相对位置计算
                    if scroll_bar.orientation() == Qt.Orientation.Vertical:
                        pos = event.pos().y()
                        groove_height = groove_rect.height()
                        slider_height = slider_rect.height()
                        pos = max(0, min(pos - slider_height / 2, groove_height - slider_height))
                        value = round(pos / (groove_height - slider_height) * (scroll_bar.maximum() - scroll_bar.minimum())) + scroll_bar.minimum()
                    else:  # 水平滚动条
                        pos = event.pos().x()
                        groove_width = groove_rect.width()
                        slider_width = slider_rect.width()
                        pos = max(0, min(pos - slider_width / 2, groove_width - slider_width))
                        value = round(pos / (groove_width - slider_width) * (scroll_bar.maximum() - scroll_bar.minimum())) + scroll_bar.minimum()
                    
                    # 设置滚动条位置
                    scroll_bar.setValue(value)
                    
                    # 事件已处理
                    return True
            
            # 其他情况调用原始事件过滤器
            if hasattr(self, 'original_event_filter') and self.original_event_filter:
                return self.original_event_filter(obj, event)
            
            # 事件未被处理
            return False
            
        except Exception as e:
            print(f"处理滚动条点击事件时出错: {str(e)}")
            return False

    def _handle_user_scroll(self, value):
        """处理用户滚动操作
        
        Args:
            value: 新的滚动条值
        """
        # 如果有动画正在运行，需要更严格地判断是否为用户操作
        if self._scroll_animation and self._scroll_animation._animation.state() == QAbstractAnimation.State.Running:
            # 我们不在这里判断，依赖SmoothScrollAnimation类中的判断逻辑
            # 这里不再直接停止动画，避免误触发
            pass
            
    def _handle_wheel_event(self, event):
        """处理鼠标滚轮事件
        
        Args:
            event: 滚轮事件对象
        """
        # 获取滚轮事件的角度增量，判断是否为真实的滚轮操作
        from PySide6.QtGui import QWheelEvent
        wheel_event = event
        if abs(wheel_event.angleDelta().y()) > 80:  # 真实滚轮操作通常有较大的角度变化
            # 确认是真实的滚轮操作，停止动画
            if self._scroll_animation and self._scroll_animation._animation.state() == QAbstractAnimation.State.Running: # Use tracked instance
                print(f"检测到明显的滚轮操作: {wheel_event.angleDelta().y()}，停止当前滚动动画")
                self._scroll_animation.stop()
            
        # 调用原始的滚轮事件处理
        from PySide6.QtWidgets import QTextBrowser
        QTextBrowser.wheelEvent(self.parent.log_text, event)

    def _set_log_text(self, html_content):
        """设置日志文本内容，保持滚动位置并实现完美丝滑过渡"""
        try:
            # 获取滚动条和当前位置
            scroll_bar = self._get_scroll_bar()
            exact_original_position = 0
            need_smooth_scroll = False
            
            # 记录精确滚动位置和是否需要平滑滚动到底部
            if scroll_bar:
                exact_original_position = scroll_bar.value()
                need_smooth_scroll = hasattr(self, 'refresh_need_scroll_to_bottom') and self.refresh_need_scroll_to_bottom
                print(f"内容更新前保存精确位置: {exact_original_position}")
                
                # 阻止滚动条发出信号，防止自动滚动到顶部，但不要长时间阻塞
                scroll_bar.blockSignals(True)
            
            # 保存详细滚动信息作为备份
            self._save_scroll_position_before_update()
            
            # 设置内容
            if hasattr(self, 'log_web_view') and self.log_web_view:
                self.log_web_view.setHtml(html_content)
            elif hasattr(self.parent, 'log_text'):
                if hasattr(self.parent.log_text, 'setHtml'):  # QTextBrowser
                    self.parent.log_text.setHtml(html_content)
                else:  # QLabel
                    self.parent.log_text.setText(html_content)
            else:
                print("未找到可用的日志显示控件")
                if scroll_bar:
                    scroll_bar.blockSignals(False)  # 确保解除信号阻止
                return
            
            # 处理布局更新
            from PySide6.QtWidgets import QApplication
            QApplication.processEvents()
            
            if scroll_bar:
                # 获取更新后的最大值
                max_value = scroll_bar.maximum()
                
                if max_value > 0:
                    # 直接恢复到之前保存的精确位置（确保不超过最大值）
                    restore_position = min(exact_original_position, max_value)
                    scroll_bar.setValue(restore_position)
                    print(f"内容更新后精确恢复到原位置: {restore_position}/{max_value}")
                    
                    # 记录实际恢复的位置，供后续滚动使用
                    self.restored_position = restore_position
                else:
                    print(f"内容更新后滚动条最大值为{max_value}，无法恢复位置")
                
                # 立即解除信号阻止，确保用户可以操作滚动条
                scroll_bar.blockSignals(False)
                
                # 如果需要滚动到底部，待位置恢复和UI更新后立即开始丝滑滚动
                if need_smooth_scroll and max_value > 0:
                    print(f"准备从恢复位置{restore_position}平滑滚动到底部")
                    
                    # 为确保滚动动画明显可见，先滚动到顶部
                    scroll_bar.setValue(0)
                    print(f"先设置滚动条到顶部位置0，准备滚动到底部{max_value}")
                    
                    # 检查是否需要显示超长动画效果（用户点击刷新按钮的情况）
                    if hasattr(self, 'force_scroll_animation') and self.force_scroll_animation:
                        print("检测到刷新按钮点击，将执行超长滚动动画")
                        # 设置额外长的延迟，确保用户能看到滚动效果
                        QTimer.singleShot(300, self._start_force_scroll_animation)
                        # 清除标记
                        self.force_scroll_animation = False
                        if hasattr(self, 'show_scroll_animation'):
                            delattr(self, 'show_scroll_animation')
                    else:
                        # 正常刷新的情况
                        # 增加延迟到200ms，确保UI完全更新且用户能看到滚动动画
                        QTimer.singleShot(200, self._smooth_scroll_to_bottom_after_refresh)
        except Exception as e:
            print(f"设置日志内容时出错: {str(e)}")
            # 确保出错时也解除信号阻止
            if scroll_bar:
                scroll_bar.blockSignals(False)

    def _save_scroll_position_before_update(self):
        """在内容更新前保存滚动位置信息"""
        try:
            # 检查当前滚动位置
            scroll_bar = self._get_scroll_bar()
            if not scroll_bar:
                print("未找到滚动条，无法在内容更新前保存滚动位置")
                return
                
            max_value = scroll_bar.maximum()
            current_value = scroll_bar.value()
            
            if max_value > 0:
                # 保存更新前的滚动位置百分比
                self.pre_update_scroll_percent = current_value / max_value
                print(f"内容更新前保存滚动位置: {current_value}/{max_value} ({self.pre_update_scroll_percent * 100:.2f}%)")
                
                # 更新底部标志
                self.pre_update_is_at_bottom = self.pre_update_scroll_percent >= 0.9
                print(f"内容更新前是否处于底部: {self.pre_update_is_at_bottom}")
            else:
                self.pre_update_scroll_percent = 0
                self.pre_update_is_at_bottom = False
                print("内容更新前滚动条最大值为0，无法保存滚动位置")
        except Exception as e:
            print(f"内容更新前保存滚动位置时出错: {str(e)}")
            self.pre_update_scroll_percent = 0
            self.pre_update_is_at_bottom = False

    def _get_scroll_bar(self):
        """获取当前使用的滚动条
        
        Returns:
            滚动条对象或None
        """
        try:
            # 优先使用log_text的内置滚动条
            if hasattr(self.parent, 'log_text') and hasattr(self.parent.log_text, 'verticalScrollBar'):
                return self.parent.log_text.verticalScrollBar()
            # 如果存在log_scroll_area并且不为None，使用它的滚动条（向后兼容）
            elif hasattr(self.parent, 'log_scroll_area') and self.parent.log_scroll_area is not None:
                return self.parent.log_scroll_area.verticalScrollBar()
            else:
                print("无法获取滚动条：找不到有效的滚动条组件")
                return None
        except Exception as e:
            print(f"获取滚动条时出错: {str(e)}")
            return None

    def _smooth_scroll_to_bottom_after_refresh(self):
        """刷新后执行完美丝滑平滑滚动到底部"""
        try:
            print("执行超丝滑平滑滚动操作...")
            # 获取滚动条
            scroll_bar = self._get_scroll_bar()
            if not scroll_bar:
                print("未找到滚动条，无法执行平滑滚动")
                return
                
            # 获取最大值
            max_value = scroll_bar.maximum()
            if max_value <= 0:
                print(f"滚动条最大值为{max_value}，无法滚动")
                return
                
            # 获取当前位置作为起始位置（刚才已经设置到顶部）
            start_position = scroll_bar.value()  # 应该是0
            print(f"当前滚动位置: {start_position}")
            
            # 如果已经在底部，不需要滚动
            if start_position >= max_value:
                print(f"已经在底部位置({start_position}/{max_value})，无需滚动")
                return
                
            print(f"开始超长超丝滑平滑滚动：从 {start_position} 到 {max_value}")
            
            # 停止任何正在进行的动画
            if self._scroll_animation and self._scroll_animation._animation.state() == QAbstractAnimation.State.Running: # Use tracked instance
                self._scroll_animation.stop()
                print("停止正在进行的滚动动画")
            
            # 创建新的滚动动画，使用超丝滑配置
            if not self._scroll_animation: # Create if doesn't exist
                self._scroll_animation = SmoothScrollAnimation(scroll_bar)
            # Apply settings regardless
            self._scroll_animation._animation.setDuration(2500)
            self._scroll_animation._animation.setEasingCurve(QEasingCurve.Type.OutQuint)
            
            print(f"超丝滑动画持续时间: {2500}ms，距离: {max_value - start_position}")
            
            # 从当前位置滚动到底部
            self._scroll_animation.scroll_to(max_value)
            
            # 添加动画完成后的清理操作
            try: self._scroll_animation._animation.finished.disconnect() # Disconnect previous
            except: pass
            self._scroll_animation._animation.finished.connect(self._clean_refresh_flags)
        except Exception as e:
            print(f"刷新后平滑滚动时出错: {str(e)}")
            
    def _clean_refresh_flags(self):
        """清理刷新相关标志"""
        if hasattr(self, 'refresh_need_scroll_to_bottom'):
            delattr(self, 'refresh_need_scroll_to_bottom')
        if hasattr(self, 'refresh_start_position'):
            delattr(self, 'refresh_start_position')

    def _start_force_scroll_animation(self):
        """启动强制滚动动画"""
        try:
            # 获取滚动条
            scroll_bar = self._get_scroll_bar()
            if not scroll_bar:
                print("未找到滚动条，无法执行强制滚动动画")
                return
            
            # 获取最大值
            max_value = scroll_bar.maximum()
            if max_value <= 0:
                print(f"滚动条最大值为{max_value}，无法执行强制滚动动画")
                return
            
            # 执行强制滚动到底部
            self._scroll_to_bottom(animated=True)
        except Exception as e:
            print(f"启动强制滚动动画时出错: {str(e)}")

    def _init_scrollbar_settings(self):
        """初始化滚动条设置"""
        try:
            # 获取滚动条
            scroll_bar = self._get_scroll_bar()
            if not scroll_bar:
                print("无法获取滚动条，无法初始化滚动条设置")
                # Try again later if failed initially
                retry_timer = QTimer(self) # Assign parent
                retry_timer.setSingleShot(True)
                retry_timer.timeout.connect(self._init_scrollbar_settings)
                retry_timer.start(1000)
                self._internal_timers.append(retry_timer) # Track timer
                return

            # 设置滚动条事件
            self.setup_scroll_bar_events()
            
            # 检查滚动条是否可以拖动
            if not scroll_bar.isSliderDown():
                print("滚动条初始化设置完成")
        except Exception as e:
            print(f"初始化滚动条设置时出错: {str(e)}")
            # 不影响主程序继续执行

    def eventFilter(self, obj, event):
        """事件过滤器，处理日志页面相关的事件
        
        Args:
            obj: 事件源对象
            event: 事件
            
        Returns:
            bool: 事件是否已处理
        """
        try:
            # 处理滚动条点击事件
            from PySide6.QtCore import QEvent
            if obj == self._get_scroll_bar() and event.type() == QEvent.Type.MouseButtonPress:
                return self._handle_scrollbar_click(obj, event)
                
            # 未处理的事件交给原始过滤器
            if hasattr(self, 'original_event_filter') and self.original_event_filter:
                return self.original_event_filter(obj, event)
                
            # 默认让事件继续传播
            return False
        except Exception as e:
            print(f"事件过滤时出错: {str(e)}")
            return False

    def _start_worker(self, worker_instance):
        """Starts a worker and adds it to the tracking list."""
        self._active_workers.append(worker_instance)
        # Ensure worker cleans itself up from the list when finished
        worker_instance.finished.connect(lambda: self._remove_worker(worker_instance))
        worker_instance.start()

    def _remove_worker(self, worker_instance):
        """Removes a worker from the tracking list."""
        if worker_instance in self._active_workers:
            self._active_workers.remove(worker_instance)
            # print(f"Worker {type(worker_instance).__name__} finished and removed.") # Debug

    def shutdown(self):
        """Stops all active workers, animations, and timers."""
        print("LogPageManager: Shutdown requested.")

        # Stop scroll animation
        if self._scroll_animation and self._scroll_animation._animation.state() == QAbstractAnimation.State.Running:
            print("LogPageManager: Stopping scroll animation.")
            self._scroll_animation.stop()
            self._scroll_animation = None # Clear reference

        # Stop active workers
        # Create a copy for safe iteration while modifying the list
        workers_to_stop = list(self._active_workers)
        if workers_to_stop:
            print(f"LogPageManager: Stopping {len(workers_to_stop)} active worker(s)...")
            for worker in workers_to_stop:
                try:
                    if hasattr(worker, 'stop'):
                        worker.stop() # Request stop
                    # Don't wait here to avoid blocking UI, rely on finished signal
                    # worker.wait(1000) # Optional short wait, but risky
                    # Explicitly disconnect signals to prevent calls after shutdown starts
                    if hasattr(worker, 'log_loaded'): worker.log_loaded.disconnect()
                    if hasattr(worker, 'filter_completed'): worker.filter_completed.disconnect()
                    if hasattr(worker, 'chunk_formatted'): worker.chunk_formatted.disconnect()
                    if hasattr(worker, 'format_completed'): worker.format_completed.disconnect()
                except Exception as e:
                    print(f"LogPageManager: Error stopping/disconnecting worker {type(worker).__name__}: {e}")
            self._active_workers.clear() # Clear the list after requesting stop

        # Stop internal timers
        print(f"LogPageManager: Stopping {len(self._internal_timers)} internal timer(s)...")
        for timer in self._internal_timers:
            if timer and timer.isActive(): # Check if timer object still exists
                timer.stop()
        self._internal_timers.clear()

        # Disconnect the main log update signal handler
        try:
            self.ui_update_thread.log_update_signal.disconnect(self._handle_log_update)
            print("LogPageManager: Disconnected from ui_update_thread signal.")
        except (RuntimeError, TypeError) as e:
            # RuntimeError if disconnected elsewhere, TypeError if never connected
            print(f"LogPageManager: Info - Could not disconnect from ui_update_thread signal (maybe already disconnected): {e}")

        print("LogPageManager: Shutdown finished.")