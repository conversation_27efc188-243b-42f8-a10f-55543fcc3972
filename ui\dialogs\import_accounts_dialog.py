#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导入账户对话框模块
处理账户信息的导入功能
"""

import json
from PySide6.QtWidgets import QFileDialog, QDialog, QVBoxLayout, QFrame
from PySide6.QtCore import Qt
from logger import Logger, info, error

class ImportAccountsDialog:
    """导入账户对话框类"""
    
    def __init__(self, main_window):
        """初始化导入账户对话框
        
        Args:
            main_window: 主窗口实例，用于调用主窗口的方法和访问属性
        """
        self.main_window = main_window
        self.logger = Logger()
    
    def show_dialog(self):
        """显示导入账户对话框"""
        # 打开文件选择对话框
        file_name, _ = QFileDialog.getOpenFileName(
            self.main_window,
            "选择账户信息JSON文件",
            "",
            "JSON Files (*.json);;All Files (*)"
        )

        # 如果用户选择了文件
        if file_name:
            info(f"选择了导入文件: {file_name}")
            # 导入账户
            self.import_accounts(file_name)
    
    def import_accounts(self, file_path):
        """导入账户数据
        
        Args:
            file_path: JSON文件路径
        """
        try:
            # 读取JSON文件
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_accounts = json.load(f)
            
            # 检查是否是有效的账户数据数组
            if not isinstance(imported_accounts, list):
                self.main_window.show_toast("导入失败：无效的账户数据格式，应为JSON数组", error=True)
                error(f"导入失败：无效的账户数据格式，不是数组: {file_path}")
                return
            
            info(f"从文件中读取了 {len(imported_accounts)} 个账户数据")
            
            # 验证和导入账户
            imported_count = 0
            updated_count = 0
            invalid_count = 0
            
            # 创建当前账户邮箱的映射（转换为小写便于不区分大小写比较）
            existing_accounts_map = {}
            for idx, account in enumerate(self.main_window.account_data.accounts):
                email = account.get("email", "").lower()
                if email:
                    existing_accounts_map[email] = idx
            
            info(f"当前已有 {len(existing_accounts_map)} 个账户")
            
            # 处理导入的账户
            for account in imported_accounts:
                # 验证必要字段
                if not self._validate_account_data(account):
                    invalid_count += 1
                    continue
                
                email = account.get("email", "").lower()
                
                # 检查是否已存在该账户
                if email in existing_accounts_map:
                    # 更新已存在账户信息
                    existing_idx = existing_accounts_map[email]
                    existing_account = self.main_window.account_data.accounts[existing_idx]
                    
                    # 合并账户信息（保留现有字段，添加/更新导入的新字段）
                    for key, value in account.items():
                        if key != "email":  # 邮箱保持不变
                            existing_account[key] = value
                    
                    updated_count += 1
                    info(f"更新了已存在账户: {email}")
                else:
                    # 添加为新账户
                    self.main_window.account_data.accounts.append(account)
                    imported_count += 1
                    info(f"添加了新账户: {email}")
            
            # 如果有有效账户导入或更新，保存账户数据
            if imported_count > 0 or updated_count > 0:
                # 修改这里: 允许在文件不存在时创建
                self.main_window.account_data.save_accounts(allow_create=True)
                
                # 显示导入结果
                result_msg = f"导入完成: 新增 {imported_count} 个账户, 更新 {updated_count} 个账户"
                if invalid_count > 0:
                    result_msg += f", {invalid_count} 个无效账户被忽略"
                
                self.main_window.show_toast(result_msg)
                info(result_msg)
                
                # 修改这里: 使用与批量删除类似的刷新方式，而不是直接调用_update_accounts_ui()
                # 1. 重新加载本地存储的账户数据
                self.main_window.account_data.load_accounts()
                info("导入完成，重新加载账户数据")
                
                # 2. 清空现有账户行，避免引用问题
                for email, row in list(self.main_window.account_rows.items()):
                    self.main_window.accounts_layout.removeWidget(row)
                    row.deleteLater()
                self.main_window.account_rows.clear()
                info("已清除现有账户行")
                
                # 3. 确保加载容器隐藏，避免后续引用问题
                if hasattr(self.main_window, 'loading_container') and self.main_window.loading_container is not None:
                    try:
                        self.main_window.loading_container.setVisible(False)
                    except Exception as e:
                        print(f"隐藏加载容器时出错: {str(e)}")
                
                # 4. 更新账户计数
                self.main_window._update_accounts_count()
                
                # 5. 重新加载账户列表
                self.main_window.load_accounts()
                info("已重新加载账户列表")
                
                # 6. 仅使用不重建UI的排序方法
                self.main_window._sort_accounts_without_rebuild_ui()
                info("已重新排序账户列表")
                
                # 7. 在有账户的情况下刷新额度数据
                if self.main_window.account_data.accounts:
                    info(f"开始刷新账户额度数据（共{len(self.main_window.account_data.accounts)}个账户）")
                    # 使用手动刷新模式，避免过多UI重建
                    self.main_window.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=False)
            else:
                if invalid_count > 0:
                    self.main_window.show_toast(f"导入失败: {invalid_count} 个无效账户，未导入任何账户", error=True)
                    error(f"导入失败: 所有 {invalid_count} 个账户都无效")
                else:
                    self.main_window.show_toast("没有新账户需要导入", error=True)
                    info("没有新账户需要导入")
        
        except json.JSONDecodeError:
            self.main_window.show_toast("导入失败：无效的JSON格式", error=True)
            error(f"导入失败：无效的JSON格式: {file_path}")
        except Exception as e:
            self.main_window.show_toast(f"导入失败：{str(e)}", error=True)
            error(f"导入账户时出错: {str(e)}")
    
    def _validate_account_data(self, account):
        """验证账户数据是否包含必要字段
        
        Args:
            account: 账户数据字典
            
        Returns:
            bool: 账户数据是否有效
        """
        # 检查是否包含email字段
        if "email" not in account or not account["email"]:
            info(f"无效账户数据: 缺少email字段")
            return False
        
        # 检查是否包含auth_info字段
        if "auth_info" not in account or not isinstance(account["auth_info"], dict):
            info(f"无效账户数据: 缺少auth_info字段或格式不正确, email={account.get('email', '未知')}")
            return False
        
        return True 