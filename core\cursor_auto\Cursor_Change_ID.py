#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import uuid
import platform
import subprocess
import tempfile
import datetime
import random
import re
import shutil
import time
import stat
import traceback
import glob
from pathlib import Path
from dotenv import load_dotenv
from colorama import init, Fore, Style

# 获取当前文件所在的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 指定.env文件路径
dotenv_path = os.path.join(current_dir, ".env")
# 只加载指定路径的.env文件
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path)

# 颜色定义
class Colors:
    """统一的颜色定义"""
    RED = ''      # 错误信息
    GREEN = ''    # 成功信息
    YELLOW = ''   # 警告/提示信息
    BLUE = ''     # 框架/标题
    PURPLE = ''   # 重要数据
    CYAN = ''     # 进度信息
    WHITE = ''    # 普通文本
    NC = ''       # 结束颜色

# 日志和界面函数
def print_box(title="", content=None, footer=None):
    """打印消息，不带框"""
    # 在开始前添加一个空行
    print()
    
    # 如果只有标题，直接显示标题
    if title and not content and not footer:
        print(title)
        return
    
    # 显示标题
    if title:
        print(title)
    
    # 显示内容
    if content:
        if isinstance(content, str):
            if content.strip():
                print(content)
        elif isinstance(content, list):
            for line in content:
                print(line)
    
    # 显示页脚
    if footer:
        print(footer)

def log_info(msg, show_box=False):
    """输出信息日志"""
    if show_box:
        print_box(f"[信息] {msg}")
    else:
        print(f"[信息] {msg}")

def log_warn(msg, show_box=False):
    """输出警告日志"""
    if show_box:
        print_box(f"[警告] {msg}")
    else:
        print(f"[警告] {msg}")

def log_error(msg, show_box=False):
    """输出错误日志"""
    if show_box:
        print_box(f"[错误] {msg}")
    else:
        print(f"[错误] {msg}")

def log_debug(msg, show_box=False):
    """输出调试日志"""
    if show_box:
        print_box(f"[调试] {msg}")
    else:
        print(f"[调试] {msg}")

def log_process(msg, show_box=False):
    """输出进度日志"""
    if show_box:
        print_box(f"[进行] {msg}")
    else:
        print(f"[进行] {msg}")

def log_success(msg, show_box=False):
    """输出成功日志"""
    if show_box:
        print_box(f"[成功] {msg}")
    else:
        print(f"[成功] {msg}")

# 通用工具函数
def get_system_type():
    """获取系统类型"""
    system = platform.system().lower()
    if system == "windows":
        return "windows"
    elif system == "darwin":
        return "mac"
    elif system == "linux":
        return "linux"
    else:
        return "unknown"

def check_and_elevate_privileges():
    """检查并提升脚本权限"""
    system_type = get_system_type()
    
    if system_type == "windows":
        import ctypes
        # 检查是否有管理员权限
        if not ctypes.windll.shell32.IsUserAnAdmin():
            log_warn("脚本需要管理员权限才能运行", show_box=True)
            try:
                # 如果没有权限，重新以管理员身份启动
                ctypes.windll.shell32.ShellExecuteW(
                    None, "runas", sys.executable, " ".join(sys.argv), None, 1
                )
                sys.exit(0)
            except Exception as e:
                log_error(f"提升权限失败: {str(e)}", show_box=True)
                sys.exit(1)
    elif system_type in ["mac", "linux"]:
        # 在 Mac/Linux 上检查 sudo 权限
        if os.geteuid() != 0:
            log_warn("脚本需要 sudo 权限才能运行", show_box=True)
            try:
                args = ['sudo', sys.executable] + sys.argv
                os.execvp('sudo', args)
            except Exception as e:
                log_error(f"提升权限失败: {str(e)}", show_box=True)
                sys.exit(1)

# 机器码修改器基类
class CursorIDModifier:
    """Cursor ID修改器基类"""

    def __init__(self):
        """初始化修改器"""
        self.storage_file = self._get_storage_path()
        self.backup_dir = self._get_backup_dir()
        self.should_disable_update = None  # 初始化为None，稍后再设置

    def _should_disable_update(self):
        """检查是否应该禁用自动更新"""
        # 从环境变量获取设置，默认为'N'
        env_setting = os.getenv('MODIFYUPDATE', '').upper()
        if env_setting in ['Y', 'N']:
            print("\n更新设置")
            print("[配置] 使用环境变量配置")
            print(f"• MODIFYUPDATE={env_setting}")
            print("")
            print("是否禁用自动更新：" + ("是" if env_setting == 'Y' else "否"))
            return env_setting == 'Y'
        
        # 如果环境变量设置不正确或未设置，询问用户，默认为N
        print("\n更新询问")
        print("[询问] 是否要禁用自动更新功能？")
        print("")
        print("• 在0.46.x往上版本中，可能不起效果")
        print("• 禁用后您仍然可以手动更新覆盖旧版 Cursor")
        
        # 直接打印提示并等待输入
        print("请选择 (y/n) [默认:n]: ", end="")
        while True:
            choice = input().strip().lower()
            if choice == '':  # 如果用户直接按回车，使用默认值N
                print("\n更新设置")
                print("[选择] 使用默认选项：不禁用自动更新")
                return False
            if choice in ['y', 'n']:
                print("\n更新设置")
                print(f"[选择] {'禁用' if choice == 'y' else '不禁用'}自动更新")
                return choice == 'y'
            print("[错误] 无效的选择，请输入 y 或 n，或直接按回车使用默认值(n)")
            print("请选择 (y/n) [默认:n]: ", end="")

    def disable_auto_update(self):
        """禁用自动更新功能"""
        raise NotImplementedError("子类必须实现此方法")

    def _get_storage_path(self):
        """获取配置文件路径（子类必须实现）"""
        raise NotImplementedError("子类必须实现此方法")

    def _get_backup_dir(self):
        """获取备份目录路径（子类必须实现）"""
        raise NotImplementedError("子类必须实现此方法")

    def check_permissions(self):
        """检查权限（子类必须实现）"""
        raise NotImplementedError("子类必须实现此方法")

    def kill_cursor_process(self):
        """关闭Cursor进程（子类必须实现）"""
        raise NotImplementedError("子类必须实现此方法")

    def backup_system_id(self):
        """备份系统ID（子类必须实现）"""
        raise NotImplementedError("子类必须实现此方法")

    def backup_config(self):
        """备份配置文件"""
        if not os.path.exists(self.storage_file):
            log_warn("配置文件不存在，跳过备份")
            return True
        
        # 创建备份目录
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # 生成备份文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(self.backup_dir, f"storage.json.backup_{timestamp}")
        
        try:
            shutil.copy2(self.storage_file, backup_file)
            print_box(
                "配置备份",
                [
                    (Colors.GREEN, "[信息] 配置文件已备份"),
                    (Colors.WHITE, f"备份文件：{backup_file}")
                ]
            )
            return True
        except Exception as e:
            log_error(f"备份失败: {str(e)}", show_box=True)
            return False

    def generate_random_id(self):
        """生成随机ID（子类可能需要重写）"""
        # 生成随机字节并转为十六进制字符串
        random_bytes = os.urandom(32)  # 32字节 = 64位十六进制
        return ''.join('{:02x}'.format(b) for b in random_bytes)

    def generate_uuid(self):
        """生成UUID（子类可能需要重写）"""
        return str(uuid.uuid4()).lower()

    def generate_auth0_machine_id(self):
        """生成auth0|user_前缀的机器ID"""
        # 将 auth0|user_ 转换为字节数组的十六进制
        prefix = "auth0|user_"
        prefix_hex = ''.join('{:02x}'.format(ord(c)) for c in prefix)
        random_part = self.generate_random_id()
        return prefix_hex + random_part

    def update_config(self):
        """更新配置文件（子类必须实现）"""
        raise NotImplementedError("子类必须实现此方法")

    def modify_system_id(self):
        """修改系统ID（子类必须实现）"""
        raise NotImplementedError("子类必须实现此方法")

    def flush_dns_cache(self):
        """清除DNS缓存（子类必须实现）"""
        raise NotImplementedError("子类必须实现此方法")

    def run(self):
        """运行修改器的主流程"""
        try:
            # 检查权限
            self.check_permissions()
            
            # 关闭Cursor进程
            self.kill_cursor_process()
            
            # 备份配置
            if not self.backup_config():
                return False
            
            # 备份系统ID
            self.backup_system_id()
            
            # 修改系统ID
            if not self.modify_system_id():
                return False
            
            # 更新配置文件
            if not self.update_config():
                return False
            
            log_success("机器码修改完成", show_box=True)
            
            # 清除DNS缓存
            self.flush_dns_cache()
            
            # 机器码修改完成后询问是否禁用自动更新
            self.should_disable_update = self._should_disable_update()
            if self.should_disable_update:
                if not self.disable_auto_update():
                    log_warn("禁用自动更新失败，但主要操作已完成")
            
            return True
            
        except Exception as e:
            log_error(f"执行过程中出现错误: {str(e)}", show_box=True)
            import traceback
            print(traceback.format_exc())
            return False

# Windows系统ID修改器
class WindowsIDModifier(CursorIDModifier):
    """Windows系统的Cursor ID修改器"""

    def _get_storage_path(self):
        """获取Windows系统下的配置文件路径"""
        appdata = os.getenv("APPDATA")
        if appdata is None:
            raise EnvironmentError("APPDATA 环境变量未设置")
        return os.path.join(appdata, "Cursor", "User", "globalStorage", "storage.json")

    def _get_backup_dir(self):
        """获取Windows系统下的备份目录路径"""
        appdata = os.getenv("APPDATA")
        if appdata is None:
            raise EnvironmentError("APPDATA 环境变量未设置")
        return os.path.join(appdata, "Cursor", "User", "globalStorage", "backups")

    def check_permissions(self):
        """检查Windows系统下的权限"""
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            log_error("请以管理员权限运行此脚本", show_box=True)
            return False
        return True

    def kill_cursor_process(self):
        """关闭Windows系统下的Cursor进程"""
        log_info("检查 Cursor 进程...")
        
        try:
            import psutil
            current_pid = os.getpid()  # 获取当前脚本的进程ID
            
            # 定义最大重试次数和等待时间
            max_retries = 5
            wait_time = 1
            
            def is_cursor_editor_process(proc):
                """判断是否为Cursor编辑器进程"""
                try:
                    # 检查进程名称
                    if not proc.name().lower().startswith('cursor'):
                        return False
                    
                    # 检查可执行文件路径
                    exe_path = proc.exe().lower()
                    if not ('\\cursor.exe' in exe_path or '\\cursor\\cursor.exe' in exe_path):
                        return False
                    
                    # 检查命令行参数
                    cmdline = ' '.join(proc.cmdline()).lower()
                    if 'cursor_change_id' in cmdline or 'cursor-auto' in cmdline:
                        return False
                    
                    # 检查是否为当前进程或其子进程
                    if proc.pid == current_pid or proc.ppid() == current_pid:
                        return False
                    
                    return True
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    return False
            
            for attempt in range(max_retries):
                # 查找Cursor编辑器进程
                cursor_processes = [p for p in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']) 
                                 if is_cursor_editor_process(p)]
                
                if not cursor_processes:
                    if attempt == 0:
                        log_info("未发现运行中的 Cursor 编辑器进程")
                    break
                
                # 如果找到了进程
                if attempt == 0:
                    log_warn(f"发现 {len(cursor_processes)} 个 Cursor 编辑器进程正在运行")
                    log_warn("尝试关闭 Cursor 编辑器进程...")
                
                # 尝试终止所有匹配的进程
                for proc in cursor_processes:
                    try:
                        if attempt == max_retries - 1:
                            proc.kill()  # 最后一次尝试时使用强制终止
                        else:
                            proc.terminate()  # 正常终止
                    except:
                        pass
                
                # 等待一段时间
                time.sleep(wait_time)
                
                # 检查进程是否仍然存在
                cursor_processes = [p for p in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']) 
                                 if is_cursor_editor_process(p)]
                
                if not cursor_processes:
                    log_info("Cursor 编辑器进程已成功关闭")
                    break
                
                if attempt < max_retries - 1:
                    log_warn(f"等待进程关闭，尝试 {attempt+1}/{max_retries}...")
                else:
                    log_error("无法关闭 Cursor 编辑器进程，请手动关闭后重试", show_box=True)
                    return False
            
            return True
            
        except Exception as e:
            log_error(f"处理进程时出错: {str(e)}")
            return False

    def get_cursor_version(self):
        """获取Cursor版本"""
        try:
            # 主要检测路径
            package_path = os.path.join(os.getenv("LOCALAPPDATA"), "Programs", "cursor", "resources", "app", "package.json")
            
            if os.path.exists(package_path):
                with open(package_path, 'r', encoding='utf-8') as f:
                    package_json = json.load(f)
                    if 'version' in package_json:
                        version = package_json['version']
                        log_info(f"当前安装的 Cursor 版本: v{version}")
                        return version
            
            # 备用路径检测
            alt_path = os.path.join(os.getenv("LOCALAPPDATA"), "cursor", "resources", "app", "package.json")
            if os.path.exists(alt_path):
                with open(alt_path, 'r', encoding='utf-8') as f:
                    package_json = json.load(f)
                    if 'version' in package_json:
                        version = package_json['version']
                        log_info(f"当前安装的 Cursor 版本: v{version}")
                        return version
            
            log_warn("无法检测到 Cursor 版本")
            return None
        except Exception as e:
            log_error(f"获取 Cursor 版本失败: {str(e)}")
            return None

    def backup_system_id(self):
        """备份Windows系统ID"""
        try:
            import winreg
            
            # 创建备份目录（如果不存在）
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 打开注册表键
            registry_path = r"SOFTWARE\Microsoft\Cryptography"
            reg_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, registry_path, 0, winreg.KEY_READ)
            
            try:
                # 读取MachineGuid值
                machine_guid, _ = winreg.QueryValueEx(reg_key, "MachineGuid")
                
                # 创建备份文件
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = os.path.join(self.backup_dir, f"MachineGuid_{timestamp}.reg")
                
                # 使用reg.exe导出注册表项
                subprocess.run([
                    "reg.exe", "export", 
                    r"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography", 
                    backup_file
                ], check=False, capture_output=True)
                
                # 显示备份信息
                print_box(
                    "系统备份",
                    [
                        (Colors.GREEN, "[信息] 注册表项已备份"),
                        (Colors.WHITE, f"备份文件：{backup_file}"),
                        (Colors.WHITE, ""),
                        (Colors.WHITE, "当前注册表值："),
                        (Colors.WHITE, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography"),
                        (Colors.WHITE, f"    MachineGuid    REG_SZ    {machine_guid}")
                    ]
                )
                
                return True
            finally:
                winreg.CloseKey(reg_key)
        except Exception as e:
            log_error(f"备份系统ID失败: {str(e)}")
            return False

    def modify_system_id(self):
        """修改Windows系统ID"""
        try:
            # 生成新的GUID
            new_guid = self.generate_uuid()
            
            # 使用reg.exe修改注册表
            import subprocess
            subprocess.run(
                ['reg', 'add', 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography', '/v', 'MachineGuid', '/t', 'REG_SZ', '/d', new_guid, '/f'],
                check=True,
                capture_output=True
            )
            
            print_box(
                f"{Colors.GREEN}[信息]{Colors.NC} 注册表更新成功",
                [
                    "",
                    f"HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography",
                    f"    MachineGuid    REG_SZ    {new_guid}"
                ]
            )
            
            return True
        except Exception as e:
            log_error(f"修改系统ID失败: {str(e)}", show_box=True)
            return False
            
    def update_config(self):
        """更新Cursor配置文件"""
        try:
            storage_path = self._get_storage_path()
            
            if not os.path.exists(storage_path):
                log_error(f"配置文件不存在: {storage_path}", show_box=True)
                return False
            
            # 读取当前配置
            with open(storage_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 生成新的ID
            new_machine_id = self.generate_auth0_machine_id()
            new_mac_id = self.generate_uuid()
            new_device_id = self.generate_uuid()
            new_sqm_id = "{" + self.generate_uuid().upper() + "}"
            
            # 更新配置
            config['telemetry.machineId'] = new_machine_id
            config['telemetry.macMachineId'] = new_mac_id
            config['telemetry.devDeviceId'] = new_device_id
            config['telemetry.sqmId'] = new_sqm_id
            
            # 获取文件当前权限
            current_mode = os.stat(storage_path).st_mode
            
            # 设置文件为可写
            os.chmod(storage_path, 0o644)
            
            # 写入新配置
            with open(storage_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)
            
            # 恢复原始权限
            os.chmod(storage_path, current_mode)
            
            print_box(
                "配置文件更新",
                [
                    (Colors.GREEN, "[信息] 配置文件更新成功"),
                    "",
                    f"• machineId: {new_machine_id}",
                    f"• macMachineId: {new_mac_id}",
                    f"• devDeviceId: {new_device_id}",
                    f"• sqmId: {new_sqm_id}"
                ]
            )
            
            return True
        except Exception as e:
            log_error(f"更新配置文件失败: {str(e)}", show_box=True)
            return False

    def disable_auto_update(self):
        """禁用Windows系统下的自动更新"""
        try:
            # 获取更新程序路径
            updater_path = os.path.join(os.getenv('LOCALAPPDATA', ''), 'cursor-updater')
            
            # 检查cursor-updater是否存在
            if os.path.exists(updater_path):
                # 如果是文件，说明已经创建了阻止更新
                if os.path.isfile(updater_path):
                    print_box(
                        "更新控制",
                        [
                            (Colors.GREEN, "[信息] 已创建阻止更新文件，无需再次阻止")
                        ]
                    )
                    return True
                # 如果是目录，先备份再删除
                else:
                    try:
                        # 创建带时间戳的zip文件名
                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                        backup_zip = f"{updater_path}_{timestamp}.zip"
                        
                        # 使用shutil.make_archive创建zip备份
                        shutil.make_archive(
                            backup_zip[:-4],  # 去掉.zip后缀，因为make_archive会自动添加
                            'zip',
                            updater_path
                        )
                        log_info(f"已备份更新程序到: {backup_zip}")
                        
                        # 删除目录
                        shutil.rmtree(updater_path)
                        log_info("成功删除 cursor-updater 目录")
                    except Exception as e:
                        log_error(f"备份或删除 cursor-updater 目录失败: {str(e)}")
                        self._show_manual_guide(updater_path)
                        return False
            
            # 创建阻止文件
            try:
                # 创建空文件
                with open(updater_path, 'w') as f:
                    pass
                log_info("成功创建阻止文件")
            except Exception as e:
                log_error(f"创建阻止文件失败: {str(e)}")
                self._show_manual_guide(updater_path)
                return False
            
            # 设置文件权限
            try:
                # 设置只读属性
                os.chmod(updater_path, 0o444)  # r--r--r--
                log_info("成功设置文件权限")
            except Exception as e:
                log_error(f"设置文件权限失败: {str(e)}")
                self._show_manual_guide(updater_path)
                return False
            
            # 验证设置
            try:
                if not os.path.exists(updater_path) or os.access(updater_path, os.W_OK):
                    log_error("验证失败：文件权限设置可能未生效")
                    self._show_manual_guide(updater_path)
                    return False
            except Exception as e:
                log_error(f"验证设置失败: {str(e)}")
                self._show_manual_guide(updater_path)
                return False
            
            print_box(
                "更新控制",
                [
                    (Colors.GREEN, "[成功] 已禁用自动更新"),
                    (Colors.WHITE, f"• 已创建阻止文件: {updater_path}"),
                    (Colors.WHITE, "• 已设置为只读"),
                    (Colors.WHITE, f"• 备份文件位置: {backup_zip if 'backup_zip' in locals() else '无需备份，因为这个更新文件夹是空的'}")
                ]
            )
            return True
                
        except Exception as e:
            log_error(f"禁用自动更新失败: {str(e)}")
            self._show_manual_guide(updater_path)
            return False

    def _show_manual_guide(self, updater_path):
        """显示手动禁用更新的指南"""
        print_box(
            "手动设置指南",
            [
                (Colors.YELLOW, "[警告] 自动设置失败，请尝试手动操作："),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "手动禁用更新步骤："),
                (Colors.WHITE, "1. 打开终端"),
                (Colors.WHITE, "2. 复制粘贴以下命令："),
                (Colors.WHITE, ""),
                (Colors.BLUE, "命令1 - 删除现有目录（如果存在）："),
                (Colors.WHITE, f'rm -rf "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.BLUE, "命令2 - 创建阻止文件："),
                (Colors.WHITE, f'touch "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.BLUE, "命令3 - 设置只读属性："),
                (Colors.WHITE, f'chmod 444 "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "如果上述命令提示权限不足，请使用 sudo："),
                (Colors.WHITE, f'sudo rm -rf "{updater_path}" && sudo touch "{updater_path}" && sudo chmod 444 "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "如果要添加额外保护（推荐），请执行："),
                (Colors.WHITE, f'sudo chattr +i "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "验证方法："),
                (Colors.WHITE, f'1. 运行命令：ls -l "{updater_path}"'),
                (Colors.WHITE, "2. 确认文件权限为 r--r--r--"),
                (Colors.WHITE, f'3. 运行命令：lsattr "{updater_path}"'),
                (Colors.WHITE, "4. 确认有 'i' 属性（如果执行了 chattr 命令）"),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "[提示] 完成后请重启 Cursor")
            ]
        )

    def flush_dns_cache(self):
        """清除DNS缓存（子类必须实现）"""
        # 在Windows上，使用ipconfig命令清除DNS缓存
        try:
            log_info("正在清除DNS缓存...")
            subprocess.run(["ipconfig", "/flushdns"], check=True, capture_output=True)
            log_success("DNS缓存已清除")
            return True
        except Exception as e:
            log_warn(f"清除DNS缓存失败: {str(e)}")
            return False

    def run(self):
        """运行Windows修改器的主流程"""
        try:
            # 检查权限
            self.check_permissions()
            
            # 关闭Cursor进程
            self.kill_cursor_process()
            
            # 备份配置
            if not self.backup_config():
                return False
            
            # 备份系统ID
            self.backup_system_id()
            
            # 修改系统ID
            if not self.modify_system_id():
                return False
            
            # 更新配置文件
            if not self.update_config():
                return False
            
            log_success("机器码修改完成", show_box=True)
            
            # 清除DNS缓存
            self.flush_dns_cache()
            
            # 机器码修改完成后询问是否禁用自动更新
            self.should_disable_update = self._should_disable_update()
            if self.should_disable_update:
                if not self.disable_auto_update():
                    log_warn("禁用自动更新失败，但主要操作已完成")
            
            return True
            
        except Exception as e:
            log_error(f"执行过程中出现错误: {str(e)}", show_box=True)
            import traceback
            print(traceback.format_exc())
            return False

# Linux系统ID修改器
class LinuxIDModifier(CursorIDModifier):
    """Linux系统的Cursor ID修改器"""

    def _get_storage_path(self):
        """获取Linux系统下的配置文件路径"""
        return os.path.expanduser("~/.config/Cursor/User/globalStorage/storage.json")

    def _get_backup_dir(self):
        """获取Linux系统下的备份目录路径"""
        return os.path.expanduser("~/.config/Cursor/User/globalStorage/backups")

    def check_permissions(self):
        """检查Linux系统下的权限"""
        if os.geteuid() != 0:
            log_error("请使用 sudo 运行此脚本", show_box=True)
            return False
        return True

    def kill_cursor_process(self):
        """关闭Linux系统下的Cursor进程"""
        log_info("检查 Cursor 进程...")
        
        try:
            # 定义最大重试次数和等待时间
            max_attempts = 5
            wait_time = 1
            
            # 获取进程详细信息的函数
            def get_process_details(process_name):
                """获取进程详细信息"""
                log_debug(f"正在获取 {process_name} 进程详细信息：")
                try:
                    process_info = subprocess.check_output(
                        ["ps", "aux"], universal_newlines=True
                    )
                    for line in process_info.split('\n'):
                        if process_name.lower() in line.lower() and 'grep' not in line.lower():
                            print(line)
                except:
                    pass
            
            for attempt in range(max_attempts):
                # 使用pgrep查找Cursor进程
                try:
                    cursor_pids_output = subprocess.check_output(
                        ["pgrep", "-i", "cursor"], universal_newlines=True, stderr=subprocess.DEVNULL
                    ).strip()
                    cursor_pids = cursor_pids_output.split('\n') if cursor_pids_output else []
                except subprocess.CalledProcessError:
                    # 如果没有找到进程，pgrep会返回非零退出码
                    cursor_pids = []
                
                if not cursor_pids:
                    log_info("未发现运行中的 Cursor 进程")
                    return True
                
                if attempt == 0:
                    log_warn("发现 Cursor 进程正在运行")
                    get_process_details("cursor")
                    log_warn("尝试关闭 Cursor 进程...")
                
                # 尝试终止进程
                if attempt == max_attempts - 1:
                    # 最后一次尝试使用SIGKILL
                    log_warn("尝试强制终止进程...")
                    for pid in cursor_pids:
                        if pid.strip():
                            try:
                                subprocess.run(["kill", "-9", pid.strip()], stderr=subprocess.DEVNULL)
                            except:
                                pass
                else:
                    # 使用正常的SIGTERM
                    for pid in cursor_pids:
                        if pid.strip():
                            try:
                                subprocess.run(["kill", pid.strip()], stderr=subprocess.DEVNULL)
                            except:
                                pass
                
                # 等待进程退出
                time.sleep(wait_time)
                
                # 检查进程是否还在运行
                try:
                    subprocess.check_output(["pgrep", "-i", "cursor"], stderr=subprocess.DEVNULL)
                    # 如果没有抛出异常，表示进程仍在运行
                    log_warn(f"等待进程关闭，尝试 {attempt+1}/{max_attempts}...")
                except subprocess.CalledProcessError:
                    # 进程已经退出
                    log_info("Cursor 进程已成功关闭")
                    return True
            
            # 最后检查
            try:
                subprocess.check_output(["pgrep", "-i", "cursor"], stderr=subprocess.DEVNULL)
                log_error(f"在 {max_attempts} 次尝试后仍无法关闭 Cursor 进程", show_box=True)
                get_process_details("cursor")
                log_error("请手动关闭进程后重试")
                return False
            except subprocess.CalledProcessError:
                # 进程已经退出
                log_info("Cursor 进程已成功关闭")
                return True
                
        except Exception as e:
            log_error(f"处理进程时出错: {str(e)}")
            return False

    def backup_system_id(self):
        """备份Linux系统ID"""
        log_info("正在备份系统 ID...")
        
        try:
            # 创建备份目录
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            system_id_file = os.path.join(self.backup_dir, f"system_id.backup_{timestamp}")
            
            # 创建备份文件并写入信息
            with open(system_id_file, 'w', encoding='utf-8') as f:
                f.write(f"# Original System ID Backup - {datetime.datetime.now()}\n")
                f.write("## Machine ID:\n")
                
                # 读取machine-id
                try:
                    with open("/etc/machine-id", 'r') as mid_file:
                        machine_id = mid_file.read().strip()
                        f.write(f"{machine_id}\n")
                except:
                    f.write("N/A\n")
                
                f.write("\n## DMI System UUID:\n")
                # 尝试读取DMI系统UUID
                try:
                    dmi_uuid = subprocess.check_output(
                        ["dmidecode", "-s", "system-uuid"], 
                        universal_newlines=True
                    ).strip()
                    f.write(f"{dmi_uuid}\n")
                except:
                    f.write("N/A\n")
            
            # 设置文件权限
            try:
                os.chmod(system_id_file, 0o444)  # 设为只读
                # 获取当前用户
                current_user = os.getenv('SUDO_USER') if os.getenv('SUDO_USER') else os.getenv('USER')
                if current_user:
                    subprocess.run(["chown", current_user, system_id_file], check=False)
            except:
                pass
            
            log_info(f"系统 ID 已备份到: {system_id_file}")
            return True
            
        except Exception as e:
            log_error(f"备份系统 ID 失败: {str(e)}")
            return False

    def modify_system_id(self):
        """修改Linux系统ID"""
        log_info("正在修改系统 ID...")
        
        try:
            # 生成新的machine-id (32个字符的十六进制)
            new_machine_id = self.generate_random_id()[:32]
            
            # 备份并修改/etc/machine-id
            if os.path.exists("/etc/machine-id"):
                # 备份
                shutil.copy2("/etc/machine-id", "/etc/machine-id.backup")
                
                # 写入新ID
                with open("/etc/machine-id", 'w') as f:
                    f.write(new_machine_id)
                
                log_info(f"已更新 /etc/machine-id: {new_machine_id}")
            else:
                log_warn("未找到 /etc/machine-id，跳过修改")
            
            # 备份并修改/var/lib/dbus/machine-id (如果存在)
            if os.path.exists("/var/lib/dbus/machine-id"):
                # 备份
                shutil.copy2("/var/lib/dbus/machine-id", "/var/lib/dbus/machine-id.backup")
                
                # 写入新ID
                with open("/var/lib/dbus/machine-id", 'w') as f:
                    f.write(new_machine_id)
                
                log_info(f"已更新 /var/lib/dbus/machine-id: {new_machine_id}")
            else:
                log_warn("未找到 /var/lib/dbus/machine-id，跳过修改")
            
            return True
            
        except Exception as e:
            log_error(f"修改系统 ID 失败: {str(e)}")
            return False

    def update_config(self):
        """更新Linux系统下的配置文件"""
        if not os.path.exists(self.storage_file):
            log_warn(f"配置文件不存在: {self.storage_file}")
            return True
        
        try:
            # 检查文件是否有不可变属性
            try:
                lsattr_output = subprocess.check_output(
                    ["lsattr", self.storage_file], 
                    universal_newlines=True, 
                    stderr=subprocess.DEVNULL
                )
                if 'i' in lsattr_output[:5]:  # 检查输出的前5个字符是否包含'i'
                    log_debug("移除文件不可变属性...")
                    subprocess.run(["sudo", "chattr", "-i", self.storage_file], check=True)
            except:
                pass
            
            # 确保文件可写
            os.chmod(self.storage_file, 0o644)
            
            # 读取原始内容
            with open(self.storage_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 解析JSON
            config = json.loads(original_content)
            
            # 生成新的ID
            auth0_machine_id = self.generate_auth0_machine_id()
            mac_machine_id = self.generate_uuid()
            device_id = self.generate_uuid()
            sqm_id = "{" + str(uuid.uuid4()).upper() + "}"
            
            # 更新JSON对象
            config['telemetry.machineId'] = auth0_machine_id
            config['telemetry.macMachineId'] = mac_machine_id
            config['telemetry.devDeviceId'] = device_id
            config['telemetry.sqmId'] = sqm_id
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp:
                # 写入更新后的JSON
                json.dump(config, temp, indent=4)
                temp_name = temp.name
            
            # 复制临时文件到原始位置
            shutil.copy2(temp_name, self.storage_file)
            
            # 删除临时文件
            os.unlink(temp_name)
            
            # 设置权限
            os.chmod(self.storage_file, 0o444)  # 设为只读
            
            log_info("配置文件更新成功")
            
            # 显示更新后的ID
            log_debug(f"machineId: {auth0_machine_id}")
            log_debug(f"macMachineId: {mac_machine_id}")
            log_debug(f"devDeviceId: {device_id}")
            log_debug(f"sqmId: {sqm_id}")
            
            return True
        except Exception as e:
            log_error(f"更新配置文件失败: {str(e)}")
            # 如果出错，尝试恢复原始内容
            try:
                if 'original_content' in locals():
                    with open(self.storage_file, 'w', encoding='utf-8') as f:
                        f.write(original_content)
                    log_warn("已恢复原始配置文件")
            except:
                pass
            return False

    def disable_auto_update(self):
        """禁用Linux系统下的自动更新"""
        try:
            # 获取更新程序路径
            updater_path = os.path.join(os.getenv('HOME', ''), '.config/cursor-updater')
            
            # 检查cursor-updater是否存在
            if os.path.exists(updater_path):
                # 如果是文件，说明已经创建了阻止更新
                if os.path.isfile(updater_path):
                    print_box(
                        "更新控制",
                        [
                            (Colors.GREEN, "[信息] 已创建阻止更新文件，无需再次阻止")
                        ]
                    )
                    return True
                # 如果是目录，先备份再删除
                else:
                    try:
                        # 创建带时间戳的zip文件名
                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                        backup_zip = f"{updater_path}_{timestamp}.zip"
                        
                        # 使用shutil.make_archive创建zip备份
                        shutil.make_archive(
                            backup_zip[:-4],  # 去掉.zip后缀，因为make_archive会自动添加
                            'zip',
                            updater_path
                        )
                        log_info(f"已备份更新程序到: {backup_zip}")
                        
                        # 删除目录
                        shutil.rmtree(updater_path)
                        log_info("成功删除 cursor-updater 目录")
                    except Exception as e:
                        log_error(f"备份或删除 cursor-updater 目录失败: {str(e)}")
                        self._show_manual_guide(updater_path)
                        return False
            
            # 创建阻止文件
            try:
                # 创建空文件
                with open(updater_path, 'w') as f:
                    pass
                log_info("成功创建阻止文件")
            except Exception as e:
                log_error(f"创建阻止文件失败: {str(e)}")
                self._show_manual_guide(updater_path)
                return False
            
            # 设置文件权限
            try:
                # 设置只读属性
                os.chmod(updater_path, 0o444)  # r--r--r--
                
                # 尝试设置不可修改属性（如果系统支持）
                try:
                    subprocess.run(['chattr', '+i', updater_path], check=True, capture_output=True)
                    log_info("成功设置文件权限和不可修改属性")
                except Exception:
                    log_warn("chattr 设置失败，仅设置了只读权限")
            except Exception as e:
                log_error(f"设置文件权限失败: {str(e)}")
                self._show_manual_guide(updater_path)
                return False
            
            # 验证设置
            try:
                if not os.path.exists(updater_path) or os.access(updater_path, os.W_OK):
                    log_error("验证失败：文件权限设置可能未生效")
                    self._show_manual_guide(updater_path)
                    return False
            except Exception as e:
                log_error(f"验证设置失败: {str(e)}")
                self._show_manual_guide(updater_path)
                return False
            
            print_box(
                "更新控制",
                [
                    (Colors.GREEN, "[成功] 已禁用自动更新"),
                    (Colors.WHITE, f"• 已创建阻止文件: {updater_path}"),
                    (Colors.WHITE, "• 已设置为只读"),
                    (Colors.WHITE, f"• 备份文件位置: {backup_zip if 'backup_zip' in locals() else '无需备份，因为这个更新文件夹是空的'}")
                ]
            )
            return True
                
        except Exception as e:
            log_error(f"禁用自动更新失败: {str(e)}")
            self._show_manual_guide(updater_path)
            return False

    def _show_manual_guide(self, updater_path):
        """显示手动禁用更新的指南"""
        print_box(
            "手动设置指南",
            [
                (Colors.YELLOW, "[警告] 自动设置失败，请尝试手动操作："),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "手动禁用更新步骤："),
                (Colors.WHITE, "1. 打开终端"),
                (Colors.WHITE, "2. 复制粘贴以下命令："),
                (Colors.WHITE, ""),
                (Colors.BLUE, "命令1 - 删除现有目录（如果存在）："),
                (Colors.WHITE, f'rm -rf "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.BLUE, "命令2 - 创建阻止文件："),
                (Colors.WHITE, f'touch "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.BLUE, "命令3 - 设置只读属性："),
                (Colors.WHITE, f'chmod 444 "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "如果上述命令提示权限不足，请使用 sudo："),
                (Colors.WHITE, f'sudo rm -rf "{updater_path}" && sudo touch "{updater_path}" && sudo chmod 444 "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "如果要添加额外保护（推荐），请执行："),
                (Colors.WHITE, f'sudo chattr +i "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "验证方法："),
                (Colors.WHITE, f'1. 运行命令：ls -l "{updater_path}"'),
                (Colors.WHITE, "2. 确认文件权限为 r--r--r--"),
                (Colors.WHITE, f'3. 运行命令：lsattr "{updater_path}"'),
                (Colors.WHITE, "4. 确认有 'i' 属性（如果执行了 chattr 命令）"),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "[提示] 完成后请重启 Cursor")
            ]
        )

    def flush_dns_cache(self):
        """清除DNS缓存（子类必须实现）"""
        # 在Linux上，不同发行版清除DNS缓存的方法不同
        try:
            log_info("正在清除DNS缓存...")
            # 尝试多种清除DNS缓存的方法
            methods = [
                # 对于使用systemd-resolved的系统
                ["systemd-resolve", "--flush-caches"],
                # 对于使用nscd的系统
                ["service", "nscd", "restart"],
                # 对于使用dnsmasq的系统
                ["service", "dnsmasq", "restart"],
                # 对于使用NetworkManager的系统
                ["service", "NetworkManager", "restart"]
            ]
            
            success = False
            for cmd in methods:
                try:
                    # 不显示错误输出，不检查返回码
                    result = subprocess.run(cmd, stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
                    if result.returncode == 0:
                        log_info(f"成功执行: {' '.join(cmd)}")
                        success = True
                except:
                    continue
            
            if success:
                log_success("DNS缓存已清除")
                return True
            else:
                log_warn("未能找到适用的DNS缓存清除方法")
                return False
        except Exception as e:
            log_warn(f"清除DNS缓存失败: {str(e)}")
            return False

    def run(self):
        """运行Linux修改器的主流程"""
        try:
            # 检查权限
            self.check_permissions()
            
            # 关闭Cursor进程
            self.kill_cursor_process()
            
            # 备份配置
            if not self.backup_config():
                return False
            
            # 备份系统ID
            self.backup_system_id()
            
            # 修改系统ID
            if not self.modify_system_id():
                return False
            
            # 更新配置文件
            if not self.update_config():
                return False
            
            log_success("机器码修改完成", show_box=True)
            
            # 清除DNS缓存
            self.flush_dns_cache()
            
            # 机器码修改完成后询问是否禁用自动更新
            self.should_disable_update = self._should_disable_update()
            if self.should_disable_update:
                if not self.disable_auto_update():
                    log_warn("禁用自动更新失败，但主要操作已完成")
            
            return True
            
        except Exception as e:
            log_error(f"执行过程中出现错误: {str(e)}", show_box=True)
            import traceback
            print(traceback.format_exc())
            return False

# Mac系统ID修改器
class MacIDModifier(CursorIDModifier):
    """Mac系统的Cursor ID修改器"""

    def __init__(self):
        """初始化Mac修改器，定义特定路径"""
        super().__init__()
        self.cursor_app_path = "/Applications/Cursor.app"

    def _get_storage_path(self):
        """获取Mac系统下的配置文件路径"""
        return os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")

    def _get_backup_dir(self):
        """获取Mac系统下的备份目录路径"""
        return os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/backups")

    def check_permissions(self):
        """检查Mac系统下的权限"""
        if os.geteuid() != 0:
            log_error("请使用 sudo 运行此脚本", show_box=True)
            return False
        return True

    def kill_cursor_process(self):
        """关闭Mac系统下的Cursor进程"""
        log_info("检查 Cursor 进程...")
        
        try:
            # 定义最大重试次数和等待时间
            max_attempts = 5
            wait_time = 1
            
            # 获取进程详细信息的函数
            def get_process_details(process_name):
                """获取进程详细信息"""
                log_debug(f"正在获取 {process_name} 进程详细信息：")
                try:
                    process_info = subprocess.check_output(
                        ["ps", "aux"], universal_newlines=True
                    )
                    for line in process_info.split('\n'):
                        if "/Applications/Cursor.app" in line and 'grep' not in line:
                            parts = line.split()
                            if len(parts) > 1:
                                cursor_pids.append(parts[1])  # 获取PID
                except:
                    pass
            
            for attempt in range(max_attempts):
                # 使用ps查找Cursor进程
                cursor_pids = []
                try:
                    ps_output = subprocess.check_output(
                        ["ps", "aux"], universal_newlines=True
                    )
                    for line in ps_output.split('\n'):
                        if "/Applications/Cursor.app" in line and 'grep' not in line:
                            parts = line.split()
                            if len(parts) > 1:
                                cursor_pids.append(parts[1])  # 获取PID
                except:
                    pass
                
                if not cursor_pids:
                    log_info("未发现运行中的 Cursor 进程")
                    return True
                
                if attempt == 0:
                    log_warn("发现 Cursor 进程正在运行")
                    get_process_details("Cursor")
                    log_warn("尝试关闭 Cursor 进程...")
                
                # 尝试终止进程
                if attempt == max_attempts - 1:
                    # 最后一次尝试使用SIGKILL
                    log_warn("尝试强制终止进程...")
                    for pid in cursor_pids:
                        if pid.strip():
                            try:
                                subprocess.run(["kill", "-9", pid], stderr=subprocess.DEVNULL)
                            except:
                                pass
                else:
                    # 使用正常的SIGTERM
                    for pid in cursor_pids:
                        try:
                            subprocess.run(["kill", pid], stderr=subprocess.DEVNULL)
                        except:
                            pass
                
                # 等待进程退出
                time.sleep(wait_time)
                
                # 检查进程是否还在运行
                still_running = False
                try:
                    ps_output = subprocess.check_output(
                        ["ps", "aux"], universal_newlines=True
                    )
                    for line in ps_output.split('\n'):
                        if "/Applications/Cursor.app" in line and 'grep' not in line:
                            still_running = True
                            break
                except:
                    pass
                
                if not still_running:
                    log_info("Cursor 进程已成功关闭")
                    return True
                
                log_warn(f"等待进程关闭，尝试 {attempt+1}/{max_attempts}...")
            
            # 最后检查
            try:
                ps_output = subprocess.check_output(
                    ["ps", "aux"], universal_newlines=True
                )
                for line in ps_output.split('\n'):
                    if "/Applications/Cursor.app" in line and 'grep' not in line:
                        log_error(f"在 {max_attempts} 次尝试后仍无法关闭 Cursor 进程", show_box=True)
                        get_process_details("Cursor")
                        log_error("请手动关闭进程后重试")
                        return False
            except:
                pass
            
            return True
                
        except Exception as e:
            log_error(f"处理进程时出错: {str(e)}")
            return False

    def backup_system_id(self):
        """备份Mac系统ID"""
        log_info("正在备份系统 ID...")
        
        try:
            # 创建备份目录
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            system_id_file = os.path.join(self.backup_dir, f"system_id.backup_{timestamp}")
            
            # 获取IOPlatformExpertDevice信息
            with open(system_id_file, 'w', encoding='utf-8') as f:
                f.write("# Original System ID Backup\n")
                f.write("## IOPlatformExpertDevice Info:\n")
                
                try:
                    ioreg_output = subprocess.check_output(
                        ["ioreg", "-rd1", "-c", "IOPlatformExpertDevice"],
                        universal_newlines=True
                    )
                    f.write(ioreg_output)
                except:
                    f.write("Failed to get IOPlatformExpertDevice info\n")
            
            # 设置文件权限
            try:
                os.chmod(system_id_file, 0o444)  # 设为只读
                # 获取当前用户
                current_user = os.getenv('SUDO_USER') if os.getenv('SUDO_USER') else os.getenv('USER')
                if current_user:
                    subprocess.run(["chown", current_user, system_id_file], check=False)
            except:
                pass
            
            log_info(f"系统 ID 已备份到: {system_id_file}", show_box=True)
            return True
            
        except Exception as e:
            log_error(f"备份系统 ID 失败: {str(e)}", show_box=True)
            return False

    def modify_system_id(self):
        """修改Mac系统ID"""
        log_info("正在修改系统 ID...")
        
        try:
            # 生成新的系统UUID
            new_system_uuid = str(uuid.uuid4())
            
            # 使用nvram更新SystemUUID
            subprocess.run(["nvram", f"SystemUUID={new_system_uuid}"], check=True)
            log_info(f"系统 UUID 已更新为: {new_system_uuid}", show_box=True)
            log_warn("请重启系统以使更改生效", show_box=True)
            
            return True
            
        except Exception as e:
            log_error(f"修改系统 ID 失败: {str(e)}", show_box=True)
            return False

    def update_config(self):
        """更新Cursor配置文件中的机器码"""
        try:
            # 检查配置文件是否存在
            if not os.path.exists(self.storage_file):
                log_error(f"配置文件不存在: {self.storage_file}", show_box=True)
                return False
            
            # 备份原始配置文件内容
            with open(self.storage_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 解析JSON
            config = json.loads(original_content)
            
            # 设置文件可写
            os.chmod(self.storage_file, 0o644)
            
            # 生成新的ID
            # 生成auth0|user_前缀的十六进制
            prefix_hex = ''.join('{:02x}'.format(ord(c)) for c in "auth0|user_")
            random_part = self.generate_random_id()
            auth0_machine_id = prefix_hex + random_part
            
            # 修改这里: 使用UUID格式生成macMachineId，而不是使用generate_random_id
            mac_machine_id = self.generate_uuid()  # 修改为使用UUID格式
            device_id = self.generate_uuid()
            sqm_id = "{" + str(uuid.uuid4()).upper() + "}"
            
            # 更新JSON对象
            config['telemetry.machineId'] = auth0_machine_id
            config['telemetry.macMachineId'] = mac_machine_id
            config['telemetry.devDeviceId'] = device_id
            config['telemetry.sqmId'] = sqm_id
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp:
                # 写入更新后的JSON
                json.dump(config, temp, indent=4)
                temp_name = temp.name
            
            # 复制临时文件到原始位置
            shutil.copy2(temp_name, self.storage_file)
            
            # 删除临时文件
            os.unlink(temp_name)
            
            # 设置权限
            os.chmod(self.storage_file, 0o444)  # 设为只读
            
            log_info("配置文件更新成功", show_box=True)
            
            # 显示更新后的ID
            log_debug(f"machineId: {auth0_machine_id}")
            log_debug(f"macMachineId: {mac_machine_id}")
            log_debug(f"devDeviceId: {device_id}")
            log_debug(f"sqmId: {sqm_id}")
            
            # 继续修改应用程序文件
            self.modify_cursor_app_files()
            
            return True
        except Exception as e:
            log_error(f"更新配置文件失败: {str(e)}", show_box=True)
            # 如果出错，尝试恢复原始内容
            try:
                if 'original_content' in locals():
                    with open(self.storage_file, 'w', encoding='utf-8') as f:
                        f.write(original_content)
                    log_warn("已恢复原始配置文件", show_box=True)
            except:
                pass
            return False

    def modify_cursor_app_files(self):
        """修改Mac系统下的Cursor应用程序文件"""
        log_info("正在安全修改 Cursor 主程序文件...", show_box=True)
        log_info("详细日志将记录到: /tmp/cursor_mac_id_modifier.log")
        
        # 先清理之前的修改
        self.clean_cursor_app()
        
        # 验证应用是否存在
        if not os.path.isdir(self.cursor_app_path):
            log_error(f"未找到 Cursor.app，请确认安装路径: {self.cursor_app_path}", show_box=True)
            return False
        
        # 定义目标文件
        target_files = [
            os.path.join(self.cursor_app_path, "Contents/Resources/app/out/main.js"),
            os.path.join(self.cursor_app_path, "Contents/Resources/app/out/vs/code/node/cliProcessMain.js")
        ]
        
        # 检查文件是否存在并且是否已修改
        need_modification = False
        missing_files = False
        
        log_debug("检查目标文件...")
        for file_path in target_files:
            if not os.path.isfile(file_path):
                log_warn(f"文件不存在: {os.path.relpath(file_path, self.cursor_app_path)}", show_box=True)
                missing_files = True
                continue
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if "return crypto.randomUUID()" not in content:
                    log_info(f"文件需要修改: {os.path.relpath(file_path, self.cursor_app_path)}")
                    need_modification = True
                    break
                else:
                    log_info(f"文件已修改: {os.path.relpath(file_path, self.cursor_app_path)}")
        
        # 如果所有文件都已修改或不存在，则退出
        if missing_files:
            log_error("部分目标文件不存在，请确认 Cursor 安装是否完整", show_box=True)
            return False
        
        if not need_modification:
            log_info("所有目标文件已经被修改过，无需重复操作", show_box=True)
            return True
        
        # 创建临时工作目录
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_dir = f"/tmp/cursor_reset_{timestamp}"
        temp_app = os.path.join(temp_dir, "Cursor.app")
        backup_app = f"/tmp/Cursor.app.backup_{timestamp}"
        
        log_debug(f"创建临时目录: {temp_dir}")
        
        # 清理可能存在的旧临时目录
        if os.path.exists(temp_dir):
            log_info("清理已存在的临时目录...")
            shutil.rmtree(temp_dir)
        
        # 创建新的临时目录
        try:
            os.makedirs(temp_dir)
        except Exception as e:
            log_error(f"无法创建临时目录: {temp_dir}: {str(e)}")
            return False
        
        try:
            # 备份原应用
            log_info("备份原应用...")
            try:
                subprocess.run([
                    "cp", "-R", self.cursor_app_path, backup_app
                ], check=True)
            except Exception as e:
                log_error(f"无法创建应用备份: {str(e)}")
                shutil.rmtree(temp_dir)
                return False
            
            # 复制应用到临时目录
            log_info("创建临时工作副本...")
            try:
                subprocess.run([
                    "cp", "-R", self.cursor_app_path, temp_dir
                ], check=True)
            except Exception as e:
                log_error(f"无法复制应用到临时目录: {str(e)}")
                shutil.rmtree(temp_dir)
                os.remove(backup_app)
                return False
            
            # 确保临时目录的权限正确
            subprocess.run([
                "chown", "-R", f"{os.getenv('SUDO_USER')}:staff", temp_dir
            ], check=True)
            subprocess.run([
                "chmod", "-R", "755", temp_dir
            ], check=True)
            
            # 移除签名
            log_info("移除应用签名...")
            try:
                subprocess.run([
                    "codesign", "--remove-signature", temp_app
                ], stderr=subprocess.PIPE, stdout=subprocess.PIPE)
            except:
                log_warn("移除应用签名失败")
            
            # 移除所有相关组件的签名
            components = [
                os.path.join(temp_app, "Contents/Frameworks/Cursor Helper.app"),
                os.path.join(temp_app, "Contents/Frameworks/Cursor Helper (GPU).app"),
                os.path.join(temp_app, "Contents/Frameworks/Cursor Helper (Plugin).app"),
                os.path.join(temp_app, "Contents/Frameworks/Cursor Helper (Renderer).app")
            ]
            
            for component in components:
                if os.path.exists(component):
                    log_info(f"正在移除签名: {os.path.basename(component)}")
                    try:
                        subprocess.run([
                            "codesign", "--remove-signature", component
                        ], stderr=subprocess.PIPE, stdout=subprocess.PIPE)
                    except:
                        pass
            
            # 修改目标文件
            modified_count = 0
            
            for file_path in target_files:
                temp_file_path = os.path.join(temp_dir, os.path.relpath(file_path, "/Applications"))
                
                if not os.path.exists(temp_file_path):
                    log_warn(f"临时目录中未找到文件: {os.path.relpath(file_path, '/Applications')}")
                    continue
                
                log_debug(f"处理文件: {os.path.relpath(file_path, '/Applications')}")
                
                # 创建文件备份
                backup_path = f"{temp_file_path}.bak"
                shutil.copy2(temp_file_path, backup_path)
                
                with open(temp_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                modified = False
                # 尝试使用不同的方法修改文件
                
                # 方法1：检查并修改UUID生成相关函数
                if "IOPlatformUUID" in content:
                    log_debug("找到 IOPlatformUUID 关键字")
                    
                    # 检查是否已经修改过
                    if "return crypto.randomUUID()" in content:
                        log_info("文件已经包含 randomUUID 调用，跳过修改")
                        modified_count += 1
                        continue
                    
                    # 尝试多种可能的函数名模式
                    if "function a$(t){" in content:
                        try:
                            new_content = content.replace(
                                "function a$(t){", 
                                "function a$(t){return crypto.randomUUID(); "
                            )
                            # 检查替换是否成功
                            if new_content != content:
                                content = new_content
                                modified = True
                                log_debug("成功注入 randomUUID 调用到 a$ 函数")
                            else:
                                # 尝试更精确的替换
                                new_content = content.replace(
                                    "function a$(t){switch", 
                                    "function a$(t){return crypto.randomUUID(); switch"
                                )
                                if new_content != content:
                                    content = new_content
                                    modified = True
                                    log_debug("成功注入 randomUUID 到 a$ 函数(精确匹配)")
                        except Exception as e:
                            log_error(f"修改 a$ 函数时出错: {str(e)}")
                    
                    elif "async function v5" in content:
                        try:
                            new_content = content.replace(
                                "async function v5(t){", 
                                "async function v5(t){return crypto.randomUUID(); "
                            )
                            # 检查替换是否成功
                            if new_content != content:
                                content = new_content
                                modified = True
                                log_debug("成功注入 randomUUID 调用到 v5 函数")
                            else:
                                # 尝试更精确的替换
                                new_content = content.replace(
                                    "async function v5(t){let e=", 
                                    "async function v5(t){return crypto.randomUUID(); let e="
                                )
                                if new_content != content:
                                    content = new_content
                                    modified = True
                                    log_debug("成功注入 randomUUID 到 v5 函数(精确匹配)")
                        except Exception as e:
                            log_error(f"修改 v5 函数时出错: {str(e)}")
                    
                    # 如果没有找到特定函数，使用通用注入方法
                    if not modified and "// Cursor ID 修改工具注入" not in content:
                        try:
                            timestamp = int(time.time())
                            inject_code = f"""
// Cursor ID 修改工具注入 - {datetime.datetime.now().strftime("%Y%m%d%H%M%S")}
// 随机设备ID生成器注入 - {timestamp}
const randomDeviceId_{timestamp} = () => {{
    try {{
        return require('crypto').randomUUID();
    }} catch (e) {{
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {{
            const r = Math.random() * 16 | 0;
            return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
        }});
    }}
}};
"""
                            # 在文件开头插入代码
                            content = inject_code + content
                            
                            # 替换调用点
                            content = content.replace("await v5(!1)", f"randomDeviceId_{timestamp}()")
                            content = content.replace("a$(t)", f"randomDeviceId_{timestamp}()")
                            
                            modified = True
                            log_debug("使用通用方法注入随机设备ID生成器")
                        except Exception as e:
                            log_error(f"通用注入方法失败: {str(e)}")
                
                elif "getMachineId" in content or "getDeviceId" in content:
                    log_debug("找到设备ID相关函数")
                    
                    # 检查是否已经修改过
                    if "return crypto.randomUUID()" in content:
                        log_info("文件已经包含 randomUUID 调用，跳过修改")
                        modified_count += 1
                        continue
                    
                    try:
                        # 尝试修改多种可能的函数
                        if "function t$()" in content:
                            new_content = content.replace(
                                "function t$(){", 
                                "function t$(){return \"00:00:00:00:00:00\"; "
                            )
                            if new_content != content:
                                content = new_content
                                modified = True
                                log_debug("修改 MAC 地址获取函数成功")
                        
                        if "async function y5" in content:
                            new_content = content.replace(
                                "async function y5(t){", 
                                "async function y5(t){return crypto.randomUUID(); "
                            )
                            if new_content != content:
                                content = new_content
                                modified = True
                                log_debug("修改设备ID获取函数成功")
                    except Exception as e:
                        log_error(f"修改ID函数时出错: {str(e)}")
                
                # 如果以上都不匹配，使用最通用的注入方法
                if not modified and "// Cursor ID 修改工具注入" not in content:
                    try:
                        timestamp = int(time.time())
                        new_uuid = str(uuid.uuid4()).lower()
                        machine_id = f"auth0|user_{self.generate_random_id()[:16]}"
                        device_id = str(uuid.uuid4()).lower()
                        mac_machine_id = str(uuid.uuid4()).lower()  # 使用UUID格式而不是generate_random_id()
                        
                        inject_universal_code = f"""
// Cursor ID 修改工具注入 - {datetime.datetime.now().strftime("%Y%m%d%H%M%S")}
// 全局拦截设备标识符 - {timestamp}
const originalRequire_{timestamp} = require;
require = function(module) {{
    const result = originalRequire_{timestamp}(module);
    if (module === 'crypto' && result.randomUUID) {{
        const originalRandomUUID_{timestamp} = result.randomUUID;
        result.randomUUID = function() {{
            return '{new_uuid}';
        }};
    }}
    return result;
}};

// 覆盖所有可能的系统ID获取函数
global.getMachineId = function() {{ return '{machine_id}'; }};
global.getDeviceId = function() {{ return '{device_id}'; }};
global.macMachineId = '{mac_machine_id}';
"""
                        # 在文件开头插入代码
                        content = inject_universal_code + content
                        modified = True
                        log_debug("使用最通用的注入方法覆盖系统ID获取函数")
                    except Exception as e:
                        log_error(f"通用注入方法失败: {str(e)}")
                
                # 如果成功修改了文件，写入更改
                if modified:
                    try:
                        with open(temp_file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        modified_count += 1
                        log_info(f"成功修改文件: {os.path.relpath(file_path, '/Applications')}")
                    except Exception as e:
                        log_error(f"写入文件失败: {str(e)}")
                        if os.path.exists(backup_path):
                            shutil.copy2(backup_path, temp_file_path)
                else:
                    # 如果没有修改成功，恢复备份
                    if os.path.exists(backup_path):
                        shutil.copy2(backup_path, temp_file_path)
                        log_warn(f"未能修改文件，已恢复原始版本: {os.path.relpath(file_path, '/Applications')}")
                
                # 删除备份
                try:
                    if os.path.exists(backup_path):
                        os.remove(backup_path)
                except:
                    pass
            
            if modified_count == 0:
                log_error("未能成功修改任何文件")
                shutil.rmtree(temp_dir)
                return False
            
            # 重新签名应用（增加重试机制）
            log_info("尝试对修改后的应用进行签名...")
            max_retry = 3
            retry_count = 0
            sign_success = False
            
            while retry_count < max_retry:
                retry_count += 1
                log_info(f"尝试签名 (第 {retry_count} 次)...")
                
                # 使用更详细的签名参数
                try:
                    result = subprocess.run([
                        "codesign", "--sign", "-", "--force", "--deep", 
                        "--preserve-metadata=entitlements,identifier,flags", 
                        temp_app
                    ], capture_output=True, text=True)
                    
                    # 验证签名
                    verify_result = subprocess.run([
                        "codesign", "--verify", "-vvvv", temp_app
                    ], stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
                    
                    if verify_result.returncode == 0:
                        sign_success = True
                        log_info("应用签名验证通过")
                        break
                    else:
                        log_warn(f"签名验证失败 (尝试 {retry_count}/{max_retry})...")
                        if result.stderr:
                            log_debug(f"错误信息: {result.stderr}")
                except Exception as e:
                    log_warn(f"签名过程出错: {str(e)}")
                
                time.sleep(1)
            
            if not sign_success:
                log_warn("无法完成应用签名，但将继续尝试替换应用")
                log_warn("您可能需要在启动Cursor时手动接受安全提示")
            
            # 停止应用程序
            log_info("确保Cursor已关闭...")
            try:
                subprocess.run([
                    "killall", "Cursor"
                ], stderr=subprocess.DEVNULL, check=False)
                time.sleep(1)  # 等待应用完全关闭
            except:
                pass
            
            # 替换原应用
            log_info("安装修改版应用...")
            try:
                subprocess.run([
                    "rm", "-rf", self.cursor_app_path
                ], check=True)
                
                subprocess.run([
                    "cp", "-R", temp_app, "/Applications/"
                ], check=True)
                
                # 设置权限
                subprocess.run([
                    "chown", "-R", f"{os.getenv('SUDO_USER')}:staff", self.cursor_app_path
                ], check=True)
                
                log_info(f"Cursor 主程序文件修改完成！原版备份在: {backup_app}", show_box=True)
                log_success("主程序文件修改成功！", show_box=True)
            except Exception as e:
                log_error(f"替换应用失败: {str(e)}", show_box=True)
                # 尝试恢复原应用
                log_warn("尝试从备份恢复...", show_box=True)
                try:
                    subprocess.run([
                        "cp", "-R", backup_app, "/Applications/"
                    ], check=True)
                    log_info("已从备份恢复原应用", show_box=True)
                except:
                    log_error("恢复失败，请手动从以下位置恢复: " + backup_app, show_box=True)
                return False
            
            return True
            
        except Exception as e:
            log_error(f"修改应用程序文件失败: {str(e)}", show_box=True)
            return False
        finally:
            # 清理临时目录
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
            except:
                pass

    def disable_auto_update(self):
        """禁用Mac系统下的自动更新"""
        try:
            # 定义路径
            updater_path = os.path.join(os.getenv('HOME', ''), 'Library/Application Support/Caches/cursor-updater')
            app_update_yml = '/Applications/Cursor.app/Contents/Resources/app-update.yml'
            
            log_info("正在禁用Cursor自动更新...", show_box=True)
            
            # 备份并清空app-update.yml
            if os.path.exists(app_update_yml):
                log_info("备份并修改app-update.yml...")
                try:
                    subprocess.run(['sudo', 'cp', app_update_yml, f"{app_update_yml}.bak"], check=False, capture_output=True)
                except:
                    log_warn("备份app-update.yml失败，继续执行...")
                
                try:
                    subprocess.run(['sudo', 'bash', '-c', f'echo "" > "{app_update_yml}"'], check=True, capture_output=True)
                    subprocess.run(['sudo', 'chmod', '444', app_update_yml], check=True, capture_output=True)
                    log_info("成功禁用app-update.yml")
                except Exception as e:
                    log_error(f"修改app-update.yml失败: {str(e)}", show_box=True)
            else:
                log_warn("未找到app-update.yml文件", show_box=True)
            
            # 处理cursor-updater
            log_info("处理cursor-updater...")
            
            # 处理有可能存在的cursor-updater目录
            if os.path.exists(updater_path):
                if os.path.isdir(updater_path):
                    # 是目录，需要删除
                    try:
                        shutil.rmtree(updater_path)
                        log_info("已删除cursor-updater目录")
                    except Exception as e:
                        log_error(f"删除cursor-updater目录失败: {str(e)}", show_box=True)
                else:
                    # 是文件，需要删除然后创建占位符
                    try:
                        os.remove(updater_path)
                        log_info("已删除cursor-updater文件")
                    except Exception as e:
                        log_error(f"删除cursor-updater文件失败: {str(e)}", show_box=True)
            
            # 创建占位符文件并设置只读
            try:
                with open(updater_path, 'w') as f:
                    f.write("")
                os.chmod(updater_path, 0o444)  # 设置为只读
                log_info("已创建cursor-updater占位符并设置为只读")
            except Exception as e:
                log_error(f"创建cursor-updater占位符失败: {str(e)}", show_box=True)
            
            log_success("自动更新已成功禁用", show_box=True)
            log_info("您可以通过以下方式验证更新是否已禁用:")
            log_info(f"1. 检查 {app_update_yml} 是否为空 (命令: cat \"{app_update_yml}\")")
            log_info(f"2. 检查文件权限是否为只读 (命令: ls -l \"{app_update_yml}\")")
            log_info(f"3. 检查 {updater_path} 是否为只读文件 (命令: ls -l \"{updater_path}\")")
            
            return True
        except Exception as e:
            log_error(f"禁用自动更新失败: {str(e)}", show_box=True)
            return False

    def _show_manual_guide(self, updater_path, app_update_yml):
        """显示手动禁用更新的指南"""
        print_box(
            "手动设置指南",
            [
                (Colors.YELLOW, "[警告] 自动设置失败，请尝试手动操作："),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "手动禁用更新步骤："),
                (Colors.WHITE, "1. 打开终端"),
                (Colors.WHITE, "2. 复制粘贴以下命令："),
                (Colors.WHITE, ""),
                (Colors.BLUE, "命令1 - 删除现有目录（如果存在）："),
                (Colors.WHITE, f'sudo rm -rf "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.BLUE, "命令2 - 创建阻止文件："),
                (Colors.WHITE, f'sudo touch "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.BLUE, "命令3 - 设置只读属性："),
                (Colors.WHITE, f'sudo chmod 444 "{updater_path}"'),
                (Colors.WHITE, ""),
                (Colors.BLUE, "命令4 - 备份并修改 app-update.yml："),
                (Colors.WHITE, f'sudo cp "{app_update_yml}" "{app_update_yml}.bak" && \\'),
                (Colors.WHITE, f'sudo bash -c \'echo "" > "{app_update_yml}"\' && \\'),
                (Colors.WHITE, f'sudo chmod 444 "{app_update_yml}"'),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "验证方法："),
                (Colors.WHITE, f'1. 运行命令：ls -l "{updater_path}"'),
                (Colors.WHITE, "   确认文件权限显示为：r--r--r--"),
                (Colors.WHITE, f'2. 运行命令：ls -l "{app_update_yml}"'),
                (Colors.WHITE, "   确认文件权限显示为：r--r--r--"),
                (Colors.WHITE, ""),
                (Colors.YELLOW, "[提示] 完成后请重启 Cursor")
            ]
        )

    def flush_dns_cache(self):
        """清除DNS缓存（子类必须实现）"""
        # 在Mac上，使用dscacheutil和killall命令清除DNS缓存
        try:
            log_info("正在清除DNS缓存...")
            
            # 首先执行dscacheutil命令
            subprocess.run(["dscacheutil", "-flushcache"], 
                           stderr=subprocess.DEVNULL, 
                           stdout=subprocess.DEVNULL)
            
            # 然后重启mDNSResponder服务
            subprocess.run(["killall", "-HUP", "mDNSResponder"], 
                           stderr=subprocess.DEVNULL, 
                           stdout=subprocess.DEVNULL)
            
            log_success("DNS缓存已清除")
            return True
        except Exception as e:
            log_warn(f"清除DNS缓存失败: {str(e)}")
            return False

    def run(self):
        """运行Mac修改器的主流程"""
        try:
            # 检查权限
            if not self.check_permissions():
                return False
            
            # 关闭Cursor进程
            self.kill_cursor_process()
            
            # 备份配置
            if not self.backup_config():
                return False
            
            # 备份系统ID
            self.backup_system_id()
            
            # 修改系统ID
            if not self.modify_system_id():
                return False
            
            # 更新配置文件
            if not self.update_config():
                return False
            
            log_success("机器码修改完成", show_box=True)
            
            # 清除DNS缓存
            self.flush_dns_cache()
            
            # 机器码修改完成后询问是否禁用自动更新
            self.should_disable_update = self._should_disable_update()
            if self.should_disable_update:
                if not self.disable_auto_update():
                    log_warn("禁用自动更新失败，但主要操作已完成", show_box=True)
            
            return True
            
        except Exception as e:
            log_error(f"执行过程中出现错误: {str(e)}", show_box=True)
            import traceback
            print(traceback.format_exc())
            return False

    def clean_cursor_app(self):
        """清理Cursor之前的修改"""
        log_info("尝试清理Cursor之前的修改...")
        
        # 查找最新的备份
        latest_backup = None
        backup_pattern = "/tmp/Cursor.app.backup_*"
        try:
            backup_files = glob.glob(backup_pattern)
            if backup_files:
                latest_backup = sorted(backup_files)[-1]  # 获取最新的备份
        except Exception as e:
            log_error(f"查找备份失败: {str(e)}", show_box=True)
        
        if latest_backup and os.path.isdir(latest_backup):
            log_info(f"找到现有备份: {latest_backup}", show_box=True)
            log_info("正在恢复原始版本...")
            
            # 停止Cursor进程
            self.kill_cursor_process()
            
            try:
                # 删除当前应用
                subprocess.run(["sudo", "rm", "-rf", self.cursor_app_path], check=True)
                # 恢复备份
                subprocess.run(["sudo", "cp", "-R", latest_backup, self.cursor_app_path], check=True)
                # 设置权限
                current_user = os.getenv('SUDO_USER') if os.getenv('SUDO_USER') else os.getenv('USER')
                subprocess.run(["sudo", "chown", "-R", f"{current_user}:staff", self.cursor_app_path], check=True)
                subprocess.run(["sudo", "chmod", "-R", "755", self.cursor_app_path], check=True)
                
                log_info("已恢复原始版本", show_box=True)
                return True
            except Exception as e:
                log_error(f"恢复备份失败: {str(e)}", show_box=True)
                return False
        else:
            log_warn("未找到现有备份，请考虑重新安装Cursor", show_box=True)
            log_info("您可以从 https://cursor.sh 下载并重新安装Cursor")
            return False
            
    def backup_system_id(self):
        """备份Mac系统ID"""
        log_info("正在备份系统 ID...")
        
        try:
            # 创建备份目录
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            system_id_file = os.path.join(self.backup_dir, f"system_id.backup_{timestamp}")
            
            # 获取IOPlatformExpertDevice信息
            with open(system_id_file, 'w', encoding='utf-8') as f:
                f.write("# Original System ID Backup\n")
                f.write("## IOPlatformExpertDevice Info:\n")
                
                try:
                    ioreg_output = subprocess.check_output(
                        ["ioreg", "-rd1", "-c", "IOPlatformExpertDevice"],
                        universal_newlines=True
                    )
                    f.write(ioreg_output)
                except:
                    f.write("Failed to get IOPlatformExpertDevice info\n")
            
            # 设置文件权限
            try:
                os.chmod(system_id_file, 0o444)  # 设为只读
                # 获取当前用户
                current_user = os.getenv('SUDO_USER') if os.getenv('SUDO_USER') else os.getenv('USER')
                if current_user:
                    subprocess.run(["chown", current_user, system_id_file], check=False)
            except:
                pass
            
            log_info(f"系统 ID 已备份到: {system_id_file}", show_box=True)
            return True
            
        except Exception as e:
            log_error(f"备份系统 ID 失败: {str(e)}", show_box=True)
            return False

    def backup_config(self):
        """备份Mac系统下的配置文件"""
        if not os.path.exists(self.storage_file):
            log_warn(f"配置文件不存在: {self.storage_file}")
            return True
        
        try:
            # 确保备份目录存在
            os.makedirs(self._get_backup_dir(), exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(self._get_backup_dir(), f"storage.json.backup_{timestamp}")
            
            # 复制配置文件
            shutil.copy2(self.storage_file, backup_file)
            
            log_info(f"配置已备份到: {backup_file}", show_box=True)
            return True
        except Exception as e:
            log_error(f"备份配置失败: {str(e)}", show_box=True)
            return False

    def backup_system_id(self):
        """备份Mac系统下的系统ID"""
        try:
            # 确保备份目录存在
            os.makedirs(self._get_backup_dir(), exist_ok=True)
            
            # 获取当前系统UUID
            system_uuid = None
            try:
                result = subprocess.check_output(["ioreg", "-ad2", "-c", "IOPlatformExpertDevice"], universal_newlines=True)
                for line in result.split('\n'):
                    if "IOPlatformUUID" in line:
                        system_uuid = line.split('"')[-2].strip()
                        break
            except:
                log_warn("无法读取当前系统UUID")
                system_uuid = "unknown"
            
            # 生成备份文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(self._get_backup_dir(), f"system_id.backup_{timestamp}")
            
            # 写入备份文件
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(f"SystemUUID={system_uuid}\n")
            
            log_info(f"系统 ID 已备份到: {backup_file}", show_box=True)
            return True
        except Exception as e:
            log_error(f"备份系统ID失败: {str(e)}", show_box=True)
            return False

# 创建工厂函数
def create_id_modifier():
    """根据系统类型创建相应的ID修改器"""
    system_type = get_system_type()
    
    if system_type == "windows":
        return WindowsIDModifier()
    elif system_type == "mac":
        return MacIDModifier()
    elif system_type == "linux":
        return LinuxIDModifier()
    else:
        raise NotImplementedError(f"不支持的操作系统: {system_type}")

def main(silent=False):
    """主函数"""
    try:
        # 检查是否以管理员/sudo权限运行
        check_and_elevate_privileges()
        
        if not silent:
            print_box(
                "系统信息",
                [
                    (Colors.YELLOW, f"[重要提示] 正在启动适用于 {get_system_type().upper()} 系统的机器码修改工具")
                ]
            )
        
        # 创建并运行ID修改器
        modifier = create_id_modifier()
        success = modifier.run()
        
        if not silent:
            if success:
                print_box(
                    "执行结果",
                    [
                        (Colors.GREEN, "[成功] 修改机器码流程完成!")
                    ]
                )
            else:
                print_box(
                    "执行结果",
                    [
                        (Colors.RED, "[错误] 机器码修改失败!")
                    ]
                )
        
        return success
    
    except Exception as e:
        print_box(
            "错误信息",
            [
                (Colors.RED, f"[错误] 执行过程中出现错误: {str(e)}"),
                (Colors.WHITE, ""),
                (Colors.WHITE, "详细错误信息:"),
                *[(Colors.WHITE, line) for line in traceback.format_exc().split('\n')]
            ]
        )
        return False

if __name__ == "__main__":
    main() 