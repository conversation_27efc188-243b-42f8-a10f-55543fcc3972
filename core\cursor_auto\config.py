from dotenv import load_dotenv
import os
import sys
from colorama import Fore, Style

# 尝试导入get_app_data_dir函数
try:
    # 先尝试从主应用导入
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
    from utils import get_app_data_dir
except ImportError:
    # 如果导入失败，定义一个简单的替代函数
    def get_app_data_dir(app_name="YCursor"):
        """简单的应用数据目录获取函数"""
        if sys.platform == "win32":  # Windows
            app_data = os.getenv("APPDATA")
            if app_data:
                app_dir = os.path.join(app_data, app_name)
        elif sys.platform == "darwin":  # macOS
            app_dir = os.path.expanduser(f"~/Library/Application Support/{app_name}")
        else:  # Linux和其他系统
            app_dir = os.path.expanduser(f"~/.config/{app_name}")
        
        # 确保目录存在
        if app_dir and not os.path.exists(app_dir):
            os.makedirs(app_dir)
            
        return app_dir


class Config:
    def __init__(self):
        # 获取应用数据目录中的temp_quota_data目录
        try:
            app_data_dir = get_app_data_dir("YCursor")
            temp_quota_data_dir = os.path.join(app_data_dir, "temp_quota_data")
            
            # 确保temp_quota_data目录存在
            if not os.path.exists(temp_quota_data_dir):
                os.makedirs(temp_quota_data_dir)
                
            # 设置.env文件路径
            self.dotenv_path = os.path.join(temp_quota_data_dir, ".env")
            
            if not os.path.exists(self.dotenv_path):
                # 如果在应用数据目录中找不到.env文件，尝试从传统位置
                # 检查是否已经导入了STARTUP_DIRECTORY
                try:
                    # 从主模块导入
                    from __main__ import STARTUP_DIRECTORY
                    legacy_path = os.path.join(STARTUP_DIRECTORY, ".env")
                except ImportError:
                    # 从cursor_pro_keep_alive导入
                    try:
                        from cursor_pro_keep_alive import STARTUP_DIRECTORY
                        legacy_path = os.path.join(STARTUP_DIRECTORY, ".env")
                    except ImportError:
                        # 如果无法导入，回退到获取环境变量
                        if 'REAL_STARTUP_DIRECTORY' in os.environ:
                            legacy_path = os.path.join(os.environ['REAL_STARTUP_DIRECTORY'], ".env")
                        else:
                            # 回退到传统方法
                            if getattr(sys, "frozen", False):
                                legacy_path = os.path.join(os.path.dirname(sys.executable), ".env")
                            else:
                                legacy_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), ".env")
                
                # 如果在传统位置找到了.env文件，提示用户
                if os.path.exists(legacy_path):
                    print(f"[信息] 在旧位置找到.env文件: {legacy_path}")
                    print(f"[信息] 将在新位置使用: {self.dotenv_path}")
                    
                    # 尝试复制文件到新位置
                    try:
                        import shutil
                        shutil.copy2(legacy_path, self.dotenv_path)
                        print(f"[信息] 已复制.env文件到新位置")
                    except Exception as e:
                        print(f"[警告] 复制.env文件失败: {e}")
                        self.dotenv_path = legacy_path
                else:
                    raise FileNotFoundError(f"文件 {self.dotenv_path} 不存在")
        except Exception as e:
            # 如果应用数据目录获取失败，回退到传统方法
            print(f"[警告] 使用应用数据目录失败: {e}，回退到传统方法")
            
            # 尝试导入全局STARTUP_DIRECTORY
            try:
                # 从主模块导入
                from __main__ import STARTUP_DIRECTORY
                application_path = STARTUP_DIRECTORY
            except ImportError:
                # 从cursor_pro_keep_alive导入
                try:
                    from cursor_pro_keep_alive import STARTUP_DIRECTORY
                    application_path = STARTUP_DIRECTORY
                except ImportError:
                    # 如果无法导入，回退到获取环境变量
                    if 'REAL_STARTUP_DIRECTORY' in os.environ:
                        application_path = os.environ['REAL_STARTUP_DIRECTORY']
                    else:
                        # 回退到传统方法
                        if getattr(sys, "frozen", False):
                            application_path = os.path.dirname(sys.executable)
                        else:
                            application_path = os.path.dirname(os.path.abspath(__file__))
            
            # 检查路径是否为临时目录
            if any(temp in application_path.upper() for temp in ["TEMP", "TMP", "ONEFILE", "~1"]):
                # 如果是临时目录，尝试使用当前工作目录
                current_dir = os.getcwd()
                if not any(temp in current_dir.upper() for temp in ["TEMP", "TMP", "ONEFILE", "~1"]):
                    application_path = current_dir
            
            # 确保路径可写
            try:
                test_file = os.path.join(application_path, ".config_test_write")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
            except Exception:
                print(f"[警告] 路径不可写: {application_path}，回退到用户主目录")
                # 回退到用户主目录
                application_path = os.path.expanduser("~")
            
            # 设置.env文件路径
            self.dotenv_path = os.path.join(application_path, ".env")
            
            if not os.path.exists(self.dotenv_path):
                raise FileNotFoundError(f"文件 {self.dotenv_path} 不存在")

        # 加载 .env 文件
        load_dotenv(self.dotenv_path)

        # 加载临时邮箱配置
        self.temp_mail = os.getenv("TEMP_MAIL", "").strip()
        self.temp_mail_epin = os.getenv("TEMP_MAIL_EPIN", "").strip()
        
        # 加载 IMAP 配置
        self.imap_server = os.getenv("IMAP_SERVER", "").strip()
        self.imap_port = os.getenv("IMAP_PORT", "").strip()
        self.imap_user = os.getenv("IMAP_USER", "").strip()
        self.imap_pass = os.getenv("IMAP_PASS", "").strip()
        self.imap_dir = os.getenv("IMAP_DIR", "inbox").strip()
        
        # 加载域名配置
        self.domain = os.getenv("DOMAIN", "").strip()

        # 确定使用哪种邮箱模式
        self.use_temp_mail = self.check_is_valid(self.temp_mail) and self.check_is_valid(self.temp_mail_epin)

        self.check_config()
        self.check_default_values()

    def check_default_values(self):
        """检查关键配置是否使用了默认值"""
        # 检查域名配置
        unconfigured_items = []
        
        # 检查域名
        domain_value = os.getenv('DOMAIN', '').strip()
        if domain_value == 'xxxxx.com' or not domain_value:
            unconfigured_items.append('域名（DOMAIN）')
        
        # 根据使用的邮箱类型检查相应配置
        if self.use_temp_mail:
            # 检查临时邮箱配置
            temp_mail = os.getenv('TEMP_MAIL', '').strip()
            temp_mail_epin = os.getenv('TEMP_MAIL_EPIN', '').strip()
            
            # 检查是否为空或是否使用了默认值
            if not temp_mail or temp_mail == '<EMAIL>':
                unconfigured_items.append('临时邮箱地址 (TEMP_MAIL)')
            if not temp_mail_epin or temp_mail_epin == 'your_epin_code':
                unconfigured_items.append('临时邮箱PIN码 (TEMP_MAIL_EPIN)')
        else:
            # 检查IMAP配置
            imap_user = os.getenv('IMAP_USER', '').strip()
            imap_pass = os.getenv('IMAP_PASS', '').strip()
            imap_server = os.getenv('IMAP_SERVER', '').strip()
            imap_port = os.getenv('IMAP_PORT', '').strip()
            
            if not imap_server:
                unconfigured_items.append('IMAP服务器 (IMAP_SERVER)')
            if not imap_port:
                unconfigured_items.append('IMAP端口 (IMAP_PORT)')
            if imap_user == '<EMAIL>' or not imap_user:
                unconfigured_items.append('邮箱地址 (IMAP_USER)')
            if imap_pass == 'xxx' or not imap_pass:
                unconfigured_items.append('邮箱授权码或密码 (IMAP_PASS)')

        if unconfigured_items:
            # 获取.env文件路径，使用与初始化时相同的路径
            env_path = self.dotenv_path

            print("\n[错误] 以下配置项未在.env文件中设置")
            print("")
            print(f"配置文件路径: {env_path}")
            print("")
            print("请配置以下必填项:")
            for item in unconfigured_items:
                print(f"  • {item}")
            print("")
            
            # 添加邮箱模式切换提示
            if self.use_temp_mail:
                print("提示: 如果您想使用的是IMAP邮箱，请注释或删除临时邮箱的配置项 (TEMP_MAIL 和 TEMP_MAIL_EPIN)")
            else:
                print("提示: 如果您想使用的是临时邮箱，请去掉临时邮箱配置项前面的#号 (TEMP_MAIL 和 TEMP_MAIL_EPIN)")
            print("")
            
            print("请修改正确的.env文件后再运行程序")
            print("不会配置？配置太繁琐？可以请群主喝杯奶茶让群主给你当牛马帮你配置文件 [滑稽]")
            print("或者可以直接使用群主提供的独享配置文件，到手就能用 [滑稽]")
            sys.exit(1)

    def get_imap(self):
        """获取IMAP配置"""
        if self.use_temp_mail:
            return False
        return {
            "imap_server": self.imap_server,
            "imap_port": self.imap_port,
            "imap_user": self.imap_user,
            "imap_pass": self.imap_pass,
            "imap_dir": self.imap_dir,
        }

    def get_temp_mail(self):
        """获取临时邮箱地址"""
        return self.temp_mail

    def get_temp_mail_epin(self):
        """获取临时邮箱PIN码"""
        return self.temp_mail_epin

    def is_using_temp_mail(self):
        """是否使用临时邮箱"""
        return self.use_temp_mail

    def get_domain(self):
        """获取域名配置"""
        return self.domain

    def get_clean_all_cursor_mails(self):
        """获取是否清理所有 Cursor 邮件的配置"""
        return os.getenv('CLEAN_ALL_CURSOR_MAILS', 'False').lower() == 'true'

    def get_show_verification_email_content(self):
        """获取是否显示验证码邮件完整内容的配置"""
        return os.getenv('SHOW_VERIFICATION_EMAIL_CONTENT', 'False').lower() == 'true'

    def get_verification_code_timeout(self):
        """获取验证码超时时间（秒）
        如果未配置，默认为180秒（3分钟）
        """
        try:
            timeout = int(os.getenv('VERIFICATION_CODE_TIMEOUT', '180'))
            return max(60, timeout)  # 确保至少为60秒
        except ValueError:
            return 180  # 如果配置的值无效，返回默认值

    def get_verification_code_pattern(self):
        """获取验证码正则表达式 (通用于所有邮箱类型)
        如果未配置，返回None，使用默认值
        """
        pattern = os.getenv('VERIFICATION_CODE_PATTERN', '').strip()
        # 如果配置了值，确保它作为原始字符串处理
        if pattern:
            return pattern.replace('\\\\', '\\')  # 处理双重转义
        return None

    def check_config(self):
        """检查配置项是否有效"""
        # 检查域名配置
        if not self.check_is_valid(self.domain):
            raise ValueError("域名未配置，请在 .env 文件中设置 DOMAIN")

        # 根据使用的邮箱类型进行不同的检查
        if self.use_temp_mail:
            # 检查临时邮箱配置
            if not self.check_is_valid(self.temp_mail):
                raise ValueError("临时邮箱地址未配置，请在 .env 文件中设置 TEMP_MAIL")
            if not self.check_is_valid(self.temp_mail_epin):
                raise ValueError("临时邮箱PIN码未配置，请在 .env 文件中设置 TEMP_MAIL_EPIN")
        else:
            # 检查是否使用163邮箱
            if "163.com" in self.imap_server.lower() or "163.com" in self.imap_user.lower():
                print("\n\033[0;31m[错误] 不支持 163 邮箱\033[0m")
                print("\033[0;37m由于163邮箱服务限制，本程序不再支持163邮箱\033[0m")
                print("\033[0;32m\n推荐使用QQ邮箱或其他邮箱\033[0m\n")
                raise ValueError("不支持163邮箱，请更换其他邮箱")

            # 检查IMAP配置
            imap_configs = {
                "imap_server": "IMAP服务器",
                "imap_port": "IMAP端口",
                "imap_user": "IMAP用户名",
                "imap_pass": "IMAP密码",
            }

            for key, name in imap_configs.items():
                value = getattr(self, key)
                if not self.check_is_valid(value):
                    raise ValueError(
                        f"{name}未配置，请在 .env 文件中设置 {key.upper()}"
                    )

            # IMAP_DIR 是可选的，如果设置了就检查其有效性
            if not self.check_is_valid(self.imap_dir):
                raise ValueError(
                    "IMAP收件箱目录配置无效，请在 .env 文件中正确设置 IMAP_DIR"
                )

    def check_is_valid(self, value):
        """检查配置项是否有效

        Args:
            value: 配置项的值

        Returns:
            bool: 配置项是否有效
        """
        return isinstance(value, str) and len(str(value).strip()) > 0

    def print_config(self):
        if self.use_temp_mail:
            print("使用临时邮箱模式")
            print(f"临时邮箱地址: {self.temp_mail}")
            print(f"临时邮箱PIN码: {'*' * len(self.temp_mail_epin)}")
        else:
            print("使用IMAP邮箱模式")
            print(f"IMAP服务器: {self.imap_server}")
            print(f"IMAP端口: {self.imap_port}")
            print(f"IMAP用户名: {self.imap_user}")
            print(f"IMAP密码: {'*' * len(self.imap_pass)}")
            print(f"IMAP收件箱目录: {self.imap_dir}")
        print(f"域名: {self.domain}")


# 颜色定义
class Colors:
    """统一的颜色定义"""
    RED = ''      # 错误信息
    GREEN = ''    # 成功信息
    YELLOW = ''   # 警告/提示信息
    BLUE = ''     # 框架/标题
    PURPLE = ''   # 重要数据
    CYAN = ''     # 进度信息
    WHITE = ''    # 普通文本
    NC = ''       # 结束颜色


# 使用示例
if __name__ == "__main__":
    try:
        config = Config()
        print("环境变量加载成功！")
        config.print_config()
    except ValueError as e:
        print(f"错误: {e}")
