"""
智能线程管理器模块
提供并行获取账户配额数据的功能
"""

import sys
import asyncio
import time
import traceback
import multiprocessing
import psutil
from threading import Thread, Lock
from concurrent.futures import ThreadPoolExecutor, as_completed
from PySide6.QtCore import QObject, Signal

# 导入自定义模块
from account.quota import AccountQuota
from utils import create_new_event_loop, safely_run_until_complete
from logger import logger, info, warning, error

class QuotaFetcherManager(QObject):
    """智能线程管理器，用于并行获取账户配额数据"""
    
    # 定义信号
    quota_fetched = Signal(str, dict)  # 单个账户配额获取完成的信号
    all_quotas_fetched = Signal()  # 所有账户配额获取完成的信号
    progress_updated = Signal(int, int)  # 进度更新信号(当前进度, 总数)
    
    def __init__(self):
        super().__init__()
        # 获取操作系统类型
        self.platform = sys.platform
        
        # 获取CPU核心数
        self.cpu_count = multiprocessing.cpu_count()
        
        # 获取系统内存信息（以GB为单位）
        if hasattr(psutil, 'virtual_memory'):
            self.total_memory = psutil.virtual_memory().total / (1024 * 1024 * 1024)
        else:
            self.total_memory = 8  # 默认假设8GB内存
        
        # 根据不同平台和系统资源调整最优线程数
        if self.platform == "win32":
            # Windows系统根据内存和CPU动态调整
            memory_based_threads = max(2, min(int(self.total_memory / 2), 12))
            cpu_based_threads = max(2, min(self.cpu_count * 2, 10))
            self.optimal_threads = min(memory_based_threads, cpu_based_threads)
        elif self.platform == "darwin":
            # macOS系统使用更保守的策略
            memory_based_threads = max(2, min(int(self.total_memory / 3), 8))
            cpu_based_threads = max(2, min(self.cpu_count - 1, 6))
            self.optimal_threads = min(memory_based_threads, cpu_based_threads)
        else:
            # Linux系统根据资源动态调整
            memory_based_threads = max(2, min(int(self.total_memory / 2.5), 10))
            cpu_based_threads = max(2, min(self.cpu_count - 1, 8))
            self.optimal_threads = min(memory_based_threads, cpu_based_threads)
        
        print(f"系统信息: {self.platform}")
        print(f"CPU核心数: {self.cpu_count}")
        print(f"系统内存: {self.total_memory:.1f}GB")
        print(f"优化线程数: {self.optimal_threads}")
        
        # 创建线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=self.optimal_threads)
        # 创建锁用于同步
        self.lock = Lock()
        # 存储结果的字典
        self.results = {}
        # 进度计数器
        self.processed_count = 0
        self.total_count = 0
    
    def create_account_groups(self, accounts):
        """创建账户分组，确保组数等于线程数，实现真正的一组一线程模式"""
        if not accounts:
            return []
            
        total_accounts = len(accounts)
        
        # 计算每个线程要处理的账户数量
        # 确保每个线程至少有一个账户，并且线程数不超过账户总数
        thread_count = min(self.optimal_threads, total_accounts)
        accounts_per_thread = max(1, total_accounts // thread_count)
        
        # 创建分组
        groups = []
        remaining_accounts = total_accounts
        start_idx = 0
        
        # 为每个线程分配一组账户
        for i in range(thread_count):
            # 为最后一个线程分配所有剩余账户
            if i == thread_count - 1:
                group = accounts[start_idx:]
            else:
                # 计算当前线程应处理的账户数
                # 确保账户均匀分配
                current_group_size = min(accounts_per_thread, remaining_accounts)
                group = accounts[start_idx:start_idx + current_group_size]
                start_idx += current_group_size
                remaining_accounts -= current_group_size
            
            if group:  # 确保不添加空组
                groups.append(group)
        
        print(f"账户分组信息 (一组一线程模式，组内异步并发):")
        print(f"总账户数: {total_accounts}")
        print(f"线程数量: {len(groups)}")
        print(f"每个线程处理的账户数: {[len(g) for g in groups]}")
        
        return groups
    
    async def _fetch_single_quota_async(self, account):
        """异步获取单个账户的配额"""
        try:
            # 使用asyncio.to_thread将同步函数转换为异步函数
            quota_data = await asyncio.to_thread(AccountQuota.get_quota, account)

            # 获取账户类型信息
            if quota_data:
                from account.account_type import AccountType
                membership_type, days_remaining, verified_student = await asyncio.to_thread(
                    AccountType.get_account_type, account
                )

                # 将账户类型信息添加到quota_data中
                if membership_type is not None:
                    quota_data["account_type_info"] = {
                        "membershipType": membership_type,
                        "daysRemainingOnTrial": days_remaining,
                        "verifiedStudent": verified_student
                    }

                return account.get('email'), quota_data
            return None
        except Exception as e:
            print(f"异步获取账户 {account.get('email')} 配额时出错: {str(e)}")
            return None
        
    async def _process_group_async(self, account_group):
        """异步处理单个账户组的所有账户"""
        # 创建所有账户的异步任务
        tasks = [self._fetch_single_quota_async(account) for account in account_group]
        
        # 设置适当的并发限制（防止过多并发请求）
        # 每个线程内的并发数调整为账户数与CPU核心数的较小值
        semaphore = asyncio.Semaphore(min(len(account_group), self.cpu_count))
        
        # 使用辅助函数进行有限制的并发
        async def fetch_with_semaphore(task_idx):
            async with semaphore:
                result = await tasks[task_idx]
                # 不再在这里更新进度，而是在成功处理数据后更新
                return result
                
        # 创建受限的任务
        limited_tasks = [fetch_with_semaphore(i) for i in range(len(tasks))]
        
        # 执行所有任务并收集结果
        results = await asyncio.gather(*limited_tasks)
        
        # 过滤掉None结果
        return [r for r in results if r is not None]
    
    def process_group(self, account_group):
        """处理单个账户组的任务，在组内使用异步并发"""
        # 使用兼容性函数创建新的事件循环
        loop = create_new_event_loop()
        if not loop:
            print("无法创建事件循环，无法处理账户组")
            return []
        
        try:
            # 在事件循环中运行异步处理函数
            results = safely_run_until_complete(self._process_group_async(account_group))
            return results if results else []
        except Exception as e:
            print(f"组内异步处理时出错: {str(e)}")
            traceback.print_exc()
            return []
        finally:
            # 确保事件循环被正确关闭
            try:
                if not loop.is_closed():
                    loop.close()
            except Exception as e:
                print(f"关闭事件循环时出错: {str(e)}")
    
    def start_processing(self, accounts):
        """开始处理账户列表（在后台线程中调用）"""
        try:
            # 清空之前的结果
            self.results = {}
            
            # 重置进度计数器
            with self.lock:
                self.processed_count = 0
                self.total_count = len(accounts)
                # 重置进度条，确保UI显示正确的初始状态
                self.progress_updated.emit(0, self.total_count)
            
            # 创建账户分组，保证组数=线程数
            account_groups = self.create_account_groups(accounts)
            
            # 创建future列表
            futures = []
            
            # 提交每个分组的任务到线程池，每个组对应一个线程
            for group in account_groups:
                future = self.thread_pool.submit(self.process_group, group)
                futures.append(future)
            
            # 处理每个分组的结果
            for future in as_completed(futures):
                try:
                    group_results = future.result()
                    
                    # 只有在成功获取到数据后才更新进度
                    if group_results:
                        with self.lock:
                            # 增加成功获取的账户数量
                            self.processed_count += len(group_results)
                            # 确保进度不超过总数
                            if self.processed_count > self.total_count:
                                self.processed_count = self.total_count
                            # 更新进度条 - 只有在成功获取数据后
                            self.progress_updated.emit(self.processed_count, self.total_count)
                    
                    # 处理获取到的额度数据
                    for email, quota_data in group_results:
                        with self.lock:
                            self.results[email] = quota_data
                            # 发送单个账户配额获取完成的信号
                            self.quota_fetched.emit(email, quota_data)
                except Exception as e:
                    print(f"处理账户组结果时出错: {str(e)}")
                
                # 平台特定的延迟处理
                if self.platform == "darwin":
                    time.sleep(0.1)
                elif self.platform != "win32":
                    time.sleep(0.05)
            
            # 发送所有账户处理完成的信号
            self.all_quotas_fetched.emit()
            
        except Exception as e:
            print(f"处理账户列表时出错: {str(e)}")
            # 发送完成信号，确保UI不会永远处于加载状态
            self.all_quotas_fetched.emit()
    
    def process_accounts(self, accounts):
        """处理账户列表（在主线程中调用）"""
        # 创建后台线程来处理数据
        worker_thread = Thread(target=self.start_processing, args=(accounts,))
        worker_thread.daemon = True  # 将线程设为守护线程，确保主程序退出时线程也会终止
        worker_thread.start()
    
    def shutdown(self, wait=True):
        """关闭线程池"""
        # Check if pool exists and is not already shut down
        if not hasattr(self, 'thread_pool') or self._is_shutdown(self.thread_pool):
            print("QuotaFetcherManager: Thread pool not initialized or already shut down.")
            return

        print(f"QuotaFetcherManager: Shutting down thread pool (wait={wait})...")
        try:
            # Attempt to cancel pending futures and shut down
            self.thread_pool.shutdown(wait=wait, cancel_futures=True)
            print("QuotaFetcherManager: Thread pool shutdown complete.")

            # Optional: Keep platform-specific sleep if needed for resource release,
            # though shutdown with wait=True should handle most cases.
            # if self.platform == \"win32\":
            #     time.sleep(0.5)
            # else:
            #     time.sleep(0.2)
        except Exception as e:
            print(f"关闭线程池时出错: {str(e)}")

    def _is_shutdown(self, executor):
        """Helper to check if a ThreadPoolExecutor is shut down."""
        # Accessing internal flag _shutdown is not ideal but a common workaround
        return getattr(executor, '_shutdown', False) 