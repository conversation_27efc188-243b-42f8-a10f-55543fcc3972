#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
批量删除对话框模块
提供批量删除账户的对话框
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QCheckBox, QFrame, QButtonGroup, QComboBox, QLineEdit, QWidget
)
from PySide6.QtCore import Qt


from theme import Theme
from utils import Utils
from logger import info, error
from widgets.dialog import StyledDialog
from ui.dialogs.delete_type_dialog import DeleteTypeDialog
from ui.dialogs.delete_quota_dialog import DeleteQuotaDialog
from account.logout_batch import LogoutBatcher, LogoutResult
import asyncio


class BatchDeleteDialog(QFrame):
    """批量删除账户对话框类"""
    
    def __init__(self, main_window, parent=None):
        """初始化批量删除对话框
        
        Args:
            main_window: 主窗口实例 (CursorAccountManager)
            parent: 父窗口
        """
        super().__init__(parent)
        self.main_window = main_window
        self.dialog = StyledDialog(parent or main_window, "批量删除账户")
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        # 添加说明文本
        description = QLabel("选择删除账户的方式：")
        description.setStyleSheet(f"""
                color: {Theme.TEXT_PRIMARY};
                font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        self.dialog.addWidget(description)
        
        # 创建条件选择区域的背景框架
        conditions_frame = QFrame()
        conditions_frame.setObjectName("conditionsFrame")
        conditions_frame.setStyleSheet(f"""
            #conditionsFrame {{
                background-color: #121317;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        
        frame_layout = QVBoxLayout(conditions_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        frame_layout.setSpacing(15)
        
        # 创建删除方式选择区域
        delete_type_layout = QHBoxLayout()
        delete_type_layout.setSpacing(10)
        
        # 添加删除方式选择标签
        delete_type_label = QLabel("删除方式：")
        delete_type_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        delete_type_layout.addWidget(delete_type_label)
        
        # 删除方式下拉菜单
        self.delete_type_combo = QComboBox()
        self.delete_type_combo.addItems(["按账户类型删除", "按邮箱域名删除"])
        self.delete_type_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px 8px 15px;
                min-width: 120px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QComboBox:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: right center;
                width: 20px;
                border-left: none;
                padding-right: 5px;
            }}
            QComboBox::down-arrow {{
                width: 10px;
                height: 10px;
                image: none;
                border-top: 5px solid {Theme.TEXT_PRIMARY};
                border-right: 5px solid transparent;
                border-left: 5px solid transparent;
            }}
            QComboBox QAbstractItemView {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                selection-background-color: {Theme.ACCENT};
                outline: none;
                padding: 5px;
            }}
            QComboBox QAbstractItemView::item {{
                padding: 8px 10px;
                min-height: 25px;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            QComboBox QAbstractItemView::item:hover {{
                background-color: #1E2128;
            }}
            QComboBox QAbstractItemView::item:selected {{
                background-color: {Theme.ACCENT};
                color: white;
            }}
        """)
        delete_type_layout.addWidget(self.delete_type_combo)
        
        # 添加删除方式布局到主框架
        frame_layout.addLayout(delete_type_layout)
        
        # 创建一个堆叠容器来切换不同的选项界面
        self.options_container = QWidget()
        options_layout = QVBoxLayout(self.options_container)
        options_layout.setContentsMargins(0, 0, 0, 0)
        options_layout.setSpacing(10)
        
        # 创建账户类型选择区域（初始可见）
        self.account_type_container = QWidget()
        account_type_layout = QHBoxLayout(self.account_type_container)
        account_type_layout.setContentsMargins(0, 0, 0, 0)
        account_type_layout.setSpacing(10)
        
        # 添加账户类型选择标签
        account_type_label = QLabel("账户类型：")
        account_type_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        account_type_layout.addWidget(account_type_label)
        
        # 账户类型下拉菜单 - 基于API账户类型
        self.type_combo = QComboBox()
        self.type_combo.addItems(["Pro用户", "试用用户", "免费用户", "未知类型"])
        self.type_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px 8px 15px;
                min-width: 120px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QComboBox:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: right center;
                width: 20px;
                border-left: none;
                padding-right: 5px;
            }}
            QComboBox::down-arrow {{
                width: 10px;
                height: 10px;
                image: none;
                border-top: 5px solid {Theme.TEXT_PRIMARY};
                border-right: 5px solid transparent;
                border-left: 5px solid transparent;
            }}
            QComboBox QAbstractItemView {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                selection-background-color: {Theme.ACCENT};
                outline: none;
                padding: 5px;
            }}
            QComboBox QAbstractItemView::item {{
                padding: 8px 10px;
                min-height: 25px;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            QComboBox QAbstractItemView::item:hover {{
                background-color: #1E2128;
            }}
            QComboBox QAbstractItemView::item:selected {{
                background-color: {Theme.ACCENT};
                color: white;
            }}
        """)
        account_type_layout.addWidget(self.type_combo)
        

        
        # 创建邮箱域名选择区域（新增）
        self.domain_container = QWidget()
        domain_layout = QHBoxLayout(self.domain_container)
        domain_layout.setContentsMargins(0, 0, 0, 0)
        domain_layout.setSpacing(10)
        
        # 添加邮箱域名选择标签
        domain_label = QLabel("邮箱域名：")
        domain_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        domain_layout.addWidget(domain_label)
        
        # 邮箱域名下拉菜单
        self.domain_combo = QComboBox()
        # 从账户中提取所有不同的邮箱域名
        domains = self._extract_email_domains()
        if domains:
            self.domain_combo.addItems(domains)
        else:
            self.domain_combo.addItem("无可用域名")
        
        self.domain_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px 8px 15px;
                min-width: 200px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QComboBox:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: right center;
                width: 20px;
                border-left: none;
                padding-right: 5px;
            }}
            QComboBox::down-arrow {{
                width: 10px;
                height: 10px;
                image: none;
                border-top: 5px solid {Theme.TEXT_PRIMARY};
                border-right: 5px solid transparent;
                border-left: 5px solid transparent;
            }}
            QComboBox QAbstractItemView {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                selection-background-color: {Theme.ACCENT};
                outline: none;
                padding: 5px;
            }}
            QComboBox QAbstractItemView::item {{
                padding: 8px 10px;
                min-height: 25px;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            QComboBox QAbstractItemView::item:hover {{
                background-color: #1E2128;
            }}
            QComboBox QAbstractItemView::item:selected {{
                background-color: {Theme.ACCENT};
                color: white;
            }}
        """)
        domain_layout.addWidget(self.domain_combo)
        
        # 添加各个容器到选项容器
        options_layout.addWidget(self.account_type_container)
        options_layout.addWidget(self.domain_container)

        # 默认只显示账户类型容器
        self.account_type_container.setVisible(True)
        self.domain_container.setVisible(False)
        
        # 添加选项容器到主框架
        frame_layout.addWidget(self.options_container)
        
        # 连接删除方式变化信号
        self.delete_type_combo.currentIndexChanged.connect(self._on_delete_type_changed)
        
        # 添加框架到对话框
        self.dialog.addWidget(conditions_frame)
        
        # 添加"同时注销账户"复选框
        self.logout_checkbox = QCheckBox("同时注销账户（推荐）")
        self.logout_checkbox.setChecked(True)
        self.logout_checkbox.setStyleSheet(f"""
            QCheckBox {{
                color: {Theme.TEXT_SECONDARY};
                font-size: {Theme.FONT_SIZE_SMALL};
                spacing: 5px;
            }}
            QCheckBox::indicator {{
                width: 15px;
                height: 15px;
                border: 1px solid {Theme.BORDER};
                border-radius: 3px;
                background-color: #2A2E36;
            }}
            QCheckBox::indicator:checked {{
                background-color: {Theme.ACCENT};
                border: 1px solid {Theme.ACCENT};
            }}
            QCheckBox::indicator:unchecked:hover {{
                border: 1px solid {Theme.ACCENT};
            }}
            QCheckBox::indicator:checked:hover {{
                background-color: {Theme.ACCENT_HOVER};
                border: 1px solid {Theme.ACCENT_HOVER};
            }}
        """)
        self.dialog.addWidget(self.logout_checkbox)
        
        # 使用 StyledDialog.addButtons 添加标准样式的按钮
        confirm_btn = self.dialog.addButtons(confirm_text="确认删除", cancel_text="取消", confirm_color=Theme.ERROR)
        # 将确认按钮连接到处理函数 (取消按钮已在addButtons内部连接到reject)
        confirm_btn.clicked.connect(self._on_confirm_clicked)
    
    def _on_delete_type_changed(self, index):
        """删除方式改变时的处理函数"""
        # 首先隐藏所有选项
        for i in range(self.options_container.layout().count()):
            widget = self.options_container.layout().itemAt(i).widget()
            if widget:
                widget.setVisible(False)

        # 按账户类型删除
        if index == 0:
            self.account_type_container.setVisible(True)
        # 按邮箱域名删除
        elif index == 1:
            self.domain_container.setVisible(True)
        
        # 强制更新布局
        self.options_container.layout().update()
        self.options_container.layout().activate()
        
        # 确保对话框大小正确调整
        self.dialog.layout().activate()
        self.dialog.adjustSize()
    
    def _on_confirm_clicked(self):
        """确认按钮点击处理"""
        self.delete_accounts_batch()
    
    def delete_accounts_batch(self):
        # 1. 获取删除条件和注销标志
        do_logout = self.logout_checkbox.isChecked()
        delete_type_index = self.delete_type_combo.currentIndex()
        
        # 根据不同的删除类型，准备条件参数和确认消息文本
        criteria = {}
        confirm_title = ""
        confirm_message = ""
        
        if delete_type_index == 0: # 按类型
            account_type = self.type_combo.currentText()
            criteria = {'type': account_type}
            confirm_title = "按类型删除账户"
            confirm_message = f"确定要删除所有【{account_type}】类型的账户吗？"
        elif delete_type_index == 1: # 按域名
            domain = self.domain_combo.currentText()
            if domain == "无可用域名":
                self.main_window.show_toast("没有可用的邮箱域名", error=True)
                return
            criteria = {'domain': domain}
            confirm_title = "按邮箱域名删除账户"
            confirm_message = f"确定要删除所有邮箱域名为 {domain} 的账户吗？"
        else:
             self.main_window.show_toast("未知的删除类型", error=True)
             return

        # 2. 弹出二次确认对话框
        self.dialog.hide()
        if not Utils.confirm_message(self.main_window, confirm_title, confirm_message):
            # 用户取消则直接关闭整个批量删除对话框，不再返回选择界面
            self.dialog.accept()  # 关闭对话框
            return

        # 3. 用户确认后，调用统一处理函数，传递删除条件和注销标志
        self._initiate_logout_and_delete(delete_type_index, criteria, do_logout)

    def _filter_accounts_for_deletion(self, delete_type_index, criteria):
        """根据删除条件筛选账户列表 (使用精确UI逻辑)"""
        accounts = self.main_window.account_data.accounts
        filtered_accounts = []
            
        if delete_type_index == 0: # 按类型
            account_type = criteria.get('type')
            # 使用API账户类型信息进行筛选
            filtered_accounts = [acc for acc in accounts if self._check_account_type_by_api(acc, account_type)]

        elif delete_type_index == 1: # 按域名
            domain = criteria.get('domain')
            if domain:
                for acc in accounts:
                    email = acc.get('email', '')
                    if email and "@" in email and email.split("@")[1].lower() == domain.lower():
                        filtered_accounts.append(acc)
                        
        info(f"筛选条件: {criteria}, 精确UI逻辑筛选出的账户数: {len(filtered_accounts)}")
        return filtered_accounts

    def _initiate_logout_and_delete(self, delete_type_index, criteria, do_logout):
        """统一处理函数：筛选 -> 删除+UI更新 -> (可选)后台注销"""
        
        info("开始批量删除流程...")
        # 1. 执行唯一一次筛选
        accounts_to_delete = self._filter_accounts_for_deletion(delete_type_index, criteria)
        
        if not accounts_to_delete:
            info("未找到符合条件的账户，操作结束。")
            self.main_window.show_toast("未找到符合条件的账户")
            self.dialog.accept() # 关闭批量删除对话框
            return

        # 2. (可选) 额度检查
        if delete_type_index in [0, 1]: 
            accounts_without_quota = [acc for acc in accounts_to_delete if 'real_usage' not in acc]
            if accounts_without_quota:
                message = f"将删除的 {len(accounts_to_delete)} 个账户中，有 {len(accounts_without_quota)} 个未获取额度信息，是否先刷新所有账户额度？\n(选择否则继续删除)"
                if Utils.confirm_message(self.main_window, "建议先刷新额度", message):
                    self.dialog.accept() # 关闭批量删除对话框
                    self.main_window.show_toast("正在获取账户额度，请稍候...")
                    def restart_process_after_quota():
                         info("额度刷新完成，重新启动批量删除流程...")
                         self._initiate_logout_and_delete(delete_type_index, criteria, do_logout)
                    try:
                         if hasattr(self.main_window, 'quotaFetcher'):
                              self.main_window.quotaFetcher.all_quotas_fetched.disconnect()
                    except TypeError:
                         pass
                    except Exception as e:
                         error(f"断开旧额度刷新信号时出错: {e}")
                    self.main_window.quotaFetcher.all_quotas_fetched.connect(restart_process_after_quota)
                    self.main_window.fetch_all_accounts_quota(show_toast=True)
                    return
                else:
                    info("用户选择不刷新额度，继续执行删除...")

        # 3. 先执行删除和UI更新
        info(f"准备删除 {len(accounts_to_delete)} 个账户并更新UI...")
        self._execute_final_deletion(accounts_to_delete)
        # 注意：_execute_final_deletion 内部会关闭对话框 (self.dialog.accept())
        # 因此后续的注销需要在对话框关闭后仍在后台运行

        # 4. 删除和UI更新完成后，在后台启动注销（如果需要）
        if do_logout:
            info(f"UI更新完成，开始在后台注销 {len(accounts_to_delete)} 个账户...")
            # 创建注销器实例，但父对象设为None或main_window，确保不随对话框关闭而销毁
            # 需要确保LogoutBatcher的信号能连接到main_window的方法或全局函数来显示toast
            # 或者，将注销统计和toast显示逻辑移到main_window中处理
            
            # 临时的处理方式：直接创建，信号连接到只显示toast的lambda
            # 更好的方式是改造LogoutBatcher或在MainWindow中处理信号
            batcher = LogoutBatcher(accounts_to_delete, parent=self.main_window) # 尝试设置父对象
            
            def on_logout_finished(results):
                # 只显示注销统计 toast
                stat = {LogoutResult.SUCCESS:0, LogoutResult.DISABLED:0, LogoutResult.REPEATED:0, LogoutResult.FAILED:0}
                for res in results.values():
                    if res in stat: stat[res] += 1
                msg = f"后台注销完成：共 {len(accounts_to_delete)} 个，成功 {stat[LogoutResult.SUCCESS]}，禁用 {stat[LogoutResult.DISABLED]}，重复 {stat[LogoutResult.REPEATED]}，失败 {stat[LogoutResult.FAILED]}"
                # 确保toast在主线程显示
                # 如果main_window可能已关闭，需要更健壮的处理
                try:
                     if self.main_window and self.main_window.isVisible():
                          self.main_window.show_toast(msg)
                     else:
                          info(f"后台注销完成，但主窗口已关闭，Toast未显示: {msg}")
                except Exception as e:
                     error(f"显示后台注销Toast时出错: {e}")
                # 清理batcher对象? 或者依赖父对象管理
                # batcher.deleteLater() ? 如果设置了父对象，可能不需要手动删除
                pass
            
            try:
                 # 在连接信号前检查batcher是否有效
                 if batcher:
                      batcher.all_logout_finished.connect(on_logout_finished)
                      batcher.start_logout() # 启动后台注销
                 else:
                      error("LogoutBatcher实例创建失败，无法启动后台注销")
            except Exception as e:
                 error(f"启动后台注销时出错: {e}")
        else:
            info("用户未选择注销。")

    def _execute_final_deletion(self, accounts_to_delete):
        """执行最终的账户数据删除和UI更新"""
        if not accounts_to_delete:
            info("最终删除列表为空，操作结束。")
            self.dialog.accept() # 关闭对话框
            return

        deleted_emails = [acc.get("email", "") for acc in accounts_to_delete if acc.get("email")]
        info(f"开始最终删除 {len(deleted_emails)} 个账户: {', '.join(deleted_emails)}")

        try:
            # 1. 更新内存中的账户列表
            current_emails = {acc.get("email") for acc in self.main_window.account_data.accounts}
            emails_to_delete_set = set(deleted_emails)
            self.main_window.account_data.accounts = [
                acc for acc in self.main_window.account_data.accounts 
                if acc.get("email") not in emails_to_delete_set
            ]
            
            # 2. 保存更改到文件
            save_success = self.main_window.account_data.save_accounts()
            if not save_success:
                 error("保存账户数据失败！删除操作未完全生效。")
                 self.main_window.show_toast("错误：保存账户文件失败！", error=True)
                 # 即使保存失败，后续UI更新也应尽量进行
            
            info(f"成功从数据源删除 {len(deleted_emails)} 个账户")
            self.main_window.show_toast(f"已删除 {len(deleted_emails)} 个账户")

            # 3. 清除UI中的行
            for email in deleted_emails:
                if email in self.main_window.account_rows:
                    row_widget = self.main_window.account_rows.pop(email) # 从字典移除并获取
                    self.main_window.accounts_layout.removeWidget(row_widget)
                    row_widget.deleteLater()
            info("从UI中移除已删除账户完成")

            # 4. 更新账户计数
            self.main_window._update_accounts_count()

            # 5. 更新当前账户UI（如果当前账户被删除）
            if self.main_window.current_email in deleted_emails:
                info(f"当前账户 {self.main_window.current_email} 在删除列表中，需要切换当前账户")
                if self.main_window.account_data.accounts:
                    new_current = self.main_window.account_data.accounts[0]
                    new_email = new_current.get("email", "")
                    self.main_window.current_email = new_email
                    info(f"切换当前账户至: {new_email}")
                else:
                    self.main_window.current_email = ""
                    info("没有可用账户，清空当前账户")
                self.main_window.load_current_account() # 更新首页UI

            # 6. 重新排序UI（可选，如果需要保持排序）
            self.main_window._sort_accounts_without_rebuild_ui()
            info("重新排序账户列表完成")

            # 7. 刷新剩余账户额度（如果还有账户）
            if self.main_window.account_data.accounts:
                info(f"开始刷新剩余账户额度数据（剩余{len(self.main_window.account_data.accounts)}个账户）")
                self.main_window.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=False)

            info(f"批量删除操作完成: 共删除 {len(deleted_emails)} 个账户")

        except Exception as e:
            error_msg = str(e)
            error(f"执行最终批量删除时发生异常: {error_msg}")
            self.main_window.show_toast(f"删除账户时出错: {error_msg}", error=True)
        finally:
             self.dialog.accept() # 确保对话框最终关闭

    def _extract_email_domains(self):
        """从账户列表中提取所有不同的邮箱域名"""
        domains = set()
        for account in self.main_window.account_data.accounts:
            email = account.get("email", "")
            if email and "@" in email:
                domain = email.split("@")[1].lower()
                domains.add(domain)
        return sorted(list(domains))  # 返回排序后的域名列表
    
    def exec(self):
        """执行对话框"""
        return self.dialog.exec() 



    def _check_account_type_by_api(self, acc, target_type):
        """基于API账户类型信息判断账户类型"""
        quota_data = acc.get("quota_data", {})
        account_type_info = quota_data.get("account_type_info")

        if not account_type_info:
            # 如果没有API账户类型信息，归为未知类型
            return target_type == "未知类型"

        membership_type = account_type_info.get("membershipType")

        # 根据API返回的membership_type判断
        if target_type == "Pro用户":
            return membership_type == "pro"
        elif target_type == "试用用户":
            return membership_type == "free_trial"
        elif target_type == "免费用户":
            return membership_type == "free"
        elif target_type == "未知类型":
            return membership_type not in ["pro", "free_trial", "free"]

        return False