"""
动画化部件模块
包含带有动画效果的界面组件，用于增强用户体验
"""

import re
from PySide6.QtWidgets import (
    QStackedWidget, QLabel
)
from PySide6.QtCore import (
    Qt, QPropertyAnimation, QEasingCurve, QParallelAnimationGroup, 
    QPoint, Signal, QAbstractAnimation, Property, QTimer
)
from PySide6.QtGui import QColor, QPainter, QBrush, QLinearGradient
import platform

from theme import Theme
from widgets.styled_widgets import StyledProgressBar


class AnimatedStackedWidget(QStackedWidget):
    """带有动画效果的堆叠窗口部件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.m_direction = Qt.Orientation.Horizontal
        self.m_speed = 300
        self.m_animationtype = QEasingCurve.Type.OutCubic
        self.m_current = 0
        self.m_next = 0
        self.m_wrap = False
        self.m_pnow = QPoint(0, 0)
        self.m_active = False
        
    def setDirection(self, direction):
        """设置动画方向：Qt.Orientation.Horizontal（水平）或Qt.Orientation.Vertical（垂直）"""
        self.m_direction = direction
        
    def setSpeed(self, speed):
        """设置动画速度"""
        self.m_speed = speed
        
    def setAnimation(self, animation_type):
        """设置动画曲线类型"""
        self.m_animationtype = animation_type
        
    def setWrap(self, wrap):
        """设置是否循环切换"""
        self.m_wrap = wrap
        
    def slideInNext(self):
        """滑动到下一个窗口"""
        now = self.currentIndex()
        if self.m_wrap or now < self.count() - 1:
            self.slideInIdx(now + 1)
            
    def slideInPrev(self):
        """滑动到前一个窗口"""
        now = self.currentIndex()
        if self.m_wrap or now > 0:
            self.slideInIdx(now - 1)
            
    def slideInIdx(self, idx, direction=None):
        """滑动到指定索引窗口"""
        if idx > self.count() - 1:
            if self.m_wrap:
                idx = 0
            else:
                idx = self.count() - 1
        elif idx < 0:
            if self.m_wrap:
                idx = self.count() - 1
            else:
                idx = 0
                
        if self.m_active:
            return
            
        self.m_active = True
        
        # 记录当前索引和下一个索引
        self.m_current = self.currentIndex()
        self.m_next = idx
        
        # 如果当前与目标相同则返回
        if self.m_current == self.m_next:
            self.m_active = False
            return
            
        # 根据切换方向设置偏移
        if direction is not None:
            directionhint = direction
        elif self.m_current < self.m_next:
            directionhint = self.m_direction
        else:
            directionhint = Qt.Orientation.Horizontal if self.m_direction == Qt.Orientation.Vertical else Qt.Orientation.Vertical
            
        # 获取当前窗口的矩形区域
        current_widget = self.widget(self.m_current)
        next_widget = self.widget(self.m_next)
        self.m_pnow = self.pos()
        
        # 设置下一个窗口的初始位置
        if directionhint == Qt.Orientation.Horizontal:
            if self.m_current < self.m_next:
                # 向左滚动
                next_widget.setGeometry(self.width(), 0, self.width(), self.height())
                end_x = 0
                end_y = 0
                start_x = -self.width()
                start_y = 0
            else:
                # 向右滚动
                next_widget.setGeometry(-self.width(), 0, self.width(), self.height())
                end_x = 0
                end_y = 0
                start_x = self.width()
                start_y = 0
        else:
            if self.m_current < self.m_next:
                # 向上滚动
                next_widget.setGeometry(0, self.height(), self.width(), self.height())
                end_x = 0
                end_y = 0
                start_x = 0
                start_y = -self.height()
            else:
                # 向下滚动
                next_widget.setGeometry(0, -self.height(), self.width(), self.height())
                end_x = 0
                end_y = 0
                start_x = 0
                start_y = self.height()
        
        # 显示下一个窗口
        next_widget.show()
        next_widget.raise_()
        
        # 创建动画组
        self.anim_group = QParallelAnimationGroup(self)
        
        # 当前窗口的滑出动画
        animation_current = QPropertyAnimation(current_widget, b"pos")
        animation_current.setDuration(self.m_speed)
        animation_current.setEasingCurve(self.m_animationtype)
        animation_current.setStartValue(QPoint(0, 0))
        animation_current.setEndValue(QPoint(start_x, start_y))
        
        # 下一个窗口的滑入动画
        animation_next = QPropertyAnimation(next_widget, b"pos")
        animation_next.setDuration(self.m_speed)
        animation_next.setEasingCurve(self.m_animationtype)
        animation_next.setStartValue(QPoint(-start_x, -start_y))
        animation_next.setEndValue(QPoint(end_x, end_y))
        
        # 添加动画到动画组
        self.anim_group.addAnimation(animation_current)
        self.anim_group.addAnimation(animation_next)
        
        # 连接动画结束信号
        self.anim_group.finished.connect(self.animationDoneSlot)
        
        # 启动动画
        self.anim_group.start()
    
    def animationDoneSlot(self):
        """动画完成后的槽函数"""
        # 设置当前窗口索引
        self.setCurrentIndex(self.m_next)
        
        # 重置所有窗口的位置
        for i in range(self.count()):
            widget = self.widget(i)
            widget.setGeometry(0, 0, self.width(), self.height())
            
        # 重置动画状态
        self.m_active = False
        
        # 重新设置窗口位置
        self.move(self.m_pnow)
        
        # 发射动画完成信号
        self.animationFinished.emit()
    
    # 定义动画完成信号
    animationFinished = Signal()


class AnimatedNumberLabel(QLabel):
    """带有动画效果的数字标签"""
    
    def __init__(self, parent=None, prefix="", suffix="", duration=1500):
        """
        初始化数字动画标签
        
        Args:
            parent: 父窗口部件
            prefix: 数字前缀文本，如"剩余时间: "
            suffix: 数字后缀文本，如"天"
            duration: 动画持续时间(毫秒)
        """
        super().__init__(parent)
        self.prefix = prefix
        self.suffix = suffix
        self.duration = duration
        
        # 动画相关变量
        self._current_value = 0
        self._target_value = 0
        self._animation_value = 0
        self._animation = QPropertyAnimation(self, b"animationValue")
        self._animation.setEasingCurve(QEasingCurve.Type.OutQuad)  # 使用更平滑的曲线
        self._animation.setDuration(self.duration)
        self._animation.valueChanged.connect(self._update_text)
        self._animation.finished.connect(self._on_animation_finished)
        
        # 调试输出
        self._last_value = -1
        
        # 特殊文本模式（非数字状态）
        self._special_text = None
        
        # 默认样式
        self.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            padding: 8px 12px;  /* 修改为与QLabel相同的padding */
            background-color: transparent;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            font-weight: bold;
            color: {Theme.SUCCESS};
        """)
        
        # 添加跳过动画标志
        self._skip_animation = False
        
        # 确保属性动画完整显示
        QTimer.singleShot(100, self._init_animation)
    
    def reset_animation(self):
        """重置并重新启动动画"""
        try:
            # 如果设置了跳过动画标志，直接设置最终值而不启动动画
            if self._skip_animation:
                if not self._special_text and self._target_value > 0:
                    self._animation_value = self._target_value
                    self._current_value = self._target_value
                    self._update_text()
                    # 重置标志，不影响后续动画
                    self._skip_animation = False
                elif self._special_text:
                    self._update_text()
                return
            
            # 尝试重置并重启动画，不论是否有特殊文本
            # 停止当前可能在运行的动画
            if self._animation.state() == QAbstractAnimation.State.Running:
                self._animation.stop()
            
            # 只在有目标值且不是特殊文本时执行动画
            if not self._special_text and self._target_value > 0:
                # 重置动画值为0并设置从0到目标值的动画
                self._animation_value = 0
                self._current_value = 0
                self._last_value = -1  # 重置上次值以确保更新UI
                
                # 配置动画
                self._animation.setStartValue(0.0)
                self._animation.setEndValue(self._target_value)
                
                # 根据数值大小调整动画持续时间，大数值动画时间更长
                adjusted_duration = min(self.duration, max(1000, int(self._target_value * 30)))
                self._animation.setDuration(adjusted_duration)
                
                # 更新UI以显示初始值
                self._update_text()
                
                # 启动动画
                QTimer.singleShot(50, self._animation.start)  # 短暂延迟确保UI准备好
            elif self._special_text:
                # 如果是特殊文本，仅更新显示
                self._update_text()
        except Exception as e:
            print(f"重置数字标签动画出错: {str(e)}")
            # 尝试恢复到可用状态
            try:
                if self._target_value > 0:
                    self._current_value = self._target_value
                    self._update_text()
            except:
                pass
    
    def _init_animation(self):
        """初始化动画设置"""
        try:
            # 仅在有具体值时初始化
            if not self._special_text and self._target_value > 0:
                self.setValue(self._target_value, animate=True)
        except Exception as e:
            print(f"初始化动画出错: {str(e)}")
    
    def _on_animation_finished(self):
        """动画完成时的回调"""
        # 确保最终值是精确的目标值
        self._animation_value = self._target_value
        self._current_value = self._target_value
        self._update_text()  # 最后一次更新
    
    def getAnimationValue(self):
        """获取动画当前值"""
        return self._animation_value
    
    def setAnimationValue(self, value):
        """设置动画当前值并更新显示"""
        self._animation_value = value
        self._update_text()
    
    # 定义动画属性
    animationValue = Property(float, getAnimationValue, setAnimationValue)
    
    def _update_text(self):
        """根据当前动画值更新文本"""
        try:
            if self._special_text:
                # 如果有特殊文本，直接显示，不添加后缀
                # 检查特殊文本是否包含"已过期"或"加载中"
                if "已过期" in self._special_text or "加载中" in self._special_text or "尊贵的有钱人Pro用户" in self._special_text:
                    # 对于"已过期"、"加载中"和"尊贵的有钱人Pro用户"的情况，不添加后缀
                    self.setText(f"{self.prefix}{self._special_text}")
                else:
                    # 其他特殊文本仍然添加后缀
                    self.setText(f"{self.prefix}{self._special_text}{self.suffix}")
            else:
                # 否则显示动画的当前值（四舍五入到整数）
                value = round(self._animation_value)
                # 仅在值变化时更新UI，减少重绘
                if value != self._last_value:
                    self._last_value = value
                    self.setText(f"{self.prefix}{value}{self.suffix}")
        except Exception as e:
            print(f"更新动画标签文本出错: {str(e)}")
    
    def setValue(self, value, animate=True):
        """
        设置标签的值
        
        Args:
            value: 数值或特殊字符串(如"已过期")
            animate: 是否使用动画效果
        """
        try:
            # 停止任何正在运行的动画
            if self._animation.state() == QAbstractAnimation.State.Running:
                self._animation.stop()
            
            # 处理非数字值
            if not isinstance(value, (int, float)) or value < 0:
                self._special_text = str(value)
                self._animation_value = 0
                self._current_value = 0
                self._target_value = 0
                self._update_text()
                return
            
            # 清除特殊文本状态
            self._special_text = None
            
            # 设置目标值
            self._target_value = float(value)
            
            # 如果当前值未初始化，则从0开始
            if self._current_value <= 0:
                self._current_value = 0
            
            if animate and self._current_value != self._target_value:
                # 设置动画，总是从0开始递增而不是从当前值，这样效果更明显
                self._animation.setStartValue(0.0)
                self._animation.setEndValue(self._target_value)
                
                # 根据数值大小调整动画持续时间，大数值动画时间更长
                adjusted_duration = min(self.duration, max(1000, int(self._target_value * 30)))
                self._animation.setDuration(adjusted_duration)
                
                # 启动动画
                QTimer.singleShot(10, self._animation.start)  # 延迟启动以确保UI准备好
            else:
                # 不使用动画，直接设置值
                self._animation_value = self._target_value
                self._current_value = self._target_value
                self._update_text()
        except Exception as e:
            print(f"设置动画数字标签值出错: {str(e)}")
            # 如果出错，尝试直接设置文本
            self._special_text = str(value)
            self._update_text()
    
    def setColor(self, color):
        """设置文本颜色"""
        self.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            padding: 8px 12px;  /* 修改为与QLabel相同的padding */
            background-color: transparent;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            font-weight: bold;
            color: {color};
        """)
        
    def setSpecialText(self, text):
        """设置特殊文本（非数字值）"""
        self._special_text = text
        self._update_text()


class AnimatedProgressBar(StyledProgressBar):
    """支持额度数字平滑递增动画的进度条"""
    
    def __init__(self, parent=None, text_duration=2000, value_duration=1500):
        """
        初始化带数字动画的进度条
        
        Args:
            parent: 父窗口部件
            text_duration: 文本动画持续时间(毫秒)
            value_duration: 值动画持续时间(毫秒)
        """
        super().__init__(parent)
        
        # 额外的动画变量
        self._from_number = 0
        self._to_number = 0
        self._current_number = 0
        self._denominator = 150  # 默认分母
        self._text_animation = QPropertyAnimation(self, b"numberValue")
        self._text_animation.setEasingCurve(QEasingCurve.Type.OutQuad)
        self._text_animation.setDuration(text_duration)
        self._text_animation.valueChanged.connect(self._updateAnimatedText)
        self._text_animation.finished.connect(self._onTextAnimationFinished)
        
        # 添加跳过动画标志
        self._skip_animation = False
        
        # 检测操作系统平台
        self._is_macos = platform.system() == 'Darwin'
        # 确保文本可见
        self.setTextVisible(True)
    
    def reset_without_animation(self, store_original=True):
        """在不触发动画的情况下重置进度条，保护特殊格式
        
        Args:
            store_original: 是否存储原始值以便后续恢复，默认为True
        """
        try:
            # 检查当前格式是否为特殊文本，如果是则不重置
            current_format = self.format()
            if current_format in ["正在加载中", "未知", "无法获取"]:  # 移除了"无限制"
                return
            
            # 检查_current_format是否为特殊文本，如果是则不重置
            if hasattr(self, '_current_format') and self._current_format in ["未知", "无法获取"]:  # 移除了"无限制"
                return
                
            # 使用父类的实现
            super().reset_without_animation(store_original)
        except Exception as e:
            print(f"重置进度条失败: {str(e)}")
    
    def reset_animation(self):
        """重置并重新启动数字动画，与进度条填充动画同步"""
        try:
            # 首先检查当前格式是否为特殊文本，如果是则检查是否有实际数据
            if hasattr(self, '_current_format') and self._current_format in ["正在加载中", "未知", "无法获取"]:  # 移除了"无限制"
                # 获取最终值（使用相同的取值逻辑）
                has_real_value = False
                
                # 优先级1：使用进度条动画的目标值
                if hasattr(self, '_animation') and self._animation.state() == QAbstractAnimation.State.Running:
                    if self._animation.endValue() > 0:
                        has_real_value = True
                # 优先级2：使用进度条的当前值
                elif self.value() > 0:
                    has_real_value = True
                # 优先级3：使用持久值存储
                elif hasattr(self, '_persistent_value') and self._persistent_value > 0:
                    has_real_value = True
                
                # 如果没有实际数据，保留特殊文本状态，不进行重置
                if not has_real_value:
                    return
            
            # 特殊处理无限制格式的进度条 - 使其作为100/100的进度条处理
            if hasattr(self, '_current_format') and self._current_format == "无限制":
                # 停止任何正在运行的动画
                if self._text_animation.state() == QAbstractAnimation.State.Running:
                    self._text_animation.stop()
                
                # 重置当前值为0，与进度条填充动画一致
                self._current_number = 0
                self._to_number = 100.0  # 设置目标值为100
                
                # 配置动画从0到100，与进度条填充动画一致
                self._text_animation.setStartValue(0.0)
                self._text_animation.setEndValue(100.0)
                
                # 减少动画持续时间
                self._text_animation.setDuration(min(1200, self._text_animation.duration()))
                
                # 同步动画持续时间与进度条
                if hasattr(self, '_animation') and self._animation.state() == QAbstractAnimation.State.Running:
                    # 使用与进度条相同的动画持续时间
                    self._text_animation.setDuration(self._animation.duration())
                    # 使用与进度条相同的缓动曲线
                    self._text_animation.setEasingCurve(self._animation.easingCurve())
                    # 获取当前进度条动画的已播放时间，并从相同时间点开始数字动画
                    elapsed = self._animation.currentTime()
                    self._text_animation.setCurrentTime(elapsed)
                else:
                    # 使用与进度条相同的动画持续时间
                    self._text_animation.setDuration(self._animation.duration())
                    # 使用与进度条相同的缓动曲线
                    self._text_animation.setEasingCurve(self._animation.easingCurve())
                    
                # 更新文本以显示初始值
                self._updateAnimatedText()
                
                # 启动动画
                self._text_animation.start()
                return
            
            # 如果设置了跳过动画标志，直接设置最终值而不启动动画
            if self._skip_animation:
                # 获取目标值（使用相同的取值逻辑）
                target_value = 0
                
                # 优先级1：使用进度条动画的目标值
                if hasattr(self, '_animation') and self._animation.state() == QAbstractAnimation.State.Running:
                    target_value = self._animation.endValue()
                # 优先级2：使用进度条的当前值
                elif self.value() > 0:
                    target_value = self.value()
                # 优先级3：使用持久值存储
                elif hasattr(self, '_persistent_value') and self._persistent_value > 0:
                    target_value = self._persistent_value
                # 优先级4：尝试从格式中提取
                else:
                    try:
                        import re
                        format_str = self.format()
                        match = re.match(r"^(\d+)/(\d+)$", format_str)
                        if match:
                            target_value = int(match.group(1))
                    except:
                        pass
                
                # 直接设置最终值
                if target_value > 0:
                    self._current_number = float(target_value)
                    self._to_number = float(target_value)
                    self._updateAnimatedText()
                # 重置标志，不影响后续动画
                self._skip_animation = False
                return
                
            # 停止任何正在运行的动画
            if self._text_animation.state() == QAbstractAnimation.State.Running:
                self._text_animation.stop()
                
            # 获取数据来源的优先级：
            # 1. 进度条动画的目标值（如果动画正在运行）
            # 2. 进度条当前值
            # 3. 持久值存储
            # 4. 格式字符串中提取的值
            
            # 初始化目标值
            target_value = 0
            
            # 优先级1：使用进度条动画的目标值
            if hasattr(self, '_animation') and self._animation.state() == QAbstractAnimation.State.Running:
                target_value = self._animation.endValue()
            # 优先级2：使用进度条的当前值
            elif self.value() > 0:
                target_value = self.value()
            # 优先级3：使用持久值存储
            elif hasattr(self, '_persistent_value') and self._persistent_value > 0:
                target_value = self._persistent_value
            # 优先级4：尝试从格式中提取
            else:
                try:
                    import re
                    format_str = self.format()
                    match = re.match(r"^(\d+)/(\d+)$", format_str)
                    if match:
                        target_value = int(match.group(1))
                except:
                    pass
            
            # 只有在有目标值时才重设动画
            if target_value > 0:
                # 重置当前值为0，与进度条填充动画一致
                self._current_number = 0
                self._to_number = float(target_value)
                
                # 配置动画从0到目标值，与进度条填充动画一致
                self._text_animation.setStartValue(0.0)
                self._text_animation.setEndValue(self._to_number)
                
                # 减少动画持续时间
                self._text_animation.setDuration(min(1200, self._text_animation.duration()))
                
                # 同步动画持续时间与进度条
                if hasattr(self, '_animation') and self._animation.state() == QAbstractAnimation.State.Running:
                    # 使用与进度条相同的动画持续时间
                    self._text_animation.setDuration(self._animation.duration())
                    # 使用与进度条相同的缓动曲线
                    self._text_animation.setEasingCurve(self._animation.easingCurve())
                    # 获取当前进度条动画的已播放时间，并从相同时间点开始数字动画
                    elapsed = self._animation.currentTime()
                    self._text_animation.setCurrentTime(elapsed)
                else:
                    # 使用与进度条相同的动画持续时间
                    self._text_animation.setDuration(self._animation.duration())
                    # 使用与进度条相同的缓动曲线
                    self._text_animation.setEasingCurve(self._animation.easingCurve())
                    
                # 更新文本以显示初始值
                self._updateAnimatedText()
                
                # 启动动画
                self._text_animation.start()
        except Exception as e:
            # 降低日志级别，仅在调试时需要
            # print(f"重置进度条数字动画出错: {str(e)}")
            # 出错时尝试直接设置最终值
            try:
                if self.value() > 0:
                    self._current_number = float(self.value())
                    self._updateAnimatedText()
            except:
                pass
    
    def getNumberValue(self):
        """获取动画数字值"""
        return self._current_number
    
    def setNumberValue(self, value):
        """设置动画数字值"""
        self._current_number = value
        self._updateAnimatedText()
    
    # 定义动画属性
    numberValue = Property(float, getNumberValue, setNumberValue)
    
    def _updateAnimatedText(self):
        """更新动画数字格式"""
        # 检查是否为特殊文本格式，如果是则不修改
        if hasattr(self, '_current_format') and self._current_format in ["正在加载中", "未知", "无法获取", "无限制"]:
            # 只有当不是被实际数据替换的特殊格式才保留
            # 检查是否有实际数据（非零的_to_number）
            if self._to_number > 0:
                # 有实际数据，不保护特殊格式，允许更新为数值显示
                pass
            else:
                # 没有实际数据，保留特殊文本格式，不进行任何修改
                return
            
        # 检查是否为无限制模式
        if hasattr(self, '_current_format') and self._current_format == "无限制":
            # 无需更改格式，保持显示"无限制"
            return
            
        # 对于数字/最大值格式，更新为动画数字
        num = round(self._current_number)
        # 设置格式字符串（如"9/150"）但不触发父类的setFormat
        self._current_format = f"{num}/{self._denominator}"
        self.update()  # 强制重绘
    
    def _onTextAnimationFinished(self):
        """文本动画完成后的回调"""
        # 确保最终显示准确的数值
        self._current_number = self._to_number
        self._updateAnimatedText()
    
    def setValue(self, value):
        """重写setValue方法，为条形图添加动画，同时同步更新数字动画"""
        try:
            # 获取当前值和最大值
            max_value = self.maximum()
            current_format = self.format()
            
            # 检查当前格式，判断是否为特殊格式模式
            if current_format in ["无限制", "无法获取"]:
                # 对于特殊格式，更新持久存储确保它们在页面切换后仍然保留
                if hasattr(self, '_persistent_format'):
                    self._persistent_format = current_format
                
                # 根据格式设置适当的值和颜色
                if current_format == "无限制":
                    super().setValue(100)  # 100%填充
                    # 确保在StyledProgressBar中也保存这些值
                    if hasattr(self, '_persistent_value'):
                        self._persistent_value = 100
                        self._persistent_max = 100
                else:  # "无法获取"
                    super().setValue(0)  # 0%填充
                    # 确保在StyledProgressBar中也保存这些值
                    if hasattr(self, '_persistent_value'):
                        self._persistent_value = 0
                        self._persistent_max = 100
                
                return
                
            # 如果最大值有效，自动更新格式文本，触发数字动画
            if max_value > 0:
                # 设置格式为"新值/最大值"，这会触发数字动画
                self.setFormat(f"{value}/{max_value}")
                
            # 调用父类的setValue方法设置进度条的值动画
            super().setValue(value)
        except Exception as e:
            print(f"设置进度条值和数字动画出错: {str(e)}")
            # 出错时简单调用父类方法
            super().setValue(value)
            
    def restore_original_value(self):
        """恢复原始值并同步启动数字动画"""
        try:
            # 首先检查当前格式是否为特殊文本，如果是则检查是否有实际数据
            if hasattr(self, '_current_format') and self._current_format in ["正在加载中", "未知", "无法获取", "无限制"]:
                # 获取最终值（使用相同的取值逻辑）
                has_real_value = False
                
                # 优先级1：使用persistent_value
                if hasattr(self, '_persistent_value') and self._persistent_value > 0:
                    has_real_value = True
                # 优先级2：使用original_value
                elif hasattr(self, '_original_value') and self._original_value > 0:
                    has_real_value = True
                
                # 如果没有实际数据，保留特殊文本状态，不进行重置
                if not has_real_value:
                    return
                
            # 检查持久格式是否为特殊格式
            if hasattr(self, '_persistent_format') and self._persistent_format in ["无限制", "无法获取", "未知", "正在加载中"]:
                # 对于特殊格式，先检查是否有实际数据覆盖
                has_real_persistent = False
                
                # 优先级1: 使用persistent_value
                if hasattr(self, '_persistent_value') and self._persistent_value > 0:
                    has_real_persistent = True
                
                # 如果没有实际数据覆盖，保留特殊格式
                if not has_real_persistent:
                    # 对于特殊格式，直接恢复格式字符串，不启动数字动画
                    self._current_format = self._persistent_format
                    
                    # 设置适当的值和颜色
                    if self._persistent_format == "无限制":
                        super().setValue(100)  # 100%填充
                    elif self._persistent_format == "未知" or self._persistent_format == "正在加载中":
                        super().setValue(0)  # 0%填充
                    else:  # "无法获取" 
                        super().setValue(0)  # 0%填充
                    
                    # 确保格式正确显示
                    super().setFormat(self._persistent_format)
                    
                    # 强制更新
                    self.update()
                    return
            
            # 重置数字动画当前值为0
            self._current_number = 0
            # 更新界面显示
            self._updateAnimatedText()
            
            # 调用父类的restore_original_value方法触发进度条填充动画
            # 这会优先使用_persistent_value，其次是_original_value，最后尝试从格式提取
            super().restore_original_value()
            
            # 确保数字动画也正确启动 - 使用延迟确保进度条动画已正确设置
            QTimer.singleShot(10, self.reset_animation)
        except Exception as e:
            # 降低日志级别，仅在调试时需要
            # print(f"恢复动画进度条原始值时出错: {str(e)}")
            try:
                # 出错时尝试使用父类方法
                super().restore_original_value()
            except:
                pass
    
    def setFormat(self, format_str):
        """重写setFormat方法，检测是否为"数字/最大值"格式并添加动画"""
        # 立即存储格式用于绘制
        self._current_format = format_str
        
        # 保存特殊格式到持久存储
        if format_str in ["无限制", "无法获取", "未知", "正在加载中"]:
            # 更新持久存储以便在页面切换后能正确恢复
            if hasattr(self, '_persistent_format'):
                self._persistent_format = format_str
                
            # 根据特殊格式设置持久值，确保重置后正确恢复
            if format_str == "无限制":
                if hasattr(self, '_persistent_value'):
                    self._persistent_value = 100
                    self._persistent_max = 100
            elif format_str in ["无法获取", "未知", "正在加载中"]:
                if hasattr(self, '_persistent_value'):
                    self._persistent_value = 0
                    self._persistent_max = 100
            
            # 直接使用父类方法设置格式
            super().setFormat(format_str)
            return
        
        # 检查是否是"数字/最大值"格式（如"9/150"）
        import re
        match = re.match(r"^(\d+)/(\d+)$", format_str)
        
        if match:
            try:
                # 提取分子和分母
                numerator = int(match.group(1))
                self._denominator = int(match.group(2))
                
                # 当页面切换后，进度条尚未完全初始化，或进度条动画尚未启动
                # 直接将当前值设为0，确保数字动画能启动
                if self._animation.state() != QAbstractAnimation.State.Running:
                    self._current_number = 0
                
                # 停止任何运行中的动画
                if self._text_animation.state() == QAbstractAnimation.State.Running:
                    self._text_animation.stop()
                
                # 设置动画目标值
                self._to_number = float(numerator)
                
                # 获取进度条动画状态
                progress_animation_running = self._animation.state() == QAbstractAnimation.State.Running
                
                # 如果进度条正在运行动画，或者页面刚刚切换
                if progress_animation_running:
                    # 配置并启动动画，从0开始
                    self._text_animation.setStartValue(0.0)
                    self._text_animation.setEndValue(self._to_number)
                    self._text_animation.setDuration(self._animation.duration())
                    self._text_animation.setEasingCurve(self._animation.easingCurve())
                    self._text_animation.start()
                else:
                    # 如果进度条没有动画，直接设置值
                    self._current_number = self._to_number
                    self._updateAnimatedText()
                    
                # 更新持久数据，覆盖可能存在的特殊状态
                if hasattr(self, '_persistent_format'):
                    self._persistent_format = format_str
            except Exception as e:
                print(f"解析进度条格式出错: {str(e)}")
                # 出错时使用默认方法
                super().setFormat(format_str)
        else:
            # 对于其他格式，直接使用父类的方法
            super().setFormat(format_str)
    
    def paintEvent(self, event):
        # 创建绘制器
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)  # 抗锯齿
        
        # 获取进度条尺寸
        width = self.width()
        height = self.height()
        radius = height / 2  # 圆角半径为高度的一半，创建胶囊形状
        
        # 绘制背景 - 胶囊形状
        bg_color = QColor(Theme.CARD_LEVEL_1)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(bg_color)
        painter.drawRoundedRect(0, 0, width, height, radius, radius)
        
        try:
            # 计算填充宽度 - 使用动画值而不是实际值
            # 确保进度在有效范围内
            min_val = float(self.minimum())
            max_val = float(self.maximum())
            range_val = max_val - min_val
            
            if range_val <= 0:
                progress = 0
            else:
                progress = (self._animation_value - min_val) / range_val
                progress = max(0.0, min(1.0, progress))
            
            # 使用round代替int，更准确的舍入
            chunk_width = round(width * progress)
            
            # 绘制填充部分 - 也是胶囊形状
            if chunk_width > 0:
                painter.setBrush(self._chunk_color)
                # 极小值特殊处理 - 绘制一个小圆形
                if chunk_width <= height:
                    # 绘制一个小于或等于高度的圆形
                    painter.drawRoundedRect(0, 0, height, height, radius, radius)
                    # 如果宽度小于高度，裁剪掉多余部分
                    if chunk_width < height:
                        # 创建一个矩形覆盖超出部分
                        painter.setBrush(bg_color)
                        painter.drawRect(chunk_width, 0, height - chunk_width, height)
                else:
                    # 正常绘制胶囊形状
                    painter.drawRoundedRect(0, 0, chunk_width, height, radius, radius)
                
                # 绘制流光效果（仅在填充部分上）
                if progress > 0:
                    # 创建裁剪区域，确保流光仅在进度条填充区域内显示
                    painter.setClipRect(0, 0, chunk_width, height)
                    
                    # 计算流光位置和宽度
                    glow_start = width * (self._glow_position - self._glow_width)
                    glow_end = width * (self._glow_position + self._glow_width)
                    
                    # 创建线性渐变作为流光效果
                    gradient = QLinearGradient(glow_start, 0, glow_end, 0)
                    base_color = QColor(self._chunk_color)
                    highlight_color = QColor(255, 255, 255, int(255 * self._glow_opacity))
                    
                    # 创建半透明的中间颜色，用于羽化效果
                    mid_opacity_1 = self._glow_opacity * 0.3
                    mid_opacity_2 = self._glow_opacity * 0.7
                    mid_color_1 = QColor(255, 255, 255, int(255 * mid_opacity_1))
                    mid_color_2 = QColor(255, 255, 255, int(255 * mid_opacity_2))
                    
                    # 设置渐变颜色，增加更多点实现平滑羽化
                    gradient.setColorAt(0.0, base_color)
                    gradient.setColorAt(0.2, mid_color_1)  # 添加过渡色
                    gradient.setColorAt(0.35, mid_color_2)  # 添加过渡色
                    gradient.setColorAt(0.5, highlight_color)  # 中心点
                    gradient.setColorAt(0.65, mid_color_2)  # 添加过渡色
                    gradient.setColorAt(0.8, mid_color_1)  # 添加过渡色
                    gradient.setColorAt(1.0, base_color)
                    
                    # 应用渐变并绘制
                    painter.setBrush(QBrush(gradient))
                    
                    # 重新绘制填充区域，但使用渐变
                    if chunk_width <= height:
                        painter.drawRoundedRect(0, 0, height, height, radius, radius)
                        if chunk_width < height:
                            painter.setBrush(bg_color)
                            painter.drawRect(chunk_width, 0, height - chunk_width, height)
                    else:
                        painter.drawRoundedRect(0, 0, chunk_width, height, radius, radius)
                    
                    # 清除裁剪区域
                    painter.setClipping(False)
        except Exception as e:
            print(f"进度条绘制错误: {str(e)}")
        
        # 绘制文本 - 使用当前格式而不是内部格式
        painter.setPen(QColor(Theme.TEXT_PRIMARY))
        painter.setFont(self.font())
        
        # 从_current_format获取文本，如果它包含格式字符串，手动替换
        display_text = self._current_format
        
        # 处理特殊格式字符串
        if display_text == "%p%":
            # 替换为实际百分比
            if self.maximum() > 0:
                percent = int((self._animation_value / self.maximum()) * 100)
                display_text = f"{percent}%"
            else:
                display_text = "0%"
        elif display_text == "%v/%m":
            # 替换为实际分数
            display_text = f"{int(self._animation_value)}/{self.maximum()}"
        
        # 如果没有设置_current_format，使用父类的text()
        if not display_text:
            display_text = self.text()
            
        # 为macOS提供特殊处理，确保文本显示
        if self._is_macos:
            # 在macOS上，使用更强的对比度和稍大的字体
            painter.setPen(QColor(Theme.TEXT_PRIMARY))
            font = self.font()
            font.setBold(True)
            painter.setFont(font)
            
            # 增加文本绘制的优先级
            painter.setOpacity(1.0)
        
        # 绘制文本，确保在所有平台上都可见
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, display_text)
        
        # 结束绘制
        painter.end()