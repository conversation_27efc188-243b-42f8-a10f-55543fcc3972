#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户数据模块
提供账户数据管理和额度查询功能
"""

import os
import sys
import json
import requests
from datetime import datetime, timedelta
from dateutil import parser, tz
from utils import get_app_data_dir  # 导入新添加的函数

# 账户配额相关字段
ACCOUNT_QUOTA_FIELDS = [
    "real_usage", "real_max_usage", "real_register_time", 
    "real_remaining_days", "quota_data", "api_register_time"
]


class AccountData:
    """账户数据管理类"""
    
    def __init__(self, accounts_file="cursor_accounts.json"):
        # 从设置文件加载自定义文件路径配置
        custom_file_path = self._get_custom_file_path()
        
        if custom_file_path:
            # 使用自定义路径
            self.accounts_file = custom_file_path
            print(f"使用自定义配置文件路径: {self.accounts_file}")
        else:
            # 使用默认路径 - 应用数据目录下的account子目录
            app_data_dir = get_app_data_dir()
            account_dir = os.path.join(app_data_dir, "account")
            
            # 确保account目录存在
            if not os.path.exists(account_dir):
                os.makedirs(account_dir, exist_ok=True)
                
            # 配置文件路径
            self.accounts_file = os.path.join(account_dir, accounts_file)
            print(f"正在使用默认配置文件路径: {self.accounts_file}")
        
        # 获取应用数据目录存放临时文件
        app_data_dir = get_app_data_dir()
        temp_quota_dir = os.path.join(app_data_dir, "temp_quota_data")
        
        # 确保临时目录存在
        if not os.path.exists(temp_quota_dir):
            os.makedirs(temp_quota_dir)
            
        # 创建临时数据文件路径 - 存储在应用数据目录
        self.temp_file = os.path.join(temp_quota_dir, "temp_quota_" + accounts_file)
        print(f"临时配额文件路径: {self.temp_file}")
        
        # 检查同名临时文件是否存在，如果存在则先删除
        if os.path.exists(self.temp_file):
            try:
                os.remove(self.temp_file)
                print(f"删除了已存在的临时文件: {self.temp_file}")
            except Exception as e:
                print(f"删除已存在临时文件失败: {str(e)}")
        
        # 加载账户数据
        self.accounts = self.load_accounts()
        
        # 从临时文件加载配额数据（如果有）
        self.load_quotas_from_temp()
    
    def _get_custom_file_path(self):
        """从设置文件中获取自定义账户文件路径"""
        try:
            # 获取settings.json文件路径
            settings_file = os.path.join(get_app_data_dir(), "settings.json")
            
            # 检查设置文件是否存在
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    
                    # 检查是否启用了自定义JSON文件路径
                    use_custom_json = settings.get("use_custom_json_file", False)
                    if use_custom_json:
                        # 获取自定义路径
                        custom_path = settings.get("custom_json_file_path", "")
                        if custom_path and os.path.exists(custom_path):
                            return custom_path
                        elif custom_path:
                            print(f"警告：设置的自定义路径不存在: {custom_path}，将使用默认路径")
            
            return None
        except Exception as e:
            print(f"读取自定义文件路径设置时出错: {str(e)}")
            return None
    
    def _clean_account_data(self, account):
        """创建不包含额度字段的账户数据副本"""
        clean_account = account.copy()
        for field in ACCOUNT_QUOTA_FIELDS:
            clean_account.pop(field, None)
        return clean_account
    
    def load_accounts(self):
        """加载账户信息"""
        try:
            if not os.path.exists(self.accounts_file):
                print(f"账号文件不存在: {self.accounts_file}")
                # macOS特殊处理：如果无法从应用目录找到文件，尝试在当前目录查找
                if sys.platform == 'darwin' and getattr(sys, 'frozen', False) and '.app' in self.accounts_file:
                    # 尝试在.app同级目录查找账户文件
                    alt_path = os.path.join(os.path.dirname(os.path.dirname(self.accounts_file)), os.path.basename(self.accounts_file))
                    print(f"尝试备用路径: {alt_path}")
                    if os.path.exists(alt_path):
                        print(f"在备用路径找到账户文件: {alt_path}")
                        self.accounts_file = alt_path
                    else:
                        print(f"备用路径也未找到账户文件: {alt_path}")
                        print("返回空账户列表，文件将在保存时创建")
                        return []
                else:
                    print("返回空账户列表，文件将在保存时创建")
                    return []
                
            with open(self.accounts_file, 'r', encoding='utf-8') as f:
                accounts = json.load(f)
                
            if not accounts:
                print("账号文件为空")
                return []
            
            # 防止新注册账户丢失：在反转前保存最近注册的账户信息（文件尾部的账户）
            most_recent_accounts = []
            if len(accounts) > 0:
                # 获取最后一个账户，这通常是最新注册的
                most_recent_account = accounts[-1]
                if 'email' in most_recent_account:
                    most_recent_accounts.append(most_recent_account)
                    print(f"保护最新注册的账户: {most_recent_account.get('email', 'unknown')}")
            
            # 反转账户列表顺序，使最新添加的账户（文件末尾的账户）排在前面
            accounts.reverse()
            
            # 再次检查：确保最新添加的账户在列表中
            if most_recent_accounts:
                most_recent_email = most_recent_accounts[0].get('email')
                found = False
                for account in accounts:
                    if account.get('email') == most_recent_email:
                        found = True
                        break
                
                # 如果最新账户不在列表中（这应该不会发生），将其添加回列表
                if not found and most_recent_email:
                    print(f"修复：将最新注册账户 {most_recent_email} 添加回列表")
                    accounts.insert(0, most_recent_accounts[0])  # 添加到列表开头
            
            return accounts
        except Exception as e:
            print(f"读取账号文件失败: {str(e)}")
            return []
    
    def save_quotas_to_temp(self):
        """保存账户额度数据到临时文件"""
        try:
            # 提取账户中的配额相关数据
            quota_data_list = []
            for account in self.accounts:
                # 只保存账户邮箱和额度相关数据
                account_quota_data = account.get("quota_data", {})
                account_quota = {
                    "email": account.get("email", ""),
                    "real_usage": account.get("real_usage", 0),
                    "real_max_usage": account.get("real_max_usage", 0),
                    "real_register_time": account.get("real_register_time", ""),
                    "real_remaining_days": account.get("real_remaining_days", ""),
                    "quota_data": account_quota_data,
                    "api_register_time": account.get("api_register_time", ""),
                    # 确保账户类型信息也被保存
                    "account_type_info": account_quota_data.get("account_type_info") if account_quota_data else None
                }
                quota_data_list.append(account_quota)
            
            # 保存到临时文件
            with open(self.temp_file, 'w', encoding='utf-8') as f:
                json.dump(quota_data_list, f, ensure_ascii=False, indent=2)
            print(f"额度数据已保存到临时文件: {self.temp_file}")
            return True
        except Exception as e:
            print(f"保存额度临时文件失败: {str(e)}")
            return False
    
    def load_quotas_from_temp(self):
        """从临时文件加载额度数据"""
        try:
            # 如果账户列表为空，不加载任何临时数据
            if not self.accounts:
                print("账户列表为空，跳过加载临时额度数据")
                return False
                
            if not os.path.exists(self.temp_file):
                print(f"临时额度文件不存在: {self.temp_file}")
                return False
            
            with open(self.temp_file, 'r', encoding='utf-8') as f:
                temp_data = json.load(f)
            
            if not temp_data:
                print("临时额度文件为空")
                return False
            
            # 将临时文件中的额度数据合并到账户数据中
            for account in self.accounts:
                email = account.get("email", "")
                # 查找对应的额度数据
                for quota_item in temp_data:
                    if quota_item.get("email") == email:
                        # 更新额度相关字段
                        account["real_usage"] = quota_item.get("real_usage", 0)
                        account["real_max_usage"] = quota_item.get("real_max_usage", 0)
                        account["real_register_time"] = quota_item.get("real_register_time", "")
                        account["real_remaining_days"] = quota_item.get("real_remaining_days", "")
                        quota_data = quota_item.get("quota_data", {})
                        account["quota_data"] = quota_data
                        account["api_register_time"] = quota_item.get("api_register_time", "")

                        # 确保账户类型信息也被正确加载
                        account_type_info = quota_item.get("account_type_info")
                        if account_type_info and isinstance(quota_data, dict):
                            quota_data["account_type_info"] = account_type_info
                        break
            
            print(f"已从临时文件加载额度数据")
            return True
        except Exception as e:
            print(f"从临时文件加载额度数据失败: {str(e)}")
            return False
    
    def save_accounts_main_only(self, allow_create=False):
        """只保存账户基本信息到主文件，不涉及临时文件
        
        Args:
            allow_create: 如果为True，则当文件不存在时自动创建；如果为False，则当文件不存在时返回False
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(self.accounts_file):
                # 根据allow_create参数决定是否创建文件
                if allow_create:
                    # 确保目录存在
                    os.makedirs(os.path.dirname(self.accounts_file), exist_ok=True)
                    print(f"账号文件不存在，将自动创建: {self.accounts_file}")
                else:
                    # 不允许创建文件，直接返回False
                    print(f"账号文件不存在，跳过保存: {self.accounts_file}")
                    return False
                
            # 从当前内存中的账户数据移除额度相关字段
            clean_accounts = []
            for account in self.accounts:
                clean_accounts.append(self._clean_account_data(account))
            
            # 反转回原始顺序（最新的放在列表末尾）
            clean_accounts.reverse()
            
            # 保存到文件（如果文件存在或允许创建）
            with open(self.accounts_file, 'w', encoding='utf-8') as f:
                json.dump(clean_accounts, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"保存账号文件失败: {str(e)}")
            return False
            
    def save_accounts(self, allow_create=False):
        """保存账户信息到主文件和临时文件
        
        Args:
            allow_create: 如果为True，则当文件不存在时自动创建；如果为False，则当文件不存在时返回False
        """
        # 先保存主文件
        result = self.save_accounts_main_only(allow_create=allow_create)
        
        # 然后保存临时文件
        temp_result = self.save_quotas_to_temp()
        
        return result and temp_result
    
    def get_remaining_days(self, register_time):
        """
        计算账户剩余天数
        :param register_time: 注册时间
        :return: 剩余天数或"已过期"
        """
        try:
            from datetime import datetime, timedelta # Import here
            from dateutil import parser
            from dateutil.tz import tz
            
            # 解析注册时间，支持多种常见格式
            try:
                reg_date = datetime.strptime(register_time, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                try:
                    # 尝试其他可能的日期格式
                    reg_date = datetime.strptime(register_time, "%Y-%m-%d")
                except ValueError:
                    try:
                        # 尝试ISO格式带Z时区的日期格式 (例如："2025-03-21T04:10:22.000Z")
                        # 解析日期，会自动处理时区
                        reg_date = parser.parse(register_time)
                        
                        # 如果是时区感知的日期时间，转换为naive时间
                        if reg_date.tzinfo is not None:
                            # 转换为UTC时间
                            reg_date = reg_date.astimezone(tz.UTC)
                            # 移除时区信息，保留UTC时间
                            reg_date = reg_date.replace(tzinfo=None)
                    except (ValueError, ImportError):
                        print(f"无法解析日期格式: {register_time}")
                        return "未知"
            
            # 获取当前时间（无时区信息）
            now = datetime.now()
            
            # 计算总试用期限（14天）
            trial_period = timedelta(days=14)
            expiry_date = reg_date + trial_period
            
            # 计算已经使用的时间
            used_time = now - reg_date
            used_days = used_time.days
            
            # 如果已过期
            if now > expiry_date:
                return "已过期"
            else:
                # 计算剩余天数（总试用期14天 - 已使用天数）
                remaining_days = 14 - used_days
                return remaining_days
        except Exception as e:
            print(f"计算剩余天数时出错: {str(e)}")
            return "未知"
    
    def delete_account(self, email):
        """
        删除指定邮箱的账号
        :param email: 要删除的账号邮箱
        :return: 是否删除成功
        """
        try:
            self.accounts = [acc for acc in self.accounts if acc['email'] != email]
            # 删除账户时需要同时更新主文件和临时文件
            return self.save_accounts()
        except Exception as e:
            print(f"删除账号时出错: {str(e)}")
            return False
    

    
    def delete_accounts_by_quota(self, quota, operator="大于等于"):
        """
        删除指定额度的账号
        :param quota: 要删除的账号额度
        :param operator: 操作符，"大于等于"或"小于等于"或"等于"
        :return: 删除的账号邮箱列表
        """
        try:
            quota = int(quota)
            original_accounts = self.accounts.copy()
            
            # 保存要删除的账户，而不是直接修改self.accounts
            accounts_to_keep = []
            
            for acc in self.accounts:
                # 获取账户的实际高级模型使用次数(real_usage字段)
                # 这个字段是通过API请求获取的高级模型使用次数，不是usage_limit
                acc_usage = int(acc.get('real_usage', 0))
                
                # 根据条件判断是否要删除
                if operator == "大于等于" and acc_usage >= quota:
                    # 不保留此账户
                    pass
                elif operator == "小于等于" and acc_usage <= quota:
                    # 不保留此账户
                    pass
                elif operator == "等于" and acc_usage == quota:
                    # 不保留此账户
                    pass
                else:
                    accounts_to_keep.append(acc)
            
            # 找出被删除的账户邮箱
            original_emails = {acc.get('email') for acc in original_accounts}
            remaining_emails = {acc.get('email') for acc in accounts_to_keep}
            deleted_emails = list(original_emails - remaining_emails)
            
            # 更新账户列表
            self.accounts = accounts_to_keep
            
            if deleted_emails:
                # 保存更改到主文件和临时文件
                self.save_accounts()
            
            return deleted_emails
        except Exception as e:
            print(f"按额度删除账号时出错: {str(e)}")
            return []

    def update_account_info(self, email, usage=None, max_usage=None, register_time=None, quota_data=None):
        """
        更新账户信息，包括使用量和最大使用量
        :param email: 账户邮箱
        :param usage: 当前使用量
        :param max_usage: 最大使用量
        :param register_time: 注册时间
        :param quota_data: 额度详细数据
        :return: 是否更新成功
        """
        try:
            # 查找账户
            for account in self.accounts:
                if account.get('email') == email:
                    # 如果提供了quota_data，从中提取使用量信息 - 适配新的JSON格式
                    if quota_data is not None:
                        # 从GPT-4数据中提取使用量（优先使用高级模型数据）
                        gpt4_data = quota_data.get("gpt-4", {})
                        if gpt4_data:
                            account['real_usage'] = gpt4_data.get("numRequests", 0)  # 使用numRequests
                            account['real_max_usage'] = gpt4_data.get("maxRequestUsage")
                        else:
                            # 如果没有GPT-4数据，尝试使用GPT-3.5数据
                            gpt35_data = quota_data.get("gpt-3.5-turbo", {})
                            if gpt35_data:
                                account['real_usage'] = gpt35_data.get("numRequests", 0)
                                account['real_max_usage'] = gpt35_data.get("maxRequestUsage")

                    # 更新使用量（如果直接提供）
                    if usage is not None:
                        account['real_usage'] = usage

                    # 更新最大使用量（如果直接提供）
                    if max_usage is not None:
                        account['real_max_usage'] = max_usage

                    # 更新注册时间
                    if register_time is not None:
                        account['real_register_time'] = register_time
                        # 计算剩余天数
                        account['real_remaining_days'] = self.get_remaining_days(register_time)

                    # 更新额度详细数据
                    if quota_data is not None:
                        account['quota_data'] = quota_data

                    # 只保存配额数据到临时文件，不修改主文件
                    return self.save_quotas_to_temp()
            
            # 未找到账户
            return False
        except Exception as e:
            print(f"更新账户信息时出错: {str(e)}")
            return False

    def clear_accounts(self):
        """清空内存中的账户数据"""
        self.accounts = []
        print("已清空账户数据")
        return True


class AccountQuota:
    """获取账户额度的类"""
    
    @staticmethod
    def get_quota(account_data, max_retries=3, retry_delay=2):
        """
        获取账户使用额度
        :param account_data: 账户数据，可以是cookies数组或包含auth_info的账户字典
        :param max_retries: 最大重试次数
        :param retry_delay: 重试延迟时间（秒）
        :return: 账户额度信息
        """
        # 获取账户的邮箱，用于日志记录
        email = account_data.get('email', 'unknown') if isinstance(account_data, dict) else 'unknown'
        
        # 定义重试计数器
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                # 修改后采用拼接方式构建token值
                fixed_prefix = "user_01000000000000000000000000%3A%3A"
                
                # 获取accessToken的方式1：从auth_info中获取
                access_token = None
                
                # 检查account_data是否是字典类型且包含auth_info
                if isinstance(account_data, dict) and 'auth_info' in account_data:
                    auth_info = account_data.get('auth_info', {})
                    access_token = auth_info.get('cursorAuth/accessToken')
                    print(f"从auth_info中获取到账户 {email} 的accessToken，长度: {len(access_token) if access_token else 0}")
                # 获取accessToken的方式2：从cookies中获取
                elif isinstance(account_data, list):  # 旧方式，传入的是cookies数组
                    cookies = account_data
                    for cookie in cookies:
                        if cookie.get('name') == 'WorkosCursorSessionToken':
                            # 尝试从cookie中提取access_token部分
                            cookie_value = cookie.get('value', '')
                            if "%3A%3A" in cookie_value:
                                access_token = cookie_value.split("%3A%3A")[-1]
                                print(f"从cookies中提取账户的accessToken，长度: {len(access_token) if access_token else 0}")
                            break
                
                if not access_token:
                    print(f"错误: 无法获取账户 {email} 的accessToken")
                    return None
                    
                # 构建完整的session token
                token_value = fixed_prefix + access_token
                
                # 创建cookies字典
                cookies_dict = {'WorkosCursorSessionToken': token_value}
                
                headers = {
                    'authority': 'www.cursor.com',
                    'accept': '*/*',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'dnt': '1',
                    'referer': 'https://www.cursor.com/cn/settings',
                    'sec-ch-ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-origin',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
                }
                
                print(f"开始获取账户 {email} 的额度信息..." + (f" (重试 {retry_count}/{max_retries})" if retry_count > 0 else ""))
                
                # 设置请求超时时间
                timeout = 15  # 15秒超时
                
                # 发送请求并设置超时
                response = requests.get(
                    'https://www.cursor.com/api/usage', 
                    cookies=cookies_dict, 
                    headers=headers,
                    timeout=timeout,
                    verify=True  # SSL验证
                )
                
                if response.status_code == 200:
                    quota_data = response.json()
                    print(f"成功获取账户 {email} 的额度信息")
                    return quota_data
                else:
                    error_msg = f"获取账户 {email} 额度失败: 状态码 {response.status_code}, 响应内容: {response.text}"
                    print(error_msg)
                    
                    # 根据状态码决定是否重试
                    if response.status_code >= 500 or response.status_code == 429:  # 服务器错误或速率限制
                        retry_count += 1
                        if retry_count <= max_retries:
                            import time
                            # 使用指数退避，延迟时间随重试次数增加
                            delay = retry_delay * (2 ** (retry_count - 1))
                            print(f"将在 {delay} 秒后重试...")
                            time.sleep(delay)
                            continue
                    
                    # 不可重试或已达最大重试次数
                    return None
                
            except requests.exceptions.SSLError as e:
                print(f"获取账户 {email} 额度时SSL错误: {str(e)}")
                
                # 增加重试计数
                retry_count += 1
                if retry_count <= max_retries:
                    import time
                    # 使用指数退避，延迟时间随重试次数增加
                    delay = retry_delay * (2 ** (retry_count - 1))
                    print(f"SSL错误，将在 {delay} 秒后重试...")
                    time.sleep(delay)
                else:
                    print(f"已达最大重试次数 {max_retries}，放弃获取账户 {email} 的额度信息")
                    import traceback
                    traceback.print_exc()
                    return None
                    
            except requests.exceptions.RequestException as e:
                print(f"获取账户 {email} 额度时网络错误: {str(e)}")
                
                # 增加重试计数
                retry_count += 1
                if retry_count <= max_retries:
                    import time
                    # 使用指数退避，延迟时间随重试次数增加
                    delay = retry_delay * (2 ** (retry_count - 1))
                    print(f"网络错误，将在 {delay} 秒后重试...")
                    time.sleep(delay)
                else:
                    print(f"已达最大重试次数 {max_retries}，放弃获取账户 {email} 的额度信息")
                    import traceback
                    traceback.print_exc()
                    return None
                
            except Exception as e:
                print(f"获取账户 {email} 额度时出错: {str(e)}")
                import traceback
                traceback.print_exc()
                return None