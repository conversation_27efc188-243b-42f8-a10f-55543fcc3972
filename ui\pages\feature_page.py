from PySide6.QtWidgets import QWidget, QVBoxLayout, QFrame
from PySide6.QtCore import Qt

from widgets.pages.functionality_page import FunctionalityPage

class FeaturePage(QWidget):
    """功能页面类，封装了CursorAccountManager.create_feature_page的逻辑"""
    
    def __init__(self, main_window, parent=None):
        """初始化功能页面
        
        Args:
            main_window: 主窗口实例
            parent: 父组件
        """
        super().__init__(parent)
        self.main_window = main_window
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        # 设置整个页面背景为透明
        self.setStyleSheet("background: transparent;")
        
        # 创建功能页面实例
        self.main_window.functionality_page = FunctionalityPage()
        
        # 连接功能选择信号
        self.main_window.functionality_page.function_selected.connect(self.main_window._handle_function_selected)
        # 连接 Toast 请求信号 - 使用 lambda 转换 success 到 error
        self.main_window.functionality_page.toast_request.connect(
            lambda msg, success: self.main_window.show_toast(msg, error=not success)
        )
        # 连接 Cursor 版本更新信号
        self.main_window.functionality_page.cursor_version_updated.connect(self.main_window._update_cursor_version_label)
        
        # 创建页面布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(25)
        layout.addWidget(self.main_window.functionality_page) 