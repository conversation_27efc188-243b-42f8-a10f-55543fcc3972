# YCursor.py 拆分方案 (修订版 v3 - PyQt6)

## 拆分原则
1.  **严格参照**: 本次拆分严格参照 `main copy.py` 文件进行，确保所有代码逻辑基于该副本。
2.  **原封不动**: 拆分过程仅移动代码位置，不修改原有逻辑、功能或变量名。必要的导入语句调整和对 `self` 引用的处理（如下所述）除外。
3.  **模块化**: 按功能模块（自定义控件、核心逻辑、UI页面等）将代码拆分到对应的目录和文件中（如 `widgets/`, `core/`, `ui/pages/`）。
4.  **增量更新**: 每完成一个小步骤（如移动一个类或方法），必须立即修改 `main.py` 文件，添加正确的导入语句并调整代码以使用新拆分出的模块。
5.  **架构意识**: 确保所有导入路径正确，避免循环导入问题。新创建的文件需要添加必要的 `import` 语句 (**使用 PyQt6**)。
6.  **可维护性**: 最终目标是降低 `main.py` 的复杂性，提高代码的可读性和可维护性。
7.  **引导执行**: 每完成一个拆分步骤并通过验证后，必须明确提供执行下一步骤的提示词，以便用户清晰地引导后续操作。
8.  **禁止测试文件**: 拆分过程中不允许创建任何形式的测试文件 (`test_*.py`, `*_test.py` 等)。
9.  **PowerShell优先**: 在需要执行终端命令时，优先使用 PowerShell 语法，因为目标环境是 Windows。
10. **严格验证**: 每完成一个小步骤，必须使用工具真实的仔细检查代码是否完整没有错误、无修改地移动，没有遗漏或多余内容，确保严格遵守"原封不动"原则。

## 文件结构 (目标)
```
YCursor/
├── main.py              # 主程序入口，逐步精简
├── main copy.py         # 原始代码副本 (不修改)
├── theme.py             # 主题常量
├── utils.py             # 通用工具函数
├── logger.py            # 日志记录器
├── widgets/             # 可复用界面组件
│   ├── clickable_copy_label.py
│   ├── dialog.py
│   ├── styled_widgets.py
│   ├── toast.py
│   ├── account_widgets.py
│   ├── animated_widgets.py
│   └── ...
├── core/                # 核心非UI逻辑
│   ├── quota_fetcher_manager.py
│   ├── quota_manager.py
│   ├── account_storage_manager.py # 新增：账户数据存储/加载
│   ├── process_utils.py         # 新增：进程管理工具
│   └── ...
├── ui/                  # UI相关，特别是页面
│   ├── styled_widgets.py # 样式化组件重导出
│   ├── widget_helpers.py # UI辅助函数
│   ├── sidebar.py           # 新增：侧边栏组件
│   ├── pages/           # 页面构建逻辑
│   │   ├── home_page.py
│   │   ├── accounts_page.py
│   │   ├── feature_page.py
│   │   ├── log_page.py
│   │   └── settings_page.py
│   └── dialogs/         # 对话框逻辑
│       ├── account_details_dialog.py
│       ├── delete_type_dialog.py
│       ├── delete_quota_dialog.py
│       ├── batch_delete_dialog.py
│       └── import_accounts_dialog.py
├── account/             # 账户特定功能
│   ├── auth.py
│   ├── account_data.py
│   ├── quota.py
│   └── account_actions.py
├── 拆分方案.md          # 本拆分计划文件
└── ...                  # 其他项目文件
```

## 完成进度 (基于上次更新)

*   [x] `theme.py`
*   [x] `utils.py` (部分)
*   [x] `logger.py`
*   [x] `log_page_manager.py`
*   [x] `animation_manager.py`
*   [x] `smooth_scroll_animation.py`
*   [x] `ui_update_thread.py`
*   [x] `ui_update_queue.py`
*   [x] `version_checker.py`
*   [x] **步骤 1: 拆分 `ClickableCopyLabel` 类**
*   [x] **步骤 2: 拆分 `QuotaFetcherManager` 类**
*   [x] **步骤 3: 拆分 `create_home_page`**
*   [x] **步骤 4: 拆分 `create_accounts_page`**
*   [x] **步骤 5: 拆分 `create_feature_page`**
*   [x] **步骤 6: 拆分 `create_log_page`**
*   [x] **步骤 6.5: 创建UI组件导出桥接层**
*   [x] **步骤 7: 拆分 `create_settings_page`**
*   [x] **步骤 8: 拆分 `_compare_quota_data`**
*   [x] **步骤 9: 拆分 `_copy_error_to_clipboard`**
*   [x] **步骤 10: 拆分账户切换逻辑 (`switch_account`)**
*   [x] **步骤 11: 拆分删除账户逻辑 (`delete_account`)**
*   [x] **步骤 12: 拆分删除过期账户逻辑 (`delete_expired_accounts`)**
*   [x] **步骤 13: 拆分显示账户详情逻辑 (`show_account_details`)**
*   [x] **步骤 14: 拆分 `_create_copy_card` 辅助方法**
*   [x] **步骤 15: 拆分按类型删除对话框逻辑**
*   [x] **步骤 16: 拆分按额度删除对话框逻辑**
*   [x] **步骤 17: 拆分批量删除对话框逻辑**
*   [x] **步骤 18: 拆分导入账户对话框逻辑**
*   [x] **步骤 19: 拆分账户配额获取和刷新逻辑**
    *   [x] 19.1 创建 `core/quota_manager.py` 文件。
    *   [x] 19.2 定义 `QuotaManager` 类，实现账户配额获取和刷新功能。
    *   [x] 19.3 将 `main.py` 中 `fetch_all_accounts_quota`、`update_fetch_progress`、`on_all_quotas_fetched`、`_fetch_other_accounts_quota`、`_auto_refresh_current_quota`、`_background_refresh_quota`、`_fetch_current_account_quota` 和 `_fetch_and_update_ui_on_main_thread` 方法替换为对 `QuotaManager` 实例的方法调用。
    *   [x] 19.4 保留 `update_account_quota` 方法用于信号连接。
    *   [x] 19.5 **验证**: 功能验证通过。

## 详细拆分计划 (继续)

**重要提示**: 以下所有行号均参照最新的 `main.py` 文件（拆分步骤 19 完成后）。请在执行时务必确认参照的文件是当前工作区的 `main.py`。所有 Qt 相关导入和类均使用 **PyQt6**。

### 阶段四：拆分核心逻辑与辅助功能

#### 步骤 20: 拆分账户数据加载相关方法

*   **目的**: 将账户列表的加载、错误显示等逻辑移至 `core` 目录，由 `AccountStorageManager` 管理。
*   **方法**: 创建 `AccountStorageManager` 类，将相关方法逻辑移入，主窗口通过该类管理账户数据加载和 UI 更新（仅限与加载直接相关的部分，如错误显示）。
*   **小步骤 20.1**: **【文件操作】** 在 `core/` 目录下创建新文件 `account_storage_manager.py`。
*   **小步骤 20.2**: **【编码操作】** 在 `core/account_storage_manager.py` 中定义 `AccountStorageManager` 类 (**使用 PyQt6**)。构造函数接收 `main_window` 实例。
    ```python
    # core/account_storage_manager.py
    import os
    from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel # ... (添加所有需要的 PyQt6 控件)
    from PyQt6.QtCore import Qt, QTimer # ... (添加所有需要的 PyQt6.QtCore)
    # 假设 theme.py, account_data.py 在根或可访问路径
    from theme import Theme
    from account.account_data import AccountData
    from widgets.account_widgets import AccountRowWidget # 假设 AccountRowWidget 在 widgets/
    from logger import info, error

    class AccountStorageManager:
        def __init__(self, main_window):
            self.main_window = main_window
            # 可能需要引用 main_window 的 account_data, accounts_layout, account_rows, current_email 等
            # 或者将这些状态也移交一部分给 Manager 管理

        def load_accounts_ui(self):
            # 将 main.py 中 load_accounts (约 L1793) 的 *主体逻辑* 复制并修改到这里
            # 1. 替换 self.xxx 为 self.main_window.xxx
            # 2. 确保 accounts_layout, account_rows, current_email, account_data 等可访问
            # 3. 处理 AccountRowWidget 的创建和信号连接 (信号连接目标仍是 main_window 的方法)
            # 4. 包含 _show_account_file_error 和 _show_empty_account_data_error 的调用逻辑
            pass # Placeholder

        def _show_account_file_error(self):
            # 将 main.py 中 _show_account_file_error (约 L1882) 的 *主体逻辑* 复制并修改到这里
            # 1. 替换 self.xxx 为 self.main_window.xxx
            # 2. 确保 accounts_layout, account_data.accounts_file 可访问
            pass # Placeholder

        def _show_empty_account_data_error(self):
            # 将 main.py 中 _show_empty_account_data_error (约 L1941) 的 *主体逻辑* 复制并修改到这里
            # 1. 替换 self.xxx 为 self.main_window.xxx
            # 2. 确保 accounts_layout, account_data.accounts_file 可访问
            pass # Placeholder

        def load_current_account_ui(self):
            # 将 main.py 中 load_current_account (约 L1756) 的 *主体逻辑* 复制并修改到这里
            # 1. 替换 self.xxx 为 self.main_window.xxx
            # 2. 确保 auth_manager, current_email, email_label, register_time_label 等 UI 元素可访问
            # 3. 处理 _fetch_current_account_quota 的调用 (应调用 main_window._fetch_current_account_quota 或 quota_mgr 的方法)
            pass # Placeholder

        # 可以考虑添加 refresh_all_data 等逻辑...
    ```
*   **小步骤 20.3**: **【编码操作】** 在 `main.py` 中:
    *   在文件顶部添加导入: `from core.account_storage_manager import AccountStorageManager`。
    *   在 `CursorAccountManager.__init__` 中创建实例: `self.storage_manager = AccountStorageManager(self)`。
    *   **删除** `CursorAccountManager` 中的 `load_accounts`, `_show_account_file_error`, `_show_empty_account_data_error` 方法定义。
    *   将 `CursorAccountManager` 中对 `self.load_accounts()` 的调用改为 `self.storage_manager.load_accounts_ui()`。
    *   将 `CursorAccountManager` 中对 `self.load_current_account()` 的调用改为 `self.storage_manager.load_current_account_ui()`。
    *   **注意**: `load_current_account` (约 L1756) 和 `load_accounts` (约 L1793) 的原始实现中可能混合了数据加载和UI更新。拆分时需要仔细分离，确保 `AccountStorageManager` 主要负责加载逻辑和与加载直接相关的UI状态（如错误提示），而核心UI内容的更新（如进度条、标签值）可能仍需保留在 `main.py` 或对应的页面类中，由加载完成后的信号触发。这可能需要引入信号机制或回调。**为简化起步，暂时将 UI 更新逻辑一起移动，后续再精细化。**
*   **小步骤 20.4**: **【验证】** 运行程序，确认账户列表在启动时和切换到账户页面时能正确加载，包括空数据和文件不存在时的错误提示。确认首页当前账户信息能正确加载。

#### 步骤 21: 拆分保存当前账户逻辑

*   **目的**: 将庞大的 `save_current_account` 方法移至 `account_actions.py`。
*   **方法**: 定义 `save_current_account_action` 函数，传递主窗口实例以访问所需属性和方法。
*   **小步骤 21.1**: **【编码操作】** 确保 `account/account_actions.py` 文件存在。
*   **小步骤 21.2**: **【编码操作】** 在 `account/account_actions.py` 中定义新函数 `save_current_account_action`，接收 `main_window` 实例作为参数。
    ```python
    # account/account_actions.py
    import os
    import sys
    import json
    import platform
    import subprocess
    import sqlite3
    import ctypes # If needed for admin check
    from datetime import datetime
    # Import necessary PyQt6 components if Utils.confirm_message uses them
    from PyQt6.QtWidgets import QMessageBox # Example, add as needed
    from logger import info, warning, error
    from utils import Utils # Assuming confirm_message is here

    def save_current_account_action(main_window):
        # 将 main.py 中 save_current_account (约 L2819) 的 *主体逻辑* 复制并修改到这里
        # 1. 替换所有 self.xxx 为 main_window.xxx (e.g., self.auth_manager -> main_window.auth_manager)
        # 2. 替换所有 self.yyy() 为 main_window.yyy() (e.g., self.show_toast -> main_window.show_toast)
        # 3. 确保 Utils.confirm_message 调用正确 (可能需要传递 main_window 作为父级)
        # 4. 添加所有在原方法中使用的导入到 account_actions.py 顶部
        pass # Placeholder
    ```
*   **小步骤 21.3**: **【编码操作】** 在 `main.py` 中:
    *   在文件顶部添加导入: `from account.account_actions import save_current_account_action`。
    *   将 `CursorAccountManager` 中的 `save_current_account` 方法 (约 L2819) 的实现替换为:
        ```python
        # main.py CursorAccountManager 类内
        def save_current_account(self):
            save_current_account_action(self)
        ```
*   **小步骤 21.4**: **【验证】** 运行程序，确认"保存当前账户"功能按钮或菜单项调用后，账户能够被正确保存或更新，包括 token 和机器码信息，并且日志和 Toast 提示正常。

#### 步骤 22: 拆分进程管理工具

*   **目的**: 将 `kill_cursor_process` 和 `start_cursor_app` 移至 `core/process_utils.py`。
*   **方法**: 创建新模块和函数，处理进程操作。`show_toast` 的调用需要通过 `main_window` 实例传递。
*   **小步骤 22.1**: **【文件操作】** 在 `core/` 目录下创建新文件 `process_utils.py`。
*   **小步骤 22.2**: **【编码操作】** 在 `core/process_utils.py` 中定义 `kill_cursor_process` 和 `start_cursor_app` 函数。`start_cursor_app` 需要接收 `main_window` 参数以调用 `show_toast`。
    ```python
    # core/process_utils.py
    import os
    import sys
    import subprocess
    from logger import info, warning, error # Assuming logger is accessible

    def kill_cursor_process():
        # 将 main.py 中 kill_cursor_process (约 L207) 的 *主体逻辑* 复制并修改到这里
        # (此方法似乎不依赖 self)
        pass # Placeholder

    def start_cursor_app(main_window):
        # 将 main.py 中 start_cursor_app (约 L259) 的 *主体逻辑* 复制并修改到这里
        # 1. 替换 self.show_toast(...) 为 main_window.show_toast(...)
        pass # Placeholder
    ```
*   **小步骤 22.3**: **【编码操作】** 在 `main.py` 中:
    *   在文件顶部添加导入: `from core.process_utils import kill_cursor_process, start_cursor_app`。
    *   **删除** `CursorAccountManager` 中的 `kill_cursor_process` 和 `start_cursor_app` 方法定义。
    *   查找并修改调用这两个方法的地方 (例如，可能在 `switch_account_action` 中或其他地方)，确保传递 `self` (即 `main_window`) 给 `start_cursor_app`：
        *   `kill_cursor_process()` (无需参数)
        *   `start_cursor_app(self)`
*   **小步骤 22.4**: **【验证】** 如果有切换账户或其他功能调用了这两个方法，验证它们是否仍然按预期工作（杀死旧进程，启动新进程，并显示 Toast）。

#### 步骤 23: 拆分侧边栏创建逻辑

*   **目的**: 将 `create_sidebar` 方法移至独立的 `SidebarWidget` 类。
*   **方法**: 创建 `ui/sidebar.py` 和 `SidebarWidget` 类，将 UI 创建逻辑移入，处理按钮点击连接到主窗口的 `switch_page` 方法。
*   **小步骤 23.1**: **【文件操作】** 在 `ui/` 目录下创建新文件 `sidebar.py`。
*   **小步骤 23.2**: **【编码操作】** 在 `ui/sidebar.py` 中定义 `SidebarWidget` 类 (**使用 PyQt6**)，继承自 `StyledFrame`。
    ```python
    # ui/sidebar.py
    import sys
    import os
    import ctypes
    from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                               QSpacerItem, QSizePolicy) # Add all needed widgets
    from PyQt6.QtGui import QIcon, QCursor # Add all needed QtGui
    from PyQt6.QtCore import Qt # Add all needed QtCore

    from theme import Theme
    from utils import Utils # Assuming Utils.get_cursor_version is static or accessible
    from version_checker import VersionChecker # Assuming VersionChecker is accessible
    from widgets.styled_widgets import StyledFrame # Or from ui.styled_widgets

    class SidebarWidget(StyledFrame):
        def __init__(self, main_window, parent=None):
            super().__init__(parent, has_glass_effect=True)
            self.main_window = main_window
            self._setup_ui()

        def _setup_ui(self):
            # 将 main.py 中 create_sidebar (约 L736) 的 *主体逻辑* 复制并修改到这里
            # 1. 设置 SidebarWidget 自身的属性 (e.g., self.setFixedWidth(240), self.setObjectName("sidebar"))
            # 2. 创建布局并设置给 self (e.g., layout = QVBoxLayout(self))
            # 3. 将创建的按钮 (home_btn, accounts_btn etc.) 存储为 self 的属性 (e.g., self.home_btn = QPushButton(...))
            # 4. 连接按钮的 clicked 信号到 lambda: self.main_window.switch_page(index)
            # 5. 添加所有必要的导入到 sidebar.py 顶部
            pass # Placeholder

        def update_button_states(self, index):
            # 可以添加一个方法来更新按钮的选中状态，由 main_window 调用
            if hasattr(self, 'home_btn'): self.home_btn.setChecked(index == 0)
            if hasattr(self, 'accounts_btn'): self.accounts_btn.setChecked(index == 1)
            if hasattr(self, 'feature_btn'): self.feature_btn.setChecked(index == 2)
            if hasattr(self, 'settings_btn'): self.settings_btn.setChecked(index == 3)
            if hasattr(self, 'log_btn'): self.log_btn.setChecked(index == 4)

    ```
*   **小步骤 23.3**: **【编码操作】** 在 `main.py` 中:
    *   在文件顶部添加导入: `from ui.sidebar import SidebarWidget`。
    *   修改 `init_ui` 方法 (约 L356):
        *   找到 `self.sidebar = self.create_sidebar()` 这一行。
        *   将其替换为: `self.sidebar = SidebarWidget(self)`。
    *   **删除** `CursorAccountManager` 中的 `create_sidebar` 方法定义 (约 L736)。
    *   修改 `switch_page` 方法 (约 L2100):
        *   在更新按钮状态的部分，将 `self.home_btn.setChecked(...)` 等调用改为 `self.sidebar.update_button_states(index)`。
*   **小步骤 23.4**: **【验证】** 运行程序，确认侧边栏显示正常，按钮样式、图标、版本信息、权限指示器均正确，并且点击按钮能够切换页面。

#### 步骤 24: 拆分账户排序逻辑

*   **目的**: 将账户列表排序逻辑从 `main.py` 移出。考虑到排序后直接影响账户页面的显示，将其移至 `AccountsPage` 类更合适。
*   **方法**: 将 `_sort_accounts_and_update_ui` 和 `_sort_accounts_without_rebuild_ui` 的逻辑移到 `ui/pages/accounts_page.py` 中。
*   **小步骤 24.1**: **【编码操作】** 打开 `ui/pages/accounts_page.py`。
*   **小步骤 24.2**: **【编码操作】** 在 `AccountsPage` 类中添加 `sort_accounts_and_update_ui` 和 `sort_accounts_without_rebuild_ui` 方法。
    ```python
    # ui/pages/accounts_page.py
    # Add necessary imports like datetime, parser from dateutil if not already present
    from datetime import datetime
    from dateutil import parser

    class AccountsPage(QWidget):
        # ... (existing __init__ and _setup_ui) ...

        def sort_accounts_and_update_ui(self):
            # 将 main.py 中 _sort_accounts_and_update_ui (约 L2227) 的 *主体逻辑* 复制并修改到这里
            # 1. 访问账户数据通过 self.main_window.account_data.accounts
            # 2. 更新账户数据也通过 self.main_window.account_data.accounts = sorted_accounts
            # 3. UI 清理和重建逻辑直接操作 self.accounts_layout 和 self.main_window.account_rows
            # 4. 访问 current_email 通过 self.main_window.current_email
            # 5. 访问 show_toast 通过 self.main_window.show_toast
            # 6. 确保 AccountRowWidget 和信号连接正确
            # 7. 调用 self.main_window._update_accounts_count() 更新计数
            pass # Placeholder

        def sort_accounts_without_rebuild_ui(self):
            # 将 main.py 中 _sort_accounts_without_rebuild_ui (约 L2310) 的 *主体逻辑* 复制并修改到这里
            # 1. 访问和更新账户数据通过 self.main_window.account_data.accounts
            # 2. 调用 self.main_window._update_accounts_count() 更新计数
            pass # Placeholder

        # 可能需要添加 _get_register_time 辅助方法
        def _get_register_time(self, account):
             # 将 main.py 中 _sort... 方法内部的 get_register_time 逻辑移到这里
            try:
                register_time = (account.get("startOfMonth") or
                               account.get("real_register_time") or
                               account.get("api_register_time") or
                               account.get("register_time"))
                # ... (rest of the logic from main.py) ...
                dt = parser.parse(register_time)
                return dt.replace(tzinfo=None)
            except Exception:
                return datetime(1970, 1, 1)

    ```
*   **小步骤 24.3**: **【编码操作】** 在 `main.py` 中:
    *   **删除** `CursorAccountManager` 中的 `_sort_accounts_and_update_ui` 和 `_sort_accounts_without_rebuild_ui` 方法定义。
    *   找到调用这两个方法的地方（例如在 `_handle_function_selected` 处理 "sort_accounts" 时，以及在 `_update_after_fetch` 中）。
    *   修改调用，改为通过 `self.accounts_page` 实例调用相应的方法：
        *   `self.accounts_page.sort_accounts_and_update_ui()`
        *   `self.accounts_page.sort_accounts_without_rebuild_ui()`
*   **小步骤 24.4**: **【验证】** 运行程序，在账户页面测试排序功能，确认账户列表按注册时间正确排序且 UI 更新正常。检查获取数据后是否也进行了排序。

#### 步骤 25: 拆分清理逻辑

*   **目的**: 将 `cleanup`, `_cleanup_old_temp_files`, `_cleanup_temp_files` 方法移至 `utils.py` 或 `core/cleanup_manager.py`。
*   **方法**: 将这些方法定义为独立函数，处理必要的资源释放。
*   **小步骤 25.1**: **【编码操作】** 打开 `utils.py`。
*   **小步骤 25.2**: **【编码操作】** 在 `utils.py` 中定义 `cleanup_application`, `cleanup_old_temp_files`, `cleanup_temp_files` 函数。
    ```python
    # utils.py
    import os
    import traceback
    # Add imports used within the original methods (e.g., get_app_data_dir, AccountData)
    # Assuming account_data and logger are structured relative to utils.py
    try:
        from .account.account_data import AccountData # Relative import
        from .logger import info, error, get_app_data_dir # Relative import
    except ImportError:
        # Fallback for direct execution or different structure
        from account.account_data import AccountData
        from logger import info, error, get_app_data_dir


    def cleanup_old_temp_files(account_data_instance):
        # 将 main.py 中 _cleanup_old_temp_files (约 L181) 的 *主体逻辑* 复制并修改到这里
        # 1. 接收 account_data_instance 作为参数
        # 2. 访问 temp_file 通过 account_data_instance.temp_file
        # 3. 访问 get_app_data_dir (假设已在 utils.py 中或可导入)
        try:
            app_data_dir = get_app_data_dir()
            temp_dir = os.path.join(app_data_dir, "temp_quota_data")
            if not os.path.exists(temp_dir):
                return
            for file in os.listdir(temp_dir):
                if file.startswith("temp_") and file.endswith(".json"):
                    try:
                        temp_file_path = os.path.join(temp_dir, file)
                        # Check if it's the currently used temp file from the instance
                        if hasattr(account_data_instance, 'temp_file') and \
                           temp_file_path != account_data_instance.temp_file:
                            os.remove(temp_file_path)
                            info(f"Deleted old temp file: {temp_file_path}")
                    except Exception as e:
                        error(f"Error deleting temp file {file}: {str(e)}")
        except Exception as e:
            error(f"Error cleaning up old temp files: {str(e)}")


    def cleanup_temp_files(account_data_instance):
        # 将 main.py 中 _cleanup_temp_files (约 L2712) 的 *主体逻辑* 复制并修改到这里
        # 1. 接收 account_data_instance 作为参数
        # 2. 访问 temp_file 通过 account_data_instance.temp_file
        # 3. 访问 get_app_data_dir
        try:
            app_data_dir = get_app_data_dir()
            temp_dir = os.path.join(app_data_dir, "temp_quota_data")
            if not os.path.exists(temp_dir):
                return
            if hasattr(account_data_instance, 'temp_file') and os.path.exists(account_data_instance.temp_file):
                try:
                    # The temp file data should have been saved elsewhere, safe to delete
                    os.remove(account_data_instance.temp_file)
                    info(f"Deleted current temp file: {account_data_instance.temp_file}")
                except Exception as e:
                    error(f"Error deleting current temp file: {str(e)}")
            # Optionally, remove the directory if empty, but be cautious
            # if not os.listdir(temp_dir):
            #     try:
            #         os.rmdir(temp_dir)
            #         info(f"Removed empty temp directory: {temp_dir}")
            #     except Exception as e:
            #         error(f"Error removing temp directory: {str(e)}")

        except Exception as e:
            error(f"Error cleaning up temp files: {str(e)}")


    def cleanup_application(manager_instance):
        # 将 main.py 中 cleanup (约 L2521) 的 *主体逻辑* 复制并修改到这里
        # 1. 接收 manager_instance (主窗口实例) 作为参数
        # 2. 访问 account_data 通过 manager_instance.account_data
        # 3. 调用 cleanup_temp_files(manager_instance.account_data)
        # 4. 处理 quota_manager, ui_update_thread, quota_refresh_timer 的停止和断开连接 (通过 manager_instance 访问)
        try:
            info("YCursor cleanup_application is running...")
            if hasattr(manager_instance, 'account_data'):
                # Save accounts if file exists
                if os.path.exists(manager_instance.account_data.accounts_file):
                    manager_instance.account_data.save_accounts(allow_create=False)
                    info("Accounts saved.")
                else:
                    info("Account file does not exist, skipping save.")
                # Call the specific temp file cleanup function
                cleanup_temp_files(manager_instance.account_data)

            # Shutdown Quota Manager safely
            if hasattr(manager_instance, 'quota_mgr') and hasattr(manager_instance.quota_mgr, 'quota_fetcher_manager'):
                 quota_manager_instance = manager_instance.quota_mgr.quota_fetcher_manager
                 if quota_manager_instance:
                    try:
                        # Disconnect signals safely before shutdown
                        # Assuming signals were connected to manager_instance methods
                        try: quota_manager_instance.quota_fetched.disconnect(manager_instance.update_account_quota)
                        except (TypeError, AttributeError): pass # Ignore if not connected or signal doesn't exist
                        try: quota_manager_instance.all_quotas_fetched.disconnect(manager_instance.quota_mgr.on_all_quotas_fetched) # Disconnect from QuotaManager's slot
                        except (TypeError, AttributeError): pass
                        try: quota_manager_instance.progress_updated.disconnect(manager_instance.quota_mgr.update_fetch_progress) # Disconnect from QuotaManager's slot
                        except (TypeError, AttributeError): pass

                        quota_manager_instance.shutdown()
                        info("Quota fetcher manager shut down.")
                    except Exception as e:
                        error(f"Error disconnecting/shutting down quota fetcher manager: {str(e)}")

            # Stop UI Update Thread
            if hasattr(manager_instance, 'ui_update_thread'):
                try:
                    manager_instance.ui_update_thread.stop()
                    info("UI update thread stopped.")
                except Exception as e:
                    error(f"Error stopping UI update thread: {str(e)}")

            # Stop Refresh Timer
            if hasattr(manager_instance, 'quota_refresh_timer'):
                try:
                    manager_instance.quota_refresh_timer.stop()
                    info("Quota refresh timer stopped.")
                except Exception as e:
                    error(f"Error stopping quota refresh timer: {str(e)}")

            info("YCursor cleanup_application finished.")
        except Exception as e:
            error(f"Error during application cleanup: {str(e)}")
            traceback.print_exc()
    ```
*   **小步骤 25.3**: **【编码操作】** 在 `main.py` 中:
    *   在文件顶部添加导入: `from utils import cleanup_application, cleanup_old_temp_files`。
    *   **删除** `CursorAccountManager` 中的 `cleanup`, `_cleanup_old_temp_files`, `_cleanup_temp_files` 方法定义。
    *   在 `CursorAccountManager.__init__` 中，将调用 `self._cleanup_old_temp_files()` 改为 `cleanup_old_temp_files(self.account_data)`。
    *   修改全局的 `cleanup` 函数 (约 L3405)，将其实现替换为调用 `cleanup_application(manager)`。
*   **小步骤 25.4**: **【验证】** 运行程序，然后关闭。检查日志确认清理步骤（如停止线程、定时器，删除临时文件）是否成功执行，并且没有错误。

## Implementation Checklist (PyQt6 Corrected - Continued)
... (之前的步骤) ...
19. ✅ **步骤 19: 拆分账户配额获取和刷新逻辑**
    *   [x] 19.1 创建 `core/quota_manager.py`。
    *   [x] 19.2 定义 `QuotaManager` 类。
    *   [x] 19.3 替换 `main.py` 中的调用。
    *   [x] 19.4 保留 `update_account_quota`。
    *   [x] 19.5 验证功能。
20.  ⬜ **步骤 20: 拆分账户数据加载相关方法**
    *   [ ] 20.1 创建 `core/account_storage_manager.py`。
    *   [ ] 20.2 定义 `AccountStorageManager` 类 (**PyQt6**)。
    *   [ ] 20.3 修改 `main.py` 以使用 `AccountStorageManager`，删除旧方法 (`load_accounts`, `_show_account_file_error`, `_show_empty_account_data_error`, 修改 `load_current_account` 调用)。
    *   [ ] 20.4 验证账户加载和错误显示。
21.  ⬜ **步骤 21: 拆分保存当前账户逻辑**
    *   [ ] 21.1 确保 `account/account_actions.py` 存在。
    *   [ ] 21.2 定义 `save_current_account_action` 函数。
    *   [ ] 21.3 修改 `main.py` 调用 `save_current_account_action`，删除旧方法。
    *   [ ] 21.4 验证保存功能。
22.  ⬜ **步骤 22: 拆分进程管理工具**
    *   [ ] 22.1 创建 `core/process_utils.py`。
    *   [ ] 22.2 定义 `kill_cursor_process` 和 `start_cursor_app` 函数。
    *   [ ] 22.3 修改 `main.py` 调用，删除旧方法。
    *   [ ] 22.4 验证进程管理功能。
23.  ⬜ **步骤 23: 拆分侧边栏创建逻辑**
    *   [ ] 23.1 创建 `ui/sidebar.py`。
    *   [ ] 23.2 定义 `SidebarWidget` 类 (**PyQt6**)。
    *   [ ] 23.3 修改 `main.py` 使用 `SidebarWidget`，删除 `create_sidebar`，修改 `switch_page`。
    *   [ ] 23.4 验证侧边栏功能。
24.  ⬜ **步骤 24: 拆分账户排序逻辑**
    *   [ ] 24.1 打开 `ui/pages/accounts_page.py`。
    *   [ ] 24.2 添加排序方法到 `AccountsPage`。
    *   [ ] 24.3 修改 `main.py` 调用 `AccountsPage` 的排序方法，删除旧方法。
    *   [ ] 24.4 验证排序功能。
25.  ⬜ **步骤 25: 拆分清理逻辑**
    *   [ ] 25.1 打开 `utils.py`。
    *   [ ] 25.2 定义清理函数 (`cleanup_application`, `cleanup_old_temp_files`, `cleanup_temp_files`)。
    *   [ ] 25.3 修改 `main.py` 调用清理函数，删除旧方法。
    *   [ ] 25.4 验证程序退出时的清理。

## Task Progress
... (之前的日志) ...
- [2024-05-15 11:30] 完成步骤19：创建了`core/quota_manager.py`文件并定义了`QuotaManager`类，实现账户配额获取和刷新功能，并更新了`main.py`中的相关方法实现。将`main.py`中的`fetch_all_accounts_quota`、`update_fetch_progress`、`on_all_quotas_fetched`、`_fetch_other_accounts_quota`、`_auto_refresh_current_quota`、`_background_refresh_quota`、`_fetch_current_account_quota`和`_fetch_and_update_ui_on_main_thread`方法替换为对QuotaManager实例的方法调用。同时保留了`update_account_quota`方法用于处理信号连接。
- [日期 时间] **更新**: 更新拆分方案，添加步骤 20-25，计划拆分账户加载、保存、进程管理、侧边栏创建、排序和清理逻辑。

## 下一步计划
当前计划执行 **步骤 20: 拆分账户数据加载相关方法**。

## 建议的下一步骤（步骤20）

请指示我开始执行 **步骤 20**：

```
请执行拆分方案中的步骤 20：拆分账户数据加载相关方法。