import os
import sys
import json
import shutil
import zipfile
import datetime
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QScrollArea, QPushButton, QFrame, QSpacerItem,
    QSizePolicy, QGridLayout, QGraphicsDropShadowEffect,
    QRadioButton, QFileDialog, QButtonGroup, QComboBox, QSpinBox, QMessageBox
)
from PySide6.QtCore import Signal, Qt, QTimer, QUrl
from PySide6.QtGui import QFont, QColor, QDesktopServices

from theme import Theme
from utils import get_app_data_dir
from widgets.styled_widgets import StyledFrame, StyledSwitch, StyledButton, StyledLineEdit, StyledComboBox
from widgets.auto_register_settings import AutoRegisterSettings

class SettingsPage(QWidget):
    """设置页面类 - 重新设计的现代界面"""
    
    # 定义信号
    settings_saved = Signal(dict)
    
    def __init__(self):
        super().__init__()
        # 设置文件路径
        self.settings_file = os.path.join(get_app_data_dir(), "settings.json")
        self.settings = {}
        
        # 加载设置
        self.load_settings()
        
        # 初始化UI
        self.init_ui()
    
    def init_ui(self):
        """初始化现代化用户界面"""
        # 设置整个页面的背景色
        self.setStyleSheet("background-color: " + Theme.GLASS_BG + ";")
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(20)
        
        # 创建设置容器 - 作为固定背景，不在滚动区域内
        settings_container = QWidget()
        settings_container.setObjectName("settingsContainer")
        settings_container.setStyleSheet("""
            #settingsContainer {
                background-color: """ + Theme.SECONDARY + """;
                border-radius: """ + Theme.BORDER_RADIUS + """;
            }
        """)
        
        # 设置容器的布局
        container_layout = QVBoxLayout(settings_container)
        container_layout.setContentsMargins(30, 30, 30, 30)
        container_layout.setSpacing(25)
        
        # 创建设置内容容器（带滚动条）- 放在设置容器内
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QScrollArea.Shape.NoFrame)
        scroll_area.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        # 隐藏垂直滚动条
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background: transparent;
                width: 0px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: transparent;
                border-radius: 0px;
                min-height: 0px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """)
        
        # 创建内容容器 - 用于放置卡片，这个会滚动
        content_widget = QWidget()
        content_widget.setObjectName("contentWidget")
        content_widget.setStyleSheet("""
            #contentWidget {
                background-color: transparent;
            }
        """)
        
        # 内容布局
        settings_layout = QVBoxLayout(content_widget)
        settings_layout.setContentsMargins(0, 0, 0, 0)  # 移除内容容器边距，因为已在外层容器设置
        settings_layout.setSpacing(25)
        settings_layout.setAlignment(Qt.AlignmentFlag.AlignHCenter)  # 使卡片水平居中
        
        # 设置滚动区域的内容为内容容器
        scroll_area.setWidget(content_widget)
        
        # 将滚动区域添加到设置容器中
        container_layout.addWidget(scroll_area)
        
        # 将设置容器添加到主布局
        main_layout.addWidget(settings_container)
        
        # ====== 自动注册配置卡片 ======
        auto_register_card = self.create_card_widget()
        auto_register_layout = QVBoxLayout(auto_register_card)
        auto_register_layout.setContentsMargins(25, 25, 25, 25)
        auto_register_layout.setSpacing(20)
        
        # 添加卡片标题 - 使用更加现代的设计
        auto_register_title_layout = QHBoxLayout()
        auto_register_title_layout.setContentsMargins(0, 0, 0, 10)  # 为标题添加下边距
        auto_register_title_layout.setSpacing(10)
        
        # 使用带颜色的图标
        auto_register_icon = QLabel("🤖")
        auto_register_icon.setStyleSheet("font-size: 18px;")
        auto_register_title_layout.addWidget(auto_register_icon)
        
        auto_register_title = QLabel("自动注册配置")
        auto_register_title.setFont(QFont(Theme.FONT_FAMILY, 16, QFont.Weight.Bold))
        auto_register_title.setStyleSheet("color: " + Theme.TEXT_PRIMARY + ";")
        auto_register_title_layout.addWidget(auto_register_title)
        auto_register_title_layout.addStretch()
        
        # 添加帮助按钮
        help_button = QPushButton("帮助")
        help_button.setCursor(Qt.CursorShape.PointingHandCursor)
        help_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 4px 10px;
                font-size: {Theme.FONT_SIZE_SMALL};
                font-weight: bold;
                min-width: 60px;
                min-height: 26px;
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: {Theme.ACCENT_PRESSED};
            }}
        """)
        help_button.clicked.connect(self.open_auto_register_help)
        auto_register_title_layout.addWidget(help_button)
        
        auto_register_layout.addLayout(auto_register_title_layout)
        
        # 添加自动注册设置组件
        auto_register_settings = AutoRegisterSettings()
        auto_register_layout.addWidget(auto_register_settings)
        
        # 添加到设置布局
        settings_layout.addWidget(auto_register_card)
        
        # ====== 偏好设置卡片 ======
        preferences_card = self.create_card_widget()
        preferences_layout = QVBoxLayout(preferences_card)
        preferences_layout.setContentsMargins(25, 25, 25, 25)
        preferences_layout.setSpacing(20)
        
        # 添加卡片标题 - 使用更加现代的设计
        pref_title_layout = QHBoxLayout()
        pref_title_layout.setContentsMargins(0, 0, 0, 10)  # 为标题添加下边距
        pref_title_layout.setSpacing(10)
        
        # 使用带颜色的图标
        pref_icon = QLabel("⚙️")
        pref_icon.setStyleSheet("font-size: 18px;")
        pref_title_layout.addWidget(pref_icon)
        
        pref_title = QLabel("偏好设置")
        pref_title.setFont(QFont(Theme.FONT_FAMILY, 16, QFont.Weight.Bold))
        pref_title.setStyleSheet("color: " + Theme.TEXT_PRIMARY + ";")
        pref_title_layout.addWidget(pref_title)
        pref_title_layout.addStretch()
        
        preferences_layout.addLayout(pref_title_layout)
        
        # 不使用传统分隔线，改用留白和背景色变化创建视觉分隔
        
        # 创建偏好设置内容区域 - 使用卡片式设计
        pref_content = QWidget()
        pref_content.setObjectName("prefContent")
        pref_content.setStyleSheet("""
            #prefContent {
                background-color: transparent;
                border-radius: 12px;
            }
        """)
        pref_content_layout = QVBoxLayout(pref_content)
        pref_content_layout.setContentsMargins(20, 20, 20, 20)
        pref_content_layout.setSpacing(15)
        
        # 自动隐藏保存当前账户信息按钮设置项
        auto_hide_item = self.create_setting_item(
            "💾", # 修改图标为保存
            "自动隐藏\"保存当前账户信息\"按钮",
            "开启后如果json里有当前登录账户信息，将隐藏功能页面里的\"保存当前账户信息\"按钮"
        )
        
        # 添加开关
        self.auto_hide_switch = StyledSwitch()
        self.auto_hide_switch.setChecked(self.settings.get("auto_hide_save_button", False))
        # 连接开关状态变化信号到自动保存函数
        self.auto_hide_switch.stateChanged.connect(self.auto_save_settings)
        auto_hide_item["control_layout"].addWidget(self.auto_hide_switch)
        
        # 添加到偏好设置内容区域
        pref_content_layout.addLayout(auto_hide_item["main_layout"])
        
        # 添加一点空间
        spacer = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        pref_content_layout.addItem(spacer)
        
        # 操作成功时自动关闭对话框设置项
        auto_close_item = self.create_setting_item(
            "✅", # 修改图标为成功
            "操作成功时自动关闭对话框",
            "开启后，重置机器码和自动注册功能在操作成功后将自动关闭对话框"
        )
        
        # 添加开关
        self.auto_close_switch = StyledSwitch()
        self.auto_close_switch.setChecked(self.settings.get("auto_close_dialog_on_success", False))
        # 连接开关状态变化信号到自动保存函数
        self.auto_close_switch.stateChanged.connect(self.auto_save_settings)
        auto_close_item["control_layout"].addWidget(self.auto_close_switch)
        
        # 添加到偏好设置内容区域
        pref_content_layout.addLayout(auto_close_item["main_layout"])
        
        # 添加一点空间
        spacer2 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        pref_content_layout.addItem(spacer2)
        
        # 关闭Cursor后自动启动设置项
        auto_restart_cursor_item = self.create_setting_item(
            "🚀", # 修改图标为启动
            "自动启动 Cursor",
            "开启后，在运行 切换、自动注册 功能时终止 Cursor 后会自动重启 Cursor"
        )
        
        # 添加开关
        self.auto_restart_cursor_switch = StyledSwitch()
        self.auto_restart_cursor_switch.setChecked(self.settings.get("auto_restart_cursor", True))
        # 连接开关状态变化信号到自动保存函数
        self.auto_restart_cursor_switch.stateChanged.connect(self.auto_save_settings)
        auto_restart_cursor_item["control_layout"].addWidget(self.auto_restart_cursor_switch)
        
        # 添加到偏好设置内容区域
        pref_content_layout.addLayout(auto_restart_cursor_item["main_layout"])
        
        # 添加一点空间
        spacer3 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        pref_content_layout.addItem(spacer3)
        
        # 自动隐藏自定义Cursor路径设置项
        auto_hide_custom_path_item = self.create_setting_item(
            "👁️", # 保持眼睛图标
            "自动隐藏自定义 Cursor 路径设置",
            "开启后，当能正常找到 Cursor 时自动隐藏功能页面的自定义 Cursor 路径设置"
        )
        
        # 添加开关
        self.auto_hide_custom_path_switch = StyledSwitch()
        self.auto_hide_custom_path_switch.setChecked(self.settings.get("auto_hide_custom_path", False)) # 从加载的设置初始化
        # 连接开关状态变化信号到自动保存函数
        self.auto_hide_custom_path_switch.stateChanged.connect(self.auto_save_settings)
        auto_hide_custom_path_item["control_layout"].addWidget(self.auto_hide_custom_path_switch)
        
        # 添加到偏好设置内容区域
        pref_content_layout.addLayout(auto_hide_custom_path_item["main_layout"])
        
        # 添加到偏好设置卡片
        preferences_layout.addWidget(pref_content)
        
        # 添加到设置布局
        settings_layout.addWidget(preferences_card)
        
        # ====== 系统设置卡片 ======
        system_settings_card = self.create_card_widget()
        system_settings_layout = QVBoxLayout(system_settings_card)
        system_settings_layout.setContentsMargins(25, 25, 25, 25)
        system_settings_layout.setSpacing(20)
        
        # 添加卡片标题 - 使用更加现代的设计
        system_title_layout = QHBoxLayout()
        system_title_layout.setContentsMargins(0, 0, 0, 10)  # 为标题添加下边距
        system_title_layout.setSpacing(10)
        
        # 使用带颜色的图标
        system_icon = QLabel("⚙️")
        system_icon.setStyleSheet("font-size: 18px;")
        system_title_layout.addWidget(system_icon)
        
        system_title = QLabel("系统设置")
        system_title.setFont(QFont(Theme.FONT_FAMILY, 16, QFont.Weight.Bold))
        system_title.setStyleSheet("color: " + Theme.TEXT_PRIMARY + ";")
        system_title_layout.addWidget(system_title)
        system_title_layout.addStretch()
        
        system_settings_layout.addLayout(system_title_layout)
        
        # 创建系统设置内容区域
        system_content = QWidget()
        system_content.setObjectName("systemContent")
        system_content.setStyleSheet("""
            #systemContent {
                background-color: transparent;
                border-radius: 12px;
            }
        """)
        system_content_layout = QVBoxLayout(system_content)
        system_content_layout.setContentsMargins(20, 20, 20, 20)
        system_content_layout.setSpacing(15)
        
        # 账户数据JSON文件位置设置
        account_json_item = self.create_setting_item(
            "📁", 
            "账户数据文件位置",
            "设置账户数据JSON文件所在位置"
        )
        
        # 创建位置选择下拉框
        self.json_location_combo = StyledComboBox()
        self.json_location_combo.addItem("默认位置", "default")
        self.json_location_combo.addItem("自定义位置", "custom")
        self.json_location_combo.setMinimumHeight(32)  # 设置固定高度
        self.json_location_combo.setFixedWidth(150)  # 设置固定宽度
        self.json_location_combo.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        # 连接下拉框改变信号
        self.json_location_combo.currentIndexChanged.connect(self.on_json_location_combo_changed)
        account_json_item["control_layout"].addWidget(self.json_location_combo)
        
        # 添加文件路径显示和选择按钮
        json_location_layout = QHBoxLayout()
        json_location_layout.setContentsMargins(24, 10, 0, 0)  # 左侧缩进，与图标对齐
        json_location_layout.setSpacing(10)
        
        # 创建路径显示容器
        path_container = QWidget()
        path_container.setObjectName("pathContainer")
        path_container.setStyleSheet(f"""
            #pathContainer {{
                background-color: rgba(30, 33, 40, 0.4);
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                border: 1px solid {Theme.BORDER};
            }}
        """)
        # 添加阴影效果
        path_shadow = QGraphicsDropShadowEffect()
        path_shadow.setBlurRadius(6)  # 减小模糊半径
        path_shadow.setColor(QColor(0, 0, 0, 60))  # 减小不透明度
        path_shadow.setOffset(0, 2)
        path_container.setGraphicsEffect(path_shadow)
        
        # 保存路径容器的引用以便在on_json_location_combo_changed中使用
        self.path_container = path_container
        
        path_layout = QHBoxLayout(path_container)
        path_layout.setContentsMargins(8, 4, 8, 4)  # 减小内边距使其更紧凑
        path_layout.setSpacing(6)
        
        # 添加文件图标
        file_icon = QLabel("📄")
        file_icon.setStyleSheet("background: transparent; font-size: 14px;")  # 减小图标大小
        path_layout.addWidget(file_icon)
        
        self.custom_json_path = StyledLineEdit()
        self.custom_json_path.setReadOnly(True)
        self.custom_json_path.setPlaceholderText('点击"选择文件"选择JSON文件')
        self.custom_json_path.setStyleSheet(f"""
            border: none;
            background-color: transparent;
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_SMALL};  /* 减小字体大小 */
            padding: 0;
        """)
        self.custom_json_path.setFixedHeight(26)  # 固定高度更紧凑
        custom_json_path_value = self.settings.get("custom_json_file_path", "")
        if custom_json_path_value:
            self.custom_json_path.setText(custom_json_path_value)
        path_layout.addWidget(self.custom_json_path)
        
        json_location_layout.addWidget(path_container, 3)  # 给路径框更多空间
        
        # 选择文件按钮 - 美化并减小尺寸
        self.select_json_file_btn = QPushButton("选择文件")
        self.select_json_file_btn.setObjectName("selectFileBtn")
        self.select_json_file_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.select_json_file_btn.setStyleSheet(f"""
            #selectFileBtn {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 4px 10px;  /* 减小内边距 */
                font-size: {Theme.FONT_SIZE_SMALL};  /* 减小字体 */
                font-weight: bold;
                min-width: 80px;  /* 减小最小宽度 */
                min-height: 26px;  /* 减小高度 */
            }}
            #selectFileBtn:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            #selectFileBtn:pressed {{
                background-color: {Theme.ACCENT};
            }}
        """)
        # 添加阴影效果但减小阴影
        btn_shadow = QGraphicsDropShadowEffect()
        btn_shadow.setBlurRadius(6)  # 减小模糊半径
        btn_shadow.setColor(QColor(0, 0, 0, 60))  # 减小不透明度
        btn_shadow.setOffset(0, 1)  # 减小偏移
        self.select_json_file_btn.setGraphicsEffect(btn_shadow)
        
        self.select_json_file_btn.clicked.connect(self.select_json_file)
        json_location_layout.addWidget(self.select_json_file_btn, 1)
        
        # 添加路径显示和按钮到布局
        account_json_item["main_layout"].addLayout(json_location_layout)
        
        # 设置初始选中状态
        use_custom_json = self.settings.get("use_custom_json_file", False)
        if use_custom_json and custom_json_path_value:
            self.json_location_combo.setCurrentIndex(1)  # 自定义位置
            self.custom_json_path.setVisible(True)
            self.select_json_file_btn.setVisible(True)
            path_container.setVisible(True)  # 确保路径容器可见
        else:
            self.json_location_combo.setCurrentIndex(0)  # 默认位置
            self.custom_json_path.setVisible(False)
            self.select_json_file_btn.setVisible(False)
            path_container.setVisible(False)  # 确保路径容器隐藏
        
        # 添加到系统设置内容区域
        system_content_layout.addLayout(account_json_item["main_layout"])
        
        # ===== 新增首页自动刷新时间设置项 =====
        refresh_time_item = self.create_setting_item(
            "⏲️", 
            "首页自动刷新时间",
            "设置首页自动刷新数据的时间间隔，范围5~600秒"
        )
        self.home_refresh_input = StyledLineEdit()
        self.home_refresh_input.setPlaceholderText("默认：30")
        self.home_refresh_input.setText(str(self.settings.get("home_auto_refresh_interval", 30)))
        self.home_refresh_input.setMinimumHeight(32)
        self.home_refresh_input.setFixedWidth(80)
        self.home_refresh_input.setStyleSheet("padding: 0px 8px;")
        self.home_refresh_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # 移除textChanged信号，改为focusOutEvent
        # self.home_refresh_input.textChanged.connect(self.on_home_refresh_input_changed)
        self.home_refresh_input.focusOutEvent = self.on_home_refresh_input_focus_out
        refresh_time_item["control_layout"].addWidget(self.home_refresh_input)
        system_content_layout.addLayout(refresh_time_item["main_layout"])
        # ===== END 新增首页自动刷新时间设置项 =====
        
        # ===== 新增应用数据导入导出设置项 =====
        data_transfer_item = self.create_setting_item(
            "🗂️", 
            "应用数据导入导出",
            "可将当前应用数据导出为备份包，或从备份包恢复数据"
        )
        self.export_data_btn = StyledButton("导出")
        self.export_data_btn.setMinimumHeight(32)
        self.export_data_btn.setFixedWidth(80)
        self.export_data_btn.clicked.connect(self.on_export_data_clicked)
        self.import_data_btn = StyledButton("导入")
        self.import_data_btn.setMinimumHeight(32)
        self.import_data_btn.setFixedWidth(80)
        self.import_data_btn.clicked.connect(self.on_import_data_clicked)
        # 按钮间距与输入框控件一致
        btn_layout = QHBoxLayout()
        btn_layout.setContentsMargins(0, 0, 0, 0)
        btn_layout.setSpacing(12)  # 与输入框控件间距一致
        btn_layout.addWidget(self.export_data_btn)
        btn_layout.addWidget(self.import_data_btn)
        data_transfer_item["control_layout"].addLayout(btn_layout)
        system_content_layout.addLayout(data_transfer_item["main_layout"])
        # ===== END 应用数据导入导出设置项 =====
        
        # 添加系统设置内容到系统设置卡片
        system_settings_layout.addWidget(system_content)
        
        # ====== 导出/导入应用数据按钮 ======
        # （本段落全部移除，改为统一风格设置项模块实现）
        # ====== END 导出/导入应用数据按钮 ======
        
        # 添加到设置布局
        settings_layout.addWidget(system_settings_card)
        
        # 添加底部空白区域（删除保存按钮）
        settings_layout.addStretch()
    
    def on_json_location_combo_changed(self, index):
        """处理JSON文件位置选项变更"""
        is_custom = (index == 1)  # 1表示"自定义位置"
        
        # 显示/隐藏自定义文件路径选择
        self.custom_json_path.setVisible(is_custom)
        self.select_json_file_btn.setVisible(is_custom)
        self.path_container.setVisible(is_custom)  # 设置路径容器的可见性
        
        # 更新设置
        self.settings["use_custom_json_file"] = is_custom
        
        # 如果选择默认位置，清除自定义路径
        if not is_custom:
            self.settings["custom_json_file_path"] = ""
            self.custom_json_path.setText("")
            # 保存设置
            self.save_settings_to_file()
            
            # 使用延迟发送设置更改信号，避免UI闪烁问题
            QTimer.singleShot(300, lambda: self.settings_saved.emit(self.settings))
        else:
            # 如果选择自定义位置且没有设置过路径，自动打开文件选择对话框
            if not self.custom_json_path.text():
                QTimer.singleShot(100, self.select_json_file)
            else:
                # 保存设置
                self.save_settings_to_file()
                
                # 使用延迟发送设置更改信号，避免UI闪烁问题
                QTimer.singleShot(300, lambda: self.settings_saved.emit(self.settings))
    
    def select_json_file(self):
        """选择JSON文件"""
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "选择 cursor_accounts.json 文件",
            "",
            "JSON Files (*.json);;All Files (*)"
        )
        
        if file_name:
            self.custom_json_path.setText(file_name)
            self.settings["custom_json_file_path"] = file_name
            self.save_settings_to_file()
            
            # 使用延迟发送设置更改信号，避免UI闪烁和空白弹窗问题
            # 延迟发送信号给应用足够的时间处理UI状态变化
            QTimer.singleShot(300, lambda: self.settings_saved.emit(self.settings))
    
    def create_card_widget(self):
        """创建卡片小部件"""
        card = QWidget()
        card.setObjectName("settingCard")
        card.setMaximumWidth(800)  # 限制最大宽度
        card.setStyleSheet("""
            #settingCard {
                background-color: """ + Theme.GLASS_BG + """;
                border-radius: """ + Theme.BORDER_RADIUS + """;
                border: 1px solid """ + Theme.GLASS_BORDER + """;
                margin: 4px;
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)  # 增加模糊半径
        shadow.setColor(QColor(0, 0, 0, 100))  # 增加不透明度
        shadow.setOffset(0, 5)  # 增加阴影偏移
        card.setGraphicsEffect(shadow)  # 应用阴影效果到卡片
        
        return card
    
    def create_separator(self):
        """创建分隔线"""
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("background-color: " + Theme.GLASS_BORDER + ";")
        separator.setFixedHeight(1)
        return separator
    
    def create_setting_item(self, icon, title, description=None):
        """创建设置项"""
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(8)
        
        # 标题和控件布局
        title_layout = QHBoxLayout()
        title_layout.setSpacing(10)
        
        # 图标
        icon_label = QLabel(icon)
        icon_label.setFont(QFont(Theme.FONT_FAMILY, 14))
        title_layout.addWidget(icon_label)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("color: " + Theme.TEXT_PRIMARY + "; font-size: " + Theme.FONT_SIZE_NORMAL + ";")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 控件区域
        control_layout = QHBoxLayout()
        control_layout.setSpacing(0)
        title_layout.addLayout(control_layout)
        
        main_layout.addLayout(title_layout)
        
        # 描述（如果有）
        if description:
            desc_layout = QHBoxLayout()
            desc_layout.setContentsMargins(24, 0, 0, 0)  # 左侧缩进，与图标对齐
            
            desc_label = QLabel(description)
            desc_label.setWordWrap(True)
            desc_label.setStyleSheet("font-size: " + Theme.FONT_SIZE_SMALL + "; color: " + Theme.TEXT_SECONDARY + "; background-color: transparent;")
            desc_layout.addWidget(desc_label)
            
            main_layout.addLayout(desc_layout)
        
        # 返回布局和控件区域，以便外部添加控件
        return {
            "main_layout": main_layout,
            "control_layout": control_layout
        }
    
    def load_settings(self):
        """从settings.json加载设置"""
        try:
            # 检查文件是否存在
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    self.settings = json.load(f)
                # 兼容旧配置，补充新项默认值
                if "home_auto_refresh_interval" not in self.settings:
                    self.settings["home_auto_refresh_interval"] = 30
            else:
                # 默认设置
                default_settings = {
                    "auto_hide_save_button": False,
                    "auto_close_dialog_on_success": True,
                    "auto_restart_cursor": True,
                    "use_custom_json_file": False,
                    "custom_json_file_path": "",
                    "auto_hide_custom_path": True,
                    "home_auto_refresh_interval": 30
                }
                # 如果文件不存在，使用默认设置
                self.settings = default_settings
                # 立即保存默认设置
                self.save_settings_to_file()
        except Exception as e:
            print(f"加载设置时出错: {str(e)}")
            # 使用默认设置
            self.settings = {
                "auto_hide_save_button": False,
                "auto_close_dialog_on_success": True,
                "auto_restart_cursor": True,
                "use_custom_json_file": False,
                "custom_json_file_path": "",
                "auto_hide_custom_path": True,
                "home_auto_refresh_interval": 30
            }
    
    def save_settings_to_file(self):
        """将设置保存到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)
            # 补充新项默认值
            if "home_auto_refresh_interval" not in self.settings:
                self.settings["home_auto_refresh_interval"] = 30
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=4)
            print("设置已保存到文件")
            # 发送信号，传递保存后的设置
            self.settings_saved.emit(self.settings)
            return True
        except Exception as e:
            print(f"保存设置时出错: {str(e)}")
            return False
    
    def auto_save_settings(self):
        """自动保存设置，当任何开关状态改变时调用"""
        # 更新设置字典
        self.settings["auto_hide_save_button"] = self.auto_hide_switch.isChecked()
        self.settings["auto_close_dialog_on_success"] = self.auto_close_switch.isChecked()
        self.settings["auto_restart_cursor"] = self.auto_restart_cursor_switch.isChecked()
        self.settings["auto_hide_custom_path"] = self.auto_hide_custom_path_switch.isChecked()
        # 新增自动刷新时间的保存
        if hasattr(self, "home_refresh_input"):
            self.settings["home_auto_refresh_interval"] = int(self.home_refresh_input.text())
        # 保存到文件
        self.save_settings_to_file()
        # 发送信号
        self.settings_saved.emit(self.settings)
    
    def open_auto_register_help(self):
        """打开自动注册配置的帮助文档"""
        help_url = QUrl("https://docs.qq.com/aio/p/sc2qff8gzww0ak1?p=TKqrmzfCkQcbQnP5Lk70iU")
        QDesktopServices.openUrl(help_url)
    
    def on_home_refresh_input_focus_out(self, event):
        # 只允许数字，范围5~600
        try:
            value = self.home_refresh_input.text()
            v = int(value)
            if v < 5:
                v = 5
            elif v > 600:
                v = 600
        except Exception:
            v = 30
        # 自动修正输入
        if str(v) != self.home_refresh_input.text():
            self.home_refresh_input.setText(str(v))
        # 仅在变更时保存并toast
        if self.settings.get("home_auto_refresh_interval", 30) != v:
            self.settings["home_auto_refresh_interval"] = v
            self.save_settings_to_file()
            # 使用全局toast提示
        if hasattr(self, 'settings_saved'):
            self.settings_saved.emit(self.settings)
        # 调用主窗口toast
        parent = self.parent()
        while parent is not None and not hasattr(parent, 'show_toast'):
            parent = parent.parent()
        if parent is not None and hasattr(parent, 'show_toast'):
            parent.show_toast("首页自动刷新时间已更改，重启YCursor后生效")
        # 恢复原有focusOutEvent
        super(type(self.home_refresh_input), self.home_refresh_input).focusOutEvent(event)

    def on_export_data_clicked(self):
        try:
            # 获取下载目录
            home_dir = os.path.expanduser('~')
            downloads_dir = os.path.join(home_dir, 'Downloads')
            if os.path.exists(downloads_dir):
                default_dir = downloads_dir
            else:
                default_dir = home_dir
            # 选择导出目录，默认定位下载目录
            export_dir = QFileDialog.getExistingDirectory(self, "选择导出目录", default_dir)
            if not export_dir:
                return
            # 生成zip包名
            now = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            zip_name = f"ycursor_backup_{now}.zip"
            zip_path = os.path.join(export_dir, zip_name)
            # 应用数据目录
            data_dir = get_app_data_dir()
            # 唯一标识文件名和内容
            signature_filename = "YCURSOR_BACKUP_SIGNATURE.txt"
            signature_content = "YCURSOR_BACKUP_v1"
            # 打包
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                for root, dirs, files in os.walk(data_dir):
                    # 排除 temp_quota_data 目录
                    if "temp_quota_data" in dirs:
                        dirs.remove("temp_quota_data")
                    for file in files:
                        if file == "ycursor.lock":
                            continue
                        abs_path = os.path.join(root, file)
                        rel_path = os.path.relpath(abs_path, data_dir)
                        zf.write(abs_path, os.path.join("data", rel_path))
                # 写入唯一标识文件
                zf.writestr(signature_filename, signature_content)
            # toast提示
            parent = self.parent()
            while parent is not None and not hasattr(parent, 'show_toast'):
                parent = parent.parent()
            if parent is not None and hasattr(parent, 'show_toast'):
                parent.show_toast(f"应用数据已成功导出到 {zip_path}")
        except Exception as e:
            parent = self.parent()
            while parent is not None and not hasattr(parent, 'show_toast'):
                parent = parent.parent()
            if parent is not None and hasattr(parent, 'show_toast'):
                parent.show_toast(f"导出失败: {str(e)}")

    def on_import_data_clicked(self):
        try:
            # 选择zip文件
            zip_file, _ = QFileDialog.getOpenFileName(self, "选择应用数据备份包", "", "Zip Files (*.zip)")
            if not zip_file:
                return
            # 校验唯一标识
            signature_filename = "YCURSOR_BACKUP_SIGNATURE.txt"
            signature_content = "YCURSOR_BACKUP_v1"
            with zipfile.ZipFile(zip_file, 'r') as zf:
                if signature_filename not in zf.namelist():
                    raise Exception("备份包无效：缺少唯一标识文件")
                if zf.read(signature_filename).decode("utf-8").strip() != signature_content:
                    raise Exception("备份包无效：唯一标识内容不符")
                # 解压data目录到应用数据目录
                data_dir = get_app_data_dir()
                # 先清空原有数据
                for root, dirs, files in os.walk(data_dir):
                    for file in files:
                        try:
                            os.remove(os.path.join(root, file))
                        except Exception:
                            pass
                # 解压data目录
                for member in zf.namelist():
                    if member.startswith("data/") and not member.endswith("/"):
                        rel_path = os.path.relpath(member, "data")
                        target_path = os.path.join(data_dir, rel_path)
                        os.makedirs(os.path.dirname(target_path), exist_ok=True)
                        with open(target_path, "wb") as f:
                            f.write(zf.read(member))
            # toast提示
            parent = self.parent()
            while parent is not None and not hasattr(parent, 'show_toast'):
                parent = parent.parent()
            if parent is not None and hasattr(parent, 'show_toast'):
                parent.show_toast("应用数据导入成功，请重启YCursor以生效")
        except Exception as e:
            parent = self.parent()
            while parent is not None and not hasattr(parent, 'show_toast'):
                parent = parent.parent()
            if parent is not None and hasattr(parent, 'show_toast'):
                parent.show_toast(f"导入失败: {str(e)}") 