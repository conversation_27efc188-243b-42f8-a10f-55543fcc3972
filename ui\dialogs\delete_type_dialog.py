#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
删除类型对话框模块
提供按账户类型删除账户的对话框
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QRadioButton, QButtonGroup, QFrame, QComboBox
)
from PySide6.QtCore import Qt

from theme import Theme
from utils import Utils
from logger import info
from widgets.dialog import StyledDialog


class DeleteTypeDialog(QFrame):
    """按类型删除账户对话框类"""
    
    def __init__(self, main_window, parent=None):
        """初始化删除类型对话框
        
        Args:
            main_window: 主窗口实例 (CursorAccountManager)
            parent: 父窗口
        """
        super().__init__(parent)
        self.main_window = main_window
        self.dialog = StyledDialog(parent, "按账户类型删除")
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        # 添加说明文本
        description = QLabel("选择要删除的账户类型：")
        description.setStyleSheet(f"""
                color: {Theme.TEXT_PRIMARY};
                font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        self.dialog.addWidget(description)
        
        # 创建条件选择区域的背景框架
        conditions_frame = QFrame()
        conditions_frame.setObjectName("conditionsFrame")
        conditions_frame.setStyleSheet(f"""
            #conditionsFrame {{
                background-color: #121317;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        
        frame_layout = QVBoxLayout(conditions_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        
        # 创建类型选择区域
        self.type_combo = QComboBox()
        self.type_combo.addItems(["普通账户", "已过期"])
        self.type_combo.setStyleSheet(f"""
            QComboBox {{
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px 10px;
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_PRIMARY};
                min-height: 30px;
                combobox-popup: 0;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QComboBox:hover {{
                border-color: {Theme.ACCENT};
            }}
            QComboBox:focus {{
                border-color: {Theme.ACCENT};
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: center right;
                width: 30px;
                border-left-width: 0px;
                border-top-right-radius: {Theme.BORDER_RADIUS_SMALL};
                border-bottom-right-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            QComboBox::down-arrow {{
                image: url(:/icons/down_arrow.png);
                width: 16px;
                height: 16px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {Theme.CARD_LEVEL_1};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                outline: none;
                selection-background-color: {Theme.ACCENT};
                selection-color: white;
                color: {Theme.TEXT_PRIMARY};
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QComboBox QAbstractItemView::item {{
                min-height: 30px;
                padding: 5px 10px;
            }}
            QComboBox QAbstractItemView::item:hover {{
                background-color: #1E2128;
            }}
            QComboBox QAbstractItemView::item:selected {{
                background-color: {Theme.ACCENT};
                color: white;
            }}
        """)
        frame_layout.addWidget(self.type_combo)
        
        # 添加框架到对话框
        self.dialog.addWidget(conditions_frame)
        
        # 添加确认和取消按钮
        confirm_btn = self.dialog.addButtons("确认", "取消")
        # 将确认按钮连接到处理函数
        confirm_btn.clicked.connect(self._on_confirm_clicked)
    
    def _on_confirm_clicked(self):
        """确认按钮点击处理"""
        self.delete_accounts_by_type()
    
    def delete_accounts_by_type(self):
        """按账户类型删除账户"""
        # 获取选择的账户类型
        account_type = self.type_combo.currentText()
        
        # 确认是否要删除
        if not Utils.confirm_message(
            self.main_window,
            "按类型删除账户",
            f"确定要删除所有【{account_type}】类型的账户吗？",
        ):
            return
        
        # 在执行删除前，检查是否所有账户都已获取额度信息
        accounts_without_quota = [acc for acc in self.main_window.account_data.accounts if 'real_usage' not in acc]
        if accounts_without_quota:
            message = f"有{len(accounts_without_quota)}个账户未获取额度信息，是否先刷新所有账户额度？"
            if Utils.confirm_message(self.main_window, "建议先刷新额度", message):
                # 显示正在获取额度的提示
                self.main_window.show_toast("正在获取账户额度，请稍候...")
                # 获取所有账户额度
                self.main_window.fetch_all_accounts_quota(show_toast=True)
                # 在获取完成后执行删除操作
                self.main_window.quotaFetcher.all_quotas_fetched.connect(lambda: self._execute_delete_by_type(account_type))
                # 关闭对话框
                self.dialog.accept()
                return  # 先返回，等待额度获取完成
            
        # 直接执行删除
        self._execute_delete_by_type(account_type)
        # 关闭对话框
        self.dialog.accept()
    
    def _execute_delete_by_type(self, account_type):
        """实际执行按类型删除的操作"""
        total_accounts = len(self.main_window.account_data.accounts)
        accounts_to_delete = []
        
        # 记录操作开始
        info(f"开始按类型删除账户: 类型={account_type}, 总账户数={total_accounts}")
        
        # 基于选择的类型筛选需要删除的账户
        if account_type == "普通账户":
            # 获取所有普通账户（real_max_usage等于50的账户）
            accounts_to_delete = [
                acc for acc in self.main_window.account_data.accounts 
                if acc.get('real_max_usage') == 50
            ]
            info(f"按类型【普通账户】筛选，找到{len(accounts_to_delete)}个符合条件的账户")
        
        elif account_type == "已过期":
            # 获取所有已过期账户（real_remaining_days为"已过期"或"试用Pro已过期"）
            accounts_to_delete = [
                acc for acc in self.main_window.account_data.accounts 
                if acc.get('real_remaining_days') == "已过期" or acc.get('real_remaining_days') == "试用Pro已过期"
            ]
            info(f"按类型【已过期】筛选，找到{len(accounts_to_delete)}个符合条件的账户")
        
        if not accounts_to_delete:
            info(f"没有符合条件的账户可删除 (类型={account_type}, 总账户数={total_accounts})")
            self.main_window.show_toast(f"没有发现{account_type}类型的账户")
            return
        
        # 获取要删除的账户邮箱列表
        deleted_emails = [account.get("email") for account in accounts_to_delete]
        info(f"找到{len(deleted_emails)}个符合条件的账户准备删除: {', '.join(deleted_emails)}")
        
        # 更新账户列表
        self.main_window.account_data.accounts = [acc for acc in self.main_window.account_data.accounts if acc.get("email") not in deleted_emails]
        
        # 保存账户数据
        self.main_window.account_data.save_accounts()
        info("保存更新后的账户数据成功")
        
        if deleted_emails:
            self.main_window.show_toast(f"已删除 {len(deleted_emails)} 个{account_type}类型的账户")
            
            # 1. 重新加载本地存储的账户数据
            self.main_window.account_data.load_accounts()
            info("重新加载账户数据完成")
            
            # 2. 清除已删除账户的行
            for email in deleted_emails:
                if email in self.main_window.account_rows:
                    row_widget = self.main_window.account_rows[email]
                    self.main_window.accounts_layout.removeWidget(row_widget)
                    row_widget.deleteLater()
                    del self.main_window.account_rows[email]
            info("从UI中移除已删除账户完成")
            
            # 3. 更新账户计数
            self.main_window._update_accounts_count()
            
            # 4. 更新当前账户UI（如果当前账户被删除）
            if self.main_window.current_email in deleted_emails:
                info(f"当前账户 {self.main_window.current_email} 在删除列表中，需要切换当前账户")
                # 如果还有账户，切换到第一个
                if self.main_window.account_data.accounts:
                    new_current = self.main_window.account_data.accounts[0]
                    new_email = new_current.get("email", "")
                    self.main_window.current_email = new_email
                    info(f"切换当前账户至: {new_email}")
                else:
                    # 没有账户了，清空当前邮箱
                    self.main_window.current_email = ""
                    info("没有可用账户，清空当前账户")
                self.main_window.load_current_account()
            
            # 5. 仅使用不重建UI的排序方法
            self.main_window._sort_accounts_without_rebuild_ui()
            info("重新排序账户列表完成")
            
            # 6. 只在有账户的情况下刷新额度数据
            if self.main_window.account_data.accounts:
                info(f"开始刷新剩余账户额度数据（剩余{len(self.main_window.account_data.accounts)}个账户）")
                # 这里使用手动刷新模式，避免重建UI
                self.main_window.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=False)
        
        # 记录操作完成
        info(f"按类型删除账户操作完成: 类型={account_type}, 共删除{len(deleted_emails)}个账户")
    
    def exec(self):
        """执行对话框"""
        return self.dialog.exec() 