"""
Toast消息模块
提供临时消息显示功能，用于通知用户操作结果或重要信息
"""

from collections import deque
from PySide6.QtWidgets import QLabel, QGraphicsDropShadowEffect
from PySide6.QtCore import (
    Qt, QTimer, QPropertyAnimation, QSequentialAnimationGroup, 
    QPoint, Signal, QObject, QAbstractAnimation, QEasingCurve, QRect
)
from PySide6.QtGui import QColor
from PySide6.QtWidgets import QApplication

from theme import Theme


class ToastQueue(QObject):
    """Toast消息队列管理器，确保消息按顺序显示"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.queue = deque()
        self.current_toast = None
        self.is_showing = False
        self.max_queue_size = 10  # 最大队列长度
    
    def add_message(self, text, duration=2000, error=False):
        """添加新消息到队列
        
        Args:
            text: 消息文本
            duration: 显示持续时间(毫秒)
            error: 是否是错误消息
        """
        if len(self.queue) >= self.max_queue_size:
            # 队列已满，移除最早的消息
            self.queue.popleft()
        
        # 添加新消息到队列
        self.queue.append({
            'text': text,
            'duration': duration,
            'error': error
        })
        
        # 如果当前没有显示消息，开始显示
        if not self.is_showing:
            self._show_next()
    
    def _show_next(self):
        """显示队列中的下一条消息"""
        if not self.queue:
            self.is_showing = False
            return
        
        # 获取下一条消息
        message = self.queue.popleft()
        
        # 创建并显示Toast
        self.is_showing = True
        self.current_toast = ToastMessage(
            self.parent,
            message['text'],
            message['duration'],
            Theme.ERROR if message['error'] else None
        )
        
        # 连接动画完成信号
        self.current_toast.animation_finished.connect(self._on_toast_finished)
        
        # 显示Toast
        self.current_toast.showToast()
    
    def _on_toast_finished(self):
        """当前Toast显示完成的回调"""
        # 清理当前Toast
        if self.current_toast:
            self.current_toast.deleteLater()
            self.current_toast = None
        
        # 显示下一条消息
        self._show_next()


class ToastMessage(QLabel):
    """Toast消息提示，短暂显示后自动消失"""
    
    # 定义动画完成信号
    animation_finished = Signal()
    
    def __init__(self, parent, text, duration=2000, color=None):
        super().__init__(parent)
        self.parent = parent
        self.setText(text)
        self.duration = duration
        self.animation_group = None
        self.is_error = color == Theme.ERROR
        self.error_text = text  # 保存错误文本，用于点击时复制
        
        # 设置样式 - 使用醒目的实心背景
        if self.is_error:
            bg_color = Theme.ERROR
            border_color = "#D64550"  # 稍亮的红色边框
            # 设置鼠标指针样式为手型，提示可点击
            self.setCursor(Qt.CursorShape.PointingHandCursor)
            # 为错误提示添加点击提示
            self.setText(f"{text} (点击复制)")
        else:
            bg_color = Theme.ACCENT
            border_color = Theme.ACCENT_HOVER
        
        # 设置整体样式
        self.setStyleSheet(f"""
            background-color: {bg_color};
            color: white;
            border: 1px solid {border_color};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            padding: 12px 20px;
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
        """)
        
        # 设置位置
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setWordWrap(False)  # 禁用自动换行
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)
        
        # 初始化隐藏
        self.hide()
        
    def hideEvent(self, event):
        """重写隐藏事件，确保停止动画"""
        if self.animation_group:
            self.animation_group.stop()
        super().hideEvent(event)
        
    def mousePressEvent(self, event):
        """处理鼠标点击事件，如果是错误提示则复制到剪贴板"""
        if self.is_error:
            # 将错误信息复制到剪贴板
            clipboard = QApplication.clipboard()
            # 添加系统信息
            import platform
            from datetime import datetime
            
            error_info = f"\n=== YCursor 错误信息 ===\n"
            error_info += f"错误消息: {self.error_text}\n"
            error_info += f"操作系统: {platform.system()} {platform.version()}\n"
            error_info += f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            clipboard.setText(error_info)
            
            # 显示复制成功提示
            parent = self.parent
            while parent and not hasattr(parent, 'show_toast'):
                parent = parent.parent()
            
            if parent and hasattr(parent, 'show_toast'):
                parent.show_toast("错误信息已复制到剪贴板", error=False)
        
        super().mousePressEvent(event)
        
    def showToast(self):
        """显示Toast消息"""
        # 如果有正在运行的动画，先停止
        if self.animation_group and self.animation_group.state() == QAbstractAnimation.State.Running:
            self.animation_group.stop()
            self.animation_group = None
        
        # 调整大小以适应内容
        self.adjustSize()
        
        # 确保宽度足够
        min_width = 220  # 最小宽度
        if self.width() < min_width:
            self.setFixedWidth(min_width)
        
        # 设置位置在底部中央
        parent_rect = self.parent.rect()
        x = (parent_rect.width() - self.width()) // 2
        y = parent_rect.height() - self.height() - 40  # 距底部距离
        
        # 设置初始位置（从下方开始）
        self.move(x, parent_rect.height() + 20)
        self.show()
        
        # 创建位置动画（滑入）
        position_in = QPropertyAnimation(self, b"pos")
        position_in.setDuration(200)
        position_in.setStartValue(QPoint(x, parent_rect.height() + 20))
        position_in.setEndValue(QPoint(x, y))
        position_in.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 创建位置动画（滑出）
        position_out = QPropertyAnimation(self, b"pos")
        position_out.setDuration(200)
        position_out.setStartValue(QPoint(x, y))
        position_out.setEndValue(QPoint(x, parent_rect.height() + 20))
        position_out.setEasingCurve(QEasingCurve.Type.InCubic)
        
        # 创建动画组
        self.animation_group = QSequentialAnimationGroup()
        self.animation_group.addAnimation(position_in)
        self.animation_group.addPause(self.duration)
        self.animation_group.addAnimation(position_out)
        
        # 连接信号，确保在动画完成后隐藏并发送完成信号
        self.animation_group.finished.connect(self._on_animation_finished)
        
        # 开始动画
        self.animation_group.start()
    
    def _on_animation_finished(self):
        """动画完成后的处理"""
        # 隐藏Toast
        self.hide()
        # 删除动画组
        if self.animation_group:
            self.animation_group.deleteLater()
            self.animation_group = None
        # 发送完成信号
        self.animation_finished.emit()