"""
可点击复制到剪贴板的标签组件
定义了一个可复用的标签控件，点击后会将文本复制到剪贴板
"""

import sys
import time
import pyperclip
from PySide6.QtWidgets import QLabel, QHBoxLayout, QApplication, QGraphicsDropShadowEffect
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QPixmap, QPainter, QColor, QFontMetrics, QCursor

# 导入主题定义
from theme import Theme

class ClickableCopyLabel(QLabel):
    """可点击复制到剪贴板的标签类"""
    
    clicked = Signal()
    copied = Signal(str)
    
    def __init__(self, text="", parent=None, hide_content=False):
        """
        初始化点击复制标签
        
        Args:
            text: 要显示的文本内容
            parent: 父组件
            hide_content: 已废弃参数，保留只为兼容性
        """
        super().__init__(parent)
        self._original_text = text
        self._copy_text = text  # 要复制的文本
        
        # 清除QLabel本身的文本，防止文本重叠
        super().setText("")
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
        
        # 创建水平布局用于放置内容和复制图标
        self._layout = QHBoxLayout(self)
        self._layout.setContentsMargins(15, 8, 15, 8)
        self._layout.setSpacing(10)
        
        # 创建内容标签
        display_text = text if text else "未设置"
        self._content_label = QLabel(display_text)
        self._content_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; background: transparent;")
        self._content_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self._content_label.setTextFormat(Qt.TextFormat.PlainText)
        # 启用自动省略
        self._content_label.setTextInteractionFlags(Qt.TextInteractionFlag.NoTextInteraction)
        # 避免过长文本问题
        if len(display_text) > 40:
            self._content_label.setWordWrap(False)
            self._content_label.setTextElideMode(Qt.TextElideMode.ElideMiddle)
        
        self._layout.addWidget(self._content_label, 1)  # 占据大部分空间
        
        # 创建复制图标标签 - 使用Unicode字符而非emoji
        self._copy_icon = QLabel("⧉")
        self._copy_icon.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY}; 
            background: transparent; 
            font-size: 16px;
            padding-right: 5px;
        """)
        self._copy_icon.setFixedSize(24, 20)
        self._layout.addWidget(self._copy_icon, 0, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        
        # 设置样式 - 不设置内部边框
        self.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_2};
            color: {Theme.TEXT_PRIMARY};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        
        # 设置为可点击状态
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 固定高度
        self.setFixedHeight(45)
        
        # 重写setText方法
        self.setText = self._update_text
    
    def _update_text(self, text):
        """更新显示的文本"""
        self._original_text = text
        self._copy_text = text
        display_text = text if text else "未设置"
        self._content_label.setText(display_text)
        # 避免过长文本问题
        if len(display_text) > 40:
            self._content_label.setWordWrap(False)
            self._content_label.setTextElideMode(Qt.TextElideMode.ElideMiddle)
    
    def enterEvent(self, event):
        """鼠标悬停时改变样式 - 只改变背景色，不改变边框"""
        self.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_3};
            color: {Theme.TEXT_PRIMARY};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        self._copy_icon.setStyleSheet(f"""
            color: {Theme.ACCENT}; 
            background: transparent; 
            font-size: 16px;
            padding-right: 5px;
        """)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开时恢复样式"""
        self.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_2};
            color: {Theme.TEXT_PRIMARY};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        self._copy_icon.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY}; 
            background: transparent; 
            font-size: 16px;
            padding-right: 5px;
        """)
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """处理鼠标点击事件：复制内容到剪贴板"""
        if event.button() == Qt.MouseButton.LeftButton and self._copy_text:
            # 复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText(self._copy_text)
            
            # 临时改变样式表，表示已复制
            self.setStyleSheet(f"""
                background-color: {Theme.ACCENT};
                color: white;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            """)
            self._content_label.setStyleSheet("color: white; background: transparent;")
            self._copy_icon.setStyleSheet(f"""
                color: white; 
                background: transparent; 
                font-size: 16px;
                padding-right: 5px;
            """)
            self._copy_icon.setText("✓")
            
            # 0.5秒后恢复样式
            QTimer.singleShot(500, lambda: self._reset_style())
            
            # 寻找主窗口来显示提示
            parent = self.parent()
            while parent and not hasattr(parent, 'show_toast'):
                parent = parent.parent()
            
            if parent and hasattr(parent, 'show_toast'):
                parent.show_toast("已复制到剪贴板")
        
        super().mousePressEvent(event)
    
    def _reset_style(self):
        """重置样式为默认状态"""
        self.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_2};
            color: {Theme.TEXT_PRIMARY};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        self._content_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; background: transparent;")
        self._copy_icon.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY}; 
            background: transparent; 
            font-size: 16px;
            padding-right: 5px;
        """)
        self._copy_icon.setText("⧉") 