#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI更新线程模块
提供专用的UI更新线程类，用于处理UI更新请求
"""

import os
import sys
import time
import traceback
from datetime import datetime
from queue import Queue, Empty
from threading import Lock
from PySide6.QtCore import QThread, Signal


class UIUpdateThread(QThread):
    """专用的UI更新线程类
    
    维护一个更新请求队列，处理UI更新请求并通过信号通知主线程执行实际的UI更新操作
    """
    
    # 定义信号，用于通知主线程执行UI更新
    update_signal = Signal(str, dict)  # 参数：邮箱/操作类型, 更新数据
    batch_update_signal = Signal(object)  # 参数：批量更新数据(字典)
    log_update_signal = Signal(object)  # 参数：操作类型, 操作参数
    animation_signal = Signal(object)  # 参数：动画类型, 动画参数
    
    def __init__(self, parent=None, use_batch=False, min_interval=100):
        """初始化UI更新线程
        
        Args:
            parent: 父对象
            use_batch: 是否使用批处理模式
            min_interval: 最小更新间隔(毫秒)
        """
        super().__init__(parent)
        self.queue = Queue()  # 使用线程安全的队列存储更新请求
        self.animation_queue = Queue()  # 使用线程安全的队列存储动画请求
        self.running = True
        self.lock = Lock()  # 用于线程同步
        
        # 批处理相关
        self.use_batch = use_batch
        self.batch_updates = {}  # 批量更新数据
        self.last_update_time = 0  # 上次更新时间
        self.min_update_interval = min_interval  # 最小更新间隔(毫秒)
        
    def add_update_request(self, request_id, data):
        """添加更新请求到队列
        
        Args:
            request_id: 请求标识符(邮箱地址或操作类型)
            data: 更新数据
        """
        with self.lock:
            self.queue.put((request_id, data))
            # 根据请求ID类型输出不同的日志信息
            if request_id == "log_operation":
                operation = data.get("operation", "unknown")
                print(f"UI更新线程: 已添加日志操作请求 - {operation}")
            else:
                print(f"UI更新线程: 已添加邮箱 {request_id} 的更新请求")
    
    def add_animation_request(self, animation_type, params):
        """添加动画请求到队列
        
        Args:
            animation_type: 动画类型，如'page_transition', 'progress_bar'等
            params: 动画参数，包含动画所需的所有信息
        """
        with self.lock:
            self.animation_queue.put((animation_type, params))
            print(f"UI更新线程: 已添加动画请求 - {animation_type}")
    
    def stop(self):
        """停止线程"""
        print("UI更新线程: 停止中...")
        with self.lock:
            self.running = False
            
        # 立即处理剩余的更新请求
        self._process_remaining_updates()
        self._process_remaining_animations()
        
        # 等待线程结束
        if self.isRunning():
            self.wait()
        print("UI更新线程: 已停止")
    
    def _process_remaining_updates(self):
        """处理队列中剩余的更新请求"""
        print(f"UI更新线程: 处理剩余的更新请求，队列大小: {self.queue.qsize()}")
        if self.use_batch:
            # 批处理模式下，将所有更新请求合并
            while not self.queue.empty():
                try:
                    request_id, data = self.queue.get(block=False)
                    self.batch_updates[request_id] = data
                    self.queue.task_done()
                except Empty:
                    break
    
    def _process_remaining_animations(self):
        """处理队列中剩余的动画请求"""
        print(f"UI更新线程: 处理剩余的动画请求，队列大小: {self.animation_queue.qsize()}")
        while not self.animation_queue.empty():
            try:
                animation_type, params = self.animation_queue.get(block=False)
                print(f"UI更新线程: 发送动画信号 - {animation_type}")
                self.animation_signal.emit(animation_type, params)
                self.animation_queue.task_done()
            except Empty:
                break
            
            # 发送批量更新信号
            if self.batch_updates:
                print(f"UI更新线程: 发送批量更新信号，包含 {len(self.batch_updates)} 个更新请求")
                self.batch_update_signal.emit(self.batch_updates.copy())
                self.batch_updates.clear()
        else:
            # 非批处理模式下，逐个处理更新请求
            while not self.queue.empty():
                try:
                    request_id, data = self.queue.get(block=False)
                    if request_id == "log_operation":
                        operation = data.get("operation", "unknown")
                        print(f"UI更新线程: 发送日志操作信号 - {operation}")
                    else:
                        print(f"UI更新线程: 发送邮箱 {request_id} 的更新信号")
                    self.update_signal.emit(request_id, data)
                    self.queue.task_done()
                except Empty:
                    break
    
    def process_batch(self):
        """处理批量更新请求"""
        if not self.batch_updates:
            return
            
        # 检查是否达到最小更新间隔
        current_time = time.time() * 1000  # 转换为毫秒
        if current_time - self.last_update_time < self.min_update_interval:
            return
            
        # 发送信号到主线程执行实际的UI更新
        with self.lock:
            updates_copy = self.batch_updates.copy()
            self.batch_updates.clear()
            self.last_update_time = current_time
            
        print(f"UI更新线程: 发送批量更新信号，包含 {len(updates_copy)} 个更新请求")
        self.batch_update_signal.emit(updates_copy)
    
    def run(self):
        """线程主函数，处理队列中的更新请求"""
        print("UI更新线程: 已启动")
        
        while self.running:
            try:
                if self.use_batch:
                    # 批处理模式
                    try:
                        # 非阻塞方式检查队列
                        request_id, data = self.queue.get(block=False)
                        
                        # 将更新请求添加到批处理字典
                        with self.lock:
                            self.batch_updates[request_id] = data
                            
                        self.queue.task_done()
                    except Empty:
                        # 队列为空，继续处理批量更新
                        pass
                        
                    # 处理批量更新
                    self.process_batch()
                else:
                    # 非批处理模式
                    try:
                        # 使用较短的超时时间，确保能够及时响应停止请求
                        request_id, data = self.queue.get(timeout=0.1)
                        
                        # 发送信号到主线程执行实际的UI更新
                        if request_id == "log_operation":
                            operation = data.get("operation", "unknown")
                            print(f"UI更新线程: 发送日志操作信号 - {operation}")
                        else:
                            print(f"UI更新线程: 发送邮箱 {request_id} 的更新信号")
                        self.update_signal.emit(request_id, data)
                        
                        self.queue.task_done()
                    except Empty:
                        # 队列为空，继续等待
                        pass
                
                # 处理动画请求
                try:
                    # 非阻塞方式检查动画队列
                    animation_type, params = self.animation_queue.get(block=False)
                    print(f"UI更新线程: 发送动画信号 - {animation_type}")
                    self.animation_signal.emit(animation_type, params)
                    self.animation_queue.task_done()
                except Empty:
                    # 队列为空，继续
                    pass
                
                # 短暂休眠，降低CPU占用
                time.sleep(0.02)
                
            except Exception as e:
                print(f"UI更新线程处理请求时出错: {str(e)}")
        
        print("UI更新线程: 退出run方法")